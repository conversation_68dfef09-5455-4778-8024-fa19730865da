{"version": 3, "sources": ["../../@radix-ui/react-tabs/dist/packages/react/tabs/src/index.ts", "../../@radix-ui/react-tabs/dist/packages/react/tabs/src/Tabs.tsx"], "sourcesContent": ["export {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n} from './Tabs';\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps } from './Tabs';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value?: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = Radix.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;ACkBA,IAAMA,kCAAY;AAGlB,IAAM,CAACC,yCAAmBC,yCAApB,IAAuCC,yCAAmBH,iCAAW;EACzEI;CAD6D;AAG/D,IAAMC,iDAA2BD,yCAA2B;AAW5D,IAAM,CAACE,oCAAcC,oCAAf,IAAiCN,wCAAoCD,+BAAnB;AA6BxD,IAAMQ,gDAAOC,aAAAA,YACX,CAACC,OAA+BC,iBAAiB;AAC/C,QAAM,EAAA,aAEJC,OAAOC,WAFH,eAAA,cAAA,cAKU,cALV,KAAA,iBAOa,aACjB,GAAGC,UAAH,IACEJ;AACJ,QAAMK,YAAYC,0CAAaC,GAAD;AAC9B,QAAM,CAACL,OAAOM,QAAR,IAAoBC,yCAAqB;IAC7CC,MAAMP;IACNQ,UAAUC;IACVC,aAAaC;GAH+B;AAM9C,aACE,aAAAC,eAAC,oCADH;IAEI,OAAOC;IACP,QAAQC,0CAAK;IACb;IACA,eAAeT;IACf;IACA,KAAKH;IACL;SAEA,aAAAU,eAAC,0CAAU,KATb,SAAA;IAUI,KAAKV;IACL,oBAAkBa;KACdd,WAHN;IAIE,KAAKH;GAJP,CAAA,CATF;CApBO;AAwCb,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkB,sCAAgB;AAOtB,IAAMC,gDAAWrB,aAAAA,YACf,CAACC,OAAmCC,iBAAiB;AACnD,QAAM,EAAA,aAAA,OAAsB,MAAM,GAAGoB,UAAH,IAAiBrB;AACnD,QAAMsB,UAAUzB,qCAAesB,qCAAeH,WAAhB;AAC9B,QAAMO,wBAAwB5B,+CAAyBqB,WAAD;AACtD,aACE,aAAAD,eAAC,2CADH,SAAA;IAEI,SAAA;KACIQ,uBAFN;IAGE,aAAaD,QAAQJ;IACrB,KAAKI,QAAQf;IACb;GALF,OAOE,aAAAQ,eAAC,0CAAU,KAPb,SAAA;IAQI,MAAK;IACL,oBAAkBO,QAAQJ;KACtBG,WAHN;IAIE,KAAKpB;GAJP,CAAA,CAPF;CANW;AAwBjB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMuB,qCAAe;AAQrB,IAAMC,gDAAc1B,aAAAA,YAClB,CAACC,OAAsCC,iBAAiB;AACtD,QAAM,EAAA,aAAA,OAAA,WAAiC,OAAO,GAAGyB,aAAH,IAAoB1B;AAClE,QAAMsB,UAAUzB,qCAAe2B,oCAAcR,WAAf;AAC9B,QAAMO,wBAAwB5B,+CAAyBqB,WAAD;AACtD,QAAMW,YAAYC,oCAAcN,QAAQO,QAAQ3B,KAAjB;AAC/B,QAAM4B,YAAYC,oCAAcT,QAAQO,QAAQ3B,KAAjB;AAC/B,QAAM8B,aAAa9B,UAAUoB,QAAQpB;AACrC,aACE,aAAAa,eAAC,2CADH,SAAA;IAEI,SAAA;KACIQ,uBAFN;IAGE,WAAW,CAACU;IACZ,QAAQD;GAJV,OAME,aAAAjB,eAAC,0CAAU,QANb,SAAA;IAOI,MAAK;IACL,MAAK;IACL,iBAAeiB;IACf,iBAAeF;IACf,cAAYE,aAAa,WAAW;IACpC,iBAAeC,WAAW,KAAKC;IAC/B;IACA,IAAIP;KACAD,cATN;IAUE,KAAKzB;IACL,aAAakC,0CAAqBnC,MAAMoC,aAAcC,CAAAA,UAAU;AAG9D,UAAI,CAACJ,YAAYI,MAAMC,WAAW,KAAKD,MAAME,YAAY;AACvDjB,gBAAQV,cAAcV,KAAtB;;AAGAmC,cAAMG,eAAN;KAP6B;IAUjC,WAAWL,0CAAqBnC,MAAMyC,WAAYJ,CAAAA,UAAU;AAC1D,UAAI;QAAC;QAAK;QAASK,SAASL,MAAMM,GAA9B;AAAoCrB,gBAAQV,cAAcV,KAAtB;KADX;IAG/B,SAASiC,0CAAqBnC,MAAM4C,SAAS,MAAM;AAGjD,YAAMC,wBAAwBvB,QAAQwB,mBAAmB;AACzD,UAAI,CAACd,cAAc,CAACC,YAAYY;AAC9BvB,gBAAQV,cAAcV,KAAtB;KALyB;GAxB/B,CAAA,CANF;CATc;AAqDpB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM6C,qCAAe;AAarB,IAAMC,gDAAcjD,aAAAA,YAClB,CAACC,OAAsCC,iBAAiB;AACtD,QAAM,EAAA,aAAA,OAAA,YAAA,UAA4C,GAAGgD,aAAH,IAAoBjD;AACtE,QAAMsB,UAAUzB,qCAAekD,oCAAc/B,WAAf;AAC9B,QAAMW,YAAYC,oCAAcN,QAAQO,QAAQ3B,KAAjB;AAC/B,QAAM4B,YAAYC,oCAAcT,QAAQO,QAAQ3B,KAAjB;AAC/B,QAAM8B,aAAa9B,UAAUoB,QAAQpB;AACrC,QAAMgD,mCAA+BnD,aAAAA,QAAaiC,UAAb;AAErCjC,mBAAAA,WAAgB,MAAM;AACpB,UAAMoD,MAAMC;MAAsB,MAAOF,6BAA6BG,UAAU;IAA/C;AACjC,WAAO,MAAMC,qBAAqBH,GAAD;KAChC,CAAA,CAHH;AAKA,aACE,aAAApC;IAAC;IADH;MACY,SAASwC,cAAcvB;;IAC9B,CAAC,EAAA,QAAEwB,UACF,aAAAzC,eAAC,0CAAU,KADX,SAAA;MAEE,cAAYiB,aAAa,WAAW;MACpC,oBAAkBV,QAAQJ;MAC1B,MAAK;MACL,mBAAiBS;MACjB,QAAQ,CAAC6B;MACT,IAAI1B;MACJ,UAAU;OACNmB,cARN;MASE,KAAKhD;MACL,OAAO;QACL,GAAGD,MAAMyD;QACTC,mBAAmBR,6BAA6BG,UAAU,OAAOnB;;KAZrE,GAeGsB,WAAWG,QAfd;EAFJ;CAfc;AAwCpB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,SAAS/B,oCAAcC,QAAgB3B,OAAe;AACpD,SAAQ,GAAE2B,MAAO,YAAW3B,KAAM;;AAGpC,SAAS6B,oCAAcF,QAAgB3B,OAAe;AACpD,SAAQ,GAAE2B,MAAO,YAAW3B,KAAM;;AAGpC,IAAM0D,4CAAO9D;AACb,IAAM+D,4CAAOzC;AACb,IAAM0C,4CAAUrC;AAChB,IAAMsC,4CAAUf;", "names": ["TABS_NAME", "createTabsContext", "createTabsScope", "createContextScope", "createRovingFocusGroupScope", "useRovingFocusGroupScope", "TabsProvider", "useTabsContext", "Tabs", "React", "props", "forwardedRef", "value", "valueProp", "tabsProps", "direction", "useDirection", "dir", "setValue", "useControllableState", "prop", "onChange", "onValueChange", "defaultProp", "defaultValue", "$1IHzk$createElement", "__scopeTabs", "useId", "orientation", "TAB_LIST_NAME", "TabsList", "listProps", "context", "rovingFocusGroupScope", "TRIGGER_NAME", "TabsTrigger", "triggerProps", "triggerId", "makeTriggerId", "baseId", "contentId", "makeContentId", "isSelected", "disabled", "undefined", "composeEventHandlers", "onMouseDown", "event", "button", "ctrl<PERSON>ey", "preventDefault", "onKeyDown", "includes", "key", "onFocus", "isAutomaticActivation", "activationMode", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentProps", "isMountAnimationPreventedRef", "rAF", "requestAnimationFrame", "current", "cancelAnimationFrame", "forceMount", "present", "style", "animationDuration", "children", "Root", "List", "<PERSON><PERSON>", "Content"]}