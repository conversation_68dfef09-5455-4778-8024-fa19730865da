{"version": 3, "sources": ["../../@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/index.ts", "../../@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/FocusGuards.tsx", "../../@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/index.ts", "../../@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/FocusScope.tsx", "../../aria-hidden/dist/es2015/index.js", "../../tslib/tslib.es6.mjs", "../../react-remove-scroll/dist/es2015/Combination.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../react-remove-scroll-bar/dist/es2015/constants.js", "../../use-callback-ref/dist/es2015/assignRef.js", "../../use-callback-ref/dist/es2015/useRef.js", "../../use-callback-ref/dist/es2015/useMergeRef.js", "../../use-sidecar/dist/es2015/hoc.js", "../../use-sidecar/dist/es2015/hook.js", "../../use-sidecar/dist/es2015/medium.js", "../../use-sidecar/dist/es2015/renderProp.js", "../../use-sidecar/dist/es2015/exports.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll-bar/dist/es2015/component.js", "../../react-style-singleton/dist/es2015/hook.js", "../../get-nonce/dist/es2015/index.js", "../../react-style-singleton/dist/es2015/singleton.js", "../../react-style-singleton/dist/es2015/component.js", "../../react-remove-scroll-bar/dist/es2015/utils.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/sidecar.js", "../../@radix-ui/react-menu/dist/packages/react/menu/src/index.ts", "../../@radix-ui/react-menu/dist/packages/react/menu/src/Menu.tsx", "../../@radix-ui/react-dropdown-menu/dist/packages/react/dropdown-menu/src/index.ts", "../../@radix-ui/react-dropdown-menu/dist/packages/react/dropdown-menu/src/DropdownMenu.tsx"], "sourcesContent": ["export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide ariaLive elements - https://github.com/theKashey/aria-hidden/issues/10\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live]')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose;\n    if (async) {\n        if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n        dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n        if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n        dispose = value[Symbol.dispose];\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = useSidecar(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, __assign({}, props)) : null;\n    };\n}\n", "import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env.forceCache || (env.isNode && !!options.ssr) || !options.async;\n    var _a = useState(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = useState(null), error = _b[0], setError = _b[1];\n    useEffect(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = useCallback(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            useLayoutEffect(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, __assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = useState(defaultState.current), state = _b[0], setState = _b[1];\n        useEffect(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(function () { return styleSingleton(); })[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if ('touches' in event && event.touches.length === 2) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && e.target === event.target && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: \"margin\" }) : null));\n}\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];\n            if (s > d) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== document.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        target = target.parentNode;\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    if (isDeltaPositive && ((noOverscroll && availableScroll === 0) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && availableScrollTop === 0) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "export {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n} from './Menu';\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n} from './Menu';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ElementRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = Radix.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (beacuse of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus();\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus();\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ElementRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ElementRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n", "export {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n} from './DropdownMenu';\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n} from './DropdownMenu';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ElementRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ElementRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ElementRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ElementRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ElementRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ElementRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ElementRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ElementRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ElementRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ElementRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACGA,IAAIA,8BAAQ;AAWZ,SAASC,4CAAiB;AACxBC,mBAAAA,WAAgB,MAAM;AAAA,QAAA,cAAA;AACpB,UAAMC,aAAaC,SAASC,iBAAiB,0BAA1B;AACnBD,aAASE,KAAKC,sBAAsB,eAApC,eAAkDJ,WAAW,CAAD,OAA5D,QAAA,iBAAA,SAAA,eAAmEK,uCAAgB,CAAnF;AACAJ,aAASE,KAAKC,sBAAsB,cAApC,gBAAiDJ,WAAW,CAAD,OAA3D,QAAA,kBAAA,SAAA,gBAAkEK,uCAAgB,CAAlF;AACAC;AAEA,WAAO,MAAM;AACX,UAAIA,gCAAU;AACZL,iBAASC,iBAAiB,0BAA1B,EAAsDK;UAASC,CAAAA,SAASA,KAAKC,OAAL;QAAxE;AAEFH;;KAED,CAAA,CAZH;;AAeF,SAASD,yCAAmB;AAC1B,QAAMK,UAAUT,SAASU,cAAc,MAAvB;AAChBD,UAAQE,aAAa,0BAA0B,EAA/C;AACAF,UAAQG,WAAW;AACnBH,UAAQI,MAAMC,UAAU;AACxB,SAAOL;;;;;AE5BT,IAAMM,2CAAqB;AAC3B,IAAMC,6CAAuB;AAC7B,IAAMC,sCAAgB;EAAEC,SAAS;EAAOC,YAAY;;AAQpD,IAAMC,yCAAmB;AAgCzB,IAAMC,gDAAaC,cAAAA,YAAqD,CAACC,OAAOC,iBAAiB;AAC/F,QAAM,EAAA,OACG,OADH,UAEM,OACVC,kBAAkBC,sBAClBC,oBAAoBC,wBACpB,GAAGC,WAAH,IACEN;AACJ,QAAM,CAACO,YAAWC,YAAZ,QAA4BT,cAAAA,UAAmC,IAAnC;AAClC,QAAMG,mBAAmBO,0CAAeN,oBAAD;AACvC,QAAMC,qBAAqBK,0CAAeJ,sBAAD;AACzC,QAAMK,4BAAwBX,cAAAA,QAAiC,IAAjC;AAC9B,QAAMY,eAAeC;IAAgBX;IAAeY,CAAAA,SAASL,aAAaK,IAAD;EAArC;AAEpC,QAAMC,iBAAaf,cAAAA,QAAa;IAC9BgB,QAAQ;IACRC,QAAQ;AACN,WAAKD,SAAS;;IAEhBE,SAAS;AACP,WAAKF,SAAS;;GANC,EAQhBG;AAGHnB,oBAAAA,WAAgB,MAAM;AACpB,QAAIoB,SAAS;AACX,UAASC,gBAAT,SAAuBC,OAAmB;AACxC,YAAIP,WAAWC,UAAU,CAACR;AAAW;AACrC,cAAMe,SAASD,MAAMC;AACrB,YAAIf,WAAUgB,SAASD,MAAnB;AACFZ,gCAAsBQ,UAAUI;;AAEhCE,sCAAMd,sBAAsBQ,SAAS;YAAEO,QAAQ;WAA1C;SAIAC,iBAAT,SAAwBL,OAAmB;AACzC,YAAIP,WAAWC,UAAU,CAACR;AAAW;AACrC,cAAMoB,gBAAgBN,MAAMM;AAY5B,YAAIA,kBAAkB;AAAM;AAI5B,YAAI,CAACpB,WAAUgB,SAASI,aAAnB;AACHH,sCAAMd,sBAAsBQ,SAAS;YAAEO,QAAQ;WAA1C;SAOAG,kBAAT,SAAyBC,WAA6B;AACpD,cAAMC,iBAAiBC,SAASC;AAChC,YAAIF,mBAAmBC,SAASE;AAAM;AACtC,mBAAWC,YAAYL;AACrB,cAAIK,SAASC,aAAaC,SAAS;AAAGZ,wCAAMjB,UAAD;;AAI/CwB,eAASM,iBAAiB,WAAWjB,aAArC;AACAW,eAASM,iBAAiB,YAAYX,cAAtC;AACA,YAAMY,mBAAmB,IAAIC,iBAAiBX,eAArB;AACzB,UAAIrB;AAAW+B,yBAAiBE,QAAQjC,YAAW;UAAEkC,WAAW;UAAMC,SAAS;SAAhE;AAEf,aAAO,MAAM;AACXX,iBAASY,oBAAoB,WAAWvB,aAAxC;AACAW,iBAASY,oBAAoB,YAAYjB,cAAzC;AACAY,yBAAiBM,WAAjB;;;KAGH;IAACzB;IAASZ;IAAWO,WAAWC;GAzDnC;AA2DAhB,oBAAAA,WAAgB,MAAM;AACpB,QAAIQ,YAAW;AACbsC,6CAAiBC,IAAIhC,UAArB;AACA,YAAMiC,2BAA2BhB,SAASC;AAC1C,YAAMgB,sBAAsBzC,WAAUgB,SAASwB,wBAAnB;AAE5B,UAAI,CAACC,qBAAqB;AACxB,cAAMC,aAAa,IAAIC,YAAY1D,0CAAoBE,mCAApC;AACnBa,mBAAU8B,iBAAiB7C,0CAAoBU,gBAA/C;AACAK,mBAAU4C,cAAcF,UAAxB;AACA,YAAI,CAACA,WAAWG,kBAAkB;AAChCC,2CAAWC,kCAAYC,4CAAsBhD,UAAD,CAAtB,GAAoC;YAAEkB,QAAQ;WAA1D;AACV,cAAIM,SAASC,kBAAkBe;AAC7BvB,wCAAMjB,UAAD;;;AAKX,aAAO,MAAM;AACXA,mBAAUoC,oBAAoBnD,0CAAoBU,gBAAlD;AAKAsD,mBAAW,MAAM;AACf,gBAAMC,eAAe,IAAIP,YAAYzD,4CAAsBC,mCAAtC;AACrBa,qBAAU8B,iBAAiB5C,4CAAsBW,kBAAjD;AACAG,qBAAU4C,cAAcM,YAAxB;AACA,cAAI,CAACA,aAAaL;AAChB5B,wCAAMuB,6BAAD,QAACA,6BAAD,SAACA,2BAA4BhB,SAASE,MAAM;cAAER,QAAQ;aAAtD;AAGPlB,qBAAUoC,oBAAoBlD,4CAAsBW,kBAApD;AAEAyC,iDAAiBa,OAAO5C,UAAxB;WACC,CAXO;;;KAcb;IAACP;IAAWL;IAAkBE;IAAoBU;GAtCrD;AAyCA,QAAM6C,oBAAgB5D,cAAAA,aACnBsB,CAAAA,UAA+B;AAC9B,QAAI,CAACuC,QAAQ,CAACzC;AAAS;AACvB,QAAIL,WAAWC;AAAQ;AAEvB,UAAM8C,WAAWxC,MAAMyC,QAAQ,SAAS,CAACzC,MAAM0C,UAAU,CAAC1C,MAAM2C,WAAW,CAAC3C,MAAM4C;AAClF,UAAMnC,iBAAiBC,SAASC;AAEhC,QAAI6B,YAAY/B,gBAAgB;AAC9B,YAAMvB,YAAYc,MAAM6C;AACxB,YAAM,CAACC,OAAOC,IAAR,IAAgBC,uCAAiB9D,SAAD;AACtC,YAAM+D,4BAA4BH,SAASC;AAG3C,UAAI,CAACE,2BACH;AAAA,YAAIxC,mBAAmBvB;AAAWc,gBAAMkD,eAAN;aAC7B;AACL,YAAI,CAAClD,MAAMmD,YAAY1C,mBAAmBsC,MAAM;AAC9C/C,gBAAMkD,eAAN;AACA,cAAIX;AAAMpC,wCAAM2C,OAAO;cAAE1C,QAAQ;aAAlB;mBACNJ,MAAMmD,YAAY1C,mBAAmBqC,OAAO;AACrD9C,gBAAMkD,eAAN;AACA,cAAIX;AAAMpC,wCAAM4C,MAAM;cAAE3C,QAAQ;aAAjB;;;;KAKvB;IAACmC;IAAMzC;IAASL,WAAWC;GA3BP;AA8BtB,aACE,cAAA0D,eAAC,0CAAU,KADb,SAAA;IACiB,UAAU;KAAQnE,YAAjC;IAA6C,KAAKK;IAAc,WAAWgD;GAA3E,CAAA;CA5Je;AAgKnB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAUA,SAASN,iCAAWqB,YAA2B,EAAA,SAAW,MAATjD,IAAmB,CAAA,GAAI;AACtE,QAAMsB,2BAA2BhB,SAASC;AAC1C,aAAW2C,aAAaD,YAAY;AAClClD,gCAAMmD,WAAW;;KAAZ;AACL,QAAI5C,SAASC,kBAAkBe;AAA0B;;;AAO7D,SAASsB,uCAAiB9D,WAAwB;AAChD,QAAMmE,aAAanB,4CAAsBhD,SAAD;AACxC,QAAM4D,QAAQS,kCAAYF,YAAYnE,SAAb;AACzB,QAAM6D,OAAOQ,kCAAYF,WAAWG,QAAX,GAAsBtE,SAAvB;AACxB,SAAO;IAAC4D;IAAOC;;;AAajB,SAASb,4CAAsBhD,WAAwB;AACrD,QAAMuE,QAAuB,CAAA;AAC7B,QAAMC,SAAShD,SAASiD,iBAAiBzE,WAAW0E,WAAWC,cAAc;IAC3EC,YAAatE,CAAAA,SAAc;AACzB,YAAMuE,gBAAgBvE,KAAKwE,YAAY,WAAWxE,KAAKyE,SAAS;AAChE,UAAIzE,KAAK0E,YAAY1E,KAAK2E,UAAUJ;AAAe,eAAOH,WAAWQ;AAIrE,aAAO5E,KAAK6E,YAAY,IAAIT,WAAWU,gBAAgBV,WAAWQ;;GAPvD;AAUf,SAAOV,OAAOa,SAAP;AAAmBd,UAAMe,KAAKd,OAAOe,WAAlB;AAG1B,SAAOhB;;AAOT,SAASF,kCAAYmB,UAAyBxF,WAAwB;AACpE,aAAWyF,WAAWD,UAAU;AAE9B,QAAI,CAACE,+BAASD,SAAS;MAAEE,MAAM3F;KAAlB;AAAgC,aAAOyF;;;AAIxD,SAASC,+BAASpF,MAAmB,EAAA,KAAEqF,GAAgC;AACrE,MAAIC,iBAAiBtF,IAAD,EAAOuF,eAAe;AAAU,WAAO;AAC3D,SAAOvF,MAAM;AAEX,QAAIqF,SAASG,UAAaxF,SAASqF;AAAM,aAAO;AAChD,QAAIC,iBAAiBtF,IAAD,EAAOyF,YAAY;AAAQ,aAAO;AACtDzF,WAAOA,KAAK0F;;AAEd,SAAO;;AAGT,SAASC,wCAAkBR,SAAmE;AAC5F,SAAOA,mBAAmBS,oBAAoB,YAAYT;;AAG5D,SAASxE,4BAAMwE,SAAkC,EAAA,SAAW,MAATvE,IAAmB,CAAA,GAAI;AAExE,MAAIuE,WAAWA,QAAQxE,OAAO;AAC5B,UAAMuB,2BAA2BhB,SAASC;AAE1CgE,YAAQxE,MAAM;MAAEkF,eAAe;KAA/B;AAEA,QAAIV,YAAYjD,4BAA4ByD,wCAAkBR,OAAD,KAAavE;AACxEuE,cAAQvE,OAAR;;;AASN,IAAMoB,yCAAmB8D,6CAAsB;AAE/C,SAASA,+CAAyB;AAEhC,MAAIC,QAAyB,CAAA;AAE7B,SAAO;IACL9D,IAAIhC,YAA2B;AAE7B,YAAM+F,mBAAmBD,MAAM,CAAD;AAC9B,UAAI9F,eAAe+F;AACjBA,6BAAgB,QAAhBA,qBAAgB,UAAhBA,iBAAkB7F,MAAlB;AAGF4F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB8F,YAAMG,QAAQjG,UAAd;;IAGF4C,OAAO5C,YAA2B;AAAA,UAAA;AAChC8F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB,OAAA,UAAA8F,MAAM,CAAD,OAAL,QAAA,YAAA,UAAA,QAAU3F,OAAV;;;;AAKN,SAAS6F,kCAAeE,OAAYC,MAAS;AAC3C,QAAMC,eAAe;OAAIF;;AACzB,QAAMG,QAAQD,aAAaE,QAAQH,IAArB;AACd,MAAIE,UAAU;AACZD,iBAAaG,OAAOF,OAAO,CAA3B;AAEF,SAAOD;;AAGT,SAAS5D,kCAAYgE,OAAsB;AACzC,SAAOA,MAAMC;IAAQN,CAAAA,SAASA,KAAK5B,YAAY;EAAxC;;;;ACvVT,IAAI,mBAAmB,SAAU,gBAAgB;AAC7C,MAAI,OAAO,aAAa,aAAa;AACjC,WAAO;AAAA,EACX;AACA,MAAI,eAAe,MAAM,QAAQ,cAAc,IAAI,eAAe,CAAC,IAAI;AACvE,SAAO,aAAa,cAAc;AACtC;AACA,IAAI,aAAa,oBAAI,QAAQ;AAC7B,IAAI,oBAAoB,oBAAI,QAAQ;AACpC,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,MAAM;AAC7B,SAAO,SAAS,KAAK,QAAQ,WAAW,KAAK,UAAU;AAC3D;AACA,IAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC5C,SAAO,QACF,IAAI,SAAU,QAAQ;AACvB,QAAI,OAAO,SAAS,MAAM,GAAG;AACzB,aAAO;AAAA,IACX;AACA,QAAI,kBAAkB,WAAW,MAAM;AACvC,QAAI,mBAAmB,OAAO,SAAS,eAAe,GAAG;AACrD,aAAO;AAAA,IACX;AACA,YAAQ,MAAM,eAAe,QAAQ,2BAA2B,QAAQ,iBAAiB;AACzF,WAAO;AAAA,EACX,CAAC,EACI,OAAO,SAAU,GAAG;AAAE,WAAO,QAAQ,CAAC;AAAA,EAAG,CAAC;AACnD;AASA,IAAI,yBAAyB,SAAU,gBAAgB,YAAY,YAAY,kBAAkB;AAC7F,MAAI,UAAU,eAAe,YAAY,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1G,MAAI,CAAC,UAAU,UAAU,GAAG;AACxB,cAAU,UAAU,IAAI,oBAAI,QAAQ;AAAA,EACxC;AACA,MAAI,gBAAgB,UAAU,UAAU;AACxC,MAAI,cAAc,CAAC;AACnB,MAAI,iBAAiB,oBAAI,IAAI;AAC7B,MAAI,iBAAiB,IAAI,IAAI,OAAO;AACpC,MAAI,OAAO,SAAU,IAAI;AACrB,QAAI,CAAC,MAAM,eAAe,IAAI,EAAE,GAAG;AAC/B;AAAA,IACJ;AACA,mBAAe,IAAI,EAAE;AACrB,SAAK,GAAG,UAAU;AAAA,EACtB;AACA,UAAQ,QAAQ,IAAI;AACpB,MAAI,OAAO,SAAU,QAAQ;AACzB,QAAI,CAAC,UAAU,eAAe,IAAI,MAAM,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAU,MAAM;AAC1D,UAAI,eAAe,IAAI,IAAI,GAAG;AAC1B,aAAK,IAAI;AAAA,MACb,OACK;AACD,YAAI;AACA,cAAI,OAAO,KAAK,aAAa,gBAAgB;AAC7C,cAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC9C,cAAI,gBAAgB,WAAW,IAAI,IAAI,KAAK,KAAK;AACjD,cAAI,eAAe,cAAc,IAAI,IAAI,KAAK,KAAK;AACnD,qBAAW,IAAI,MAAM,YAAY;AACjC,wBAAc,IAAI,MAAM,WAAW;AACnC,sBAAY,KAAK,IAAI;AACrB,cAAI,iBAAiB,KAAK,eAAe;AACrC,8BAAkB,IAAI,MAAM,IAAI;AAAA,UACpC;AACA,cAAI,gBAAgB,GAAG;AACnB,iBAAK,aAAa,YAAY,MAAM;AAAA,UACxC;AACA,cAAI,CAAC,eAAe;AAChB,iBAAK,aAAa,kBAAkB,MAAM;AAAA,UAC9C;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,mCAAmC,MAAM,CAAC;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,OAAK,UAAU;AACf,iBAAe,MAAM;AACrB;AACA,SAAO,WAAY;AACf,gBAAY,QAAQ,SAAU,MAAM;AAChC,UAAI,eAAe,WAAW,IAAI,IAAI,IAAI;AAC1C,UAAI,cAAc,cAAc,IAAI,IAAI,IAAI;AAC5C,iBAAW,IAAI,MAAM,YAAY;AACjC,oBAAc,IAAI,MAAM,WAAW;AACnC,UAAI,CAAC,cAAc;AACf,YAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAC9B,eAAK,gBAAgB,gBAAgB;AAAA,QACzC;AACA,0BAAkB,OAAO,IAAI;AAAA,MACjC;AACA,UAAI,CAAC,aAAa;AACd,aAAK,gBAAgB,UAAU;AAAA,MACnC;AAAA,IACJ,CAAC;AACD;AACA,QAAI,CAAC,WAAW;AAEZ,mBAAa,oBAAI,QAAQ;AACzB,mBAAa,oBAAI,QAAQ;AACzB,0BAAoB,oBAAI,QAAQ;AAChC,kBAAY,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AAQO,IAAI,aAAa,SAAU,gBAAgB,YAAY,YAAY;AACtE,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAoB;AAC9D,MAAI,UAAU,MAAM,KAAK,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1F,MAAI,mBAAmB,cAAc,iBAAiB,cAAc;AACpE,MAAI,CAAC,kBAAkB;AACnB,WAAO,WAAY;AAAE,aAAO;AAAA,IAAM;AAAA,EACtC;AAEA,UAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,iBAAiB,iBAAiB,aAAa,CAAC,CAAC;AACxF,SAAO,uBAAuB,SAAS,kBAAkB,YAAY,aAAa;AACtF;;;ACtGO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASmC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,QAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW;AAAG,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,UAAI,MAAM,EAAE,KAAK,OAAO;AACpB,YAAI,CAAC;AAAI,eAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,WAAG,CAAC,IAAI,KAAK,CAAC;AAAA,MAClB;AAAA,IACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC5NA,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;;;ACDhB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAK5B,IAAI,yBAAyB;;;ACM7B,SAAS,UAAU,KAAK,OAAO;AAClC,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WACS,KAAK;AACV,QAAI,UAAU;AAAA,EAClB;AACA,SAAO;AACX;;;ACrBA,IAAAC,gBAAyB;AAelB,SAAS,eAAe,cAAc,UAAU;AACnD,MAAI,UAAM,wBAAS,WAAY;AAAE,WAAQ;AAAA;AAAA,MAErC,OAAO;AAAA;AAAA,MAEP;AAAA;AAAA,MAEA,QAAQ;AAAA,QACJ,IAAI,UAAU;AACV,iBAAO,IAAI;AAAA,QACf;AAAA,QACA,IAAI,QAAQ,OAAO;AACf,cAAI,OAAO,IAAI;AACf,cAAI,SAAS,OAAO;AAChB,gBAAI,QAAQ;AACZ,gBAAI,SAAS,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EAAI,CAAC,EAAE,CAAC;AAER,MAAI,WAAW;AACf,SAAO,IAAI;AACf;;;ACtCA,YAAuB;AAGvB,IAAI,4BAA4B,OAAO,WAAW,cAAoB,wBAAwB;AAC9F,IAAI,gBAAgB,oBAAI,QAAQ;AAezB,SAAS,aAAa,MAAM,cAAc;AAC7C,MAAI,cAAc,eAAe,gBAAgB,MAAM,SAAU,UAAU;AACvE,WAAO,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,UAAU,KAAK,QAAQ;AAAA,IAAG,CAAC;AAAA,EAC3E,CAAC;AAED,4BAA0B,WAAY;AAClC,QAAI,WAAW,cAAc,IAAI,WAAW;AAC5C,QAAI,UAAU;AACV,UAAI,aAAa,IAAI,IAAI,QAAQ;AACjC,UAAI,aAAa,IAAI,IAAI,IAAI;AAC7B,UAAI,YAAY,YAAY;AAC5B,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,SAAS;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,kBAAc,IAAI,aAAa,IAAI;AAAA,EACvC,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACX;;;AC3CA,IAAAC,SAAuB;;;ACDvB,IAAAC,gBAAoC;;;ACCpC,SAAS,KAAK,GAAG;AACb,SAAO;AACX;AACA,SAAS,kBAAkB,UAAU,YAAY;AAC7C,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAM;AAChD,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,MAAI,SAAS;AAAA,IACT,MAAM,WAAY;AACd,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACtH;AACA,UAAI,OAAO,QAAQ;AACf,eAAO,OAAO,OAAO,SAAS,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,SAAU,MAAM;AACvB,UAAI,OAAO,WAAW,MAAM,QAAQ;AACpC,aAAO,KAAK,IAAI;AAChB,aAAO,WAAY;AACf,iBAAS,OAAO,OAAO,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM,CAAC;AAAA,MAC9D;AAAA,IACJ;AAAA,IACA,kBAAkB,SAAU,IAAI;AAC5B,iBAAW;AACX,aAAO,OAAO,QAAQ;AAClB,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AAAA,MAClB;AACA,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AAAE,iBAAO,GAAG,CAAC;AAAA,QAAG;AAAA,QACnC,QAAQ,WAAY;AAAE,iBAAO;AAAA,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,cAAc,SAAU,IAAI;AACxB,iBAAW;AACX,UAAI,eAAe,CAAC;AACpB,UAAI,OAAO,QAAQ;AACf,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AACd,uBAAe;AAAA,MACnB;AACA,UAAI,eAAe,WAAY;AAC3B,YAAIC,OAAM;AACV,uBAAe,CAAC;AAChB,QAAAA,KAAI,QAAQ,EAAE;AAAA,MAClB;AACA,UAAI,QAAQ,WAAY;AAAE,eAAO,QAAQ,QAAQ,EAAE,KAAK,YAAY;AAAA,MAAG;AACvE,YAAM;AACN,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AACf,uBAAa,KAAK,CAAC;AACnB,gBAAM;AAAA,QACV;AAAA,QACA,QAAQ,SAAU,QAAQ;AACtB,yBAAe,aAAa,OAAO,MAAM;AACzC,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,oBAAoB,SAAS;AACzC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,kBAAkB,IAAI;AACnC,SAAO,UAAU,SAAS,EAAE,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO;AAC9D,SAAO;AACX;;;AC5EA,IAAAC,SAAuB;AACvB,IAAAC,gBAAkE;;;ACDlE,IAAAC,SAAuB;AACvB,IAAI,UAAU,SAAU,IAAI;AACxB,MAAI,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;AACvD,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACxF;AACA,MAAI,SAAS,QAAQ,KAAK;AAC1B,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,SAAa,qBAAc,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC;AACzD;AACA,QAAQ,kBAAkB;AACnB,SAAS,cAAc,QAAQ,UAAU;AAC5C,SAAO,UAAU,QAAQ;AACzB,SAAO;AACX;;;AChBO,IAAI,YAAY,oBAAoB;;;AVI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,kBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,cAAO,IAAI;AAC3B,MAAI,KAAW,gBAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,eAAe,SAAS,kBAAkB,IAAI,CAAC;AACtgB,MAAIC,WAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,qBAAcA,UAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,IAAI,CAAC;AAAA,IACrO,eAAsB,oBAAmB,gBAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,qBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AWjCA,IAAAC,SAAuB;;;ACDvB,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;;;ACAvB,IAAI;AAIG,IAAI,WAAW,WAAY;AAC9B,MAAI,cAAc;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,sBAAsB,aAAa;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACXA,SAAS,eAAe;AACpB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,SAAS,cAAc,OAAO;AACxC,MAAI,OAAO;AACX,MAAI,QAAQ,SAAS;AACrB,MAAI,OAAO;AACP,QAAI,aAAa,SAAS,KAAK;AAAA,EACnC;AACA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,KAAK;AAE5B,MAAI,IAAI,YAAY;AAEhB,QAAI,WAAW,UAAU;AAAA,EAC7B,OACK;AACD,QAAI,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,OAAK,YAAY,GAAG;AACxB;AACO,IAAI,sBAAsB,WAAY;AACzC,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,SAAO;AAAA,IACH,KAAK,SAAU,OAAO;AAClB,UAAI,WAAW,GAAG;AACd,YAAK,aAAa,aAAa,GAAI;AAC/B,uBAAa,YAAY,KAAK;AAC9B,yBAAe,UAAU;AAAA,QAC7B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB;AACA,UAAI,CAAC,WAAW,YAAY;AACxB,mBAAW,cAAc,WAAW,WAAW,YAAY,UAAU;AACrE,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACJ;;;AFpCO,IAAI,qBAAqB,WAAY;AACxC,MAAI,QAAQ,oBAAoB;AAChC,SAAO,SAAU,QAAQ,WAAW;AAChC,IAAM,iBAAU,WAAY;AACxB,YAAM,IAAI,MAAM;AAChB,aAAO,WAAY;AACf,cAAM,OAAO;AAAA,MACjB;AAAA,IACJ,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,EAC5B;AACJ;;;AGdO,IAAI,iBAAiB,WAAY;AACpC,MAAI,WAAW,mBAAmB;AAClC,MAAI,QAAQ,SAAU,IAAI;AACtB,QAAI,SAAS,GAAG,QAAQ,UAAU,GAAG;AACrC,aAAS,QAAQ,OAAO;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACfO,IAAI,UAAU;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,QAAQ,SAAU,GAAG;AAAE,SAAO,SAAS,KAAK,IAAI,EAAE,KAAK;AAAG;AAC9D,IAAI,YAAY,SAAU,SAAS;AAC/B,MAAI,KAAK,OAAO,iBAAiB,SAAS,IAAI;AAC9C,MAAI,OAAO,GAAG,YAAY,YAAY,gBAAgB,YAAY;AAClE,MAAI,MAAM,GAAG,YAAY,YAAY,eAAe,WAAW;AAC/D,MAAI,QAAQ,GAAG,YAAY,YAAY,iBAAiB,aAAa;AACrE,SAAO,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC;AACjD;AACO,IAAI,cAAc,SAAU,SAAS;AACxC,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU,OAAO;AAC/B,MAAI,gBAAgB,SAAS,gBAAgB;AAC7C,MAAI,cAAc,OAAO;AACzB,SAAO;AAAA,IACH,MAAM,QAAQ,CAAC;AAAA,IACf,KAAK,QAAQ,CAAC;AAAA,IACd,OAAO,QAAQ,CAAC;AAAA,IAChB,KAAK,KAAK,IAAI,GAAG,cAAc,gBAAgB,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;AAAA,EAC1E;AACJ;;;ALxBA,IAAI,QAAQ,eAAe;AACpB,IAAI,gBAAgB;AAI3B,IAAI,YAAY,SAAU,IAAI,eAAe,SAAS,WAAW;AAC7D,MAAI,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,SAAO,QAAQ,OAAO,uBAAuB,0BAA0B,EAAE,OAAO,WAAW,uBAAuB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,eAAe,4BAA4B,EAAE,OAAO,WAAW,4CAA4C,EAAE,OAAO;AAAA,IACnS,iBAAiB,sBAAsB,OAAO,WAAW,GAAG;AAAA,IAC5D,YAAY,YACR,uBAAuB,OAAO,MAAM,wBAAwB,EAAE,OAAO,KAAK,0BAA0B,EAAE,OAAO,OAAO,gEAAgE,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,SAAS;AAAA,IACxO,YAAY,aAAa,kBAAkB,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,GAAG;AAAA,EACvF,EACK,OAAO,OAAO,EACd,KAAK,EAAE,GAAG,gBAAgB,EAAE,OAAO,oBAAoB,iBAAiB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,wBAAwB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,mBAAmB,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,0BAA0B,EAAE,OAAO,WAAW,qBAAqB,EAAE,OAAO,eAAe,WAAW,EAAE,OAAO,wBAAwB,IAAI,EAAE,OAAO,KAAK,YAAY;AAC/kB;AACA,IAAI,uBAAuB,WAAY;AACnC,MAAI,UAAU,SAAS,SAAS,KAAK,aAAa,aAAa,KAAK,KAAK,EAAE;AAC3E,SAAO,SAAS,OAAO,IAAI,UAAU;AACzC;AACO,IAAI,mBAAmB,WAAY;AACtC,EAAM,iBAAU,WAAY;AACxB,aAAS,KAAK,aAAa,gBAAgB,qBAAqB,IAAI,GAAG,SAAS,CAAC;AACjF,WAAO,WAAY;AACf,UAAI,aAAa,qBAAqB,IAAI;AAC1C,UAAI,cAAc,GAAG;AACjB,iBAAS,KAAK,gBAAgB,aAAa;AAAA,MAC/C,OACK;AACD,iBAAS,KAAK,aAAa,eAAe,WAAW,SAAS,CAAC;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAIO,IAAI,kBAAkB,SAAU,IAAI;AACvC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,WAAW;AACpH,mBAAiB;AAMjB,MAAI,MAAY,eAAQ,WAAY;AAAE,WAAO,YAAY,OAAO;AAAA,EAAG,GAAG,CAAC,OAAO,CAAC;AAC/E,SAAa,qBAAc,OAAO,EAAE,QAAQ,UAAU,KAAK,CAAC,YAAY,SAAS,CAAC,cAAc,eAAe,EAAE,EAAE,CAAC;AACxH;;;AMpDA,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AAC/D,UAAI,IAAI,GAAG;AACP,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,SAAS;AACzC,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,aAAS,OAAO;AAAA,EACpB;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAClE,MAAI,oBAAqB,gBAAgB,oBAAoB,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AAC5G,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,uBAAuB,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AAChG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;ARzFO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,cAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,cAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,cAAO;AAC9B,MAAI,KAAW,gBAAS,WAAW,EAAE,CAAC;AACtC,MAAIC,SAAc,gBAAS,WAAY;AAAE,WAAO,eAAe;AAAA,EAAG,CAAC,EAAE,CAAC;AACtE,MAAI,YAAkB,cAAO,KAAK;AAClC,EAAM,iBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,iBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,mBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAI,aAAa,SAAS,MAAM,QAAQ,WAAW,GAAG;AAClD,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,mBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAMA,QAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,QAAQ,EAAE,WAAW,MAAM,UAAU,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAElK,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,mBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,OAAe;AACvE,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,mBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,mBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,mBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,WAAY;AACxB,cAAU,KAAKA,MAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAASA;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,qBAAcA,QAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,qBAAc,iBAAiB,EAAE,SAAS,SAAS,CAAC,IAAI;AAAA,EAAI;AAC5F;;;AS9IA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ArBC3D,IAAI,oBAA0B,kBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,qBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AuBqBf,IAAMC,uCAAiB;EAAC;EAAS;;AACjC,IAAMC,mCAAa;EAAC;EAAa;EAAU;;AAC3C,IAAMC,kCAAY;EAAC;EAAW;EAAY;;AAC1C,IAAMC,wCAAkB;KAAIF;KAAeC;;AAC3C,IAAME,sCAA6C;EACjDC,KAAK;OAAIL;IAAgB;;EACzBM,KAAK;OAAIN;IAAgB;;;AAE3B,IAAMO,uCAA8C;EAClDF,KAAK;IAAC;;EACNC,KAAK;IAAC;;;AAOR,IAAME,kCAAY;AAGlB,IAAM,CAACC,kCAAYC,qCAAeC,2CAA5B,IAAqDC,0CAGzDJ,+BAHyE;AAM3E,IAAM,CAACK,yCAAmBC,yCAApB,IAAuCC,yCAAmBP,iCAAW;EACzEG;EACAK;EACAC;CAH6D;AAK/D,IAAMC,uCAAiBF,wCAAiB;AACxC,IAAMG,iDAA2BF,yCAA2B;AAS5D,IAAM,CAACG,oCAAcC,oCAAf,IAAiCR,wCAAoCL,+BAAnB;AASxD,IAAM,CAACc,wCAAkBC,wCAAnB,IAAyCV,wCAAwCL,+BAAvB;AAUhE,IAAMgB,4CAA6BC,CAAAA,UAAkC;AACnE,QAAM,EAAA,aAAA,OAAsB,OAAtB,UAAA,KAAA,cAAA,QAAkE,KAARC,IAAiBD;AACjF,QAAME,cAAcT,qCAAeU,WAAD;AAClC,QAAM,CAACC,SAASC,UAAV,QAAwBC,cAAAA,UAA0C,IAA1C;AAC9B,QAAMC,yBAAqBD,cAAAA,QAAa,KAAb;AAC3B,QAAME,mBAAmBC,0CAAeC,YAAD;AACvC,QAAMC,YAAYC,0CAAaC,GAAD;AAE9BP,oBAAAA,WAAgB,MAAM;AAGpB,UAAMQ,gBAAgB,MAAM;AAC1BP,yBAAmBQ,UAAU;AAC7BC,eAASC,iBAAiB,eAAeC,eAAe;QAAEC,SAAS;QAAMC,MAAM;OAA/E;AACAJ,eAASC,iBAAiB,eAAeC,eAAe;QAAEC,SAAS;QAAMC,MAAM;OAA/E;;AAEF,UAAMF,gBAAgB,MAAOX,mBAAmBQ,UAAU;AAC1DC,aAASC,iBAAiB,WAAWH,eAAe;MAAEK,SAAS;KAA/D;AACA,WAAO,MAAM;AACXH,eAASK,oBAAoB,WAAWP,eAAe;QAAEK,SAAS;OAAlE;AACAH,eAASK,oBAAoB,eAAeH,eAAe;QAAEC,SAAS;OAAtE;AACAH,eAASK,oBAAoB,eAAeH,eAAe;QAAEC,SAAS;OAAtE;;KAED,CAAA,CAfH;AAiBA,aACE,cAAAG,eAAC,2CAAyBpB,iBACxB,cAAAoB,eAAC,oCAFL;IAGM,OAAOnB;IACP;IACA,cAAcK;IACd;IACA,iBAAiBH;SAEjB,cAAAiB,eAAC,wCAPH;IAQI,OAAOnB;IACP,aAASG,cAAAA;MAAkB,MAAME,iBAAiB,KAAD;MAAS;QAACA;;IAAlD;IACT;IACA,KAAKG;IACL;KAECY,QAPH,CAPF,CADF;;AAsBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,oCAAc;AAMpB,IAAMC,gDAAanB,cAAAA,YACjB,CAACN,OAAqC0B,iBAAiB;AACrD,QAAM,EAAA,aAAe,GAAGC,YAAH,IAAmB3B;AACxC,QAAME,cAAcT,qCAAeU,WAAD;AAClC,aAAO,cAAAmB,eAAC,2CAAD,SAAA,CAAA,GAA4BpB,aAAiByB,aAApD;IAAiE,KAAKD;GAA/D,CAAA;CAJQ;AAQnB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAME,oCAAc;AAGpB,IAAM,CAACC,sCAAgBC,sCAAjB,IAAqC1C,wCAAsCwC,mCAAa;EAC5FG,YAAYC;CAD8C;AAkB5D,IAAMC,4CAAyCjC,CAAAA,UAAwC;AACrF,QAAM,EAAA,aAAA,YAAA,UAAA,UAAqCkC,IAAclC;AACzD,QAAMmC,UAAUvC,qCAAegC,mCAAazB,WAAd;AAC9B,aACE,cAAAmB,eAAC,sCADH;IACkB,OAAOnB;IAAa;SAClC,cAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,cAAAd,eAAC,2CADH;IACmB,SAAO;IAAC;KACtBC,QADH,CADF,CADF;;AAUJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMc,qCAAe;AAUrB,IAAM,CAACC,2CAAqBC,2CAAtB,IACJnD,wCAA2CiD,kCAA1B;AAgBnB,IAAMG,gDAAclC,cAAAA,YAClB,CAACN,OAAsC0B,iBAAiB;AACtD,QAAMe,gBAAgBX,uCAAiBO,oCAAcrC,MAAMG,WAArB;AACtC,QAAM,EAAA,aAAesC,cAAcV,YAAY,GAAGW,aAAH,IAAoB1C;AACnE,QAAMmC,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAcrC,MAAMG,WAArB;AAEtC,aACE,cAAAmB,eAAC,iCAAW,UADd;IACuB,OAAOtB,MAAMG;SAChC,cAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,cAAAd,eAAC,iCAAW,MADd;IACmB,OAAOtB,MAAMG;KAC3BwC,YAAY1C,YACX,cAAAqB,eAAC,4CAAD,SAAA,CAAA,GAA0BoB,cAF9B;IAE4C,KAAKhB;GAA7C,CAAA,QAEA,cAAAJ,eAAC,+CAAD,SAAA,CAAA,GAA6BoB,cAF7B;IAE2C,KAAKhB;GAAhD,CAAA,CAJJ,CADF,CADF;CARc;AA6BpB,IAAMkB,iDAAuBtC,cAAAA,YAC3B,CAACN,OAA8C0B,iBAAiB;AAC9D,QAAMS,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAM0C,UAAMvC,cAAAA,QAAyC,IAAzC;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AAGpCvC,oBAAAA,WAAgB,MAAM;AACpB,UAAMF,UAAUyC,IAAI9B;AACpB,QAAIX;AAAS,aAAO4C,WAAW5C,OAAD;KAC7B,CAAA,CAHH;AAKA,aACE,cAAAkB,eAAC,uCAAD,SAAA,CAAA,GACMtB,OAFR;IAGI,KAAK8C;IAGL,WAAWX,QAAQC;IAGnB,6BAA6BD,QAAQC;IACrC,sBAAoB;IAGpB,gBAAgBa;MACdjD,MAAMkD;MACLC,CAAAA,UAAUA,MAAMC,eAAN;MACX;QAAEC,0BAA0B;;IAHM;IAKpC,WAAW,MAAMlB,QAAQzB,aAAa,KAArB;GAjBnB,CAAA;CAbuB;AAoC7B,IAAM4C,oDAA0BhD,cAAAA,YAG9B,CAACN,OAA8C0B,iBAAiB;AAChE,QAAMS,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,aACE,cAAAmB,eAAC,uCAAD,SAAA,CAAA,GACMtB,OAFR;IAGI,KAAK0B;IACL,WAAW;IACX,6BAA6B;IAC7B,sBAAsB;IACtB,WAAW,MAAMS,QAAQzB,aAAa,KAArB;GANnB,CAAA;CAN4B;AA+DhC,IAAM6C,4CAAkBjD,cAAAA,YACtB,CAACN,OAA0C0B,iBAAiB;AAC1D,QAAM,EAAA,aAAA,OAEG,OAFH,WAAA,iBAAA,kBAAA,6BAAA,cAAA,iBAAA,sBAAA,gBAAA,mBAAA,WAAA,sBAcJ,GAAGgB,aAAH,IACE1C;AACJ,QAAMmC,UAAUvC,qCAAeyC,oCAAclC,WAAf;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAclC,WAAf;AACtC,QAAMD,cAAcT,qCAAeU,WAAD;AAClC,QAAMqD,wBAAwB9D,+CAAyBS,WAAD;AACtD,QAAMsD,WAAWxE,oCAAckB,WAAD;AAC9B,QAAM,CAACuD,eAAeC,gBAAhB,QAAoCrD,cAAAA,UAA8B,IAA9B;AAC1C,QAAMsD,iBAAatD,cAAAA,QAA6B,IAA7B;AACnB,QAAMwC,eAAeC,0CAAgBrB,cAAckC,YAAYzB,QAAQ0B,eAAnC;AACpC,QAAMC,eAAWxD,cAAAA,QAAa,CAAb;AACjB,QAAMyD,gBAAYzD,cAAAA,QAAa,EAAb;AAClB,QAAM0D,2BAAuB1D,cAAAA,QAAa,CAAb;AAC7B,QAAM2D,4BAAwB3D,cAAAA,QAAiC,IAAjC;AAC9B,QAAM4D,oBAAgB5D,cAAAA,QAAmB,OAAnB;AACtB,QAAM6D,sBAAkB7D,cAAAA,QAAa,CAAb;AAExB,QAAM8D,oBAAoBC,uBAAuBC,sBAAehE,cAAAA;AAChE,QAAMiE,yBAAyBF,uBAC3B;IAAEG,IAAIC;IAAMC,gBAAgB;MAC5B1C;AAEJ,QAAM2C,wBAAyBC,CAAAA,QAAgB;AAAA,QAAA,aAAA;AAC7C,UAAMC,SAASd,UAAUhD,UAAU6D;AACnC,UAAME,QAAQrB,SAAQ,EAAGsB;MAAQC,CAAAA,SAAS,CAACA,KAAKC;IAAlC;AACd,UAAMC,cAAclE,SAASmE;AAC7B,UAAMC,gBAAY,cAAGN,MAAMO;MAAML,CAAAA,SAASA,KAAKnC,IAAI9B,YAAYmE;IAA1C,OAAH,QAAA,gBAAA,SAAA,SAAG,YAAwDI;AAC7E,UAAMC,SAAST,MAAMU;MAAKR,CAAAA,SAASA,KAAKM;IAAzB;AACf,UAAMG,YAAYC,mCAAaH,QAAQV,QAAQO,YAAjB;AAC9B,UAAMO,WAAO,eAAGb,MAAMO;MAAML,CAAAA,SAASA,KAAKM,cAAcG;IAAxC,OAAH,QAAA,iBAAA,SAAA,SAAG,aAAoD5C,IAAI9B;AAGvE,KAAA,SAAS6E,aAAaC,OAAe;AACpC9B,gBAAUhD,UAAU8E;AACpBC,aAAOC,aAAajC,SAAS/C,OAA7B;AACA,UAAI8E,UAAU;AAAI/B,iBAAS/C,UAAU+E,OAAOE;UAAW,MAAMJ,aAAa,EAAD;UAAM;QAA1C;OACpCf,MAJH;AAMA,QAAIc;AAKFK;QAAW,MAAOL,QAAwBM,MAAzB;MAAP;;AAId3F,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAMwF,OAAOC,aAAajC,SAAS/C,OAA7B;KACZ,CAAA,CAFH;AAMAmF,4CAAc;AAEd,QAAMC,+BAA2B7F,cAAAA,aAAmB6C,CAAAA,UAA8B;AAAA,QAAA,uBAAA;AAChF,UAAMiD,kBAAkBlC,cAAcnD,cAAd,wBAA0BkD,sBAAsBlD,aAAhD,QAAA,0BAAA,SAAA,SAA0B,sBAA+BsF;AACjF,WAAOD,mBAAmBE,2CAAqBnD,QAAD,yBAAQc,sBAAsBlD,aAA9B,QAAA,2BAAA,SAAA,SAAQ,uBAA+BwF,IAAvC;KAC7C,CAAA,CAH8B;AAKjC,aACE,cAAAjF,eAAC,2CADH;IAEI,OAAOnB;IACP;IACA,iBAAaG,cAAAA,aACV6C,CAAAA,UAAU;AACT,UAAIgD,yBAAyBhD,KAAD;AAASA,cAAMC,eAAN;OAEvC;MAAC+C;KAJU;IAMb,iBAAa7F,cAAAA,aACV6C,CAAAA,UAAU;AAAA,UAAA;AACT,UAAIgD,yBAAyBhD,KAAD;AAAS;AACrC,OAAA,sBAAAS,WAAW7C,aAAX,QAAA,wBAAA,UAAA,oBAAoBkF,MAApB;AACAtC,uBAAiB,IAAD;OAElB;MAACwC;KANU;IAQb,oBAAgB7F,cAAAA,aACb6C,CAAAA,UAAU;AACT,UAAIgD,yBAAyBhD,KAAD;AAASA,cAAMC,eAAN;OAEvC;MAAC+C;KAJa;IAMhB;IACA,gCAA4B7F,cAAAA,aAAmBkG,CAAAA,WAAW;AACxDvC,4BAAsBlD,UAAUyF;OAC/B,CAAA,CAFyB;SAI5B,cAAAlF,eAAC,mBAAsBiD,4BACrB,cAAAjD,eAAC,2CA7BL;IA8BM,SAAO;IACP,SAASmF;IACT,kBAAkBxD,0CAAqByD,iBAAkBvD,CAAAA,UAAU;AAAA,UAAA;AAGjEA,YAAMC,eAAN;AACA,OAAA,uBAAAQ,WAAW7C,aAAX,QAAA,yBAAA,UAAA,qBAAoBkF,MAApB;KAJoC;IAMtC,oBAAoBU;SAEpB,cAAArF,eAAC,2CAXH;IAYI,SAAO;IACP;IACA;IACA;IACA;IACA;IACA;SAEA,cAAAA,eAAC,2CATH,SAAA;IAUI,SAAA;KACIkC,uBAFN;IAGE,KAAKb,YAAY9B;IACjB,aAAY;IACZ;IACA,kBAAkB6C;IAClB,0BAA0BC;IAC1B,cAAcV,0CAAqB2D,cAAezD,CAAAA,UAAU;AAE1D,UAAI,CAACR,YAAYpC,mBAAmBQ;AAASoC,cAAMC,eAAN;KAFb;GARpC,OAaE,cAAA9B,eAAC,2CAbH,SAAA;IAcI,MAAK;IACL,oBAAiB;IACjB,cAAYuF,mCAAa1E,QAAQC,IAAT;IACxB,2BAAwB;IACxB,KAAKO,YAAY9B;KACbX,aACAwC,cAPN;IAQE,KAAKI;IACL,OAAO;MAAEgE,SAAS;MAAQ,GAAGpE,aAAaqE;;IAC1C,WAAW9D,0CAAqBP,aAAasE,WAAY7D,CAAAA,UAAU;AAEjE,YAAM8D,SAAS9D,MAAM8D;AACrB,YAAMC,kBACJD,OAAOE,QAAQ,2BAAf,MAAgDhE,MAAMiE;AACxD,YAAMC,gBAAgBlE,MAAMmE,WAAWnE,MAAMoE,UAAUpE,MAAMqE;AAC7D,YAAMC,iBAAiBtE,MAAMyB,IAAI8C,WAAW;AAC5C,UAAIR,iBAAiB;AAEnB,YAAI/D,MAAMyB,QAAQ;AAAOzB,gBAAMC,eAAN;AACzB,YAAI,CAACiE,iBAAiBI;AAAgB9C,gCAAsBxB,MAAMyB,GAAP;;AAG7D,YAAMxE,UAAUwD,WAAW7C;AAC3B,UAAIoC,MAAM8D,WAAW7G;AAAS;AAC9B,UAAI,CAAC1B,sCAAgBiJ,SAASxE,MAAMyB,GAA/B;AAAqC;AAC1CzB,YAAMC,eAAN;AACA,YAAM0B,QAAQrB,SAAQ,EAAGsB;QAAQC,CAAAA,SAAS,CAACA,KAAKC;MAAlC;AACd,YAAM2C,iBAAiB9C,MAAMU;QAAKR,CAAAA,SAASA,KAAKnC,IAAI9B;MAA7B;AACvB,UAAItC,gCAAUkJ,SAASxE,MAAMyB,GAAzB;AAA+BgD,uBAAeC,QAAf;AACnCC,uCAAWF,cAAD;KApBmB;IAsB/B,QAAQ3E,0CAAqBjD,MAAM+H,QAAS5E,CAAAA,UAAU;AAEpD,UAAI,CAACA,MAAMiE,cAAcY,SAAS7E,MAAM8D,MAAnC,GAA4C;AAC/CnB,eAAOC,aAAajC,SAAS/C,OAA7B;AACAgD,kBAAUhD,UAAU;;KAJI;IAO5B,eAAekC,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB,YAAM8D,SAAS9D,MAAM8D;AACrB,YAAMkB,qBAAqBhE,gBAAgBpD,YAAYoC,MAAMiF;AAI7D,UAAIjF,MAAMiE,cAAcY,SAASf,MAA7B,KAAwCkB,oBAAoB;AAC9D,cAAME,SAASlF,MAAMiF,UAAUjE,gBAAgBpD,UAAU,UAAU;AACnEmD,sBAAcnD,UAAUsH;AACxBlE,wBAAgBpD,UAAUoC,MAAMiF;;KAT3B,CAFwB;GAvCrC,CAAA,CAbF,CATF,CAXF,CADF,CA5BF;CA7EkB;AA2MxB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAME,mCAAa;AAMnB,IAAMC,gDAAYjI,cAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAG8G,WAAH,IAAkBxI;AACvC,aAAO,cAAAsB,eAAC,0CAAU,KAAlB,SAAA;IAAsB,MAAK;KAAYkH,YAAhC;IAA4C,KAAK9G;GAAjD,CAAA;CAHO;AAOlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM+G,mCAAa;AAKnB,IAAMC,gDAAYpI,cAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAGiH,WAAH,IAAkB3I;AACvC,aAAO,cAAAsB,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBqH,YAA1B;IAAsC,KAAKjH;GAApC,CAAA;CAHO;AAOlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkH,kCAAY;AAClB,IAAMC,oCAAc;AAOpB,IAAMC,gDAAWxI,cAAAA,YACf,CAACN,OAAmC0B,iBAAiB;AACnD,QAAM,EAAA,WAAa,OAAb,UAA8B,GAAGqH,UAAH,IAAiB/I;AACrD,QAAM6C,UAAMvC,cAAAA,QAA6B,IAA7B;AACZ,QAAMqC,cAAc7C,yCAAmB8I,iCAAW5I,MAAMG,WAAlB;AACtC,QAAM6I,iBAAiBzG,4CAAsBqG,iCAAW5I,MAAMG,WAAlB;AAC5C,QAAM2C,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,QAAMoG,uBAAmB3I,cAAAA,QAAa,KAAb;AAEzB,QAAM4I,eAAe,MAAM;AACzB,UAAMC,WAAWtG,IAAI9B;AACrB,QAAI,CAACkE,YAAYkE,UAAU;AACzB,YAAMC,kBAAkB,IAAIC,YAAYR,mCAAa;QAAES,SAAS;QAAMC,YAAY;OAA1D;AACxBJ,eAASlI;QAAiB4H;QAAc1F,CAAAA,UAAUqG,aAAX,QAAWA,aAAX,SAAA,SAAWA,SAAWrG,KAAH;QAAW;UAAE/B,MAAM;;MAA7E;AACAqI,gDAA4BN,UAAUC,eAAX;AAC3B,UAAIA,gBAAgBM;AAClBT,yBAAiBlI,UAAU;;AAE3B4B,oBAAYgH,QAAZ;;;AAKN,aACE,cAAArI,eAAC,oCAAD,SAAA,CAAA,GACMyH,WAFR;IAGI,KAAKjG;IACL;IACA,SAASG,0CAAqBjD,MAAM4J,SAASV,YAAhB;IAC7B,eAAgB/F,CAAAA,UAAU;AAAA,UAAA;AACxB,OAAA,uBAAAnD,MAAM6J,mBAAN,QAAA,yBAAA,UAAA,qBAAA,KAAA7J,OAAsBmD,KAAjB;AACL8F,uBAAiBlI,UAAU;;IAE7B,aAAakC,0CAAqBjD,MAAM8J,aAAc3G,CAAAA,UAAU;AAAA,UAAA;AAI9D,UAAI,CAAC8F,iBAAiBlI;AAAS,SAAA,uBAAAoC,MAAMiE,mBAAN,QAAA,yBAAA,UAAA,qBAAqB2C,MAArB;KAJA;IAMjC,WAAW9G,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAC1D,YAAM6G,gBAAgBhB,eAAejF,UAAUhD,YAAY;AAC3D,UAAIkE,YAAa+E,iBAAiB7G,MAAMyB,QAAQ;AAAM;AACtD,UAAIrG,qCAAeoJ,SAASxE,MAAMyB,GAA9B,GAAoC;AACtCzB,cAAMiE,cAAc2C,MAApB;AAOA5G,cAAMC,eAAN;;KAX2B;GAfjC,CAAA;CAxBW;AA0DjB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAUA,IAAM6G,yCAAe3J,cAAAA,YACnB,CAACN,OAAuC0B,iBAAiB;AACvD,QAAM,EAAA,aAAA,WAA0B,OAA1B,WAA4C,GAAGqH,UAAH,IAAiB/I;AACnE,QAAMgJ,iBAAiBzG,4CAAsBqG,iCAAWzI,WAAZ;AAC5C,QAAMqD,wBAAwB9D,+CAAyBS,WAAD;AACtD,QAAM0C,UAAMvC,cAAAA,QAA6B,IAA7B;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,QAAM,CAACqH,WAAWC,YAAZ,QAA4B7J,cAAAA,UAAe,KAAf;AAGlC,QAAM,CAAC8J,aAAaC,cAAd,QAAgC/J,cAAAA,UAAe,EAAf;AACtCA,oBAAAA,WAAgB,MAAM;AACpB,UAAM6I,WAAWtG,IAAI9B;AACrB,QAAIoI,UAAU;AAAA,UAAA;AACZkB,uBAAe,wBAAClB,SAASiB,iBAAV,QAAA,0BAAA,SAAA,wBAAyB,IAAIE,KAA7B,CAAD;;KAEf;IAACvB,UAAUxH;GALd;AAOA,aACE,cAAAD,eAAC,iCAAW,UADd;IAEI,OAAOnB;IACP;IACA,WAAWmF,cAAF,QAAEA,cAAF,SAAEA,YAAa8E;SAExB,cAAA9I,eAAC,2CALH,SAAA;IAKyB,SAAA;KAAYkC,uBAAnC;IAA0D,WAAW,CAACyB;GAAtE,OACE,cAAA3D,eAAC,0CAAU,KADb,SAAA;IAEI,MAAK;IACL,oBAAkB4I,YAAY,KAAKlI;IACnC,iBAAeiD,YAAYjD;IAC3B,iBAAeiD,WAAW,KAAKjD;KAC3B+G,WALN;IAME,KAAKjG;IAYL,eAAeG,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB,UAAI8B;AACF+D,uBAAeuB,YAAYpH,KAA3B;WACK;AACL6F,uBAAewB,YAAYrH,KAA3B;AACA,YAAI,CAACA,MAAMuG,kBAAkB;AAC3B,gBAAM1E,OAAO7B,MAAMiE;AACnBpC,eAAKiB,MAAL;;;KAPG,CAFwB;IAcnC,gBAAgBhD,0CACdjD,MAAMyK,gBACNvC;MAAW/E,CAAAA,UAAU6F,eAAeuB,YAAYpH,KAA3B;IAAZ,CAFyB;IAIpC,SAASF;MAAqBjD,MAAM0K;MAAS,MAAMP,aAAa,IAAD;IAAlC;IAC7B,QAAQlH;MAAqBjD,MAAM+H;MAAQ,MAAMoC,aAAa,KAAD;IAAjC;GArC9B,CAAA,CADF,CALF;CAnBe;AA0ErB,IAAMQ,2CAAqB;AAY3B,IAAMC,gDAAmBtK,cAAAA,YACvB,CAACN,OAA2C0B,iBAAiB;AAC3D,QAAM,EAAA,UAAY,OAAZ,iBAAoC,GAAGmJ,kBAAH,IAAyB7K;AACnE,aACE,cAAAsB,eAAC,6CADH;IACyB,OAAOtB,MAAMG;IAAa;SAC/C,cAAAmB,eAAC,2CADH,SAAA;IAEI,MAAK;IACL,gBAAcwJ,sCAAgBC,OAAD,IAAY,UAAUA;KAC/CF,mBAHN;IAIE,KAAKnJ;IACL,cAAYsJ,sCAAgBD,OAAD;IAC3B,UAAU9H;MACR4H,kBAAkBrB;MAClB,MAAMyB,oBAAN,QAAMA,oBAAN,SAAA,SAAMA,gBAAkBH,sCAAgBC,OAAD,IAAY,OAAO,CAACA,OAAtC;MACrB;QAAE1H,0BAA0B;;IAHA;GANhC,CAAA,CADF;CAJmB;AAsBzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM6H,yCAAmB;AAEzB,IAAM,CAACC,0CAAoBC,0CAArB,IAA6ChM,wCACjD8L,wCACA;EAAErF,OAAO7D;EAAWqJ,eAAe,MAAM;EAAA;CAFyB;AAWpE,IAAMC,gDAAiBhL,cAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAM,EAAA,OAAA,eAAwB,GAAG8G,WAAH,IAAkBxI;AAChD,QAAMuL,oBAAoB9K,0CAAe4K,aAAD;AACxC,aACE,cAAA/J,eAAC,0CADH;IACsB,OAAOtB,MAAMG;IAAa;IAAc,eAAeoL;SACzE,cAAAjK,eAAC,2CAAD,SAAA,CAAA,GAAekH,YADjB;IAC6B,KAAK9G;GAAhC,CAAA,CADF;CALiB;AAYvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM8J,wCAAkB;AAOxB,IAAMC,gDAAgBnL,cAAAA,YACpB,CAACN,OAAwC0B,iBAAiB;AACxD,QAAM,EAAA,OAAS,GAAGgK,eAAH,IAAsB1L;AACrC,QAAMmC,UAAUiJ,2CAAqBI,uCAAiBxL,MAAMG,WAAxB;AACpC,QAAM4K,UAAUlF,UAAU1D,QAAQ0D;AAClC,aACE,cAAAvE,eAAC,6CADH;IACyB,OAAOtB,MAAMG;IAAa;SAC/C,cAAAmB,eAAC,2CADH,SAAA;IAEI,MAAK;IACL,gBAAcyJ;KACVW,gBAHN;IAIE,KAAKhK;IACL,cAAYsJ,sCAAgBD,OAAD;IAC3B,UAAU9H,0CACRyI,eAAelC,UACf,MAFF;AAEE,UAAA;AAAA,cAAA,wBAAMrH,QAAQkJ,mBAAd,QAAA,0BAAA,SAAA,SAAM,sBAAA,KAAAlJ,SAAwB0D,KAAjB;OACb;MAAExC,0BAA0B;KAHA;GANhC,CAAA,CADF;CANgB;AAwBtB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMsI,4CAAsB;AAI5B,IAAM,CAACC,6CAAuBC,6CAAxB,IAAmDzM,wCACvDuM,2CACA;EAAEZ,SAAS;CAF6D;AAe1E,IAAMe,gDAAoBxL,cAAAA,YACxB,CAACN,OAA4C0B,iBAAiB;AAC5D,QAAM,EAAA,aAAA,YAA2B,GAAGqK,mBAAH,IAA0B/L;AAC3D,QAAMgM,mBAAmBH,8CAAwBF,2CAAqBxL,WAAtB;AAChD,aACE,cAAAmB,eAAC,2CADH;IAEI,SACES,cACA+I,sCAAgBkB,iBAAiBjB,OAAlB,KACfiB,iBAAiBjB,YAAY;SAG/B,cAAAzJ,eAAC,0CAAU,MAAX,SAAA,CAAA,GACMyK,oBARR;IASI,KAAKrK;IACL,cAAYsJ,sCAAgBgB,iBAAiBjB,OAAlB;GAH7B,CAAA,CAPF;CALoB;AAsB1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkB,uCAAiB;AAKvB,IAAMC,gDAAgB5L,cAAAA,YACpB,CAACN,OAAwC0B,iBAAiB;AACxD,QAAM,EAAA,aAAe,GAAGyK,eAAH,IAAsBnM;AAC3C,aACE,cAAAsB,eAAC,0CAAU,KADb,SAAA;IAEI,MAAK;IACL,oBAAiB;KACb6K,gBAHN;IAIE,KAAKzK;GAJP,CAAA;CAJgB;AActB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM0K,mCAAa;AAMnB,IAAMC,gDAAY/L,cAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAG4K,WAAH,IAAkBtM;AACvC,QAAME,cAAcT,qCAAeU,WAAD;AAClC,aAAO,cAAAmB,eAAC,2CAAD,SAAA,CAAA,GAA2BpB,aAAiBoM,YAAnD;IAA+D,KAAK5K;GAA7D,CAAA;CAJO;AAQlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM6K,iCAAW;AASjB,IAAM,CAACC,uCAAiBC,uCAAlB,IAAuCrN,wCAAuCmN,8BAAtB;AAQ9D,IAAMG,4CAAmC1M,CAAAA,UAAqC;AAC5E,QAAM,EAAA,aAAA,UAAA,OAAgC,OAAhC,aAAuCU,IAAiBV;AAC9D,QAAM2M,oBAAoB/M,qCAAe2M,gCAAUpM,WAAX;AACxC,QAAMD,cAAcT,qCAAeU,WAAD;AAClC,QAAM,CAACyM,SAASC,UAAV,QAAwBvM,cAAAA,UAA6C,IAA7C;AAC9B,QAAM,CAACF,SAASC,UAAV,QAAwBC,cAAAA,UAA0C,IAA1C;AAC9B,QAAME,mBAAmBC,0CAAeC,YAAD;AAGvCJ,oBAAAA,WAAgB,MAAM;AACpB,QAAIqM,kBAAkBvK,SAAS;AAAO5B,uBAAiB,KAAD;AACtD,WAAO,MAAMA,iBAAiB,KAAD;KAC5B;IAACmM,kBAAkBvK;IAAM5B;GAH5B;AAKA,aACE,cAAAc,eAAC,2CAAyBpB,iBACxB,cAAAoB,eAAC,oCAFL;IAGM,OAAOnB;IACP;IACA,cAAcK;IACd;IACA,iBAAiBH;SAEjB,cAAAiB,eAAC,uCAPH;IAQI,OAAOnB;IACP,WAAW2M,0CAAK;IAChB,WAAWA,0CAAK;IAChB;IACA,iBAAiBD;KAEhBtL,QAPH,CAPF,CADF;;AAsBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMwL,yCAAmB;AAKzB,IAAMC,gDAAiB1M,cAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAMS,UAAUvC,qCAAemN,wCAAkB/M,MAAMG,WAAzB;AAC9B,QAAMwC,cAAc7C,yCAAmBiN,wCAAkB/M,MAAMG,WAAzB;AACtC,QAAM8M,aAAaR,wCAAkBM,wCAAkB/M,MAAMG,WAAzB;AACpC,QAAM6I,iBAAiBzG,4CAAsBwK,wCAAkB/M,MAAMG,WAAzB;AAC5C,QAAM+M,mBAAe5M,cAAAA,QAA4B,IAA5B;AACrB,QAAM,EAAA,sBAAA,2BAAwB6M,IAA+BnE;AAC7D,QAAMoE,QAAQ;IAAEjN,aAAaH,MAAMG;;AAEnC,QAAMkN,qBAAiB/M,cAAAA,aAAkB,MAAM;AAC7C,QAAI4M,aAAanM;AAAS+E,aAAOC,aAAamH,aAAanM,OAAjC;AAC1BmM,iBAAanM,UAAU;KACtB,CAAA,CAHoB;AAKvBT,oBAAAA;IAAgB,MAAM+M;IAAgB;MAACA;;EAAvC;AAEA/M,oBAAAA,WAAgB,MAAM;AACpB,UAAMgN,oBAAoBtJ,qBAAqBjD;AAC/C,WAAO,MAAM;AACX+E,aAAOC,aAAauH,iBAApB;AACAH,iCAA2B,IAAD;;KAE3B;IAACnJ;IAAsBmJ;GAN1B;AAQA,aACE,cAAA7L,eAAC,2CADH,SAAA;IACc,SAAA;KAAY8L,KAAxB,OACE,cAAA9L,eAAC,oCADH,SAAA;IAEI,IAAI2L,WAAWM;IACf,iBAAc;IACd,iBAAepL,QAAQC;IACvB,iBAAe6K,WAAWO;IAC1B,cAAY3G,mCAAa1E,QAAQC,IAAT;KACpBpC,OANN;IAOE,KAAKyN,0CAAY/L,cAAcuL,WAAWS,eAA1B;IAGhB,SAAUvK,CAAAA,UAAU;AAAA,UAAA;AAClB,OAAA,iBAAAnD,MAAM4J,aAAN,QAAA,mBAAA,UAAA,eAAA,KAAA5J,OAAgBmD,KAAX;AACL,UAAInD,MAAMiF,YAAY9B,MAAMuG;AAAkB;AAM9CvG,YAAMiE,cAAcnB,MAApB;AACA,UAAI,CAAC9D,QAAQC;AAAMD,gBAAQzB,aAAa,IAArB;;IAErB,eAAeuC,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB6F,qBAAewB,YAAYrH,KAA3B;AACA,UAAIA,MAAMuG;AAAkB;AAC5B,UAAI,CAAC1J,MAAMiF,YAAY,CAAC9C,QAAQC,QAAQ,CAAC8K,aAAanM,SAAS;AAC7DiI,uBAAemE,2BAA2B,IAA1C;AACAD,qBAAanM,UAAU+E,OAAOE,WAAW,MAAM;AAC7C7D,kBAAQzB,aAAa,IAArB;AACA2M,yBAAc;WACb,GAHoB;;KALlB,CAFwB;IAcnC,gBAAgBpK,0CACdjD,MAAMyK,gBACNvC,gCAAW/E,CAAAA,UAAU;AAAA,UAAA;AACnBkK,qBAAc;AAEd,YAAMM,eAAW,mBAAGxL,QAAQ/B,aAAX,QAAA,qBAAA,SAAA,SAAG,iBAAiBwN,sBAAjB;AACpB,UAAID,aAAa;AAAA,YAAA;AAEf,cAAMtH,QAAI,oBAAGlE,QAAQ/B,aAAX,QAAA,sBAAA,SAAA,SAAG,kBAAiByN,QAAQxH;AACtC,cAAMyH,YAAYzH,SAAS;AAC3B,cAAM0H,QAAQD,YAAY,KAAK;AAC/B,cAAME,kBAAkBL,YAAYG,YAAY,SAAS,OAAtB;AACnC,cAAMG,iBAAiBN,YAAYG,YAAY,UAAU,MAAvB;AAElC9E,uBAAemE,2BAA2B;UACxC5G,MAAM;;YAGJ;cAAE2H,GAAG/K,MAAMiF,UAAU2F;cAAOI,GAAGhL,MAAMiL;;YACrC;cAAEF,GAAGF;cAAiBG,GAAGR,YAAYU;;YACrC;cAAEH,GAAGD;cAAgBE,GAAGR,YAAYU;;YACpC;cAAEH,GAAGD;cAAgBE,GAAGR,YAAYW;;YACpC;cAAEJ,GAAGF;cAAiBG,GAAGR,YAAYW;;;;SARzC;AAaAxI,eAAOC,aAAa/B,qBAAqBjD,OAAzC;AACAiD,6BAAqBjD,UAAU+E,OAAOE;UACpC,MAAMgD,eAAemE,2BAA2B,IAA1C;UACN;QAF6B;aAI1B;AACLnE,uBAAeuF,eAAepL,KAA9B;AACA,YAAIA,MAAMuG;AAAkB;AAG5BV,uBAAemE,2BAA2B,IAA1C;;KAnCK,CAFyB;IAyCpC,WAAWlK,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAC1D,YAAM6G,gBAAgBhB,eAAejF,UAAUhD,YAAY;AAC3D,UAAIf,MAAMiF,YAAa+E,iBAAiB7G,MAAMyB,QAAQ;AAAM;AAC5D,UAAIjG,oCAAcgE,YAAY9B,GAAb,EAAkB8G,SAASxE,MAAMyB,GAA9C,GAAoD;AAAA,YAAA;AACtDzC,gBAAQzB,aAAa,IAArB;AAGA,SAAA,oBAAAyB,QAAQ/B,aAAR,QAAA,sBAAA,UAAA,kBAAiB6F,MAAjB;AAEA9C,cAAMC,eAAN;;KAT2B;GA5EjC,CAAA,CADF;CA1BiB;AAyHvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMoL,yCAAmB;AAezB,IAAMC,gDAAiBnO,cAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAMe,gBAAgBX,uCAAiBO,oCAAcrC,MAAMG,WAArB;AACtC,QAAM,EAAA,aAAesC,cAAcV,YAAY,GAAG2M,gBAAH,IAAuB1O;AACtE,QAAMmC,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAcrC,MAAMG,WAArB;AACtC,QAAM8M,aAAaR,wCAAkB+B,wCAAkBxO,MAAMG,WAAzB;AACpC,QAAM0C,UAAMvC,cAAAA,QAAoC,IAApC;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,aACE,cAAAvB,eAAC,iCAAW,UADd;IACuB,OAAOtB,MAAMG;SAChC,cAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,cAAAd,eAAC,iCAAW,MADd;IACmB,OAAOtB,MAAMG;SAC5B,cAAAmB,eAAC,uCADH,SAAA;IAEI,IAAI2L,WAAWO;IACf,mBAAiBP,WAAWM;KACxBmB,iBAHN;IAIE,KAAK5L;IACL,OAAM;IACN,MAAMH,YAAY9B,QAAQ,QAAQ,SAAS;IAC3C,6BAA6B;IAC7B,sBAAsB;IACtB,WAAW;IACX,iBAAkBsC,CAAAA,UAAU;AAAA,UAAA;AAE1B,UAAIR,YAAYpC,mBAAmBQ;AAAS,SAAA,eAAA8B,IAAI9B,aAAJ,QAAA,iBAAA,UAAA,aAAakF,MAAb;AAC5C9C,YAAMC,eAAN;;IAIF,kBAAmBD,CAAAA,UAAUA,MAAMC,eAAN;IAC7B,gBAAgBH,0CAAqBjD,MAAMkD,gBAAiBC,CAAAA,UAAU;AAGpE,UAAIA,MAAM8D,WAAWgG,WAAWL;AAASzK,gBAAQzB,aAAa,KAArB;KAHP;IAKpC,iBAAiBuC,0CAAqBjD,MAAM2O,iBAAkBxL,CAAAA,UAAU;AACtER,kBAAYgH,QAAZ;AAEAxG,YAAMC,eAAN;KAHmC;IAKrC,WAAWH,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAE1D,YAAM+D,kBAAkB/D,MAAMiE,cAAcY,SAAS7E,MAAM8D,MAAnC;AACxB,YAAM2H,aAAa9P,qCAAe6D,YAAY9B,GAAb,EAAkB8G,SAASxE,MAAMyB,GAA/C;AACnB,UAAIsC,mBAAmB0H,YAAY;AAAA,YAAA;AACjCzM,gBAAQzB,aAAa,KAArB;AAEA,SAAA,sBAAAuM,WAAWL,aAAX,QAAA,wBAAA,UAAA,oBAAoB3G,MAApB;AAEA9C,cAAMC,eAAN;;KAT2B;GA5BjC,CAAA,CADF,CADF,CADF;CAViB;AA6DvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,SAASyD,mCAAazE,MAAe;AACnC,SAAOA,OAAO,SAAS;;AAGzB,SAAS0I,sCAAgBC,SAAoD;AAC3E,SAAOA,YAAY;;AAGrB,SAASC,sCAAgBD,SAAuB;AAC9C,SAAOD,sCAAgBC,OAAD,IAAY,kBAAkBA,UAAU,YAAY;;AAG5E,SAASjD,iCAAW+G,YAA2B;AAC7C,QAAMC,6BAA6B9N,SAASmE;AAC5C,aAAW4J,aAAaF,YAAY;AAElC,QAAIE,cAAcD;AAA4B;AAC9CC,cAAU9I,MAAV;AACA,QAAIjF,SAASmE,kBAAkB2J;AAA4B;;;AAQ/D,SAASE,gCAAaC,OAAYC,YAAoB;AACpD,SAAOD,MAAMzJ;IAAI,CAAC2J,GAAGC,UAAUH,OAAOC,aAAaE,SAASH,MAAMvH,MAA9B;EAA7B;;AAoBT,SAAShC,mCAAaH,QAAkBV,QAAgBO,cAAuB;AAC7E,QAAMiK,aAAaxK,OAAO6C,SAAS,KAAK4H,MAAMC,KAAK1K,MAAX,EAAmB2K;IAAOC,CAAAA,SAASA,SAAS5K,OAAO,CAAD;EAAlD;AACxC,QAAM6K,mBAAmBL,aAAaxK,OAAO,CAAD,IAAMA;AAClD,QAAM8K,oBAAoBvK,eAAeG,OAAOqK,QAAQxK,YAAf,IAA+B;AACxE,MAAIyK,gBAAgBb,gCAAUzJ,QAAQuK,KAAKC,IAAIJ,mBAAmB,CAA5B,CAAT;AAC7B,QAAMK,sBAAsBN,iBAAiBhI,WAAW;AACxD,MAAIsI;AAAqBH,oBAAgBA,cAAc9K;MAAQkL,CAAAA,MAAMA,MAAM7K;IAAlC;AACzC,QAAMK,YAAYoK,cAAcxK;IAAMQ,CAAAA,UACpCA,MAAMqK,YAAN,EAAoBC,WAAWT,iBAAiBQ,YAAjB,CAA/B;EADgB;AAGlB,SAAOzK,cAAcL,eAAeK,YAAYzD;;AAUlD,SAASoO,uCAAiBC,OAAcC,SAAkB;AACxD,QAAM,EAAA,GAAA,EAAKnC,IAAMkC;AACjB,MAAIE,SAAS;AACb,WAASC,IAAI,GAAGC,IAAIH,QAAQ5I,SAAS,GAAG8I,IAAIF,QAAQ5I,QAAQ+I,IAAID,KAAK;AACnE,UAAME,KAAKJ,QAAQE,CAAD,EAAItC;AACtB,UAAMyC,KAAKL,QAAQE,CAAD,EAAIrC;AACtB,UAAMyC,KAAKN,QAAQG,CAAD,EAAIvC;AACtB,UAAM2C,KAAKP,QAAQG,CAAD,EAAItC;AAGtB,UAAM2C,YAAcH,KAAKxC,MAAQ0C,KAAK1C,KAAQD,KAAK0C,KAAKF,OAAOvC,IAAIwC,OAAOE,KAAKF,MAAMD;AACrF,QAAII;AAAWP,eAAS,CAACA;;AAG3B,SAAOA;;AAGT,SAASjK,2CAAqBnD,OAA2BoD,MAAgB;AACvE,MAAI,CAACA;AAAM,WAAO;AAClB,QAAMwK,YAAY;IAAE7C,GAAG/K,MAAMiF;IAAS+F,GAAGhL,MAAMiL;;AAC/C,SAAOgC,uCAAiBW,WAAWxK,IAAZ;;AAGzB,SAAS2B,gCAAa8I,SAAqE;AACzF,SAAQ7N,CAAAA,UAAWA,MAAM8N,gBAAgB,UAAUD,QAAQ7N,KAAD,IAAUnB;;AAGtE,IAAMkP,4CAAOnR;AACb,IAAMoR,4CAAS1P;AACf,IAAM2P,4CAASnP;AACf,IAAMoP,4CAAU7O;AAChB,IAAM8O,4CAAQ/I;AACd,IAAMgJ,4CAAQ7I;AACd,IAAM8I,4CAAO1I;AACb,IAAM2I,4CAAe7G;AACrB,IAAM8G,4CAAapG;AACnB,IAAMqG,4CAAYlG;AAClB,IAAMmG,4CAAgB9F;AACtB,IAAM+F,4CAAY3F;AAClB,IAAM4F,4CAAQzF;AACd,IAAM0F,4CAAMrF;AACZ,IAAMsF,4CAAahF;AACnB,IAAMiF,4CAAaxD;;;AEnyCnB,IAAMyD,2CAAqB;AAG3B,IAAM,CAACC,iDAA2BC,yCAA5B,IAAuDC,yCAC3DH,0CACA;EAACI;CAF4E;AAI/E,IAAMC,qCAAeD,0CAAe;AAYpC,IAAM,CAACE,4CAAsBC,4CAAvB,IACJN,gDAAoDD,wCAA3B;AAW3B,IAAMQ,4CAA6CC,CAAAA,UAA0C;AAC3F,QAAM,EAAA,qBAAA,UAAA,KAIJC,MAAMC,UAJF,aAAA,cAAA,QAOI,KAARC,IACEH;AACJ,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,QAAMC,iBAAaC,cAAAA,QAAgC,IAAhC;AACnB,QAAM,CAACN,OAAO,OAAOO,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMR;IACNS,aAAaC;IACbC,UAAUC;GAHwC;AAMpD,aACE,cAAAC,eAAC,4CADH;IAEI,OAAOV;IACP,WAAWW,0CAAK;IAChB;IACA,WAAWA,0CAAK;IAChB;IACA,cAAcR;IACd,kBAAcD,cAAAA;MAAkB,MAAMC;QAASS,CAAAA,aAAa,CAACA;MAAhB;MAA2B;QAACT;;IAA3D;IACd;SAEA,cAAAO,eAAC,2CAAD,SAAA,CAAA,GAAwBX,WAV1B;IAUqC;IAAY,cAAcI;IAAS;IAAU;GAAhF,GACGU,QADH,CAVF;;AAiBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAMrB,IAAMC,gDAAsBb,cAAAA,YAC1B,CAACP,OAA8CqB,iBAAiB;AAC9D,QAAM,EAAA,qBAAA,WAAkC,OAAO,GAAGC,aAAH,IAAoBtB;AACnE,QAAMuB,UAAUzB,6CAAuBqB,oCAAcd,mBAAf;AACtC,QAAMD,YAAYR,mCAAaS,mBAAD;AAC9B,aACE,cAAAU,eAAC,2CADH,SAAA;IACwB,SAAA;KAAYX,SAAlC,OACE,cAAAW,eAAC,0CAAU,QADb,SAAA;IAEI,MAAK;IACL,IAAIQ,QAAQC;IACZ,iBAAc;IACd,iBAAeD,QAAQtB;IACvB,iBAAesB,QAAQtB,OAAOsB,QAAQE,YAAYC;IAClD,cAAYH,QAAQtB,OAAO,SAAS;IACpC,iBAAe0B,WAAW,KAAKD;IAC/B;KACIJ,cATN;IAUE,KAAKM,0CAAYP,cAAcE,QAAQjB,UAAvB;IAChB,eAAeuB,0CAAqB7B,MAAM8B,eAAgBC,CAAAA,UAAU;AAGlE,UAAI,CAACJ,YAAYI,MAAMC,WAAW,KAAKD,MAAME,YAAY,OAAO;AAC9DV,gBAAQW,aAAR;AAGA,YAAI,CAACX,QAAQtB;AAAM8B,gBAAMI,eAAN;;KAPY;IAUnC,WAAWN,0CAAqB7B,MAAMoC,WAAYL,CAAAA,UAAU;AAC1D,UAAIJ;AAAU;AACd,UAAI;QAAC;QAAS;QAAKU,SAASN,MAAMO,GAA9B;AAAoCf,gBAAQW,aAAR;AACxC,UAAIH,MAAMO,QAAQ;AAAaf,gBAAQT,aAAa,IAArB;AAG/B,UAAI;QAAC;QAAS;QAAK;QAAauB,SAASN,MAAMO,GAA3C;AAAiDP,cAAMI,eAAN;KANxB;GArBjC,CAAA,CADF;CANsB;AA0C5B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMI,oCAAc;AAKpB,IAAMC,4CACJxC,CAAAA,UACG;AACH,QAAM,EAAA,qBAAuB,GAAGyC,YAAH,IAAmBzC;AAChD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAA0BX,WAAeqC,WAAzC,CAAA;;AAGT,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAMrB,IAAMC,gDAAsBpC,cAAAA,YAC1B,CAACP,OAA8CqB,iBAAiB;AAC9D,QAAM,EAAA,qBAAuB,GAAGuB,aAAH,IAAoB5C;AACjD,QAAMuB,UAAUzB,6CAAuB4C,oCAAcrC,mBAAf;AACtC,QAAMD,YAAYR,mCAAaS,mBAAD;AAC9B,QAAMwC,8BAA0BtC,cAAAA,QAAa,KAAb;AAEhC,aACE,cAAAQ,eAAC,2CADH,SAAA;IAEI,IAAIQ,QAAQE;IACZ,mBAAiBF,QAAQC;KACrBpB,WACAwC,cAJN;IAKE,KAAKvB;IACL,kBAAkBQ,0CAAqB7B,MAAM8C,kBAAmBf,CAAAA,UAAU;AAAA,UAAA;AACxE,UAAI,CAACc,wBAAwBE;AAAS,SAAA,wBAAAxB,QAAQjB,WAAWyC,aAAnB,QAAA,0BAAA,UAAA,sBAA4BC,MAA5B;AACtCH,8BAAwBE,UAAU;AAElChB,YAAMI,eAAN;KAJoC;IAMtC,mBAAmBN,0CAAqB7B,MAAMiD,mBAAoBlB,CAAAA,UAAU;AAC1E,YAAMmB,gBAAgBnB,MAAMoB,OAAOD;AACnC,YAAME,gBAAgBF,cAAclB,WAAW,KAAKkB,cAAcjB,YAAY;AAC9E,YAAMoB,eAAeH,cAAclB,WAAW,KAAKoB;AACnD,UAAI,CAAC7B,QAAQpB,SAASkD;AAAcR,gCAAwBE,UAAU;KAJjC;IAMvC,OAAO;MACL,GAAG/C,MAAMsD;MAGP,kDACE;MACF,iDAAiD;MACjD,kDACE;MACF,uCAAuC;MACvC,wCAAwC;;GA5B9C,CAAA;CARsB;AA4C5B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,mCAAa;AAMnB,IAAMC,gDAAoBjD,cAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAGoC,WAAH,IAAkBzD;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAeqD,YAA/C;IAA2D,KAAKpC;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMqC,mCAAa;AAMnB,IAAMC,gDAAoBpD,cAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAGuC,WAAH,IAAkB5D;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAewD,YAA/C;IAA2D,KAAKvC;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMwC,kCAAY;AAMlB,IAAMC,gDAAmBvD,cAAAA,YACvB,CAACP,OAA2CqB,iBAAiB;AAC3D,QAAM,EAAA,qBAAuB,GAAG0C,UAAH,IAAiB/D;AAC9C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAwBX,WAAe2D,WAA9C;IAAyD,KAAK1C;GAAvD,CAAA;CAJc;AAQzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM2C,2CAAqB;AAM3B,IAAMC,gDAA2B1D,cAAAA,YAG/B,CAACP,OAAmDqB,iBAAiB;AACrE,QAAM,EAAA,qBAAuB,GAAG6C,kBAAH,IAAyBlE;AACtD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAgCX,WAAe8D,mBAAtD;IAAyE,KAAK7C;GAAvE,CAAA;CANwB;AASjC,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM8C,yCAAmB;AAMzB,IAAMC,gDAAyB7D,cAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGgD,gBAAH,IAAuBrE;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAA8BX,WAAeiE,iBAApD;IAAqE,KAAKhD;GAAnE,CAAA;CANsB;AAS/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiD,wCAAkB;AAMxB,IAAMC,gDAAwBhE,cAAAA,YAG5B,CAACP,OAAgDqB,iBAAiB;AAClE,QAAM,EAAA,qBAAuB,GAAGmD,eAAH,IAAsBxE;AACnD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAA6BX,WAAeoE,gBAAnD;IAAmE,KAAKnD;GAAjE,CAAA;CANqB;AAS9B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMoD,uCAAiB;AAMvB,IAAMC,gDAA4BnE,cAAAA,YAGhC,CAACP,OAAoDqB,iBAAiB;AACtE,QAAM,EAAA,qBAAuB,GAAGsD,mBAAH,IAA0B3E;AACvD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAiCX,WAAeuE,oBAAvD;IAA2E,KAAKtD;GAAzE,CAAA;CANyB;AASlC,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMuD,uCAAiB;AAMvB,IAAMC,gDAAwBtE,cAAAA,YAG5B,CAACP,OAAgDqB,iBAAiB;AAClE,QAAM,EAAA,qBAAuB,GAAGyD,eAAH,IAAsB9E;AACnD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAA6BX,WAAe0E,gBAAnD;IAAmE,KAAKzD;GAAjE,CAAA;CANqB;AAS9B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM0D,mCAAa;AAMnB,IAAMC,gDAAoBzE,cAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAG4D,WAAH,IAAkBjF;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAe6E,YAA/C;IAA2D,KAAK5D;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAaA,IAAM6D,4CACJlF,CAAAA,UACG;AACH,QAAM,EAAA,qBAAA,UAAiCC,MAAMC,UAAvC,cAAA,YAA+DU,IAAgBZ;AACrF,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,QAAM,CAACJ,OAAO,OAAOO,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMR;IACNS,aAAaC;IACbC,UAAUC;GAHwC;AAMpD,aACE,cAAAC,eAAC,2CAAD,SAAA,CAAA,GAAuBX,WADzB;IACoC;IAAY,cAAcI;GAA5D,GACGU,QADH;;AAUJ,IAAMiE,yCAAmB;AAMzB,IAAMC,gDAAyB7E,cAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGgE,gBAAH,IAAuBrF;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,cAAAU,eAAC,2CAAD,SAAA,CAAA,GAA8BX,WAAeiF,iBAApD;IAAqE,KAAKhE;GAAnE,CAAA;CANsB;AAS/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiE,yCAAmB;AAMzB,IAAMC,gDAAyBhF,cAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGmE,gBAAH,IAAuBxF;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAE9B,aACE,cAAAU,eAAC,2CAAD,SAAA,CAAA,GACMX,WACAoF,iBAHR;IAII,KAAKnE;IACL,OAAO;MACL,GAAGrB,MAAMsD;MAGP,kDAAkD;MAClD,iDAAiD;MACjD,kDAAkD;MAClD,uCAAuC;MACvC,wCAAwC;;GAZ9C,CAAA;CAR2B;AA2B/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,IAAMmC,4CAAO1F;AACb,IAAM2F,4CAAUtE;AAChB,IAAMuE,4CAASnD;AACf,IAAMoD,4CAAUjD;AAChB,IAAMkD,4CAAQrC;AACd,IAAMsC,4CAAQnC;AACd,IAAMoC,4CAAOjC;AACb,IAAMkC,4CAAe/B;AACrB,IAAMgC,4CAAa7B;AACnB,IAAM8B,4CAAY3B;AAClB,IAAM4B,4CAAgBzB;AACtB,IAAM0B,4CAAYvB;AAClB,IAAMwB,4CAAQrB;AACd,IAAMsB,4CAAMpB;AACZ,IAAMqB,4CAAanB;AACnB,IAAMoB,4CAAajB;", "names": ["count", "useFocusGuards", "React", "edgeGuards", "document", "querySelectorAll", "body", "insertAdjacentElement", "createFocusGuard", "count", "for<PERSON>ach", "node", "remove", "element", "createElement", "setAttribute", "tabIndex", "style", "cssText", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FOCUS_SCOPE_NAME", "FocusScope", "React", "props", "forwardedRef", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useCallbackRef", "lastFocusedElementRef", "composedRefs", "useComposedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "trapped", "handleFocusIn", "event", "target", "contains", "focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "document", "activeElement", "body", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "focusScopesStack", "add", "previouslyFocusedElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "focusFirst", "removeLinks", "getTabbableCandidates", "setTimeout", "unmountEvent", "remove", "handleKeyDown", "loop", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "currentTarget", "first", "last", "getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "$45QHv$createElement", "candidates", "candidate", "findVisible", "reverse", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "tabIndex", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "isSelectableInput", "HTMLInputElement", "preventScroll", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "unshift", "array", "item", "updatedArray", "index", "indexOf", "splice", "items", "filter", "__assign", "React", "React", "import_react", "React", "import_react", "cbs", "React", "import_react", "React", "SideCar", "React", "React", "React", "Style", "SELECTION_KEYS", "FIRST_KEYS", "LAST_KEYS", "FIRST_LAST_KEYS", "SUB_OPEN_KEYS", "ltr", "rtl", "SUB_CLOSE_KEYS", "MENU_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createMenuContext", "createMenuScope", "createContextScope", "createPopperScope", "createRovingFocusGroupScope", "usePopperScope", "useRovingFocusGroupScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContext", "MenuRootProvider", "useMenuRootContext", "<PERSON><PERSON>", "props", "modal", "popperScope", "__scopeMenu", "content", "<PERSON><PERSON><PERSON><PERSON>", "React", "isUsingKeyboardRef", "handleOpenChange", "useCallbackRef", "onOpenChange", "direction", "useDirection", "dir", "handleKeyDown", "current", "document", "addEventListener", "handlePointer", "capture", "once", "removeEventListener", "$epM9y$createElement", "children", "ANCHOR_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedRef", "anchorProps", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "undefined", "<PERSON>uPort<PERSON>", "container", "context", "open", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "contentProps", "rootContext", "MenuRootContentModal", "ref", "composedRefs", "useComposedRefs", "hideOthers", "composeEventHandlers", "onFocusOutside", "event", "preventDefault", "checkForDefaultPrevented", "MenuRootContentNonModal", "MenuContentImpl", "rovingFocusGroupScope", "getItems", "currentItemId", "setCurrentItemId", "contentRef", "onContentChange", "timerRef", "searchRef", "pointerGraceTimerRef", "pointerGraceIntentRef", "pointerDirRef", "lastPointerXRef", "ScrollLockWrapper", "disableOutsideScroll", "RemoveScroll", "scrollLockWrapperProps", "as", "Slot", "allowPinchZoom", "handleTypeaheadSearch", "key", "search", "items", "filter", "item", "disabled", "currentItem", "activeElement", "currentMatch", "find", "textValue", "values", "map", "nextMatch", "getNextMatch", "newItem", "updateSearch", "value", "window", "clearTimeout", "setTimeout", "focus", "useFocusGuards", "isPointerMovingToSubmenu", "isMovingTowards", "side", "isPointerInGraceArea", "area", "intent", "trapFocus", "onOpenAutoFocus", "onCloseAutoFocus", "onEntryFocus", "getOpenState", "outline", "style", "onKeyDown", "target", "isKeyDownInside", "closest", "currentTarget", "isModifierKey", "ctrl<PERSON>ey", "altKey", "metaKey", "isCharacterKey", "length", "includes", "candidateNodes", "reverse", "focusFirst", "onBlur", "contains", "onPointerMove", "whenMouse", "pointer<PERSON><PERSON>as<PERSON><PERSON><PERSON>", "clientX", "newDir", "GROUP_NAME", "MenuGroup", "groupProps", "LABEL_NAME", "<PERSON>u<PERSON><PERSON><PERSON>", "labelProps", "ITEM_NAME", "ITEM_SELECT", "MenuItem", "itemProps", "contentContext", "isPointerDownRef", "handleSelect", "menuItem", "itemSelectEvent", "CustomEvent", "bubbles", "cancelable", "onSelect", "dispatchDiscreteCustomEvent", "defaultPrevented", "onClose", "onClick", "onPointerDown", "onPointerUp", "click", "isTypingAhead", "MenuItemImpl", "isFocused", "setIsFocused", "textContent", "setTextContent", "trim", "onItemLeave", "onItemEnter", "onPointerLeave", "onFocus", "CHECKBOX_ITEM_NAME", "MenuCheckboxItem", "checkboxItemProps", "isIndeterminate", "checked", "getCheckedState", "onCheckedChange", "RADIO_GROUP_NAME", "RadioGroupProvider", "useRadioGroupContext", "onValueChange", "MenuRadioGroup", "handleValueChange", "RADIO_ITEM_NAME", "MenuRadioItem", "radioItemProps", "ITEM_INDICATOR_NAME", "ItemIndicatorProvider", "useItemIndicatorContext", "MenuItemIndicator", "itemIndicatorProps", "indicatorContext", "SEPARATOR_NAME", "MenuSeparator", "separatorProps", "ARROW_NAME", "MenuArrow", "arrowProps", "SUB_NAME", "MenuSub<PERSON><PERSON><PERSON>", "useMenuSubContext", "MenuSub", "parentMenuContext", "trigger", "setTrigger", "useId", "SUB_TRIGGER_NAME", "MenuSubTrigger", "subContext", "openTimerRef", "onPointerGraceIntentChange", "scope", "clearOpenTimer", "pointerGraceTimer", "triggerId", "contentId", "composeRefs", "onTriggerChange", "contentRect", "getBoundingClientRect", "dataset", "rightSide", "bleed", "contentNearEdge", "contentFarEdge", "x", "y", "clientY", "top", "bottom", "onTriggerLeave", "SUB_CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subContentProps", "onEscapeKeyDown", "isClose<PERSON>ey", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "candidate", "wrapArray", "array", "startIndex", "_", "index", "isRepeated", "Array", "from", "every", "char", "normalizedSearch", "currentMatchIndex", "indexOf", "wrappedValues", "Math", "max", "excludeCurrentMatch", "v", "toLowerCase", "startsWith", "isPointInPolygon", "point", "polygon", "inside", "i", "j", "xi", "yi", "xj", "yj", "intersect", "cursorPos", "handler", "pointerType", "Root", "<PERSON><PERSON>", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent", "DROPDOWN_MENU_NAME", "createDropdownMenuContext", "createDropdownMenuScope", "createContextScope", "createMenuScope", "useMenuScope", "DropdownMenuProvider", "useDropdownMenuContext", "DropdownMenu", "props", "open", "openProp", "modal", "menuScope", "__scopeDropdownMenu", "triggerRef", "React", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "defaultOpen", "onChange", "onOpenChange", "$9kmUS$createElement", "useId", "prevOpen", "children", "TRIGGER_NAME", "DropdownMenuTrigger", "forwardedRef", "triggerProps", "context", "triggerId", "contentId", "undefined", "disabled", "composeRefs", "composeEventHandlers", "onPointerDown", "event", "button", "ctrl<PERSON>ey", "onOpenToggle", "preventDefault", "onKeyDown", "includes", "key", "PORTAL_NAME", "DropdownMenuPortal", "portalProps", "CONTENT_NAME", "DropdownMenuContent", "contentProps", "hasInteractedOutsideRef", "onCloseAutoFocus", "current", "focus", "onInteractOutside", "originalEvent", "detail", "ctrlLeftClick", "isRightClick", "style", "GROUP_NAME", "DropdownMenuGroup", "groupProps", "LABEL_NAME", "DropdownMenuLabel", "labelProps", "ITEM_NAME", "DropdownMenuItem", "itemProps", "CHECKBOX_ITEM_NAME", "DropdownMenuCheckboxItem", "checkboxItemProps", "RADIO_GROUP_NAME", "DropdownMenuRadioGroup", "radioGroupProps", "RADIO_ITEM_NAME", "DropdownMenuRadioItem", "radioItemProps", "INDICATOR_NAME", "DropdownMenuItemIndicator", "itemIndicatorProps", "SEPARATOR_NAME", "DropdownMenuSeparator", "separatorProps", "ARROW_NAME", "DropdownMenuArrow", "arrowProps", "DropdownMenuSub", "SUB_TRIGGER_NAME", "DropdownMenuSubTrigger", "subTriggerProps", "SUB_CONTENT_NAME", "DropdownMenuSubContent", "subContentProps", "Root", "<PERSON><PERSON>", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}