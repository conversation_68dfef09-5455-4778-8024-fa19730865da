{"version": 3, "sources": ["../../@0no-co/graphql.web/src/kind.js", "../../@0no-co/graphql.web/src/error.ts", "../../@0no-co/graphql.web/src/parser.ts", "../../@0no-co/graphql.web/src/visitor.ts", "../../@0no-co/graphql.web/src/printer.ts", "../../@0no-co/graphql.web/src/values.ts", "../../wonka/dist/wonka.mjs", "../../@urql/core/src/utils/error.ts", "../../@urql/core/src/utils/hash.ts", "../../@urql/core/src/utils/variables.ts", "../../@urql/core/src/utils/request.ts", "../../@urql/core/src/utils/result.ts", "../../@urql/core/src/internal/fetchOptions.ts", "../../@urql/core/src/internal/fetchSource.ts", "../../@urql/core/src/utils/collectTypenames.ts", "../../@urql/core/src/utils/formatDocument.ts", "../../@urql/core/src/utils/maskTypename.ts", "../../@urql/core/src/utils/streamUtils.ts", "../../@urql/core/src/utils/operation.ts", "../../@urql/core/src/utils/index.ts", "../../@urql/core/src/gql.ts", "../../@urql/core/src/exchanges/cache.ts", "../../@urql/core/src/exchanges/ssr.ts", "../../@urql/core/src/exchanges/subscription.ts", "../../@urql/core/src/exchanges/debug.ts", "../../@urql/core/src/exchanges/dedup.ts", "../../@urql/core/src/exchanges/fetch.ts", "../../@urql/core/src/exchanges/compose.ts", "../../@urql/core/src/exchanges/map.ts", "../../@urql/core/src/exchanges/fallback.ts", "../../@urql/core/src/client.ts", "../../urql/src/context.ts", "../../urql/src/hooks/state.ts", "../../urql/src/hooks/useMutation.ts", "../../urql/src/hooks/useRequest.ts", "../../urql/src/hooks/cache.ts", "../../urql/src/hooks/useQuery.ts", "../../urql/src/hooks/useSubscription.ts", "../../urql/src/components/Mutation.ts", "../../urql/src/components/Query.ts", "../../urql/src/components/Subscription.ts"], "sourcesContent": ["export const Kind = {\n  NAME: 'Name',\n  DOCUMENT: 'Document',\n  OPERATION_DEFINITION: 'OperationDefinition',\n  VARIABLE_DEFINITION: 'VariableDefinition',\n  SELECTION_SET: 'SelectionSet',\n  FIELD: 'Field',\n  ARGUMENT: 'Argument',\n  FRAGMENT_SPREAD: 'FragmentSpread',\n  INLINE_FRAGMENT: 'InlineFragment',\n  FRAGMENT_DEFINITION: 'FragmentDefinition',\n  VARIABLE: 'Variable',\n  INT: 'IntValue',\n  FLOAT: 'FloatValue',\n  STRING: 'StringValue',\n  BOOLEAN: 'BooleanValue',\n  NULL: 'NullValue',\n  ENUM: 'EnumValue',\n  LIST: 'ListValue',\n  OBJECT: 'ObjectValue',\n  OBJECT_FIELD: 'ObjectField',\n  DIRECTIVE: 'Directive',\n  NAMED_TYPE: 'NamedType',\n  LIST_TYPE: 'ListType',\n  NON_NULL_TYPE: 'NonNullType',\n\n  /*\n  SCHEMA_DEFINITION: 'SchemaDefinition',\n  OPERATION_TYPE_DEFINITION: 'OperationTypeDefinition',\n  SCALAR_TYPE_DEFINITION: 'ScalarTypeDefinition',\n  OBJECT_TYPE_DEFINITION: 'ObjectTypeDefinition',\n  FIELD_DEFINITION: 'FieldDefinition',\n  INPUT_VALUE_DEFINITION: 'InputValueDefinition',\n  INTERFACE_TYPE_DEFINITION: 'InterfaceTypeDefinition',\n  UNION_TYPE_DEFINITION: 'UnionTypeDefinition',\n  ENUM_TYPE_DEFINITION: 'EnumTypeDefinition',\n  ENUM_VALUE_DEFINITION: 'EnumValueDefinition',\n  INPUT_OBJECT_TYPE_DEFINITION: 'InputObjectTypeDefinition',\n  DIRECTIVE_DEFINITION: 'DirectiveDefinition',\n  SCHEMA_EXTENSION: 'SchemaExtension',\n  SCALAR_TYPE_EXTENSION: 'ScalarTypeExtension',\n  OBJECT_TYPE_EXTENSION: 'ObjectTypeExtension',\n  INTERFACE_TYPE_EXTENSION: 'InterfaceTypeExtension',\n  UNION_TYPE_EXTENSION: 'UnionTypeExtension',\n  ENUM_TYPE_EXTENSION: 'EnumTypeExtension',\n  INPUT_OBJECT_TYPE_EXTENSION: 'InputObjectTypeExtension',\n  */\n};\n\nexport const OperationTypeNode = {\n  QUERY: 'query',\n  MUTATION: 'mutation',\n  SUBSCRIPTION: 'subscription',\n};\n", "import { Maybe, Extensions, Source } from './types';\nimport { ASTNode } from './ast';\n\nexport class GraphQLError extends Error {\n  readonly locations: ReadonlyArray<any> | undefined;\n  readonly path: ReadonlyArray<string | number> | undefined;\n  readonly nodes: Readonly<PERSON>rray<any> | undefined;\n  readonly source: Source | undefined;\n  readonly positions: ReadonlyArray<number> | undefined;\n  readonly originalError: Error | undefined;\n  readonly extensions: Extensions;\n\n  constructor(\n    message: string,\n    nodes?: ReadonlyArray<ASTNode> | ASTNode | null,\n    source?: Maybe<Source>,\n    positions?: Maybe<ReadonlyArray<number>>,\n    path?: Maybe<ReadonlyArray<string | number>>,\n    originalError?: Maybe<Error>,\n    extensions?: Maybe<Extensions>\n  ) {\n    super(message);\n\n    this.name = 'GraphQLError';\n    this.message = message;\n\n    if (path) this.path = path;\n    if (nodes) this.nodes = (Array.isArray(nodes) ? nodes : [nodes]) as ASTNode[];\n    if (source) this.source = source;\n    if (positions) this.positions = positions;\n    if (originalError) this.originalError = originalError;\n\n    let _extensions = extensions;\n    if (!_extensions && originalError) {\n      const originalExtensions = (originalError as any).extensions;\n      if (originalExtensions && typeof originalExtensions === 'object') {\n        _extensions = originalExtensions;\n      }\n    }\n\n    this.extensions = _extensions || {};\n  }\n\n  toJSON(): any {\n    return { ...this, message: this.message };\n  }\n\n  toString() {\n    return this.message;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n}\n", "/**\n * This is a spec-compliant implementation of a GraphQL query language parser,\n * up-to-date with the October 2021 Edition. Unlike the reference implementation\n * in graphql.js it will only parse the query language, but not the schema\n * language.\n */\nimport { Kind, OperationTypeNode } from './kind';\nimport { GraphQLError } from './error';\nimport { Source } from './types';\nimport type * as ast from './ast';\n\nlet input: string;\nlet idx: number;\n\nfunction error(kind: string) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${idx} in ${kind}`);\n}\n\nfunction advance(pattern: RegExp) {\n  pattern.lastIndex = idx;\n  if (pattern.test(input)) {\n    const match = input.slice(idx, (idx = pattern.lastIndex));\n    return match;\n  }\n}\n\nconst leadingRe = / +(?=[^\\s])/y;\nfunction blockString(string: string) {\n  const lines = string.split('\\n');\n  let out = '';\n  let commonIndent = 0;\n  let firstNonEmptyLine = 0;\n  let lastNonEmptyLine = lines.length - 1;\n  for (let i = 0; i < lines.length; i++) {\n    leadingRe.lastIndex = 0;\n    if (leadingRe.test(lines[i])) {\n      if (i && (!commonIndent || leadingRe.lastIndex < commonIndent))\n        commonIndent = leadingRe.lastIndex;\n      firstNonEmptyLine = firstNonEmptyLine || i;\n      lastNonEmptyLine = i;\n    }\n  }\n  for (let i = firstNonEmptyLine; i <= lastNonEmptyLine; i++) {\n    if (i !== firstNonEmptyLine) out += '\\n';\n    out += lines[i].slice(commonIndent).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return out;\n}\n\n// Note: This is equivalent to: /(?:[\\s,]*|#[^\\n\\r]*)*/y\nfunction ignored() {\n  for (\n    let char = input.charCodeAt(idx++) | 0;\n    char === 9 /*'\\t'*/ ||\n    char === 10 /*'\\n'*/ ||\n    char === 13 /*'\\r'*/ ||\n    char === 32 /*' '*/ ||\n    char === 35 /*'#'*/ ||\n    char === 44 /*','*/ ||\n    char === 65279 /*'\\ufeff'*/;\n    char = input.charCodeAt(idx++) | 0\n  ) {\n    if (char === 35 /*'#'*/) while ((char = input.charCodeAt(idx++)) !== 10 && char !== 13);\n  }\n  idx--;\n}\n\nconst nameRe = /[_A-Za-z]\\w*/y;\nfunction name(): ast.NameNode | undefined {\n  let match: string | undefined;\n  if ((match = advance(nameRe))) {\n    return {\n      kind: 'Name' as Kind.NAME,\n      value: match,\n    };\n  }\n}\n\n// NOTE(Safari10 Quirk): This needs to be wrapped in a non-capturing group\nconst constRe = /(?:null|true|false)/y;\n\nconst variableRe = /\\$[_A-Za-z]\\w*/y;\nconst intRe = /-?\\d+/y;\n\n// NOTE(Safari10 Quirk): This cannot be further simplified\nconst floatPartRe = /(?:\\.\\d+)?[eE][+-]?\\d+|\\.\\d+/y;\n\nconst complexStringRe = /\\\\/g;\nconst blockStringRe = /\"\"\"(?:\"\"\"|(?:[\\s\\S]*?[^\\\\])\"\"\")/y;\nconst stringRe = /\"(?:\"|[^\\r\\n]*?[^\\\\]\")/y;\n\nfunction value(constant: true): ast.ConstValueNode;\nfunction value(constant: boolean): ast.ValueNode;\n\nfunction value(constant: boolean): ast.ValueNode | undefined {\n  let out: ast.ValueNode | undefined;\n  let match: string | undefined;\n  if ((match = advance(constRe))) {\n    out =\n      match === 'null'\n        ? {\n            kind: 'NullValue' as Kind.NULL,\n          }\n        : {\n            kind: 'BooleanValue' as Kind.BOOLEAN,\n            value: match === 'true',\n          };\n  } else if (!constant && (match = advance(variableRe))) {\n    out = {\n      kind: 'Variable' as Kind.VARIABLE,\n      name: {\n        kind: 'Name' as Kind.NAME,\n        value: match.slice(1),\n      },\n    };\n  } else if ((match = advance(intRe))) {\n    const intPart = match;\n    if ((match = advance(floatPartRe))) {\n      out = {\n        kind: 'FloatValue' as Kind.FLOAT,\n        value: intPart + match,\n      };\n    } else {\n      out = {\n        kind: 'IntValue' as Kind.INT,\n        value: intPart,\n      };\n    }\n  } else if ((match = advance(nameRe))) {\n    out = {\n      kind: 'EnumValue' as Kind.ENUM,\n      value: match,\n    };\n  } else if ((match = advance(blockStringRe))) {\n    out = {\n      kind: 'StringValue' as Kind.STRING,\n      value: blockString(match.slice(3, -3)),\n      block: true,\n    };\n  } else if ((match = advance(stringRe))) {\n    out = {\n      kind: 'StringValue' as Kind.STRING,\n      value: complexStringRe.test(match) ? (JSON.parse(match) as string) : match.slice(1, -1),\n      block: false,\n    };\n  } else if ((out = list(constant) || object(constant))) {\n    return out;\n  }\n\n  ignored();\n  return out;\n}\n\nfunction list(constant: boolean): ast.ListValueNode | undefined {\n  let match: ast.ValueNode | undefined;\n  if (input.charCodeAt(idx) === 91 /*'['*/) {\n    idx++;\n    ignored();\n    const values: ast.ValueNode[] = [];\n    while ((match = value(constant))) values.push(match);\n    if (input.charCodeAt(idx++) !== 93 /*']'*/) throw error('ListValue');\n    ignored();\n    return {\n      kind: 'ListValue' as Kind.LIST,\n      values,\n    };\n  }\n}\n\nfunction object(constant: boolean): ast.ObjectValueNode | undefined {\n  if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    idx++;\n    ignored();\n    const fields: ast.ObjectFieldNode[] = [];\n    let _name: ast.NameNode | undefined;\n    while ((_name = name())) {\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('ObjectField' as Kind.OBJECT_FIELD);\n      ignored();\n      const _value = value(constant);\n      if (!_value) throw error('ObjectField');\n      fields.push({\n        kind: 'ObjectField' as Kind.OBJECT_FIELD,\n        name: _name,\n        value: _value,\n      });\n    }\n    if (input.charCodeAt(idx++) !== 125 /*'}'*/) throw error('ObjectValue');\n    ignored();\n    return {\n      kind: 'ObjectValue' as Kind.OBJECT,\n      fields,\n    };\n  }\n}\n\nfunction arguments_(constant: boolean): ast.ArgumentNode[] {\n  const args: ast.ArgumentNode[] = [];\n  ignored();\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    idx++;\n    ignored();\n    let _name: ast.NameNode | undefined;\n    while ((_name = name())) {\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('Argument');\n      ignored();\n      const _value = value(constant);\n      if (!_value) throw error('Argument');\n      args.push({\n        kind: 'Argument' as Kind.ARGUMENT,\n        name: _name,\n        value: _value,\n      });\n    }\n    if (!args.length || input.charCodeAt(idx++) !== 41 /*')'*/) throw error('Argument');\n    ignored();\n  }\n  return args;\n}\n\nfunction directives(constant: true): ast.ConstDirectiveNode[];\nfunction directives(constant: boolean): ast.DirectiveNode[];\n\nfunction directives(constant: boolean): ast.DirectiveNode[] {\n  const directives: ast.DirectiveNode[] = [];\n  ignored();\n  while (input.charCodeAt(idx) === 64 /*'@'*/) {\n    idx++;\n    const _name = name();\n    if (!_name) throw error('Directive');\n    ignored();\n    directives.push({\n      kind: 'Directive' as Kind.DIRECTIVE,\n      name: _name,\n      arguments: arguments_(constant),\n    });\n  }\n  return directives;\n}\n\nfunction field(): ast.FieldNode | undefined {\n  let _name = name();\n  if (_name) {\n    ignored();\n    let _alias: ast.NameNode | undefined;\n    if (input.charCodeAt(idx) === 58 /*':'*/) {\n      idx++;\n      ignored();\n      _alias = _name;\n      _name = name();\n      if (!_name) throw error('Field');\n      ignored();\n    }\n    return {\n      kind: 'Field' as Kind.FIELD,\n      alias: _alias,\n      name: _name,\n      arguments: arguments_(false),\n      directives: directives(false),\n      selectionSet: selectionSet(),\n    };\n  }\n}\n\nfunction type(): ast.TypeNode {\n  let match: ast.NameNode | ast.TypeNode | undefined;\n  ignored();\n  if (input.charCodeAt(idx) === 91 /*'['*/) {\n    idx++;\n    ignored();\n    const _type = type();\n    if (!_type || input.charCodeAt(idx++) !== 93 /*']'*/) throw error('ListType');\n    match = {\n      kind: 'ListType' as Kind.LIST_TYPE,\n      type: _type,\n    };\n  } else if ((match = name())) {\n    match = {\n      kind: 'NamedType' as Kind.NAMED_TYPE,\n      name: match,\n    };\n  } else {\n    throw error('NamedType');\n  }\n\n  ignored();\n  if (input.charCodeAt(idx) === 33 /*'!'*/) {\n    idx++;\n    ignored();\n    return {\n      kind: 'NonNullType' as Kind.NON_NULL_TYPE,\n      type: match,\n    };\n  } else {\n    return match;\n  }\n}\n\nconst typeConditionRe = /on/y;\nfunction typeCondition(): ast.NamedTypeNode | undefined {\n  if (advance(typeConditionRe)) {\n    ignored();\n    const _name = name();\n    if (!_name) throw error('NamedType');\n    ignored();\n    return {\n      kind: 'NamedType' as Kind.NAMED_TYPE,\n      name: _name,\n    };\n  }\n}\n\nconst fragmentSpreadRe = /\\.\\.\\./y;\n\nfunction fragmentSpread(): ast.FragmentSpreadNode | ast.InlineFragmentNode | undefined {\n  if (advance(fragmentSpreadRe)) {\n    ignored();\n    const _idx = idx;\n    let _name: ast.NameNode | undefined;\n    if ((_name = name()) && _name.value !== 'on') {\n      return {\n        kind: 'FragmentSpread' as Kind.FRAGMENT_SPREAD,\n        name: _name,\n        directives: directives(false),\n      };\n    } else {\n      idx = _idx;\n      const _typeCondition = typeCondition();\n      const _directives = directives(false);\n      const _selectionSet = selectionSet();\n      if (!_selectionSet) throw error('InlineFragment');\n      return {\n        kind: 'InlineFragment' as Kind.INLINE_FRAGMENT,\n        typeCondition: _typeCondition,\n        directives: _directives,\n        selectionSet: _selectionSet,\n      };\n    }\n  }\n}\n\nfunction selectionSet(): ast.SelectionSetNode | undefined {\n  let match: ast.SelectionNode | undefined;\n  ignored();\n  if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    idx++;\n    ignored();\n    const selections: ast.SelectionNode[] = [];\n    while ((match = fragmentSpread() || field())) selections.push(match);\n    if (!selections.length || input.charCodeAt(idx++) !== 125 /*'}'*/) throw error('SelectionSet');\n    ignored();\n    return {\n      kind: 'SelectionSet' as Kind.SELECTION_SET,\n      selections,\n    };\n  }\n}\n\nfunction variableDefinitions(): ast.VariableDefinitionNode[] {\n  let match: string | undefined;\n  const vars: ast.VariableDefinitionNode[] = [];\n  ignored();\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    idx++;\n    ignored();\n    while ((match = advance(variableRe))) {\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('VariableDefinition');\n      const _type = type();\n      let _defaultValue: ast.ValueNode | undefined;\n      if (input.charCodeAt(idx) === 61 /*'='*/) {\n        idx++;\n        ignored();\n        _defaultValue = value(true);\n        if (!_defaultValue) throw error('VariableDefinition');\n      }\n      ignored();\n      vars.push({\n        kind: 'VariableDefinition' as Kind.VARIABLE_DEFINITION,\n        variable: {\n          kind: 'Variable' as Kind.VARIABLE,\n          name: {\n            kind: 'Name' as Kind.NAME,\n            value: match.slice(1),\n          },\n        },\n        type: _type,\n        defaultValue: _defaultValue as ast.ConstValueNode,\n        directives: directives(true),\n      });\n    }\n    if (input.charCodeAt(idx++) !== 41 /*')'*/) throw error('VariableDefinition');\n    ignored();\n  }\n  return vars;\n}\n\nconst fragmentDefinitionRe = /fragment/y;\nfunction fragmentDefinition(): ast.FragmentDefinitionNode | undefined {\n  if (advance(fragmentDefinitionRe)) {\n    ignored();\n    const _name = name();\n    if (!_name) throw error('FragmentDefinition');\n    ignored();\n    const _typeCondition = typeCondition();\n    if (!_typeCondition) throw error('FragmentDefinition');\n    const _directives = directives(false);\n    const _selectionSet = selectionSet();\n    if (!_selectionSet) throw error('FragmentDefinition');\n    return {\n      kind: 'FragmentDefinition' as Kind.FRAGMENT_DEFINITION,\n      name: _name,\n      typeCondition: _typeCondition,\n      directives: _directives,\n      selectionSet: _selectionSet,\n    };\n  }\n}\n\n// NOTE(Safari10 Quirk): This *might* need to be wrapped in a group, but worked without it too\nconst operationDefinitionRe = /(?:query|mutation|subscription)/y;\n\nfunction operationDefinition(): ast.OperationDefinitionNode | undefined {\n  let _operation: string | undefined;\n  let _name: ast.NameNode | undefined;\n  let _variableDefinitions: ast.VariableDefinitionNode[] = [];\n  let _directives: ast.DirectiveNode[] = [];\n  if ((_operation = advance(operationDefinitionRe))) {\n    ignored();\n    _name = name();\n    _variableDefinitions = variableDefinitions();\n    _directives = directives(false);\n  }\n  const _selectionSet = selectionSet();\n  if (_selectionSet) {\n    return {\n      kind: 'OperationDefinition' as Kind.OPERATION_DEFINITION,\n      operation: (_operation || 'query') as OperationTypeNode,\n      name: _name,\n      variableDefinitions: _variableDefinitions,\n      directives: _directives,\n      selectionSet: _selectionSet,\n    };\n  }\n}\n\nfunction document(): ast.DocumentNode {\n  let match: ast.ExecutableDefinitionNode | void;\n  ignored();\n  const definitions: ast.ExecutableDefinitionNode[] = [];\n  while ((match = fragmentDefinition() || operationDefinition())) definitions.push(match);\n  return {\n    kind: 'Document' as Kind.DOCUMENT,\n    definitions,\n  };\n}\n\ntype ParseOptions = {\n  [option: string]: any;\n};\n\nexport function parse(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.DocumentNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return document();\n}\n\nexport function parseValue(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.ValueNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  ignored();\n  const _value = value(false);\n  if (!_value) throw error('ValueNode');\n  return _value;\n}\n\nexport function parseType(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.TypeNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return type();\n}\n", "import { ASTNode } from './ast';\n\nexport const BREAK = {};\n\nexport function visit<N extends ASTNode>(root: N, visitor: ASTVisitor): N;\nexport function visit<R>(root: ASTNode, visitor: ASTReducer<R>): R;\n\nexport function visit(node: ASTNode, visitor: ASTVisitor | ASTReducer<any>) {\n  const ancestors: Array<ASTNode | ReadonlyArray<ASTNode>> = [];\n  const path: Array<string | number> = [];\n\n  function traverse(\n    node: ASTNode,\n    key?: string | number | undefined,\n    parent?: ASTNode | ReadonlyArray<ASTNode> | undefined\n  ) {\n    let hasEdited = false;\n\n    const enter =\n      (visitor[node.kind] && visitor[node.kind].enter) ||\n      visitor[node.kind] ||\n      (visitor as EnterLeaveVisitor<ASTNode>).enter;\n    const resultEnter = enter && enter.call(visitor, node, key, parent, path, ancestors);\n    if (resultEnter === false) {\n      return node;\n    } else if (resultEnter === null) {\n      return null;\n    } else if (resultEnter === BREAK) {\n      throw BREAK;\n    } else if (resultEnter && typeof resultEnter.kind === 'string') {\n      hasEdited = resultEnter !== node;\n      node = resultEnter;\n    }\n\n    if (parent) ancestors.push(parent);\n\n    let result: any;\n    const copy = { ...node };\n    for (const nodeKey in node) {\n      path.push(nodeKey);\n      let value = node[nodeKey];\n      if (Array.isArray(value)) {\n        const newValue: any[] = [];\n        for (let index = 0; index < value.length; index++) {\n          if (value[index] != null && typeof value[index].kind === 'string') {\n            ancestors.push(node);\n            path.push(index);\n            result = traverse(value[index], index, value);\n            path.pop();\n            ancestors.pop();\n            if (result == null) {\n              hasEdited = true;\n            } else {\n              hasEdited = hasEdited || result !== value[index];\n              newValue.push(result);\n            }\n          }\n        }\n        value = newValue;\n      } else if (value != null && typeof value.kind === 'string') {\n        result = traverse(value, nodeKey, node);\n        if (result !== undefined) {\n          hasEdited = hasEdited || value !== result;\n          value = result;\n        }\n      }\n\n      path.pop();\n      if (hasEdited) copy[nodeKey] = value;\n    }\n\n    if (parent) ancestors.pop();\n    const leave =\n      (visitor[node.kind] && visitor[node.kind].leave) ||\n      (visitor as EnterLeaveVisitor<ASTNode>).leave;\n    const resultLeave = leave && leave.call(visitor, node, key, parent, path, ancestors);\n    if (resultLeave === BREAK) {\n      throw BREAK;\n    } else if (resultLeave !== undefined) {\n      return resultLeave;\n    } else if (resultEnter !== undefined) {\n      return hasEdited ? copy : resultEnter;\n    } else {\n      return hasEdited ? copy : node;\n    }\n  }\n\n  try {\n    const result = traverse(node);\n    return result !== undefined && result !== false ? result : node;\n  } catch (error) {\n    if (error !== BREAK) throw error;\n    return node;\n  }\n}\n\nexport type ASTVisitor = EnterLeaveVisitor<ASTNode> | KindVisitor;\n\ntype KindVisitor = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: ASTVisitFn<NodeT> | EnterLeaveVisitor<NodeT>;\n};\n\ninterface EnterLeaveVisitor<TVisitedNode extends ASTNode> {\n  readonly enter?: ASTVisitFn<TVisitedNode> | undefined;\n  readonly leave?: ASTVisitFn<TVisitedNode> | undefined;\n}\n\nexport type ASTVisitFn<Node extends ASTNode> = (\n  node: Node,\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => any;\n\nexport type ASTReducer<R> = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: {\n    readonly enter?: ASTVisitFn<NodeT>;\n    readonly leave: ASTReducerFn<NodeT, R>;\n  };\n};\n\ntype ASTReducerFn<TReducedNode extends ASTNode, R> = (\n  node: { [K in keyof TReducedNode]: ReducedField<TReducedNode[K], R> },\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => R;\n\ntype ReducedField<T, R> = T extends null | undefined\n  ? T\n  : T extends ReadonlyArray<any>\n  ? ReadonlyArray<R>\n  : R;\n", "import { ASTNode } from './ast';\n\nexport function printString(string: string) {\n  return JSON.stringify(string);\n}\n\nexport function printBlockString(string: string) {\n  return '\"\"\"\\n' + string.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nconst hasItems = <T>(array: ReadonlyArray<T> | undefined | null): array is ReadonlyArray<T> =>\n  !!(array && array.length);\n\nconst MAX_LINE_LENGTH = 80;\n\nconst nodes: {\n  [NodeT in ASTNode as NodeT['kind']]?: (node: NodeT) => string;\n} = {\n  OperationDefinition(node) {\n    if (\n      node.operation === 'query' &&\n      !node.name &&\n      !hasItems(node.variableDefinitions) &&\n      !hasItems(node.directives)\n    ) {\n      return nodes.SelectionSet!(node.selectionSet);\n    }\n    let out: string = node.operation;\n    if (node.name) out += ' ' + node.name.value;\n    if (hasItems(node.variableDefinitions)) {\n      if (!node.name) out += ' ';\n      out += '(' + node.variableDefinitions.map(nodes.VariableDefinition!).join(', ') + ')';\n    }\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return out + ' ' + nodes.SelectionSet!(node.selectionSet);\n  },\n  VariableDefinition(node) {\n    let out = nodes.Variable!(node.variable) + ': ' + print(node.type);\n    if (node.defaultValue) out += ' = ' + print(node.defaultValue);\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return out;\n  },\n  Field(node) {\n    let out = (node.alias ? node.alias.value + ': ' : '') + node.name.value;\n    if (hasItems(node.arguments)) {\n      const args = node.arguments.map(nodes.Argument!);\n      const argsLine = out + '(' + args.join(', ') + ')';\n      out =\n        argsLine.length > MAX_LINE_LENGTH\n          ? out + '(\\n  ' + args.join('\\n').replace(/\\n/g, '\\n  ') + '\\n)'\n          : argsLine;\n    }\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return node.selectionSet ? out + ' ' + nodes.SelectionSet!(node.selectionSet) : out;\n  },\n  StringValue(node) {\n    return node.block ? printBlockString(node.value) : printString(node.value);\n  },\n  BooleanValue(node) {\n    return '' + node.value;\n  },\n  NullValue(_node) {\n    return 'null';\n  },\n  IntValue(node) {\n    return node.value;\n  },\n  FloatValue(node) {\n    return node.value;\n  },\n  EnumValue(node) {\n    return node.value;\n  },\n  Name(node) {\n    return node.value;\n  },\n  Variable(node) {\n    return '$' + node.name.value;\n  },\n  ListValue(node) {\n    return '[' + node.values.map(print).join(', ') + ']';\n  },\n  ObjectValue(node) {\n    return '{' + node.fields.map(nodes.ObjectField!).join(', ') + '}';\n  },\n  ObjectField(node) {\n    return node.name.value + ': ' + print(node.value);\n  },\n  Document(node) {\n    return hasItems(node.definitions) ? node.definitions.map(print).join('\\n\\n') : '';\n  },\n  SelectionSet(node) {\n    return '{\\n  ' + node.selections.map(print).join('\\n').replace(/\\n/g, '\\n  ') + '\\n}';\n  },\n  Argument(node) {\n    return node.name.value + ': ' + print(node.value);\n  },\n  FragmentSpread(node) {\n    let out = '...' + node.name.value;\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return out;\n  },\n  InlineFragment(node) {\n    let out = '...';\n    if (node.typeCondition) out += ' on ' + node.typeCondition.name.value;\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return out + ' ' + print(node.selectionSet);\n  },\n  FragmentDefinition(node) {\n    let out = 'fragment ' + node.name.value;\n    out += ' on ' + node.typeCondition.name.value;\n    if (hasItems(node.directives)) out += ' ' + node.directives.map(nodes.Directive!).join(' ');\n    return out + ' ' + print(node.selectionSet);\n  },\n  Directive(node) {\n    let out = '@' + node.name.value;\n    if (hasItems(node.arguments)) out += '(' + node.arguments.map(nodes.Argument!).join(', ') + ')';\n    return out;\n  },\n  NamedType(node) {\n    return node.name.value;\n  },\n  ListType(node) {\n    return '[' + print(node.type) + ']';\n  },\n  NonNullType(node) {\n    return print(node.type) + '!';\n  },\n};\n\nexport function print(node: ASTNode): string {\n  return nodes[node.kind] ? (nodes as any)[node.kind]!(node) : '';\n}\n", "import { TypeNode, ValueNode } from './ast';\nimport { Maybe } from './types';\n\nexport function valueFromASTUntyped(\n  node: ValueNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  switch (node.kind) {\n    case 'NullValue':\n      return null;\n    case 'IntValue':\n      return parseInt(node.value, 10);\n    case 'FloatValue':\n      return parseFloat(node.value);\n    case 'StringValue':\n    case 'EnumValue':\n    case 'BooleanValue':\n      return node.value;\n    case 'ListValue': {\n      const values: unknown[] = [];\n      for (const value of node.values) values.push(valueFromASTUntyped(value, variables));\n      return values;\n    }\n    case 'ObjectValue': {\n      const obj = Object.create(null);\n      for (const field of node.fields)\n        obj[field.name.value] = valueFromASTUntyped(field.value, variables);\n      return obj;\n    }\n    case 'Variable':\n      return variables && variables[node.name.value];\n  }\n}\n\nexport function valueFromTypeNode(\n  node: ValueNode,\n  type: TypeNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  if (node.kind === 'Variable') {\n    const variableName = node.name.value;\n    return variables ? valueFromTypeNode(variables[variableName], type, variables) : undefined;\n  } else if (type.kind === 'NonNullType') {\n    return node.kind !== 'NullValue' ? valueFromTypeNode(node, type, variables) : undefined;\n  } else if (node.kind === 'NullValue') {\n    return null;\n  } else if (type.kind === 'ListType') {\n    if (node.kind === 'ListValue') {\n      const values: unknown[] = [];\n      for (const value of node.values) {\n        const coerced = valueFromTypeNode(value, type.type, variables);\n        if (coerced === undefined) {\n          return undefined;\n        } else {\n          values.push(coerced);\n        }\n      }\n      return values;\n    }\n  } else if (type.kind === 'NamedType') {\n    switch (type.name.value) {\n      case 'Int':\n      case 'Float':\n      case 'String':\n      case 'Bool':\n        return type.name.value + 'Value' === node.kind\n          ? valueFromASTUntyped(node, variables)\n          : undefined;\n      default:\n        return valueFromASTUntyped(node, variables);\n    }\n  }\n}\n", "var teardownPlaceholder = () => {};\n\nvar e = teardownPlaceholder;\n\nfunction start(e) {\n  return {\n    tag: 0,\n    0: e\n  };\n}\n\nfunction push(e) {\n  return {\n    tag: 1,\n    0: e\n  };\n}\n\nvar asyncIteratorSymbol = () => \"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\";\n\nvar observableSymbol = () => \"function\" == typeof Symbol && Symbol.observable || \"@@observable\";\n\nvar identity = e => e;\n\nfunction buffer(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        n(1);\n        if (a.length) {\n          i(push(a));\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        r((e => {\n          if (l) {} else if (0 === e) {\n            l = !0;\n            f(1);\n            if (a.length) {\n              i(push(a));\n            }\n            i(0);\n          } else if (0 === e.tag) {\n            n = e[0];\n          } else if (a.length) {\n            var r = push(a);\n            a = [];\n            i(r);\n          }\n        }));\n      } else {\n        a.push(e[0]);\n        if (!s) {\n          s = !0;\n          f(0);\n          n(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        f(1);\n        n(1);\n      } else if (!l && !s) {\n        s = !0;\n        f(0);\n        n(0);\n      }\n    })));\n  };\n}\n\nfunction concatMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = !1;\n    function applyInnerSource(e) {\n      u = !0;\n      e((e => {\n        if (0 === e) {\n          if (u) {\n            u = !1;\n            if (a.length) {\n              applyInnerSource(r(a.shift()));\n            } else if (o) {\n              i(0);\n            } else if (!s) {\n              s = !0;\n              f(0);\n            }\n          }\n        } else if (0 === e.tag) {\n          l = !1;\n          (n = e[0])(0);\n        } else if (u) {\n          i(e);\n          if (l) {\n            l = !1;\n          } else {\n            n(0);\n          }\n        }\n      }));\n    }\n    t((e => {\n      if (o) {} else if (0 === e) {\n        o = !0;\n        if (!u && !a.length) {\n          i(0);\n        }\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else {\n        s = !1;\n        if (u) {\n          a.push(e[0]);\n        } else {\n          applyInnerSource(r(e[0]));\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!o) {\n          o = !0;\n          f(1);\n        }\n        if (u) {\n          u = !1;\n          n(1);\n        }\n      } else {\n        if (!o && !s) {\n          s = !0;\n          f(0);\n        }\n        if (u && !l) {\n          l = !0;\n          n(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction concatAll(e) {\n  return concatMap(identity)(e);\n}\n\nfunction concat(e) {\n  return concatAll(r(e));\n}\n\nfunction filter(r) {\n  return t => i => {\n    var a = e;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (!r(e[0])) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction map(e) {\n  return r => t => r((r => {\n    if (0 === r || 0 === r.tag) {\n      t(r);\n    } else {\n      t(push(e(r[0])));\n    }\n  }));\n}\n\nfunction mergeMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = !1;\n    var s = !1;\n    t((t => {\n      if (s) {} else if (0 === t) {\n        s = !0;\n        if (!a.length) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        f = t[0];\n      } else {\n        n = !1;\n        !function applyInnerSource(r) {\n          var t = e;\n          r((e => {\n            if (0 === e) {\n              if (a.length) {\n                var r = a.indexOf(t);\n                if (r > -1) {\n                  (a = a.slice()).splice(r, 1);\n                }\n                if (!a.length) {\n                  if (s) {\n                    i(0);\n                  } else if (!n) {\n                    n = !0;\n                    f(0);\n                  }\n                }\n              }\n            } else if (0 === e.tag) {\n              a.push(t = e[0]);\n              t(0);\n            } else if (a.length) {\n              i(e);\n              t(0);\n            }\n          }));\n        }(r(t[0]));\n        if (!n) {\n          n = !0;\n          f(0);\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!s) {\n          s = !0;\n          f(1);\n        }\n        for (var r = 0, t = a, i = a.length; r < i; r++) {\n          t[r](1);\n        }\n        a.length = 0;\n      } else {\n        if (!s && !n) {\n          n = !0;\n          f(0);\n        } else {\n          n = !1;\n        }\n        for (var l = 0, u = a, o = a.length; l < o; l++) {\n          u[l](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction mergeAll(e) {\n  return mergeMap(identity)(e);\n}\n\nfunction merge(e) {\n  return mergeAll(r(e));\n}\n\nfunction onEnd(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n        e();\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((r => {\n          if (1 === r) {\n            i = !0;\n            a(1);\n            e();\n          } else {\n            a(r);\n          }\n        })));\n      } else {\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onPush(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((e => {\n          if (1 === e) {\n            i = !0;\n          }\n          a(e);\n        })));\n      } else {\n        e(r[0]);\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onStart(e) {\n  return r => t => r((r => {\n    if (0 === r) {\n      t(0);\n    } else if (0 === r.tag) {\n      t(r);\n      e();\n    } else {\n      t(r);\n    }\n  }));\n}\n\nfunction sample(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n      } else {\n        n = e[0];\n        if (!s) {\n          s = !0;\n          f(0);\n          a(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    r((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        a(1);\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else if (void 0 !== n) {\n        var r = push(n);\n        n = void 0;\n        i(r);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        f(1);\n      } else if (!l && !s) {\n        s = !0;\n        a(0);\n        f(0);\n      }\n    })));\n  };\n}\n\nfunction scan(e, r) {\n  return t => i => {\n    var a = r;\n    t((r => {\n      if (0 === r) {\n        i(0);\n      } else if (0 === r.tag) {\n        i(r);\n      } else {\n        i(push(a = e(a, r[0])));\n      }\n    }));\n  };\n}\n\nfunction share(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  return e => {\n    t.push(e);\n    if (1 === t.length) {\n      r((e => {\n        if (0 === e) {\n          for (var r = 0, f = t, n = t.length; r < n; r++) {\n            f[r](0);\n          }\n          t.length = 0;\n        } else if (0 === e.tag) {\n          i = e[0];\n        } else {\n          a = !1;\n          for (var s = 0, l = t, u = t.length; s < u; s++) {\n            l[s](e);\n          }\n        }\n      }));\n    }\n    e(start((r => {\n      if (1 === r) {\n        var f = t.indexOf(e);\n        if (f > -1) {\n          (t = t.slice()).splice(f, 1);\n        }\n        if (!t.length) {\n          i(1);\n        }\n      } else if (!a) {\n        a = !0;\n        i(0);\n      }\n    })));\n  };\n}\n\nfunction skip(r) {\n  return t => i => {\n    var a = e;\n    var f = r;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f-- > 0) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction skipUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !0;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        if (n) {\n          f(1);\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {\n            if (n) {\n              l = !0;\n              a(1);\n            }\n          } else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !1;\n            f(1);\n          }\n        }));\n      } else if (!n) {\n        s = !1;\n        i(e);\n      } else if (!s) {\n        s = !0;\n        a(0);\n        f(0);\n      } else {\n        s = !1;\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        if (n) {\n          f(1);\n        }\n      } else if (!l && !s) {\n        s = !0;\n        if (n) {\n          f(0);\n        }\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction skipWhile(r) {\n  return t => i => {\n    var a = e;\n    var f = !0;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f) {\n        if (r(e[0])) {\n          a(0);\n        } else {\n          f = !1;\n          i(e);\n        }\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction switchMap(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    t((t => {\n      if (u) {} else if (0 === t) {\n        u = !0;\n        if (!l) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        a = t[0];\n      } else {\n        if (l) {\n          f(1);\n          f = e;\n        }\n        if (!n) {\n          n = !0;\n          a(0);\n        } else {\n          n = !1;\n        }\n        !function applyInnerSource(e) {\n          l = !0;\n          e((e => {\n            if (!l) {} else if (0 === e) {\n              l = !1;\n              if (u) {\n                i(0);\n              } else if (!n) {\n                n = !0;\n                a(0);\n              }\n            } else if (0 === e.tag) {\n              s = !1;\n              (f = e[0])(0);\n            } else {\n              i(e);\n              if (!s) {\n                f(0);\n              } else {\n                s = !1;\n              }\n            }\n          }));\n        }(r(t[0]));\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!u) {\n          u = !0;\n          a(1);\n        }\n        if (l) {\n          l = !1;\n          f(1);\n        }\n      } else {\n        if (!u && !n) {\n          n = !0;\n          a(0);\n        }\n        if (l && !s) {\n          s = !0;\n          f(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction switchAll(e) {\n  return switchMap(identity)(e);\n}\n\nfunction take(r) {\n  return t => i => {\n    var a = e;\n    var f = !1;\n    var n = 0;\n    t((e => {\n      if (f) {} else if (0 === e) {\n        f = !0;\n        i(0);\n      } else if (0 === e.tag) {\n        if (r <= 0) {\n          f = !0;\n          i(0);\n          e[0](1);\n        } else {\n          a = e[0];\n        }\n      } else if (n++ < r) {\n        i(e);\n        if (!f && n >= r) {\n          f = !0;\n          i(0);\n          a(1);\n        }\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !f) {\n        f = !0;\n        a(1);\n      } else if (0 === e && !f && n < r) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeLast(t) {\n  return i => a => {\n    var f = [];\n    var n = e;\n    i((e => {\n      if (0 === e) {\n        r(f)(a);\n      } else if (0 === e.tag) {\n        if (t <= 0) {\n          e[0](1);\n          r(f)(a);\n        } else {\n          (n = e[0])(0);\n        }\n      } else {\n        if (f.length >= t && t) {\n          f.shift();\n        }\n        f.push(e[0]);\n        n(0);\n      }\n    }));\n  };\n}\n\nfunction takeUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    t((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {} else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !0;\n            f(1);\n            a(1);\n            i(0);\n          }\n        }));\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !n) {\n        n = !0;\n        a(1);\n        f(1);\n      } else if (!n) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeWhile(r, t) {\n  return i => a => {\n    var f = e;\n    var n = !1;\n    i((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        a(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        a(e);\n      } else if (!r(e[0])) {\n        n = !0;\n        if (t) {\n          a(e);\n        }\n        a(0);\n        f(1);\n      } else {\n        a(e);\n      }\n    }));\n  };\n}\n\nfunction debounce(e) {\n  return r => t => {\n    var i;\n    var a = !1;\n    var f = !1;\n    r((r => {\n      if (f) {} else if (0 === r) {\n        f = !0;\n        if (i) {\n          a = !0;\n        } else {\n          t(0);\n        }\n      } else if (0 === r.tag) {\n        var n = r[0];\n        t(start((e => {\n          if (1 === e && !f) {\n            f = !0;\n            a = !1;\n            if (i) {\n              clearTimeout(i);\n            }\n            n(1);\n          } else if (!f) {\n            n(0);\n          }\n        })));\n      } else {\n        if (i) {\n          clearTimeout(i);\n        }\n        i = setTimeout((() => {\n          i = void 0;\n          t(r);\n          if (a) {\n            t(0);\n          }\n        }), e(r[0]));\n      }\n    }));\n  };\n}\n\nfunction delay(e) {\n  return r => t => {\n    var i = 0;\n    r((r => {\n      if (0 !== r && 0 === r.tag) {\n        t(r);\n      } else {\n        i++;\n        setTimeout((() => {\n          if (i) {\n            i--;\n            t(r);\n          }\n        }), e);\n      }\n    }));\n  };\n}\n\nfunction throttle(e) {\n  return r => t => {\n    var i = !1;\n    var a;\n    r((r => {\n      if (0 === r) {\n        if (a) {\n          clearTimeout(a);\n        }\n        t(0);\n      } else if (0 === r.tag) {\n        var f = r[0];\n        t(start((e => {\n          if (1 === e) {\n            if (a) {\n              clearTimeout(a);\n            }\n            f(1);\n          } else {\n            f(0);\n          }\n        })));\n      } else if (!i) {\n        i = !0;\n        if (a) {\n          clearTimeout(a);\n        }\n        a = setTimeout((() => {\n          a = void 0;\n          i = !1;\n        }), e(r[0]));\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction lazy(e) {\n  return r => e()(r);\n}\n\nfunction fromAsyncIterable(e) {\n  return r => {\n    var t = e[asyncIteratorSymbol()] && e[asyncIteratorSymbol()]() || e;\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((async e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = await t.next()).done) {\n            i = !0;\n            if (t.return) {\n              await t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!(await t.throw(e)).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nfunction fromIterable(e) {\n  if (e[Symbol.asyncIterator]) {\n    return fromAsyncIterable(e);\n  }\n  return r => {\n    var t = e[Symbol.iterator]();\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = t.next()).done) {\n            i = !0;\n            if (t.return) {\n              t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!t.throw(e).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nvar r = fromIterable;\n\nfunction fromValue(e) {\n  return r => {\n    var t = !1;\n    r(start((i => {\n      if (1 === i) {\n        t = !0;\n      } else if (!t) {\n        t = !0;\n        r(push(e));\n        r(0);\n      }\n    })));\n  };\n}\n\nfunction make(e) {\n  return r => {\n    var t = !1;\n    var i = e({\n      next(e) {\n        if (!t) {\n          r(push(e));\n        }\n      },\n      complete() {\n        if (!t) {\n          t = !0;\n          r(0);\n        }\n      }\n    });\n    r(start((e => {\n      if (1 === e && !t) {\n        t = !0;\n        i();\n      }\n    })));\n  };\n}\n\nfunction makeSubject() {\n  var e;\n  var r;\n  return {\n    source: share(make((t => {\n      e = t.next;\n      r = t.complete;\n      return teardownPlaceholder;\n    }))),\n    next(r) {\n      if (e) {\n        e(r);\n      }\n    },\n    complete() {\n      if (r) {\n        r();\n      }\n    }\n  };\n}\n\nvar empty = e => {\n  var r = !1;\n  e(start((t => {\n    if (1 === t) {\n      r = !0;\n    } else if (!r) {\n      r = !0;\n      e(0);\n    }\n  })));\n};\n\nvar never = r => {\n  r(start(e));\n};\n\nfunction interval(e) {\n  return make((r => {\n    var t = 0;\n    var i = setInterval((() => r.next(t++)), e);\n    return () => clearInterval(i);\n  }));\n}\n\nfunction fromDomEvent(e, r) {\n  return make((t => {\n    e.addEventListener(r, t.next);\n    return () => e.removeEventListener(r, t.next);\n  }));\n}\n\nfunction fromPromise(e) {\n  return make((r => {\n    e.then((e => {\n      Promise.resolve(e).then((() => {\n        r.next(e);\n        r.complete();\n      }));\n    }));\n    return teardownPlaceholder;\n  }));\n}\n\nfunction subscribe(r) {\n  return t => {\n    var i = e;\n    var a = !1;\n    t((e => {\n      if (0 === e) {\n        a = !0;\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else if (!a) {\n        r(e[0]);\n        i(0);\n      }\n    }));\n    return {\n      unsubscribe() {\n        if (!a) {\n          a = !0;\n          i(1);\n        }\n      }\n    };\n  };\n}\n\nfunction forEach(e) {\n  return r => {\n    subscribe(e)(r);\n  };\n}\n\nfunction publish(e) {\n  subscribe((e => {}))(e);\n}\n\nvar t = {\n  done: !0\n};\n\nvar toAsyncIterable = r => {\n  var i = [];\n  var a = !1;\n  var f = !1;\n  var n = !1;\n  var s = e;\n  var l;\n  return {\n    async next() {\n      if (!f) {\n        f = !0;\n        r((e => {\n          if (a) {} else if (0 === e) {\n            if (l) {\n              l = l(t);\n            }\n            a = !0;\n          } else if (0 === e.tag) {\n            n = !0;\n            (s = e[0])(0);\n          } else {\n            n = !1;\n            if (l) {\n              l = l({\n                value: e[0],\n                done: !1\n              });\n            } else {\n              i.push(e[0]);\n            }\n          }\n        }));\n      }\n      if (a && !i.length) {\n        return t;\n      } else if (!a && !n && i.length <= 1) {\n        n = !0;\n        s(0);\n      }\n      return i.length ? {\n        value: i.shift(),\n        done: !1\n      } : new Promise((e => l = e));\n    },\n    async return() {\n      if (!a) {\n        l = s(1);\n      }\n      a = !0;\n      return t;\n    },\n    [asyncIteratorSymbol()]() {\n      return this;\n    }\n  };\n};\n\nfunction toArray(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  r((e => {\n    if (0 === e) {\n      a = !0;\n    } else if (0 === e.tag) {\n      (i = e[0])(0);\n    } else {\n      t.push(e[0]);\n      i(0);\n    }\n  }));\n  if (!a) {\n    i(1);\n  }\n  return t;\n}\n\nfunction toPromise(r) {\n  return new Promise((t => {\n    var i = e;\n    var a;\n    r((e => {\n      if (0 === e) {\n        Promise.resolve(a).then(t);\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else {\n        a = e[0];\n        i(0);\n      }\n    }));\n  }));\n}\n\nfunction zip(r) {\n  var t = Object.keys(r).length;\n  return i => {\n    var a = new Set;\n    var f = Array.isArray(r) ? new Array(t).fill(e) : {};\n    var n = Array.isArray(r) ? new Array(t) : {};\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = 0;\n    var loop = function(v) {\n      r[v]((c => {\n        if (0 === c) {\n          if (o >= t - 1) {\n            u = !0;\n            i(0);\n          } else {\n            o++;\n          }\n        } else if (0 === c.tag) {\n          f[v] = c[0];\n        } else if (!u) {\n          n[v] = c[0];\n          a.add(v);\n          if (!s && a.size < t) {\n            if (!l) {\n              for (var h in r) {\n                if (!a.has(h)) {\n                  (f[h] || e)(0);\n                }\n              }\n            } else {\n              l = !1;\n            }\n          } else {\n            s = !0;\n            l = !1;\n            i(push(Array.isArray(n) ? n.slice() : {\n              ...n\n            }));\n          }\n        }\n      }));\n    };\n    for (var v in r) {\n      loop(v);\n    }\n    i(start((e => {\n      if (u) {} else if (1 === e) {\n        u = !0;\n        for (var r in f) {\n          f[r](1);\n        }\n      } else if (!l) {\n        l = !0;\n        for (var t in f) {\n          f[t](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction combine(...e) {\n  return zip(e);\n}\n\nfunction fromObservable(e) {\n  return r => {\n    var t = (e[observableSymbol()] ? e[observableSymbol()]() : e).subscribe({\n      next(e) {\n        r(push(e));\n      },\n      complete() {\n        r(0);\n      },\n      error(e) {\n        throw e;\n      }\n    });\n    r(start((e => {\n      if (1 === e) {\n        t.unsubscribe();\n      }\n    })));\n  };\n}\n\nfunction toObservable(r) {\n  return {\n    subscribe(t, i, a) {\n      var f = \"object\" == typeof t ? t : {\n        next: t,\n        error: i,\n        complete: a\n      };\n      var n = e;\n      var s = !1;\n      r((e => {\n        if (s) {} else if (0 === e) {\n          s = !0;\n          if (f.complete) {\n            f.complete();\n          }\n        } else if (0 === e.tag) {\n          (n = e[0])(0);\n        } else {\n          f.next(e[0]);\n          n(0);\n        }\n      }));\n      var l = {\n        closed: !1,\n        unsubscribe() {\n          l.closed = !0;\n          s = !0;\n          n(1);\n        }\n      };\n      return l;\n    },\n    [observableSymbol()]() {\n      return this;\n    }\n  };\n}\n\nfunction fromCallbag(e) {\n  return r => {\n    e(0, ((e, t) => {\n      if (0 === e) {\n        r(start((e => {\n          t(e + 1);\n        })));\n      } else if (1 === e) {\n        r(push(t));\n      } else {\n        r(0);\n      }\n    }));\n  };\n}\n\nfunction toCallbag(e) {\n  return (r, t) => {\n    if (0 === r) {\n      e((e => {\n        if (0 === e) {\n          t(2);\n        } else if (0 === e.tag) {\n          t(0, (r => {\n            if (r < 3) {\n              e[0](r - 1);\n            }\n          }));\n        } else {\n          t(1, e[0]);\n        }\n      }));\n    }\n  };\n}\n\nvar pipe = (...e) => {\n  var r = e[0];\n  for (var t = 1, i = e.length; t < i; t++) {\n    r = e[t](r);\n  }\n  return r;\n};\n\nexport { buffer, combine, concat, concatAll, concatMap, debounce, delay, empty, filter, mergeAll as flatten, forEach, r as fromArray, fromAsyncIterable, fromCallbag, fromDomEvent, fromIterable, fromObservable, fromPromise, fromValue, interval, lazy, make, makeSubject, map, merge, mergeAll, mergeMap, never, onEnd, onPush, onStart, pipe, publish, sample, scan, share, skip, skipUntil, skipWhile, subscribe, switchAll, switchMap, take, takeLast, takeUntil, takeWhile, onPush as tap, throttle, toArray, toAsyncIterable, toCallbag, toObservable, toPromise, zip };\n//# sourceMappingURL=wonka.mjs.map\n", "import { GraphQLError } from '@0no-co/graphql.web';\nimport type { ErrorLike } from '../types';\n\nconst generateErrorMessage = (\n  networkErr?: Error,\n  graphQlErrs?: GraphQLError[]\n) => {\n  let error = '';\n  if (networkErr) return `[Network] ${networkErr.message}`;\n  if (graphQlErrs) {\n    for (const err of graphQlErrs) {\n      if (error) error += '\\n';\n      error += `[GraphQL] ${err.message}`;\n    }\n  }\n  return error;\n};\n\nconst rehydrateGraphQlError = (error: any): GraphQLError => {\n  if (\n    error &&\n    error.message &&\n    (error.extensions || error.name === 'GraphQLError')\n  ) {\n    return error;\n  } else if (typeof error === 'object' && error.message) {\n    return new GraphQLError(\n      error.message,\n      error.nodes,\n      error.source,\n      error.positions,\n      error.path,\n      error,\n      error.extensions || {}\n    );\n  } else {\n    return new GraphQLError(error as any);\n  }\n};\n\n/** An abstracted `Error` that provides either a `networkError` or `graphQLErrors`.\n *\n * @remarks\n * During a GraphQL request, either the request can fail entirely, causing a network error,\n * or the GraphQL execution or fields can fail, which will cause an {@link ExecutionResult}\n * to contain an array of GraphQL errors.\n *\n * The `CombinedError` abstracts and normalizes both failure cases. When {@link OperationResult.error}\n * is set to this error, the `CombinedError` abstracts all errors, making it easier to handle only\n * a subset of error cases.\n *\n * @see {@link https://urql.dev/goto/docs/basics/errors} for more information on handling\n * GraphQL errors and the `CombinedError`.\n */\nexport class CombinedError extends Error {\n  public name: string;\n  public message: string;\n\n  /** A list of GraphQL errors rehydrated from a {@link ExecutionResult}.\n   *\n   * @remarks\n   * If an {@link ExecutionResult} received from the API contains a list of errors,\n   * the `CombinedError` will rehydrate them, normalize them to\n   * {@link GraphQLError | GraphQLErrors} and list them here.\n   * An empty list indicates that no GraphQL error has been sent by the API.\n   */\n  public graphQLErrors: GraphQLError[];\n\n  /** Set to an error, if a GraphQL request has failed outright.\n   *\n   * @remarks\n   * A GraphQL over HTTP request may fail and not reach the API. Any error that\n   * prevents a GraphQl request outright, will be considered a “network error” and\n   * set here.\n   */\n  public networkError?: Error;\n\n  /** Set to the {@link Response} object a fetch exchange received.\n   *\n   * @remarks\n   * If a built-in fetch {@link Exchange} is used in `urql`, this may\n   * be set to the {@link Response} object of the Fetch API response.\n   * However, since `urql` doesn’t assume that all users will use HTTP\n   * as the only or exclusive transport for GraphQL this property is\n   * neither typed nor guaranteed and may be re-used for other purposes\n   * by non-fetch exchanges.\n   *\n   * Hint: It can be useful to use `response.status` here, however, if\n   * you plan on relying on this being a {@link Response} in your app,\n   * which it is by default, then make sure you add some extra checks\n   * before blindly assuming so!\n   */\n  public response?: any;\n\n  constructor(input: {\n    networkError?: Error;\n    graphQLErrors?: Array<string | ErrorLike>;\n    response?: any;\n  }) {\n    const normalizedGraphQLErrors = (input.graphQLErrors || []).map(\n      rehydrateGraphQlError\n    );\n    const message = generateErrorMessage(\n      input.networkError,\n      normalizedGraphQLErrors\n    );\n\n    super(message);\n\n    this.name = 'CombinedError';\n    this.message = message;\n    this.graphQLErrors = normalizedGraphQLErrors;\n    this.networkError = input.networkError;\n    this.response = input.response;\n  }\n\n  toString() {\n    return this.message;\n  }\n}\n", "/** A hash value as computed by {@link phash}.\n *\n * @remarks\n * Typically `HashValue`s are used as hashes and keys of GraphQL documents,\n * variables, and combined, for GraphQL requests.\n */\nexport type HashValue = number & {\n  /** Marker to indicate that a `HashValue` may not be created by a user.\n   *\n   * @remarks\n   * `HashValue`s are created by {@link phash} and are marked as such to not mix them\n   * up with other numbers and prevent them from being created or used outside of this\n   * hashing function.\n   *\n   * @internal\n   */\n  readonly _opaque: unique symbol;\n};\n\n/** Computes a djb2 hash of the given string.\n *\n * @param x - the string to be hashed\n * @param seed - optionally a prior hash for progressive hashing\n * @returns a hash value, i.e. a number\n *\n * @remark\n * This is the hashing function used throughout `urql`, primarily to compute\n * {@link Operation.key}.\n *\n * @see {@link http://www.cse.yorku.ca/~oz/hash.html#djb2} for a further description of djb2.\n */\nexport const phash = (x: string, seed?: HashValue): HashValue => {\n  let h = (seed || 5381) | 0;\n  for (let i = 0, l = x.length | 0; i < l; i++)\n    h = (h << 5) + h + x.charCodeAt(i);\n  return h as HashValue;\n};\n", "export type FileMap = Map<string, File | Blob>;\n\nconst seen = new Set();\nconst cache = new WeakMap();\n\nconst stringify = (x: any): string => {\n  if (x === null || seen.has(x)) {\n    return 'null';\n  } else if (typeof x !== 'object') {\n    return JSON.stringify(x) || '';\n  } else if (x.toJSON) {\n    return stringify(x.toJSON());\n  } else if (Array.isArray(x)) {\n    let out = '[';\n    for (const value of x) {\n      if (out.length > 1) out += ',';\n      out += stringify(value) || 'null';\n    }\n    out += ']';\n    return out;\n  } else if (\n    (FileConstructor !== NoopConstructor && x instanceof FileConstructor) ||\n    (BlobConstructor !== NoopConstructor && x instanceof BlobConstructor)\n  ) {\n    return 'null';\n  }\n\n  const keys = Object.keys(x).sort();\n  if (\n    !keys.length &&\n    x.constructor &&\n    Object.getPrototypeOf(x).constructor !== Object.prototype.constructor\n  ) {\n    const key = cache.get(x) || Math.random().toString(36).slice(2);\n    cache.set(x, key);\n    return stringify({ __key: key });\n  }\n\n  seen.add(x);\n  let out = '{';\n  for (const key of keys) {\n    const value = stringify(x[key]);\n    if (value) {\n      if (out.length > 1) out += ',';\n      out += stringify(key) + ':' + value;\n    }\n  }\n\n  seen.delete(x);\n  out += '}';\n  return out;\n};\n\nconst extract = (map: FileMap, path: string, x: any) => {\n  if (x == null || typeof x !== 'object' || x.toJSON || seen.has(x)) {\n    /*noop*/\n  } else if (Array.isArray(x)) {\n    for (let i = 0, l = x.length; i < l; i++)\n      extract(map, `${path}.${i}`, x[i]);\n  } else if (x instanceof FileConstructor || x instanceof BlobConstructor) {\n    map.set(path, x as File | Blob);\n  } else {\n    seen.add(x);\n    for (const key of Object.keys(x)) extract(map, `${path}.${key}`, x[key]);\n  }\n};\n\n/** A stable stringifier for GraphQL variables objects.\n *\n * @param x - any JSON-like data.\n * @return A JSON string.\n *\n * @remarks\n * This utility creates a stable JSON string from any passed data,\n * and protects itself from throwing.\n *\n * The JSON string is stable insofar as objects’ keys are sorted,\n * and instances of non-plain objects are replaced with random keys\n * replacing their values, which remain stable for the objects’\n * instance.\n */\nexport const stringifyVariables = (x: any): string => {\n  seen.clear();\n  return stringify(x);\n};\n\nclass NoopConstructor {}\nconst FileConstructor = typeof File !== 'undefined' ? File : NoopConstructor;\nconst BlobConstructor = typeof Blob !== 'undefined' ? Blob : NoopConstructor;\n\nexport const extractFiles = (x: any): FileMap => {\n  const map: FileMap = new Map();\n  if (\n    FileConstructor !== NoopConstructor ||\n    BlobConstructor !== NoopConstructor\n  ) {\n    seen.clear();\n    extract(map, 'variables', x);\n  }\n  return map;\n};\n", "import { Kind, parse, print } from '@0no-co/graphql.web';\nimport type { DocumentNode, DefinitionNode } from './graphql';\nimport type { HashValue } from './hash';\nimport { phash } from './hash';\nimport { stringifyVariables } from './variables';\n\nimport type {\n  DocumentInput,\n  TypedDocumentNode,\n  AnyVariables,\n  GraphQLRequest,\n  RequestExtensions,\n} from '../types';\n\n/** A `DocumentNode` annotated with its hashed key.\n * @internal\n */\nexport type KeyedDocumentNode = TypedDocumentNode & {\n  __key: HashValue;\n};\n\nconst SOURCE_NAME = 'gql';\nconst GRAPHQL_STRING_RE = /(\"{3}[\\s\\S]*\"{3}|\"(?:\\\\.|[^\"])*\")/g;\nconst REPLACE_CHAR_RE = /(?:#[^\\n\\r]+)?(?:[\\r\\n]+|$)/g;\n\nconst replaceOutsideStrings = (str: string, idx: number) =>\n  idx % 2 === 0 ? str.replace(REPLACE_CHAR_RE, '\\n') : str;\n\n/** Sanitizes a GraphQL document string by replacing comments and redundant newlines in it. */\nconst sanitizeDocument = (node: string): string =>\n  node.split(GRAPHQL_STRING_RE).map(replaceOutsideStrings).join('').trim();\n\nconst prints = new Map<DocumentNode | DefinitionNode, string>();\nconst docs = new Map<HashValue, KeyedDocumentNode>();\n\n/** A cached printing function for GraphQL documents.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A normalized printed string of the passed GraphQL document.\n *\n * @remarks\n * This function accepts a GraphQL query string or {@link DocumentNode},\n * then prints and sanitizes it. The sanitizer takes care of removing\n * comments, which otherwise alter the key of the document although the\n * document is otherwise equivalent to another.\n *\n * When a {@link DocumentNode} is passed to this function, it caches its\n * output by modifying the `loc.source.body` property on the GraphQL node.\n */\nexport const stringifyDocument = (\n  node: string | DefinitionNode | DocumentNode\n): string => {\n  let printed: string;\n  if (typeof node === 'string') {\n    printed = sanitizeDocument(node);\n  } else if (node.loc && docs.get((node as KeyedDocumentNode).__key) === node) {\n    printed = node.loc.source.body;\n  } else {\n    printed = prints.get(node) || sanitizeDocument(print(node));\n    prints.set(node, printed);\n  }\n\n  if (typeof node !== 'string' && !node.loc) {\n    (node as any).loc = {\n      start: 0,\n      end: printed.length,\n      source: {\n        body: printed,\n        name: SOURCE_NAME,\n        locationOffset: { line: 1, column: 1 },\n      },\n    };\n  }\n\n  return printed;\n};\n\n/** Computes the hash for a document's string using {@link stringifyDocument}'s output.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A {@link HashValue}\n *\n * @privateRemarks\n * This function adds the operation name of the document to the hash, since sometimes\n * a merged document with multiple operations may be used. Although `urql` requires a\n * `DocumentNode` to only contain a single operation, when the cached `loc.source.body`\n * of a `DocumentNode` is used, this string may still contain multiple operations and\n * the resulting hash should account for only one at a time.\n */\nconst hashDocument = (\n  node: string | DefinitionNode | DocumentNode\n): HashValue => {\n  let key = phash(stringifyDocument(node));\n  // Add the operation name to the produced hash\n  if ((node as DocumentNode).definitions) {\n    const operationName = getOperationName(node as DocumentNode);\n    if (operationName) key = phash(`\\n# ${operationName}`, key);\n  }\n  return key;\n};\n\n/** Returns a canonical version of the passed `DocumentNode` with an added hash key.\n *\n * @param node - A string of a document or a {@link DocumentNode}\n * @returns A {@link KeyedDocumentNode}\n *\n * @remarks\n * `urql` will always avoid unnecessary work, no matter whether a user passes `DocumentNode`s\n * or strings of GraphQL documents to its APIs.\n *\n * This function will return a canonical version of a {@link KeyedDocumentNode} no matter\n * which kind of input is passed, avoiding parsing or hashing of passed data as needed.\n */\nexport const keyDocument = (node: string | DocumentNode): KeyedDocumentNode => {\n  let key: HashValue;\n  let query: DocumentNode;\n  if (typeof node === 'string') {\n    key = hashDocument(node);\n    query = docs.get(key) || parse(node, { noLocation: true });\n  } else {\n    key = (node as KeyedDocumentNode).__key || hashDocument(node);\n    query = docs.get(key) || node;\n  }\n\n  // Add location information if it's missing\n  if (!query.loc) stringifyDocument(query);\n\n  (query as KeyedDocumentNode).__key = key;\n  docs.set(key, query as KeyedDocumentNode);\n  return query as KeyedDocumentNode;\n};\n\n/** Creates a `GraphQLRequest` from the passed parameters.\n *\n * @param q - A string of a document or a {@link DocumentNode}\n * @param variables - A variables object for the defined GraphQL operation.\n * @returns A {@link GraphQLRequest}\n *\n * @remarks\n * `createRequest` creates a {@link GraphQLRequest} from the passed parameters,\n * while replacing the document as needed with a canonical version of itself,\n * to avoid parsing, printing, or hashing the same input multiple times.\n *\n * If no variables are passed, canonically it'll default to an empty object,\n * which is removed from the resulting hash key.\n */\nexport const createRequest = <\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  _query: DocumentInput<Data, Variables>,\n  _variables: Variables,\n  extensions?: RequestExtensions | undefined\n): GraphQLRequest<Data, Variables> => {\n  const variables = _variables || ({} as Variables);\n  const query = keyDocument(_query);\n  const printedVars = stringifyVariables(variables);\n  let key = query.__key;\n  if (printedVars !== '{}') key = phash(printedVars, key);\n  return { key, query, variables, extensions };\n};\n\n/** Returns the name of the `DocumentNode`'s operation, if any.\n * @param query - A {@link DocumentNode}\n * @returns the operation's name contained within the document, or `undefined`\n */\nexport const getOperationName = (query: DocumentNode): string | undefined => {\n  for (const node of query.definitions) {\n    if (node.kind === Kind.OPERATION_DEFINITION) {\n      return node.name ? node.name.value : undefined;\n    }\n  }\n};\n\n/** Returns the type of the `DocumentNode`'s operation, if any.\n * @param query - A {@link DocumentNode}\n * @returns the operation's type contained within the document, or `undefined`\n */\nexport const getOperationType = (query: DocumentNode): string | undefined => {\n  for (const node of query.definitions) {\n    if (node.kind === Kind.OPERATION_DEFINITION) {\n      return node.operation;\n    }\n  }\n};\n", "import type {\n  ExecutionResult,\n  Operation,\n  OperationResult,\n  IncrementalPayload,\n} from '../types';\nimport { CombinedError } from './error';\n\n/** Converts the `ExecutionResult` received for a given `Operation` to an `OperationResult`.\n *\n * @param operation - The {@link Operation} for which the API’s result is for.\n * @param result - The GraphQL API’s {@link ExecutionResult}.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns An {@link OperationResult}.\n *\n * @remarks\n * This utility can be used to create {@link OperationResult | OperationResults} in the shape\n * that `urql` expects and defines, and should be used rather than creating the results manually.\n *\n * @throws\n * If no data, or errors are contained within the result, or the result is instead an incremental\n * response containing a `path` property, a “No Content” error is thrown.\n *\n * @see {@link ExecutionResult} for the type definition of GraphQL API results.\n */\nexport const makeResult = (\n  operation: Operation,\n  result: ExecutionResult,\n  response?: any\n): OperationResult => {\n  if (\n    !('data' in result) &&\n    (!('errors' in result) || !Array.isArray(result.errors))\n  ) {\n    throw new Error('No Content');\n  }\n\n  const defaultHasNext = operation.kind === 'subscription';\n  return {\n    operation,\n    data: result.data,\n    error: Array.isArray(result.errors)\n      ? new CombinedError({\n          graphQLErrors: result.errors,\n          response,\n        })\n      : undefined,\n    extensions: result.extensions ? { ...result.extensions } : undefined,\n    hasNext: result.hasNext == null ? defaultHasNext : result.hasNext,\n    stale: false,\n  };\n};\n\nconst deepMerge = (target: any, source: any) => {\n  if (typeof target === 'object' && target != null) {\n    if (\n      !target.constructor ||\n      target.constructor === Object ||\n      Array.isArray(target)\n    ) {\n      target = Array.isArray(target) ? [...target] : { ...target };\n      for (const key of Object.keys(source))\n        target[key] = deepMerge(target[key], source[key]);\n      return target;\n    }\n  }\n  return source;\n};\n\n/** Merges an incrementally delivered `ExecutionResult` into a previous `OperationResult`.\n *\n * @param prevResult - The {@link OperationResult} that preceded this result.\n * @param path - The GraphQL API’s {@link ExecutionResult} that should be patching the `prevResult`.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns A new {@link OperationResult} patched with the incremental result.\n *\n * @remarks\n * This utility should be used to merge subsequent {@link ExecutionResult | ExecutionResults} of\n * incremental responses into a prior {@link OperationResult}.\n *\n * When directives like `@defer`, `@stream`, and `@live` are used, GraphQL may deliver new\n * results that modify previous results. In these cases, it'll set a `path` property to modify\n * the result it sent last. This utility is built to handle these cases and merge these payloads\n * into existing {@link OperationResult | OperationResults}.\n *\n * @see {@link ExecutionResult} for the type definition of GraphQL API results.\n */\nexport const mergeResultPatch = (\n  prevResult: OperationResult,\n  nextResult: ExecutionResult,\n  response?: any,\n  pending?: ExecutionResult['pending']\n): OperationResult => {\n  let errors = prevResult.error ? prevResult.error.graphQLErrors : [];\n  let hasExtensions =\n    !!prevResult.extensions || !!(nextResult.payload || nextResult).extensions;\n  const extensions = {\n    ...prevResult.extensions,\n    ...(nextResult.payload || nextResult).extensions,\n  };\n\n  let incremental = nextResult.incremental;\n\n  // NOTE: We handle the old version of the incremental delivery payloads as well\n  if ('path' in nextResult) {\n    incremental = [nextResult as IncrementalPayload];\n  }\n\n  const withData = { data: prevResult.data };\n  if (incremental) {\n    for (const patch of incremental) {\n      if (Array.isArray(patch.errors)) {\n        errors.push(...(patch.errors as any));\n      }\n\n      if (patch.extensions) {\n        Object.assign(extensions, patch.extensions);\n        hasExtensions = true;\n      }\n\n      let prop: string | number = 'data';\n      let part: Record<string, any> | Array<any> = withData;\n      let path: readonly (string | number)[] = [];\n      if (patch.path) {\n        path = patch.path;\n      } else if (pending) {\n        const res = pending.find(pendingRes => pendingRes.id === patch.id);\n        if (patch.subPath) {\n          path = [...res!.path, ...patch.subPath];\n        } else {\n          path = res!.path;\n        }\n      }\n\n      for (let i = 0, l = path.length; i < l; prop = path[i++]) {\n        part = part[prop] = Array.isArray(part[prop])\n          ? [...part[prop]]\n          : { ...part[prop] };\n      }\n\n      if (patch.items) {\n        const startIndex = +prop >= 0 ? (prop as number) : 0;\n        for (let i = 0, l = patch.items.length; i < l; i++)\n          part[startIndex + i] = deepMerge(\n            part[startIndex + i],\n            patch.items[i]\n          );\n      } else if (patch.data !== undefined) {\n        part[prop] = deepMerge(part[prop], patch.data);\n      }\n    }\n  } else {\n    withData.data = (nextResult.payload || nextResult).data || prevResult.data;\n    errors =\n      (nextResult.errors as any[]) ||\n      (nextResult.payload && nextResult.payload.errors) ||\n      errors;\n  }\n\n  return {\n    operation: prevResult.operation,\n    data: withData.data,\n    error: errors.length\n      ? new CombinedError({ graphQLErrors: errors, response })\n      : undefined,\n    extensions: hasExtensions ? extensions : undefined,\n    hasNext:\n      nextResult.hasNext != null ? nextResult.hasNext : prevResult.hasNext,\n    stale: false,\n  };\n};\n\n/** Creates an `OperationResult` containing a network error for requests that encountered unexpected errors.\n *\n * @param operation - The {@link Operation} for which the API’s result is for.\n * @param error - The network-like error that prevented an API result from being delivered.\n * @param response - Optionally, a raw object representing the API’s result (Typically a {@link Response}).\n * @returns An {@link OperationResult} containing only a {@link CombinedError}.\n *\n * @remarks\n * This utility can be used to create {@link OperationResult | OperationResults} in the shape\n * that `urql` expects and defines, and should be used rather than creating the results manually.\n * This function should be used for when the {@link CombinedError.networkError} property is\n * populated and no GraphQL execution actually occurred.\n */\nexport const makeErrorResult = (\n  operation: Operation,\n  error: Error,\n  response?: any\n): OperationResult => ({\n  operation,\n  data: undefined,\n  error: new CombinedError({\n    networkError: error,\n    response,\n  }),\n  extensions: undefined,\n  hasNext: false,\n  stale: false,\n});\n", "import {\n  stringifyDocument,\n  getOperationName,\n  stringifyVariables,\n  extractFiles,\n} from '../utils';\n\nimport type { AnyVariables, GraphQLRequest, Operation } from '../types';\n\n/** Abstract definition of the JSON data sent during GraphQL HTTP POST requests. */\nexport interface FetchBody {\n  query?: string;\n  documentId?: string;\n  operationName: string | undefined;\n  variables: undefined | Record<string, any>;\n  extensions: undefined | Record<string, any>;\n}\n\n/** Creates a GraphQL over HTTP compliant JSON request body.\n * @param request - An object containing a `query` document and `variables`.\n * @returns A {@link FetchBody}\n * @see {@link https://github.com/graphql/graphql-over-http} for the GraphQL over HTTP spec.\n */\nexport function makeFetchBody<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(request: Omit<GraphQLRequest<Data, Variables>, 'key'>): FetchBody {\n  const body: FetchBody = {\n    query: undefined,\n    documentId: undefined,\n    operationName: getOperationName(request.query),\n    variables: request.variables || undefined,\n    extensions: request.extensions,\n  };\n\n  if (\n    'documentId' in request.query &&\n    request.query.documentId &&\n    // NOTE: We have to check that the document will definitely be sent\n    // as a persisted document to avoid breaking changes\n    (!request.query.definitions || !request.query.definitions.length)\n  ) {\n    body.documentId = request.query.documentId;\n  } else if (\n    !request.extensions ||\n    !request.extensions.persistedQuery ||\n    !!request.extensions.persistedQuery.miss\n  ) {\n    body.query = stringifyDocument(request.query);\n  }\n\n  return body;\n}\n\n/** Creates a URL that will be called for a GraphQL HTTP request.\n *\n * @param operation - An {@link Operation} for which to make the request.\n * @param body - A {@link FetchBody} which may be replaced with a URL.\n *\n * @remarks\n * Creates the URL that’ll be called as part of a GraphQL HTTP request.\n * Built-in fetch exchanges support sending GET requests, even for\n * non-persisted full requests, which this function supports by being\n * able to serialize GraphQL requests into the URL.\n */\nexport const makeFetchURL = (\n  operation: Operation,\n  body?: FetchBody\n): string => {\n  const useGETMethod =\n    operation.kind === 'query' && operation.context.preferGetMethod;\n  if (!useGETMethod || !body) return operation.context.url;\n\n  const urlParts = splitOutSearchParams(operation.context.url);\n  for (const key in body) {\n    const value = body[key];\n    if (value) {\n      urlParts[1].set(\n        key,\n        typeof value === 'object' ? stringifyVariables(value) : value\n      );\n    }\n  }\n  const finalUrl = urlParts.join('?');\n  if (finalUrl.length > 2047 && useGETMethod !== 'force') {\n    operation.context.preferGetMethod = false;\n    return operation.context.url;\n  }\n\n  return finalUrl;\n};\n\nconst splitOutSearchParams = (\n  url: string\n): readonly [string, URLSearchParams] => {\n  const start = url.indexOf('?');\n  return start > -1\n    ? [url.slice(0, start), new URLSearchParams(url.slice(start + 1))]\n    : [url, new URLSearchParams()];\n};\n\n/** Serializes a {@link FetchBody} into a {@link RequestInit.body} format. */\nconst serializeBody = (\n  operation: Operation,\n  body?: FetchBody\n): FormData | string | undefined => {\n  const omitBody =\n    operation.kind === 'query' && !!operation.context.preferGetMethod;\n  if (body && !omitBody) {\n    const json = stringifyVariables(body);\n    const files = extractFiles(body.variables);\n    if (files.size) {\n      const form = new FormData();\n      form.append('operations', json);\n      form.append(\n        'map',\n        stringifyVariables({\n          ...[...files.keys()].map(value => [value]),\n        })\n      );\n      let index = 0;\n      for (const file of files.values()) form.append(`${index++}`, file);\n      return form;\n    }\n    return json;\n  }\n};\n\nconst isHeaders = (headers: HeadersInit): headers is Headers =>\n  'has' in headers && !Object.keys(headers).length;\n\n/** Creates a `RequestInit` object for a given `Operation`.\n *\n * @param operation - An {@link Operation} for which to make the request.\n * @param body - A {@link FetchBody} which is added to the options, if the request isn’t a GET request.\n *\n * @remarks\n * Creates the fetch options {@link RequestInit} object that’ll be passed to the Fetch API\n * as part of a GraphQL over HTTP request. It automatically sets a default `Content-Type`\n * header.\n *\n * @see {@link https://github.com/graphql/graphql-over-http} for the GraphQL over HTTP spec.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n */\nexport const makeFetchOptions = (\n  operation: Operation,\n  body?: FetchBody\n): RequestInit => {\n  const headers: HeadersInit = {\n    accept:\n      operation.kind === 'subscription'\n        ? 'text/event-stream, multipart/mixed'\n        : 'application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed',\n  };\n  const extraOptions =\n    (typeof operation.context.fetchOptions === 'function'\n      ? operation.context.fetchOptions()\n      : operation.context.fetchOptions) || {};\n  if (extraOptions.headers) {\n    if (isHeaders(extraOptions.headers)) {\n      extraOptions.headers.forEach((value, key) => {\n        headers[key] = value;\n      });\n    } else if (Array.isArray(extraOptions.headers)) {\n      (extraOptions.headers as Array<[string, string]>).forEach(\n        (value, key) => {\n          if (Array.isArray(value)) {\n            if (headers[value[0]]) {\n              headers[value[0]] = `${headers[value[0]]},${value[1]}`;\n            } else {\n              headers[value[0]] = value[1];\n            }\n          } else {\n            headers[key] = value;\n          }\n        }\n      );\n    } else {\n      for (const key in extraOptions.headers) {\n        headers[key.toLowerCase()] = extraOptions.headers[key];\n      }\n    }\n  }\n\n  const serializedBody = serializeBody(operation, body);\n  if (typeof serializedBody === 'string' && !headers['content-type'])\n    headers['content-type'] = 'application/json';\n  return {\n    ...extraOptions,\n    method: serializedBody ? 'POST' : 'GET',\n    body: serializedBody,\n    headers,\n  };\n};\n", "/* Summary: This file handles the HTTP transport via GraphQL over HTTP\n * See: https://graphql.github.io/graphql-over-http/draft/\n *\n * `@urql/core`, by default, implements several RFC'd protocol extensions\n * on top of this. As such, this implementation supports:\n * - [Incremental Delivery](https://github.com/graphql/graphql-over-http/blob/main/rfcs/IncrementalDelivery.md)\n * - [GraphQL over SSE](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverSSE.md)\n *\n * This also supports the \"Defer Stream\" payload format.\n * See: https://github.com/graphql/graphql-wg/blob/main/rfcs/DeferStream.md\n * Implementation for this is located in `../utils/result.ts` in `mergeResultPatch`\n *\n * And; this also supports the GraphQL Multipart spec for file uploads.\n * See: https://github.com/jaydenseric/graphql-multipart-request-spec\n * Implementation for this is located in `../utils/variables.ts` in `extractFiles`,\n * and `./fetchOptions.ts` in `serializeBody`.\n *\n * And; this also supports GET requests (and hence; automatic persisted queries)\n * via the `@urql/exchange-persisted` package.\n *\n * This implementation DOES NOT support Batching.\n * See: https://github.com/graphql/graphql-over-http/blob/main/rfcs/Batching.md\n * Which is deemed out-of-scope, as it's sufficiently unnecessary given\n * modern handling of HTTP requests being in parallel.\n *\n * The implementation in this file needs to make certain accommodations for:\n * - The Web Fetch API\n * - Non-browser or polyfill Fetch APIs\n * - Node.js-like Fetch implementations (see `toString` below)\n *\n * GraphQL over SSE has a reference implementation, which supports non-HTTP/2\n * modes and is a faithful implementation of the spec.\n * See: https://github.com/enisdenjo/graphql-sse\n *\n * GraphQL Inremental Delivery (aka “GraphQL Multipart Responses”) has a\n * reference implementation, which a prior implementation of this file heavily\n * leaned on (See prior attribution comments)\n * See: https://github.com/maraisr/meros\n *\n * This file merges support for all three GraphQL over HTTP response formats\n * via async generators and Wonka’s `fromAsyncIterable`. As part of this, `streamBody`\n * and `split` are the common, cross-compatible base implementations.\n */\n\nimport type { Source } from 'wonka';\nimport { fromAsyncIterable, onEnd, filter, pipe } from 'wonka';\nimport type { Operation, OperationResult, ExecutionResult } from '../types';\nimport { makeResult, makeErrorResult, mergeResultPatch } from '../utils';\n\nconst decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder() : null;\nconst boundaryHeaderRe = /boundary=\"?([^=\";]+)\"?/i;\nconst eventStreamRe = /data: ?([^\\n]+)/;\n\ntype ChunkData = Buffer | Uint8Array;\n\n// NOTE: We're avoiding referencing the `Buffer` global here to prevent\n// auto-polyfilling in Webpack\nconst toString = (input: Buffer | ArrayBuffer): string =>\n  input.constructor.name === 'Buffer'\n    ? (input as Buffer).toString()\n    : decoder!.decode(input as ArrayBuffer);\n\nasync function* streamBody(response: Response): AsyncIterableIterator<string> {\n  if (response.body![Symbol.asyncIterator]) {\n    for await (const chunk of response.body! as any)\n      yield toString(chunk as ChunkData);\n  } else {\n    const reader = response.body!.getReader();\n    let result: ReadableStreamReadResult<ChunkData>;\n    try {\n      while (!(result = await reader.read()).done) yield toString(result.value);\n    } finally {\n      reader.cancel();\n    }\n  }\n}\n\nasync function* split(\n  chunks: AsyncIterableIterator<string>,\n  boundary: string\n): AsyncIterableIterator<string> {\n  let buffer = '';\n  let boundaryIndex: number;\n  for await (const chunk of chunks) {\n    buffer += chunk;\n    while ((boundaryIndex = buffer.indexOf(boundary)) > -1) {\n      yield buffer.slice(0, boundaryIndex);\n      buffer = buffer.slice(boundaryIndex + boundary.length);\n    }\n  }\n}\n\nasync function* parseJSON(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  yield JSON.parse(await response.text());\n}\n\nasync function* parseEventStream(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  let payload: any;\n  for await (const chunk of split(streamBody(response), '\\n\\n')) {\n    const match = chunk.match(eventStreamRe);\n    if (match) {\n      const chunk = match[1];\n      try {\n        yield (payload = JSON.parse(chunk));\n      } catch (error) {\n        if (!payload) throw error;\n      }\n      if (payload && payload.hasNext === false) break;\n    }\n  }\n  if (payload && payload.hasNext !== false) {\n    yield { hasNext: false };\n  }\n}\n\nasync function* parseMultipartMixed(\n  contentType: string,\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  const boundaryHeader = contentType.match(boundaryHeaderRe);\n  const boundary = '--' + (boundaryHeader ? boundaryHeader[1] : '-');\n  let isPreamble = true;\n  let payload: any;\n  for await (let chunk of split(streamBody(response), '\\r\\n' + boundary)) {\n    if (isPreamble) {\n      isPreamble = false;\n      const preambleIndex = chunk.indexOf(boundary);\n      if (preambleIndex > -1) {\n        chunk = chunk.slice(preambleIndex + boundary.length);\n      } else {\n        continue;\n      }\n    }\n    try {\n      yield (payload = JSON.parse(chunk.slice(chunk.indexOf('\\r\\n\\r\\n') + 4)));\n    } catch (error) {\n      if (!payload) throw error;\n    }\n    if (payload && payload.hasNext === false) break;\n  }\n  if (payload && payload.hasNext !== false) {\n    yield { hasNext: false };\n  }\n}\n\nasync function* parseMaybeJSON(\n  response: Response\n): AsyncIterableIterator<ExecutionResult> {\n  const text = await response.text();\n  try {\n    const result = JSON.parse(text);\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        `Found response with content-type \"text/plain\" but it had a valid \"application/json\" response.`\n      );\n    }\n    yield result;\n  } catch (e) {\n    throw new Error(text);\n  }\n}\n\nasync function* fetchOperation(\n  operation: Operation,\n  url: string,\n  fetchOptions: RequestInit\n) {\n  let networkMode = true;\n  let result: OperationResult | null = null;\n  let response: Response | undefined;\n\n  try {\n    // Delay for a tick to give the Client a chance to cancel the request\n    // if a teardown comes in immediately\n    yield await Promise.resolve();\n\n    response = await (operation.context.fetch || fetch)(url, fetchOptions);\n    const contentType = response.headers.get('Content-Type') || '';\n\n    let results: AsyncIterable<ExecutionResult>;\n    if (/multipart\\/mixed/i.test(contentType)) {\n      results = parseMultipartMixed(contentType, response);\n    } else if (/text\\/event-stream/i.test(contentType)) {\n      results = parseEventStream(response);\n    } else if (!/text\\//i.test(contentType)) {\n      results = parseJSON(response);\n    } else {\n      results = parseMaybeJSON(response);\n    }\n\n    let pending: ExecutionResult['pending'];\n    for await (const payload of results) {\n      if (payload.pending && !result) {\n        pending = payload.pending;\n      } else if (payload.pending) {\n        pending = [...pending!, ...payload.pending];\n      }\n      result = result\n        ? mergeResultPatch(result, payload, response, pending)\n        : makeResult(operation, payload, response);\n      networkMode = false;\n      yield result;\n      networkMode = true;\n    }\n\n    if (!result) {\n      yield (result = makeResult(operation, {}, response));\n    }\n  } catch (error: any) {\n    if (!networkMode) {\n      throw error;\n    }\n\n    yield makeErrorResult(\n      operation,\n      response &&\n        (response.status < 200 || response.status >= 300) &&\n        response.statusText\n        ? new Error(response.statusText)\n        : error,\n      response\n    );\n  }\n}\n\n/** Makes a GraphQL HTTP request to a given API by wrapping around the Fetch API.\n *\n * @param operation - The {@link Operation} that should be sent via GraphQL over HTTP.\n * @param url - The endpoint URL for the GraphQL HTTP API.\n * @param fetchOptions - The {@link RequestInit} fetch options for the request.\n * @returns A Wonka {@link Source} of {@link OperationResult | OperationResults}.\n *\n * @remarks\n * This utility defines how all built-in fetch exchanges make GraphQL HTTP requests,\n * supporting multipart incremental responses, cancellation and other smaller\n * implementation details.\n *\n * If you’re implementing a modified fetch exchange for a GraphQL over HTTP API\n * it’s recommended you use this utility.\n *\n * Hint: This function does not use the passed `operation` to create or modify the\n * `fetchOptions` and instead expects that the options have already been created\n * using {@link makeFetchOptions} and modified as needed.\n *\n * @throws\n * If the `fetch` polyfill or globally available `fetch` function doesn’t support\n * streamed multipart responses while trying to handle a `multipart/mixed` GraphQL response,\n * the source will throw “Streaming requests unsupported”.\n * This shouldn’t happen in modern browsers and Node.js.\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n */\nexport function makeFetchSource(\n  operation: Operation,\n  url: string,\n  fetchOptions: RequestInit\n): Source<OperationResult> {\n  let abortController: AbortController | void;\n  if (typeof AbortController !== 'undefined') {\n    fetchOptions.signal = (abortController = new AbortController()).signal;\n  }\n  return pipe(\n    fromAsyncIterable(fetchOperation(operation, url, fetchOptions)),\n    filter((result): result is OperationResult => !!result),\n    onEnd(() => {\n      if (abortController) abortController.abort();\n    })\n  );\n}\n", "interface EntityLike {\n  [key: string]: EntityLike | EntityLike[] | any;\n  __typename: string | null | void;\n}\n\nconst collectTypes = (obj: EntityLike | EntityLike[], types: Set<string>) => {\n  if (Array.isArray(obj)) {\n    for (const item of obj) collectTypes(item, types);\n  } else if (typeof obj === 'object' && obj !== null) {\n    for (const key in obj) {\n      if (key === '__typename' && typeof obj[key] === 'string') {\n        types.add(obj[key] as string);\n      } else {\n        collectTypes(obj[key], types);\n      }\n    }\n  }\n\n  return types;\n};\n\n/** Finds and returns a list of `__typename` fields found in response data.\n *\n * @privateRemarks\n * This is used by `@urql/core`’s document `cacheExchange` to find typenames\n * in a given GraphQL response’s data.\n */\nexport const collectTypenames = (response: object): string[] => [\n  ...collectTypes(response as EntityLike, new Set()),\n];\n", "import type {\n  FieldNode,\n  SelectionNode,\n  DefinitionNode,\n  DirectiveNode,\n} from '@0no-co/graphql.web';\nimport { Kind } from '@0no-co/graphql.web';\nimport type { KeyedDocumentNode } from './request';\nimport { keyDocument } from './request';\nimport type { FormattedNode, TypedDocumentNode } from '../types';\n\nconst formatNode = <\n  T extends SelectionNode | DefinitionNode | TypedDocumentNode<any, any>,\n>(\n  node: T\n): FormattedNode<T> => {\n  if ('definitions' in node) {\n    const definitions: FormattedNode<DefinitionNode>[] = [];\n    for (const definition of node.definitions) {\n      const newDefinition = formatNode(definition);\n      definitions.push(newDefinition);\n    }\n\n    return { ...node, definitions } as FormattedNode<T>;\n  }\n\n  if ('directives' in node && node.directives && node.directives.length) {\n    const directives: DirectiveNode[] = [];\n    const _directives = {};\n    for (const directive of node.directives) {\n      let name = directive.name.value;\n      if (name[0] !== '_') {\n        directives.push(directive);\n      } else {\n        name = name.slice(1);\n      }\n      _directives[name] = directive;\n    }\n    node = { ...node, directives, _directives };\n  }\n\n  if ('selectionSet' in node) {\n    const selections: FormattedNode<SelectionNode>[] = [];\n    let hasTypename = node.kind === Kind.OPERATION_DEFINITION;\n    if (node.selectionSet) {\n      for (const selection of node.selectionSet.selections || []) {\n        hasTypename =\n          hasTypename ||\n          (selection.kind === Kind.FIELD &&\n            selection.name.value === '__typename' &&\n            !selection.alias);\n        const newSelection = formatNode(selection);\n        selections.push(newSelection);\n      }\n\n      if (!hasTypename) {\n        selections.push({\n          kind: Kind.FIELD,\n          name: {\n            kind: Kind.NAME,\n            value: '__typename',\n          },\n          _generated: true,\n        } as FormattedNode<FieldNode>);\n      }\n\n      return {\n        ...node,\n        selectionSet: { ...node.selectionSet, selections },\n      } as FormattedNode<T>;\n    }\n  }\n\n  return node as FormattedNode<T>;\n};\n\nconst formattedDocs = new Map<number, KeyedDocumentNode>();\n\n/** Formats a GraphQL document to add `__typename` fields and process client-side directives.\n *\n * @param node - a {@link DocumentNode}.\n * @returns a {@link FormattedDocument}\n *\n * @remarks\n * Cache {@link Exchange | Exchanges} will require typename introspection to\n * recognize types in a GraphQL response. To retrieve these typenames,\n * this function is used to add the `__typename` fields to non-root\n * selection sets of a GraphQL document.\n *\n * Additionally, this utility will process directives, filter out client-side\n * directives starting with an `_` underscore, and place a `_directives` dictionary\n * on selection nodes.\n *\n * This utility also preserves the internally computed key of the\n * document as created by {@link createRequest} to avoid any\n * formatting from being duplicated.\n *\n * @see {@link https://spec.graphql.org/October2021/#sec-Type-Name-Introspection} for more information\n * on typename introspection via the `__typename` field.\n */\nexport const formatDocument = <T extends TypedDocumentNode<any, any>>(\n  node: T\n): FormattedNode<T> => {\n  const query = keyDocument(node);\n\n  let result = formattedDocs.get(query.__key);\n  if (!result) {\n    formattedDocs.set(\n      query.__key,\n      (result = formatNode(query) as KeyedDocumentNode)\n    );\n    // Ensure that the hash of the resulting document won't suddenly change\n    // we are marking __key as non-enumerable so when external exchanges use visit\n    // to manipulate a document we won't restore the previous query due to the __key\n    // property.\n    Object.defineProperty(result, '__key', {\n      value: query.__key,\n      enumerable: false,\n    });\n  }\n\n  return result as FormattedNode<T>;\n};\n", "/** Used to recursively mark `__typename` fields in data as non-enumerable.\n *\n * @deprecated Not recommended over modelling inputs manually (See #3299)\n *\n * @remarks\n * This utility can be used to recursively copy GraphQl response data and hide\n * all `__typename` fields present on it.\n *\n * Hint: It’s not recommended to do this, unless it's absolutely necessary as\n * cloning and modifying all data of a response can be unnecessarily slow, when\n * a manual and more specific copy/mask is more efficient.\n *\n * @see {@link ClientOptions.maskTypename} for a description of how the `Client` uses this utility.\n */\nexport const maskTypename = (data: any, isRoot?: boolean): any => {\n  if (!data || typeof data !== 'object') {\n    return data;\n  } else if (Array.isArray(data)) {\n    return data.map(d => maskTypename(d));\n  } else if (\n    data &&\n    typeof data === 'object' &&\n    (isRoot || '__typename' in data)\n  ) {\n    const acc = {};\n    for (const key in data) {\n      if (key === '__typename') {\n        Object.defineProperty(acc, '__typename', {\n          enumerable: false,\n          value: data.__typename,\n        });\n      } else {\n        acc[key] = maskTypename(data[key]);\n      }\n    }\n    return acc;\n  } else {\n    return data;\n  }\n};\n", "import type { Sink, Source } from 'wonka';\nimport { subscribe, take, filter, toPromise, pipe } from 'wonka';\nimport type { OperationResult, OperationResultSource } from '../types';\n\n/** Patches a `toPromise` method onto the `Source` passed to it.\n * @param source$ - the Wonka {@link Source} to patch.\n * @returns The passed `source$` with a patched `toPromise` method as a {@link PromisifiedSource}.\n * @internal\n */\nexport function withPromise<T extends OperationResult>(\n  _source$: Source<T>\n): OperationResultSource<T> {\n  const source$ = ((sink: Sink<T>) =>\n    _source$(sink)) as OperationResultSource<T>;\n  source$.toPromise = () =>\n    pipe(\n      source$,\n      filter(result => !result.stale && !result.hasNext),\n      take(1),\n      toPromise\n    );\n  source$.then = (onResolve, onReject) =>\n    source$.toPromise().then(onResolve, onReject);\n  source$.subscribe = onResult => subscribe(onResult)(source$);\n  return source$;\n}\n", "import type {\n  AnyVariables,\n  GraphQLRequest,\n  Operation,\n  OperationContext,\n  OperationType,\n} from '../types';\n\n/** Creates a {@link Operation} from the given parameters.\n *\n * @param kind - The {@link OperationType} of GraphQL operation, i.e. `query`, `mutation`, or `subscription`.\n * @param request - The {@link GraphQLRequest} or {@link Operation} used as a template for the new `Operation`.\n * @param context - The {@link OperationContext} `context` data for the `Operation`.\n * @returns A new {@link Operation}.\n *\n * @remarks\n * This method is both used to create new {@link Operation | Operations} as well as copy and modify existing\n * operations. While it’s not required to use this function to copy an `Operation`, it is recommended, in case\n * additional dynamic logic is added to them in the future.\n *\n * Hint: When an {@link Operation} is passed to the `request` argument, the `context` argument does not have to be\n * a complete {@link OperationContext} and will instead be combined with passed {@link Operation.context}.\n *\n * @example\n * An example of copying an existing `Operation` to modify its `context`:\n *\n * ```ts\n * makeOperation(\n *   operation.kind,\n *   operation,\n *   { requestPolicy: 'cache-first' },\n * );\n * ```\n */\nfunction makeOperation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  kind: OperationType,\n  request: GraphQLRequest<Data, Variables>,\n  context: OperationContext\n): Operation<Data, Variables>;\n\nfunction makeOperation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  kind: OperationType,\n  request: Operation<Data, Variables>,\n  context?: Partial<OperationContext>\n): Operation<Data, Variables>;\n\nfunction makeOperation(kind, request, context) {\n  return {\n    ...request,\n    kind,\n    context: request.context\n      ? {\n          ...request.context,\n          ...context,\n        }\n      : context || request.context,\n  };\n}\n\nexport { makeOperation };\n\n/** Adds additional metadata to an `Operation`'s `context.meta` property while copying it.\n * @see {@link OperationDebugMeta} for more information on the {@link OperationContext.meta} property.\n */\nexport const addMetadata = (\n  operation: Operation,\n  meta: OperationContext['meta']\n) => {\n  return makeOperation(operation.kind, operation, {\n    meta: {\n      ...operation.context.meta,\n      ...meta,\n    },\n  });\n};\n", "export * from './error';\nexport * from './request';\nexport * from './result';\nexport * from './variables';\nexport * from './collectTypenames';\nexport * from './formatDocument';\nexport * from './maskTypename';\nexport * from './streamUtils';\nexport * from './operation';\n\nexport const noop = () => {\n  /* noop */\n};\n", "/* eslint-disable prefer-rest-params */\nimport { Kind } from '@0no-co/graphql.web';\nimport type { DocumentNode, DefinitionNode } from './utils/graphql';\nimport type { AnyVariables, TypedDocumentNode } from './types';\nimport { keyDocument, stringifyDocument } from './utils';\n\n/** A GraphQL parse function, which may be called as a tagged template literal, returning a parsed {@link DocumentNode}.\n *\n * @remarks\n * The `gql` tag or function is used to parse a GraphQL query document into a {@link DocumentNode}.\n *\n * When used as a tagged template, `gql` will automatically merge fragment definitions into the resulting\n * document and deduplicate them.\n *\n * It enforces that all fragments have a unique name. When fragments with different definitions share a name,\n * it will log a warning in development.\n *\n * Hint: It’s recommended to use this `gql` function over other GraphQL parse functions, since it puts the parsed\n * results directly into `@urql/core`’s internal caches and prevents further unnecessary work.\n *\n * @example\n * ```ts\n * const AuthorFragment = gql`\n *   fragment AuthorDisplayComponent on Author {\n *     id\n *     name\n *   }\n * `;\n *\n * const BookFragment = gql`\n *   fragment ListBookComponent on Book {\n *     id\n *     title\n *     author {\n *       ...AuthorDisplayComponent\n *     }\n *   }\n *\n *   ${AuthorFragment}\n * `;\n *\n * const BookQuery = gql`\n *   query Book($id: ID!) {\n *     book(id: $id) {\n *       ...BookFragment\n *     }\n *   }\n *\n *   ${BookFragment}\n * `;\n * ```\n */\nfunction gql<Data = any, Variables extends AnyVariables = AnyVariables>(\n  strings: TemplateStringsArray,\n  ...interpolations: Array<TypedDocumentNode | DocumentNode | string>\n): TypedDocumentNode<Data, Variables>;\n\nfunction gql<Data = any, Variables extends AnyVariables = AnyVariables>(\n  string: string\n): TypedDocumentNode<Data, Variables>;\n\nfunction gql(parts: string | TemplateStringsArray /* arguments */) {\n  const fragmentNames = new Map<string, string>();\n  const definitions: DefinitionNode[] = [];\n  const source: DocumentNode[] = [];\n\n  // Apply the entire tagged template body's definitions\n  let body: string = Array.isArray(parts) ? parts[0] : parts || '';\n  for (let i = 1; i < arguments.length; i++) {\n    const value = arguments[i];\n    if (value && value.definitions) {\n      source.push(value);\n    } else {\n      body += value;\n    }\n\n    body += arguments[0][i];\n  }\n\n  source.unshift(keyDocument(body));\n  for (const document of source) {\n    for (const definition of document.definitions) {\n      if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n        const name = definition.name.value;\n        const value = stringifyDocument(definition);\n        // Fragments will be deduplicated according to this Map\n        if (!fragmentNames.has(name)) {\n          fragmentNames.set(name, value);\n          definitions.push(definition);\n        } else if (\n          process.env.NODE_ENV !== 'production' &&\n          fragmentNames.get(name) !== value\n        ) {\n          // Fragments with the same names is expected to have the same contents\n          console.warn(\n            '[WARNING: Duplicate Fragment] A fragment with name `' +\n              name +\n              '` already exists in this document.\\n' +\n              'While fragment names may not be unique across your source, each name must be unique per document.'\n          );\n        }\n      } else {\n        definitions.push(definition);\n      }\n    }\n  }\n\n  return keyDocument({\n    kind: Kind.DOCUMENT,\n    definitions,\n  });\n}\n\nexport { gql };\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport { filter, map, merge, pipe, tap } from 'wonka';\n\nimport type { Client } from '../client';\nimport type { Exchange, Operation, OperationResult } from '../types';\n\nimport {\n  makeOperation,\n  addMetadata,\n  collectTypenames,\n  formatDocument,\n  makeResult,\n} from '../utils';\n\ntype ResultCache = Map<number, OperationResult>;\ntype OperationCache = Map<string, Set<number>>;\n\nconst shouldSkip = ({ kind }: Operation) =>\n  kind !== 'mutation' && kind !== 'query';\n\n/** Adds unique typenames to query (for invalidating cache entries) */\nexport const mapTypeNames = (operation: Operation): Operation => {\n  const query = formatDocument(operation.query);\n  if (query !== operation.query) {\n    const formattedOperation = makeOperation(operation.kind, operation);\n    formattedOperation.query = query;\n    return formattedOperation;\n  } else {\n    return operation;\n  }\n};\n\n/** Default document cache exchange.\n *\n * @remarks\n * The default document cache in `urql` avoids sending the same GraphQL request\n * multiple times by caching it using the {@link Operation.key}. It will invalidate\n * query results automatically whenever it sees a mutation responses with matching\n * `__typename`s in their responses.\n *\n * The document cache will get the introspected `__typename` fields by modifying\n * your GraphQL operation documents using the {@link formatDocument} utility.\n *\n * This automatic invalidation strategy can fail if your query or mutation don’t\n * contain matching typenames, for instance, because the query contained an\n * empty list.\n * You can manually add hints for this exchange by specifying a list of\n * {@link OperationContext.additionalTypenames} for queries and mutations that\n * should invalidate one another.\n *\n * @see {@link https://urql.dev/goto/docs/basics/document-caching} for more information on this cache.\n */\nexport const cacheExchange: Exchange = ({ forward, client, dispatchDebug }) => {\n  const resultCache: ResultCache = new Map();\n  const operationCache: OperationCache = new Map();\n\n  const isOperationCached = (operation: Operation) =>\n    operation.kind === 'query' &&\n    operation.context.requestPolicy !== 'network-only' &&\n    (operation.context.requestPolicy === 'cache-only' ||\n      resultCache.has(operation.key));\n\n  return ops$ => {\n    const cachedOps$ = pipe(\n      ops$,\n      filter(op => !shouldSkip(op) && isOperationCached(op)),\n      map(operation => {\n        const cachedResult = resultCache.get(operation.key);\n\n        dispatchDebug({\n          operation,\n          ...(cachedResult\n            ? {\n                type: 'cacheHit',\n                message: 'The result was successfully retried from the cache',\n              }\n            : {\n                type: 'cacheMiss',\n                message: 'The result could not be retrieved from the cache',\n              }),\n        });\n\n        let result: OperationResult =\n          cachedResult ||\n          makeResult(operation, {\n            data: null,\n          });\n\n        result = {\n          ...result,\n          operation: addMetadata(operation, {\n            cacheOutcome: cachedResult ? 'hit' : 'miss',\n          }),\n        };\n\n        if (operation.context.requestPolicy === 'cache-and-network') {\n          result.stale = true;\n          reexecuteOperation(client, operation);\n        }\n\n        return result;\n      })\n    );\n\n    const forwardedOps$ = pipe(\n      merge([\n        pipe(\n          ops$,\n          filter(op => !shouldSkip(op) && !isOperationCached(op)),\n          map(mapTypeNames)\n        ),\n        pipe(\n          ops$,\n          filter(op => shouldSkip(op))\n        ),\n      ]),\n      map(op => addMetadata(op, { cacheOutcome: 'miss' })),\n      filter(\n        op => op.kind !== 'query' || op.context.requestPolicy !== 'cache-only'\n      ),\n      forward,\n      tap(response => {\n        let { operation } = response;\n        if (!operation) return;\n\n        let typenames = operation.context.additionalTypenames || [];\n        // NOTE: For now, we only respect `additionalTypenames` from subscriptions to\n        // avoid unexpected breaking changes\n        // We'd expect live queries or other update mechanisms to be more suitable rather\n        // than using subscriptions as “signals” to reexecute queries. However, if they’re\n        // just used as signals, it’s intuitive to hook them up using `additionalTypenames`\n        if (response.operation.kind !== 'subscription') {\n          typenames = collectTypenames(response.data).concat(typenames);\n        }\n\n        // Invalidates the cache given a mutation's response\n        if (\n          response.operation.kind === 'mutation' ||\n          response.operation.kind === 'subscription'\n        ) {\n          const pendingOperations = new Set<number>();\n\n          dispatchDebug({\n            type: 'cacheInvalidation',\n            message: `The following typenames have been invalidated: ${typenames}`,\n            operation,\n            data: { typenames, response },\n          });\n\n          for (let i = 0; i < typenames.length; i++) {\n            const typeName = typenames[i];\n            let operations = operationCache.get(typeName);\n            if (!operations)\n              operationCache.set(typeName, (operations = new Set()));\n            for (const key of operations.values()) pendingOperations.add(key);\n            operations.clear();\n          }\n\n          for (const key of pendingOperations.values()) {\n            if (resultCache.has(key)) {\n              operation = (resultCache.get(key) as OperationResult).operation;\n              resultCache.delete(key);\n              reexecuteOperation(client, operation);\n            }\n          }\n        } else if (operation.kind === 'query' && response.data) {\n          resultCache.set(operation.key, response);\n          for (let i = 0; i < typenames.length; i++) {\n            const typeName = typenames[i];\n            let operations = operationCache.get(typeName);\n            if (!operations)\n              operationCache.set(typeName, (operations = new Set()));\n            operations.add(operation.key);\n          }\n        }\n      })\n    );\n\n    return merge([cachedOps$, forwardedOps$]);\n  };\n};\n\n/** Reexecutes an `Operation` with the `network-only` request policy.\n * @internal\n */\nexport const reexecuteOperation = (client: Client, operation: Operation) => {\n  return client.reexecuteOperation(\n    makeOperation(operation.kind, operation, {\n      requestPolicy: 'network-only',\n    })\n  );\n};\n", "import type { GraphQLError } from '../utils/graphql';\nimport { pipe, filter, merge, map, tap } from 'wonka';\nimport type { Exchange, OperationResult, Operation } from '../types';\nimport { addMetadata, CombinedError } from '../utils';\nimport { reexecuteOperation, mapTypeNames } from './cache';\n\n/** A serialized version of an {@link OperationResult}.\n *\n * @remarks\n * All properties are serialized separately as JSON strings, except for the\n * {@link CombinedError} to speed up JS parsing speed, even if a result doesn’t\n * end up being used.\n *\n * @internal\n */\nexport interface SerializedResult {\n  hasNext?: boolean;\n  /** JSON-serialized version of {@link OperationResult.data}. */\n  data?: string | undefined; // JSON string of data\n  /** JSON-serialized version of {@link OperationResult.extensions}. */\n  extensions?: string | undefined;\n  /** JSON version of {@link CombinedError}. */\n  error?: {\n    graphQLErrors: Array<Partial<GraphQLError> | string>;\n    networkError?: string;\n  };\n}\n\n/** A dictionary of {@link Operation.key} keys to serializable {@link SerializedResult} objects.\n *\n * @remarks\n * It’s not recommended to modify the serialized data manually, however, multiple payloads of\n * this dictionary may safely be merged and combined.\n */\nexport interface SSRData {\n  [key: string]: SerializedResult;\n}\n\n/** Options for the `ssrExchange` allowing it to either operate on the server- or client-side. */\nexport interface SSRExchangeParams {\n  /** Indicates to the {@link SSRExchange} whether it's currently in server-side or client-side mode.\n   *\n   * @remarks\n   * Depending on this option, the {@link SSRExchange} will either capture or replay results.\n   * When `true`, it’s in client-side mode and results will be serialized. When `false`, it’ll\n   * use its deserialized data and replay results from it.\n   */\n  isClient?: boolean;\n  /** May be used on the client-side to pass the {@link SSRExchange} serialized data from the server-side.\n   *\n   * @remarks\n   * Alternatively, {@link SSRExchange.restoreData} may be called to imperatively add serialized data to\n   * the exchange.\n   *\n   * Hint: This method also works on the server-side to add to the initial serialized data, which enables\n   * you to combine multiple {@link SSRExchange} results, as needed.\n   */\n  initialState?: SSRData;\n  /** Forces a new API request to be sent in the background after replaying the deserialized result.\n   *\n   * @remarks\n   * Similarly to the `cache-and-network` {@link RequestPolicy}, this option tells the {@link SSRExchange}\n   * to send a new API request for the {@link Operation} after replaying a serialized result.\n   *\n   * Hint: This is useful when you're caching SSR results and need the client-side to update itself after\n   * rendering the initial serialized SSR results.\n   */\n  staleWhileRevalidate?: boolean;\n  /** Forces {@link OperationResult.extensions} to be serialized alongside the rest of a result.\n   *\n   * @remarks\n   * Entries in the `extension` object of a GraphQL result are often non-standard metdata, and many\n   * APIs use it for data that changes between every request. As such, the {@link SSRExchange} will\n   * not serialize this data by default, unless this flag is set.\n   */\n  includeExtensions?: boolean;\n}\n\n/** An `SSRExchange` either in server-side mode, serializing results, or client-side mode, deserializing and replaying results..\n *\n * @remarks\n * This same {@link Exchange} is used in your code both for the client-side and server-side as it’s “universal”\n * and can be put into either client-side or server-side mode using the {@link SSRExchangeParams.isClient} flag.\n *\n * In server-side mode, the `ssrExchange` will “record” results it sees from your API and provide them for you\n * to send to the client-side using the {@link SSRExchange.extractData} method.\n *\n * In client-side mode, the `ssrExchange` will use these serialized results, rehydrated either using\n * {@link SSRExchange.restoreData} or {@link SSRexchangeParams.initialState}, to replay results the\n * server-side has seen and sent before.\n *\n * Each serialized result will only be replayed once, as it’s assumed that your cache exchange will have the\n * results cached afterwards.\n */\nexport interface SSRExchange extends Exchange {\n  /** Client-side method to add serialized results to the {@link SSRExchange}.\n   * @param data - {@link SSRData},\n   */\n  restoreData(data: SSRData): void;\n  /** Server-side method to get all serialized results the {@link SSRExchange} has captured.\n   * @returns an {@link SSRData} dictionary.\n   */\n  extractData(): SSRData;\n}\n\n/** Serialize an OperationResult to plain JSON */\nconst serializeResult = (\n  result: OperationResult,\n  includeExtensions: boolean\n): SerializedResult => {\n  const serialized: SerializedResult = {\n    data: JSON.stringify(result.data),\n    hasNext: result.hasNext,\n  };\n\n  if (result.data !== undefined) {\n    serialized.data = JSON.stringify(result.data);\n  }\n\n  if (includeExtensions && result.extensions !== undefined) {\n    serialized.extensions = JSON.stringify(result.extensions);\n  }\n\n  if (result.error) {\n    serialized.error = {\n      graphQLErrors: result.error.graphQLErrors.map(error => {\n        if (!error.path && !error.extensions) return error.message;\n\n        return {\n          message: error.message,\n          path: error.path,\n          extensions: error.extensions,\n        };\n      }),\n    };\n\n    if (result.error.networkError) {\n      serialized.error.networkError = '' + result.error.networkError;\n    }\n  }\n\n  return serialized;\n};\n\n/** Deserialize plain JSON to an OperationResult\n * @internal\n */\nconst deserializeResult = (\n  operation: Operation,\n  result: SerializedResult,\n  includeExtensions: boolean\n): OperationResult => ({\n  operation,\n  data: result.data ? JSON.parse(result.data) : undefined,\n  extensions:\n    includeExtensions && result.extensions\n      ? JSON.parse(result.extensions)\n      : undefined,\n  error: result.error\n    ? new CombinedError({\n        networkError: result.error.networkError\n          ? new Error(result.error.networkError)\n          : undefined,\n        graphQLErrors: result.error.graphQLErrors,\n      })\n    : undefined,\n  stale: false,\n  hasNext: !!result.hasNext,\n});\n\nconst revalidated = new Set<number>();\n\n/** Creates a server-side rendering `Exchange` that either captures responses on the server-side or replays them on the client-side.\n *\n * @param params - An {@link SSRExchangeParams} configuration object.\n * @returns the created {@link SSRExchange}\n *\n * @remarks\n * When dealing with server-side rendering, we essentially have two {@link Client | Clients} making requests,\n * the server-side client, and the client-side one. The `ssrExchange` helps implementing a tiny cache on both\n * sides that:\n *\n * - captures results on the server-side which it can serialize,\n * - replays results on the client-side that it deserialized from the server-side.\n *\n * Hint: The `ssrExchange` is basically an exchange that acts like a replacement for any fetch exchange\n * temporarily. As such, you should place it after your cache exchange but in front of any fetch exchange.\n */\nexport const ssrExchange = (params: SSRExchangeParams = {}): SSRExchange => {\n  const staleWhileRevalidate = !!params.staleWhileRevalidate;\n  const includeExtensions = !!params.includeExtensions;\n  const data: Record<string, SerializedResult | null> = {};\n\n  // On the client-side, we delete results from the cache as they're resolved\n  // this is delayed so that concurrent queries don't delete each other's data\n  const invalidateQueue: number[] = [];\n  const invalidate = (result: OperationResult) => {\n    invalidateQueue.push(result.operation.key);\n    if (invalidateQueue.length === 1) {\n      Promise.resolve().then(() => {\n        let key: number | void;\n        while ((key = invalidateQueue.shift())) {\n          data[key] = null;\n        }\n      });\n    }\n  };\n\n  // The SSR Exchange is a temporary cache that can populate results into data for suspense\n  // On the client it can be used to retrieve these temporary results from a rehydrated cache\n  const ssr: SSRExchange =\n    ({ client, forward }) =>\n    ops$ => {\n      // params.isClient tells us whether we're on the client-side\n      // By default we assume that we're on the client if suspense-mode is disabled\n      const isClient =\n        params && typeof params.isClient === 'boolean'\n          ? !!params.isClient\n          : !client.suspense;\n\n      let forwardedOps$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind === 'teardown' ||\n            !data[operation.key] ||\n            !!data[operation.key]!.hasNext ||\n            operation.context.requestPolicy === 'network-only'\n        ),\n        map(mapTypeNames),\n        forward\n      );\n\n      // NOTE: Since below we might delete the cached entry after accessing\n      // it once, cachedOps$ needs to be merged after forwardedOps$\n      let cachedOps$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind !== 'teardown' &&\n            !!data[operation.key] &&\n            operation.context.requestPolicy !== 'network-only'\n        ),\n        map(op => {\n          const serialized = data[op.key]!;\n          const cachedResult = deserializeResult(\n            op,\n            serialized,\n            includeExtensions\n          );\n\n          if (staleWhileRevalidate && !revalidated.has(op.key)) {\n            cachedResult.stale = true;\n            revalidated.add(op.key);\n            reexecuteOperation(client, op);\n          }\n\n          const result: OperationResult = {\n            ...cachedResult,\n            operation: addMetadata(op, {\n              cacheOutcome: 'hit',\n            }),\n          };\n          return result;\n        })\n      );\n\n      if (!isClient) {\n        // On the server we cache results in the cache as they're resolved\n        forwardedOps$ = pipe(\n          forwardedOps$,\n          tap((result: OperationResult) => {\n            const { operation } = result;\n            if (operation.kind !== 'mutation') {\n              const serialized = serializeResult(result, includeExtensions);\n              data[operation.key] = serialized;\n            }\n          })\n        );\n      } else {\n        // On the client we delete results from the cache as they're resolved\n        cachedOps$ = pipe(cachedOps$, tap(invalidate));\n      }\n\n      return merge([forwardedOps$, cachedOps$]);\n    };\n\n  ssr.restoreData = (restore: SSRData) => {\n    for (const key in restore) {\n      // We only restore data that hasn't been previously invalidated\n      if (data[key] !== null) {\n        data[key] = restore[key];\n      }\n    }\n  };\n\n  ssr.extractData = () => {\n    const result: SSRData = {};\n    for (const key in data) if (data[key] != null) result[key] = data[key]!;\n    return result;\n  };\n\n  if (params && params.initialState) {\n    ssr.restoreData(params.initialState);\n  }\n\n  return ssr;\n};\n", "import type { Subscription, Source } from 'wonka';\nimport { filter, make, merge, mergeMap, pipe, takeUntil } from 'wonka';\n\nimport {\n  makeResult,\n  mergeResultPatch,\n  makeErrorResult,\n  makeOperation,\n} from '../utils';\n\nimport type {\n  Exchange,\n  ExecutionResult,\n  Operation,\n  OperationResult,\n} from '../types';\n\nimport type { FetchBody } from '../internal';\nimport { makeFetchBody } from '../internal';\n\n/** An abstract observer-like interface.\n *\n * @remarks\n * Observer-like interfaces are passed to {@link ObservableLike.subscribe} to provide them\n * with callbacks for their events.\n *\n * @see {@link https://github.com/tc39/proposal-observable} for the full TC39 Observable proposal.\n */\nexport interface ObserverLike<T> {\n  /** Callback for values an {@link ObservableLike} emits. */\n  next: (value: T) => void;\n  /** Callback for an error an {@link ObservableLike} emits, which ends the subscription. */\n  error: (err: any) => void;\n  /** Callback for the completion of an {@link ObservableLike}, which ends the subscription. */\n  complete: () => void;\n}\n\n/** An abstract observable-like interface.\n *\n * @remarks\n * Observable, or Observable-like interfaces, are often used by GraphQL transports to abstract\n * how they send {@link ExecutionResult | ExecutionResults} to consumers. These generally contain\n * a `subscribe` method accepting an {@link ObserverLike} structure.\n *\n * @see {@link https://github.com/tc39/proposal-observable} for the full TC39 Observable proposal.\n */\nexport interface ObservableLike<T> {\n  /** Start the Observable-like subscription and returns a subscription handle.\n   *\n   * @param observer - an {@link ObserverLike} object with result, error, and completion callbacks.\n   * @returns a subscription handle providing an `unsubscribe` method to stop the subscription.\n   */\n  subscribe(observer: ObserverLike<T>): {\n    unsubscribe: () => void;\n  };\n}\n\n/** A more cross-compatible version of the {@link GraphQLRequest} structure.\n * {@link FetchBody} for more details\n */\nexport type SubscriptionOperation = FetchBody;\n\n/** A subscription forwarding function, which must accept a {@link SubscriptionOperation}.\n *\n * @param operation - A {@link SubscriptionOperation}\n * @returns An {@link ObservableLike} object issuing {@link ExecutionResult | ExecutionResults}.\n */\nexport type SubscriptionForwarder = (\n  request: FetchBody,\n  operation: Operation\n) => ObservableLike<ExecutionResult>;\n\n/** This is called to create a subscription and needs to be hooked up to a transport client. */\nexport interface SubscriptionExchangeOpts {\n  /** A subscription forwarding function, which must accept a {@link SubscriptionOperation}.\n   *\n   * @param operation - A {@link SubscriptionOperation}\n   * @returns An {@link ObservableLike} object issuing {@link ExecutionResult | ExecutionResults}.\n   *\n   * @remarks\n   * This callback is called for each {@link Operation} that this `subscriptionExchange` will\n   * handle. It receives the {@link SubscriptionOperation}, which is a more compatible version\n   * of the raw {@link Operation} objects and must return an {@link ObservableLike} of results.\n   */\n  forwardSubscription: SubscriptionForwarder;\n\n  /** Flag to enable this exchange to handle all types of GraphQL operations.\n   *\n   * @remarks\n   * When you aren’t using fetch exchanges and GraphQL over HTTP as a transport for your GraphQL requests,\n   * or you have a third-party GraphQL transport implementation, which must also be used for queries and\n   * mutations, this flag may be used to allow this exchange to handle all kinds of GraphQL operations.\n   *\n   * By default, this flag is `false` and the exchange will only handle GraphQL subscription operations.\n   */\n  enableAllOperations?: boolean;\n\n  /** A predicate function that causes an operation to be handled by this `subscriptionExchange` if `true` is returned.\n   *\n   * @param operation - an {@link Operation}\n   * @returns true when the operation is handled by this exchange.\n   *\n   * @remarks\n   * In some cases, a `subscriptionExchange` will be used to only handle some {@link Operation | Operations},\n   * e.g. all that contain `@live` directive. For these cases, this function may be passed to precisely\n   * determine which `Operation`s this exchange should handle, instead of forwarding.\n   *\n   * When specified, the {@link SubscriptionExchangeOpts.enableAllOperations} flag is disregarded.\n   */\n  isSubscriptionOperation?: (operation: Operation) => boolean;\n}\n\n/** Generic subscription exchange factory used to either create an exchange handling just subscriptions or all operation kinds.\n *\n * @remarks\n * `subscriptionExchange` can be used to create an {@link Exchange} that either\n * handles just GraphQL subscription operations, or optionally all operations,\n * when the {@link SubscriptionExchangeOpts.enableAllOperations} flag is passed.\n *\n * The {@link SubscriptionExchangeOpts.forwardSubscription} function must\n * be provided and provides a generic input that's based on {@link Operation}\n * but is compatible with many libraries implementing GraphQL request or\n * subscription interfaces.\n */\nexport const subscriptionExchange =\n  ({\n    forwardSubscription,\n    enableAllOperations,\n    isSubscriptionOperation,\n  }: SubscriptionExchangeOpts): Exchange =>\n  ({ client, forward }) => {\n    const createSubscriptionSource = (\n      operation: Operation\n    ): Source<OperationResult> => {\n      const observableish = forwardSubscription(\n        makeFetchBody(operation),\n        operation\n      );\n\n      return make<OperationResult>(observer => {\n        let isComplete = false;\n        let sub: Subscription | void;\n        let result: OperationResult | void;\n\n        function nextResult(value: ExecutionResult) {\n          observer.next(\n            (result = result\n              ? mergeResultPatch(result, value)\n              : makeResult(operation, value))\n          );\n        }\n\n        Promise.resolve().then(() => {\n          if (isComplete) return;\n\n          sub = observableish.subscribe({\n            next: nextResult,\n            error(error) {\n              if (Array.isArray(error)) {\n                // NOTE: This is an exception for transports that deliver `GraphQLError[]`, as part\n                // of the observer’s error callback (may happen as part of `graphql-ws`).\n                // We only check for arrays here, as this is an extremely “unexpected” case as the\n                // shape of `ExecutionResult` is instead strictly defined.\n                nextResult({ errors: error });\n              } else {\n                observer.next(makeErrorResult(operation, error));\n              }\n              observer.complete();\n            },\n            complete() {\n              if (!isComplete) {\n                isComplete = true;\n                if (operation.kind === 'subscription') {\n                  client.reexecuteOperation(\n                    makeOperation('teardown', operation, operation.context)\n                  );\n                }\n                if (result && result.hasNext) {\n                  nextResult({ hasNext: false });\n                }\n                observer.complete();\n              }\n            },\n          });\n        });\n\n        return () => {\n          isComplete = true;\n          if (sub) sub.unsubscribe();\n        };\n      });\n    };\n\n    const isSubscriptionOperationFn =\n      isSubscriptionOperation ||\n      (operation =>\n        operation.kind === 'subscription' ||\n        (!!enableAllOperations &&\n          (operation.kind === 'query' || operation.kind === 'mutation')));\n\n    return ops$ => {\n      const subscriptionResults$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind !== 'teardown' &&\n            isSubscriptionOperationFn(operation)\n        ),\n        mergeMap(operation => {\n          const { key } = operation;\n          const teardown$ = pipe(\n            ops$,\n            filter(op => op.kind === 'teardown' && op.key === key)\n          );\n\n          return pipe(\n            createSubscriptionSource(operation),\n            takeUntil(teardown$)\n          );\n        })\n      );\n\n      const forward$ = pipe(\n        ops$,\n        filter(\n          operation =>\n            operation.kind === 'teardown' ||\n            !isSubscriptionOperationFn(operation)\n        ),\n        forward\n      );\n\n      return merge([subscriptionResults$, forward$]);\n    };\n  };\n", "import { pipe, tap } from 'wonka';\nimport type { Exchange } from '../types';\n\n/** Simple log debugger exchange.\n *\n * @remarks\n * An exchange that logs incoming {@link Operation | Operations} and\n * {@link OperationResult | OperationResults} in development.\n *\n * This exchange is a no-op in production and often used in issue reporting\n * to understand certain usage patterns of `urql` without having access to\n * the original source code.\n *\n * Hint: When you report an issue you’re having with `urql`, adding\n * this as your first exchange and posting its output can speed up\n * issue triaging a lot!\n */\nexport const debugExchange: Exchange = ({ forward }) => {\n  if (process.env.NODE_ENV === 'production') {\n    return ops$ => forward(ops$);\n  } else {\n    return ops$ =>\n      pipe(\n        ops$,\n        // eslint-disable-next-line no-console\n        tap(op => console.log('[Exchange debug]: Incoming operation: ', op)),\n        forward,\n        tap(result =>\n          // eslint-disable-next-line no-console\n          console.log('[Exchange debug]: Completed operation: ', result)\n        )\n      );\n  }\n};\n", "import type { Exchange } from '../types';\n\n/** Default deduplication exchange.\n * @deprecated\n * This exchange's functionality is now built into the {@link Client}.\n */\nexport const dedupExchange: Exchange =\n  ({ forward }) =>\n  ops$ =>\n    forward(ops$);\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport { filter, merge, mergeMap, pipe, takeUntil, onPush } from 'wonka';\n\nimport type { Exchange } from '../types';\nimport {\n  makeFetchBody,\n  makeFetchURL,\n  makeFetchOptions,\n  makeFetchSource,\n} from '../internal';\n\n/** Default GraphQL over HTTP fetch exchange.\n *\n * @remarks\n * The default fetch exchange in `urql` supports sending GraphQL over HTTP\n * requests, can optionally send GraphQL queries as GET requests, and\n * handles incremental multipart responses.\n *\n * This exchange does not handle persisted queries or multipart uploads.\n * Support for the former can be added using `@urql/exchange-persisted-fetch`\n * and the latter using `@urql/exchange-multipart-fetch`.\n *\n * Hint: The `fetchExchange` and the two other exchanges all use the built-in fetch\n * utilities in `@urql/core/internal`, which you can also use to implement\n * a customized fetch exchange.\n *\n * @see {@link makeFetchSource} for the shared utility calling the Fetch API.\n */\nexport const fetchExchange: Exchange = ({ forward, dispatchDebug }) => {\n  return ops$ => {\n    const fetchResults$ = pipe(\n      ops$,\n      filter(operation => {\n        return (\n          operation.kind !== 'teardown' &&\n          (operation.kind !== 'subscription' ||\n            !!operation.context.fetchSubscriptions)\n        );\n      }),\n      mergeMap(operation => {\n        const body = makeFetchBody(operation);\n        const url = makeFetchURL(operation, body);\n        const fetchOptions = makeFetchOptions(operation, body);\n\n        dispatchDebug({\n          type: 'fetchRequest',\n          message: 'A fetch request is being executed.',\n          operation,\n          data: {\n            url,\n            fetchOptions,\n          },\n        });\n\n        const source = pipe(\n          makeFetchSource(operation, url, fetchOptions),\n          takeUntil(\n            pipe(\n              ops$,\n              filter(op => op.kind === 'teardown' && op.key === operation.key)\n            )\n          )\n        );\n\n        if (process.env.NODE_ENV !== 'production') {\n          return pipe(\n            source,\n            onPush(result => {\n              const error = !result.data ? result.error : undefined;\n\n              dispatchDebug({\n                type: error ? 'fetchError' : 'fetchSuccess',\n                message: `A ${\n                  error ? 'failed' : 'successful'\n                } fetch response has been returned.`,\n                operation,\n                data: {\n                  url,\n                  fetchOptions,\n                  value: error || result,\n                },\n              });\n            })\n          );\n        }\n\n        return source;\n      })\n    );\n\n    const forward$ = pipe(\n      ops$,\n      filter(operation => {\n        return (\n          operation.kind === 'teardown' ||\n          (operation.kind === 'subscription' &&\n            !operation.context.fetchSubscriptions)\n        );\n      }),\n      forward\n    );\n\n    return merge([fetchResults$, forward$]);\n  };\n};\n", "import { share } from 'wonka';\nimport type { ExchangeIO, Exchange, ExchangeInput } from '../types';\n\n/** Composes an array of Exchanges into a single one.\n *\n * @param exchanges - An array of {@link Exchange | Exchanges}.\n * @returns - A composed {@link Exchange}.\n *\n * @remarks\n * `composeExchanges` returns an {@link Exchange} that when instantiated\n * composes the array of passed `Exchange`s into one, calling them from\n * right to left, with the prior `Exchange`’s {@link ExchangeIO} function\n * as the {@link ExchangeInput.forward} input.\n *\n * This simply merges all exchanges into one and is used by the {@link Client}\n * to merge the `exchanges` option it receives.\n *\n * @throws\n * In development, if {@link ExchangeInput.forward} is called repeatedly\n * by an {@link Exchange} an error is thrown, since `forward()` must only\n * be called once per `Exchange`.\n */\nexport const composeExchanges =\n  (exchanges: Exchange[]): Exchange =>\n  ({ client, forward, dispatchDebug }: ExchangeInput): ExchangeIO =>\n    exchanges.reduceRight((forward, exchange) => {\n      let forwarded = false;\n      return exchange({\n        client,\n        forward(operations$) {\n          if (process.env.NODE_ENV !== 'production') {\n            if (forwarded)\n              throw new Error(\n                'forward() must only be called once in each Exchange.'\n              );\n            forwarded = true;\n          }\n          return share(forward(share(operations$)));\n        },\n        dispatchDebug(event) {\n          dispatchDebug({\n            timestamp: Date.now(),\n            source: exchange.name,\n            ...event,\n          });\n        },\n      });\n    }, forward);\n", "import { mergeMap, fromV<PERSON><PERSON>, from<PERSON>romise, pipe } from 'wonka';\nimport type { Operation, OperationResult, Exchange } from '../types';\nimport type { CombinedError } from '../utils';\n\n/** Options for the `mapExchange` allowing it to react to incoming operations, results, or errors. */\nexport interface MapExchangeOpts {\n  /** Accepts a callback for incoming `Operation`s.\n   *\n   * @param operation - An {@link Operation} that the {@link mapExchange} received.\n   * @returns optionally a new {@link Operation} replacing the original.\n   *\n   * @remarks\n   * You may return new {@link Operation | Operations} from this function replacing\n   * the original that the {@link mapExchange} received.\n   * It’s recommended that you use the {@link makeOperation} utility to create a copy\n   * of the original when you do this. (However, this isn’t required)\n   *\n   * Hint: The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onOperation?(operation: Operation): Promise<Operation> | Operation | void;\n  /** Accepts a callback for incoming `OperationResult`s.\n   *\n   * @param result - An {@link OperationResult} that the {@link mapExchange} received.\n   * @returns optionally a new {@link OperationResult} replacing the original.\n   *\n   * @remarks\n   * This callback may optionally return a new {@link OperationResult} that replaces the original,\n   * which you can use to modify incoming API results.\n   *\n   * Hint: The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onResult?(\n    result: OperationResult\n  ): Promise<OperationResult> | OperationResult | void;\n  /** Accepts a callback for incoming `CombinedError`s.\n   *\n   * @param error - A {@link CombinedError} that an incoming {@link OperationResult} contained.\n   * @param operation - The {@link Operation} of the incoming {@link OperationResult}.\n   *\n   * @remarks\n   * The callback may also be promisified and return a new {@link Operation} asynchronously,\n   * provided you place your {@link mapExchange} after all synchronous {@link Exchange | Exchanges},\n   * like after your `cacheExchange`.\n   */\n  onError?(error: CombinedError, operation: Operation): void;\n}\n\n/** Creates an `Exchange` mapping over incoming operations, results, and/or errors.\n *\n * @param opts - A {@link MapExchangeOpts} configuration object, containing the callbacks the `mapExchange` will use.\n * @returns the created {@link Exchange}\n *\n * @remarks\n * The `mapExchange` may be used to react to or modify incoming {@link Operation | Operations}\n * and {@link OperationResult | OperationResults}. Optionally, it can also modify these\n * asynchronously, when a promise is returned from the callbacks.\n *\n * This is useful to, for instance, add an authentication token to a given request, when\n * the `@urql/exchange-auth` package would be overkill.\n *\n * It can also accept an `onError` callback, which can be used to react to incoming\n * {@link CombinedError | CombinedErrors} on results, and trigger side-effects.\n *\n */\nexport const mapExchange = ({\n  onOperation,\n  onResult,\n  onError,\n}: MapExchangeOpts): Exchange => {\n  return ({ forward }) =>\n    ops$ => {\n      return pipe(\n        pipe(\n          ops$,\n          mergeMap(operation => {\n            const newOperation =\n              (onOperation && onOperation(operation)) || operation;\n            return 'then' in newOperation\n              ? fromPromise(newOperation)\n              : fromValue(newOperation);\n          })\n        ),\n        forward,\n        mergeMap(result => {\n          if (onError && result.error) onError(result.error, result.operation);\n          const newResult = (onResult && onResult(result)) || result;\n          return 'then' in newResult\n            ? fromPromise(newResult)\n            : fromValue(newResult);\n        })\n      );\n    };\n};\n", "import { filter, pipe, tap } from 'wonka';\nimport type { ExchangeIO, ExchangeInput } from '../types';\n\n/** Used by the `Client` as the last exchange to warn about unhandled operations.\n *\n * @remarks\n * In a normal setup, some operations may go unhandled when a {@link Client} isn’t set up\n * with the right exchanges.\n * For instance, a `Client` may be missing a fetch exchange, or an exchange handling subscriptions.\n * This {@link Exchange} is added by the `Client` automatically to log warnings about unhandled\n * {@link Operaiton | Operations} in development.\n */\nexport const fallbackExchange: ({\n  dispatchDebug,\n}: Pick<ExchangeInput, 'dispatchDebug'>) => ExchangeIO =\n  ({ dispatchDebug }) =>\n  ops$ => {\n    if (process.env.NODE_ENV !== 'production') {\n      ops$ = pipe(\n        ops$,\n        tap(operation => {\n          if (\n            operation.kind !== 'teardown' &&\n            process.env.NODE_ENV !== 'production'\n          ) {\n            const message = `No exchange has handled operations of kind \"${operation.kind}\". Check whether you've added an exchange responsible for these operations.`;\n\n            dispatchDebug({\n              type: 'fallbackCatch',\n              message,\n              operation,\n            });\n            console.warn(message);\n          }\n        })\n      );\n    }\n\n    // All operations that skipped through the entire exchange chain should be filtered from the output\n    return filter((_x): _x is never => false)(ops$);\n  };\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\n\nimport type { Source, Subscription } from 'wonka';\nimport {\n  lazy,\n  filter,\n  makeSubject,\n  onEnd,\n  onPush,\n  onStart,\n  pipe,\n  share,\n  take,\n  takeUntil,\n  takeWhile,\n  publish,\n  subscribe,\n  switchMap,\n  fromValue,\n  merge,\n  map,\n} from 'wonka';\n\nimport { composeExchanges } from './exchanges';\nimport { fallbackExchange } from './exchanges/fallback';\n\nimport type {\n  DocumentInput,\n  AnyVariables,\n  Exchange,\n  ExchangeInput,\n  GraphQLRequest,\n  Operation,\n  OperationInstance,\n  OperationContext,\n  OperationResult,\n  OperationResultSource,\n  OperationType,\n  RequestPolicy,\n  DebugEvent,\n} from './types';\n\nimport {\n  createRequest,\n  withPromise,\n  maskTypename,\n  noop,\n  makeOperation,\n  getOperationType,\n} from './utils';\n\n/** Configuration options passed when creating a new {@link Client}.\n *\n * @remarks\n * The `ClientOptions` are passed when creating a new {@link Client}, and\n * are used to instantiate the pipeline of {@link Exchange | Exchanges}, configure\n * options used to initialize {@link OperationContext | OperationContexts}, or to\n * change the general behaviour of the {@link Client}.\n */\nexport interface ClientOptions {\n  /** Target URL used by fetch exchanges to make GraphQL API requests to.\n   *\n   * @remarks\n   * This is the URL that fetch exchanges will call to make GraphQL API requests.\n   * This value is copied to {@link OperationContext.url}.\n   */\n  url: string;\n  /** Additional options used by fetch exchanges that'll be passed to the `fetch` call on API requests.\n   *\n   * @remarks\n   * The options in this object or an object returned by a callback function will be merged into the\n   * {@link RequestInit} options passed to the `fetch` call.\n   *\n   * Hint: If you're trying to implement more complex changes per {@link Operation}, it's worth considering\n   * to use the {@link mapExchange} instead, which allows you to change `Operation`s and `OperationResult`s.\n   *\n   * Hint: If you're trying to use this as a function for authentication, consider checking out\n   * `@urql/exchange-auth` instead, which allows you to handle refresh auth flows, and more\n   * complex auth flows.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/fetch} for a description of this object.\n   */\n  fetchOptions?: RequestInit | (() => RequestInit);\n  /** A `fetch` function polyfill used by fetch exchanges to make API calls.\n   *\n   * @remarks\n   * This is the fetch polyfill used by any fetch exchange to make an API request. By default, when this\n   * option isn't set, any fetch exchange will attempt to use the globally available `fetch` function\n   * to make a request instead.\n   *\n   * It's recommended to only pass a polyfill, if any of the environments you're running the {@link Client}\n   * in don't support the Fetch API natively.\n   *\n   * Hint: If you're using the \"Incremental Delivery\" multipart spec, for instance with `@defer` directives,\n   * you're better off using the native `fetch` function, or must ensure that your polyfill supports streamed\n   * results. However, a \"Streaming requests unsupported\" error will be thrown, to let you know that your `fetch`\n   * API doesn't support incrementally streamed responses, if this mode is used.\n   *\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API} for the Fetch API spec.\n   */\n  fetch?: typeof fetch;\n  /** Allows a subscription to be executed using a `fetch` API request.\n   *\n   * @remarks\n   * If your API supports the `text/event-stream` and/or `multipart/mixed` response protocol, and you use\n   * this protocol to handle subscriptions, then you may switch this flag to `true`.\n   *\n   * This means you won’t have to create a {@link subscriptionExchange} to handle subscriptions with an\n   * external transport, and will instead be able to use GraphQL over HTTP transports.\n   */\n  fetchSubscriptions?: boolean;\n  /** A list of `Exchange`s that will be used to create the `Client`'s execution pipeline.\n   *\n   * @remarks\n   * The {@link Client} accepts and composes a list of {@link Exchange | Exchanges} into an “exchange pipeline”\n   * which receive a stream of {@link Operation | Operations} the `Client` wishes to execute, and return a stream\n   * of {@link OperationResult | OperationResults}.\n   *\n   * This is the basis for how `urql` handles GraphQL operations, and exchanges handle the creation, execution,\n   * and control flow of exchanges for the `Client`.\n   *\n   * To easily get started you should consider using the {@link dedupExchange}, {@link cacheExchange} and {@link fetchExchange}\n   * these are all exported from the core package.\n   *\n   * @see {@link https://urql.dev/goto/docs/architecture/#the-client-and-exchanges} for more information\n   * on what `Exchange`s are and how they work.\n   */\n  exchanges: Exchange[];\n  /** A configuration flag indicating whether support for \"Suspense\" is activated.\n   *\n   * @remarks\n   * This configuration flag is only relevant for using `urql` with the React or Preact bindings.\n   * When activated it allows `useQuery` to \"suspend\" instead of returning a loading state, which\n   * will stop updates in a querying component and instead cascade\n   * to a higher suspense boundary for a loading state.\n   *\n   * Hint: While, when this option is enabled, by default all `useQuery` hooks will suspense, you can\n   * disable Suspense selectively for each hook.\n   *\n   * @see {@link https://beta.reactjs.org/blog/2022/03/29/react-v18#new-suspense-features} for more information on React Suspense.\n   */\n  suspense?: boolean;\n  /** The request and caching strategy that all `Operation`s on this `Client` will use by default.\n   *\n   * @remarks\n   * The {@link RequestPolicy} instructs cache exchanges how to use and treat their cached results.\n   * By default `cache-first` is set and used, which will use cache results, and only make an API request\n   * on a cache miss.\n   *\n   * The `requestPolicy` can be overriden per operation, since it's added to the {@link OperationContext},\n   * which allows you to change the policy per `Operation`, rather than changing it by default here.\n   *\n   * Hint: We don’t recommend changing this from the default `cache-first` option, unless you know what\n   * you‘re doing. Setting this to `cache-and-network` is not recommend and may not lead to the behaviour\n   * you expect. If you’re looking to always update your cache frequently, use `@urql/exchange-request-policy`\n   * instead.\n   */\n  requestPolicy?: RequestPolicy;\n  /** Instructs fetch exchanges to use a GET request.\n   *\n   * @remarks\n   * This changes the {@link OperationContext.preferGetMethod} option, which tells fetch exchanges\n   * to use GET requests for queries instead of POST requests.\n   *\n   * When set to `true` or `'within-url-limit'`, built-in fetch exchanges will always attempt to send query\n   * operations as GET requests, unless the resulting URL exceeds a length of 2,048 characters.\n   * If you want to bypass this restriction, set this option to `'force'` instead, to always send GET.\n   * requests for queries.\n   */\n  preferGetMethod?: boolean | 'force' | 'within-url-limit';\n  /** Instructs the `Client` to remove `__typename` properties on all results.\n   *\n   * @deprecated Not recommended over modelling inputs manually (See #3299)\n   *\n   * @remarks\n   * By default, cache exchanges will alter your GraphQL documents to request `__typename` fields\n   * for all selections. However, this means that your GraphQL data will now contain `__typename` fields you\n   * didn't ask for. This is why the {@link Client} supports “masking” this field by marking it\n   * as non-enumerable via this option.\n   *\n   * Only use this option if you absolutely have to. It's popular to model mutation inputs in\n   * GraphQL schemas after the object types they modify, and if you're using this option to make\n   * it possible to directly pass objects from results as inputs to your mutation variables, it's\n   * more performant and idomatic to instead create a new input object.\n   *\n   * Hint: With `@urql/exchange-graphcache` you will never need this option, as it selects fields on\n   * the client-side according to which fields you specified, rather than the fields it modified.\n   *\n   * @see {@link https://spec.graphql.org/October2021/#sec-Type-Name-Introspection} for more information\n   * on typename introspection via the `__typename` field.\n   */\n  maskTypename?: boolean;\n}\n\n/** The `Client` is the central hub for your GraphQL operations and holds `urql`'s state.\n *\n * @remarks\n * The `Client` manages your active GraphQL operations and their state, and contains the\n * {@link Exchange} pipeline to execute your GraphQL operations.\n *\n * It contains methods that allow you to execute GraphQL operations manually, but the `Client`\n * is also interacted with by bindings (for React, Preact, Vue, Svelte, etc) to execute GraphQL\n * operations.\n *\n * While {@link Exchange | Exchanges} are ultimately responsible for the control flow of operations,\n * sending API requests, and caching, the `Client` still has the important responsibility for\n * creating operations, managing consumers of active operations, sharing results for operations,\n * and more tasks as a “central hub”.\n *\n * @see {@link https://urql.dev/goto/docs/architecture/#requests-and-operations-on-the-client} for more information\n * on what the `Client` is and does.\n */\nexport interface Client {\n  new (options: ClientOptions): Client;\n\n  /** Exposes the stream of `Operation`s that is passed to the `Exchange` pipeline.\n   *\n   * @remarks\n   * This is a Wonka {@link Source} that issues the {@link Operation | Operations} going into\n   * the exchange pipeline.\n   * @internal\n   */\n  operations$: Source<Operation>;\n\n  /** Flag indicating whether support for “Suspense” is activated.\n   *\n   * @remarks\n   * This flag indicates whether support for “Suspense” has been activated via the\n   * {@link ClientOptions.suspense} flag.\n   *\n   * When this is enabled, the {@link Client} itself doesn’t function any differently, and the flag\n   * only serves as an instructions for the React/Preact bindings to change their behaviour.\n   *\n   * @see {@link ClientOptions.suspense} for more information.\n   * @internal\n   */\n  suspense: boolean;\n\n  /** Dispatches an `Operation` to the `Exchange` pipeline, if this `Operation` is active.\n   *\n   * @remarks\n   * This method is frequently used in {@link Exchange | Exchanges}, for instance caches, to reexecute\n   * an operation. It’s often either called because an `Operation` will need to be queried against the\n   * cache again, if a cache result has changed or been invalidated, or it’s called with an {@link Operation}'s\n   * {@link RequestPolicy} set to `network-only` to issue a network request.\n   *\n   * This method will only dispatch an {@link Operation} if it has active consumers, meaning,\n   * active subscribers to the sources of {@link OperationResult}. For instance, if no bindings\n   * (e.g. `useQuery`) is subscribed to the `Operation`, then `reexecuteOperation` will do nothing.\n   *\n   * All operations are put onto a queue and executed after a micro-tick. The queue of operations is\n   * emptied eagerly and synchronously, similar to a trampoline scheduler.\n   */\n  reexecuteOperation(operation: Operation): void;\n\n  /** Subscribe method to add an event listener to debug events.\n   *\n   * @param onEvent - A callback called with new debug events, each time an `Exchange` issues them.\n   * @returns A Wonka {@link Subscription} which is used to optionally terminate the event listener.\n   *\n   * @remarks\n   * This is a method that's only available in development, and allows the `urql-devtools` to receive\n   * to debug events that are issued by exchanges, giving the devtools more information about the flow\n   * and execution of {@link Operation | Operations}.\n   *\n   * @see {@link DebugEventTypes} for a description of all debug events.\n   * @internal\n   */\n  subscribeToDebugTarget?(onEvent: (event: DebugEvent) => void): Subscription;\n\n  /** Creates an `Operation` from a `GraphQLRequest` and optionally, overriding `OperationContext` options.\n   *\n   * @param kind - The {@link OperationType} of GraphQL operation, i.e. `query`, `mutation`, or `subscription`.\n   * @param request - A {@link GraphQLRequest} created prior to calling this method.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns An {@link Operation} created from the parameters.\n   *\n   * @remarks\n   * This method is expected to be called with a `kind` set to the `OperationType` of the GraphQL operation.\n   * In development, this is enforced by checking that the GraphQL document's operation matches this `kind`.\n   *\n   * Hint: While bindings will use this method combined with {@link Client.executeRequestOperation}, if\n   * you’re executing operations manually, you can use one of the other convenience methods instead.\n   *\n   * @see {@link Client.executeRequestOperation} for the method used to execute operations.\n   * @see {@link createRequest} which creates a `GraphQLRequest` from a `DocumentNode` and variables.\n   */\n  createRequestOperation<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    kind: OperationType,\n    request: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): Operation<Data, Variables>;\n\n  /** Creates a `Source` that executes the `Operation` and issues `OperationResult`s for this `Operation`.\n   *\n   * @param operation - {@link Operation} that will be executed.\n   * @returns A Wonka {@link Source} of {@link OperationResult | OperationResults} for the passed `Operation`.\n   *\n   * @remarks\n   * The {@link Operation} will be dispatched to the pipeline of {@link Exchange | Exchanges} when\n   * subscribing to the returned {@link Source}, which issues {@link OperationResult | OperationResults}\n   * belonging to this `Operation`.\n   *\n   * Internally, {@link OperationResult | OperationResults} are filtered and deliverd to this source by\n   * comparing the {@link Operation.key} on the operation and the {@link OperationResult.operation}.\n   * For mutations, the {@link OperationContext._instance | `OperationContext._instance`} will additionally be compared, since two mutations\n   * with, even given the same variables, will have two distinct results and will be executed separately.\n   *\n   * The {@link Client} dispatches the {@link Operation} when we subscribe to the returned {@link Source}\n   * and will from then on consider the `Operation` as “active” until we unsubscribe. When all consumers unsubscribe\n   * from an `Operation` and it becomes “inactive” a `teardown` signal will be dispatched to the\n   * {@link Exchange | Exchanges}.\n   *\n   * Hint: While bindings will use this method, if you’re executing operations manually, you can use one\n   * of the other convenience methods instead, like {@link Client.executeQuery} et al.\n   */\n  executeRequestOperation<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    operation: Operation<Data, Variables>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL query operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the query operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link OperationResultSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.query` method is useful to programmatically create and issue a GraphQL query operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Since it returns a {@link OperationResultSource} it may be chained with a `toPromise()` call to only\n   * await a single result in an async function.\n   *\n   * Hint: This is the recommended way to create queries programmatically when not using the bindings,\n   * or when you’re trying to get a single, promisified result.\n   *\n   * @example\n   * ```ts\n   * const getBookQuery = gql`\n   *   query GetBook($id: ID!) {\n   *     book(id: $id) {\n   *       id\n   *       name\n   *       author {\n   *         name\n   *       }\n   *     }\n   *   }\n   * `;\n   *\n   * async function getBook(id) {\n   *   const result = await client.query(getBookQuery, { id }).toPromise();\n   *   if (result.error) {\n   *     throw result.error;\n   *   }\n   *\n   *   return result.data.book;\n   * }\n   * ```\n   */\n  query<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Returns the first synchronous result a `Client` provides for a given operation.\n   *\n   * @param query - a GraphQL document containing the query operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns An {@link OperationResult} if one became available synchronously or `null`.\n   *\n   * @remarks\n   * The `Client.readQuery` method returns a result synchronously or defaults to `null`. This is useful\n   * as it limits the result for a query operation to whatever the cache {@link Exchange} of a {@link Client}\n   * had stored and available at that moment.\n   *\n   * In `urql`, it's expected that cache exchanges return their results synchronously. The bindings\n   * and this method exploit this by using synchronous results, like these, to check what data is already\n   * in the cache.\n   *\n   * This method is similar to what all bindings do to synchronously provide the initial state for queries,\n   * regardless of whether effects afterwards that subscribe to the query operation update this state synchronously\n   * or asynchronously.\n   */\n  readQuery<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResult<Data, Variables> | null;\n\n  /** Creates a `Source` that executes the GraphQL query operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeQuery` method is used to programmatically issue a GraphQL query operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.query} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeQuery<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL subscription operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the subscription operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A Wonka {@link Source} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.subscription` method is useful to programmatically create and issue a GraphQL subscription operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Hint: This is the recommended way to create subscriptions programmatically when not using the bindings.\n   *\n   * @example\n   * ```ts\n   * import { pipe, subscribe } from 'wonka';\n   *\n   * const getNewsSubscription = gql`\n   *   subscription GetNews {\n   *     breakingNews {\n   *       id\n   *       text\n   *       createdAt\n   *     }\n   *   }\n   * `;\n   *\n   * function subscribeToBreakingNews() {\n   *   const subscription = pipe(\n   *     client.subscription(getNewsSubscription, {}),\n   *     subscribe(result => {\n   *       if (result.data) {\n   *         console.log(result.data.breakingNews.text);\n   *       }\n   *     })\n   *   );\n   *\n   *   return subscription.unsubscribe;\n   * }\n   * ```\n   */\n  subscription<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL subscription operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeSubscription` method is used to programmatically issue a GraphQL subscription operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.subscription} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeSubscription<\n    Data = any,\n    Variables extends AnyVariables = AnyVariables,\n  >(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL mutation operation created from the passed parameters.\n   *\n   * @param query - a GraphQL document containing the mutation operation that will be executed.\n   * @param variables - the variables used to execute the operation.\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.mutation` method is useful to programmatically create and issue a GraphQL mutation operation.\n   * It automatically calls {@link createRequest}, {@link client.createRequestOperation}, and\n   * {@link client.executeRequestOperation} for you, and is a convenience method.\n   *\n   * Since it returns a {@link PromisifiedSource} it may be chained with a `toPromise()` call to only\n   * await a single result in an async function. Since mutations will only typically issue one result,\n   * using this method is recommended.\n   *\n   * Hint: This is the recommended way to create mutations programmatically when not using the bindings,\n   * or when you’re trying to get a single, promisified result.\n   *\n   * @example\n   * ```ts\n   * const createPostMutation = gql`\n   *   mutation CreatePost($text: String!) {\n   *     createPost(text: $text) {\n   *       id\n   *       text\n   *     }\n   *   }\n   * `;\n   *\n   * async function createPost(text) {\n   *   const result = await client.mutation(createPostMutation, {\n   *     text,\n   *   }).toPromise();\n   *   if (result.error) {\n   *     throw result.error;\n   *   }\n   *\n   *   return result.data.createPost;\n   * }\n   * ```\n   */\n  mutation<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context?: Partial<OperationContext>\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n\n  /** Creates a `Source` that executes the GraphQL mutation operation for the passed `GraphQLRequest`.\n   *\n   * @param query - a {@link GraphQLRequest}\n   * @param opts - {@link OperationContext} options that'll override and be merged with options from the {@link ClientOptions}.\n   * @returns A {@link PromisifiedSource} issuing the {@link OperationResult | OperationResults} for the GraphQL operation.\n   *\n   * @remarks\n   * The `Client.executeMutation` method is used to programmatically issue a GraphQL mutation operation.\n   * It automatically calls {@link client.createRequestOperation} and {@link client.executeRequestOperation} for you,\n   * but requires you to create a {@link GraphQLRequest} using {@link createRequest} yourself first.\n   *\n   * @see {@link Client.mutation} for a method that doesn't require calling {@link createRequest} yourself.\n   */\n  executeMutation<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: GraphQLRequest<Data, Variables>,\n    opts?: Partial<OperationContext> | undefined\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n}\n\nexport const Client: new (opts: ClientOptions) => Client = function Client(\n  this: Client | {},\n  opts: ClientOptions\n) {\n  if (process.env.NODE_ENV !== 'production' && !opts.url) {\n    throw new Error('You are creating an urql-client without a url.');\n  }\n\n  let ids = 0;\n\n  const replays = new Map<number, OperationResult>();\n  const active: Map<number, Source<OperationResult>> = new Map();\n  const dispatched = new Set<number>();\n  const queue: Operation[] = [];\n\n  const baseOpts = {\n    url: opts.url,\n    fetchSubscriptions: opts.fetchSubscriptions,\n    fetchOptions: opts.fetchOptions,\n    fetch: opts.fetch,\n    preferGetMethod: opts.preferGetMethod,\n    requestPolicy: opts.requestPolicy || 'cache-first',\n  };\n\n  // This subject forms the input of operations; executeOperation may be\n  // called to dispatch a new operation on the subject\n  const operations = makeSubject<Operation>();\n\n  function nextOperation(operation: Operation) {\n    if (\n      operation.kind === 'mutation' ||\n      operation.kind === 'teardown' ||\n      !dispatched.has(operation.key)\n    ) {\n      if (operation.kind === 'teardown') {\n        dispatched.delete(operation.key);\n      } else if (operation.kind !== 'mutation') {\n        dispatched.add(operation.key);\n      }\n      operations.next(operation);\n    }\n  }\n\n  // We define a queued dispatcher on the subject, which empties the queue when it's\n  // activated to allow `reexecuteOperation` to be trampoline-scheduled\n  let isOperationBatchActive = false;\n  function dispatchOperation(operation?: Operation | void) {\n    if (operation) nextOperation(operation);\n\n    if (!isOperationBatchActive) {\n      isOperationBatchActive = true;\n      while (isOperationBatchActive && (operation = queue.shift()))\n        nextOperation(operation);\n      isOperationBatchActive = false;\n    }\n  }\n\n  /** Defines how result streams are created */\n  const makeResultSource = (operation: Operation) => {\n    let result$ = pipe(\n      results$,\n      // Filter by matching key (or _instance if it’s set)\n      filter(\n        (res: OperationResult) =>\n          res.operation.kind === operation.kind &&\n          res.operation.key === operation.key &&\n          (!res.operation.context._instance ||\n            res.operation.context._instance === operation.context._instance)\n      ),\n      // End the results stream when an active teardown event is sent\n      takeUntil(\n        pipe(\n          operations.source,\n          filter(op => op.kind === 'teardown' && op.key === operation.key)\n        )\n      )\n    );\n\n    // Mask typename properties if the option for it is turned on\n    if (opts.maskTypename) {\n      result$ = pipe(\n        result$,\n        map(res => ({ ...res, data: maskTypename(res.data, true) }))\n      );\n    }\n\n    if (operation.kind !== 'query') {\n      // Interrupt subscriptions and mutations when they have no more results\n      result$ = pipe(\n        result$,\n        takeWhile(result => !!result.hasNext, true)\n      );\n    } else {\n      result$ = pipe(\n        result$,\n        // Add `stale: true` flag when a new operation is sent for queries\n        switchMap(result => {\n          const value$ = fromValue(result);\n          return result.stale || result.hasNext\n            ? value$\n            : merge([\n                value$,\n                pipe(\n                  operations.source,\n                  filter(op => op.key === operation.key),\n                  take(1),\n                  map(() => {\n                    result.stale = true;\n                    return result;\n                  })\n                ),\n              ]);\n        })\n      );\n    }\n\n    if (operation.kind !== 'mutation') {\n      result$ = pipe(\n        result$,\n        // Store replay result\n        onPush(result => {\n          if (result.stale) {\n            // If the current result has queued up an operation of the same\n            // key, then `stale` refers to it\n            for (const operation of queue) {\n              if (operation.key === result.operation.key) {\n                dispatched.delete(operation.key);\n                break;\n              }\n            }\n          } else if (!result.hasNext) {\n            dispatched.delete(operation.key);\n          }\n          replays.set(operation.key, result);\n        }),\n        // Cleanup active states on end of source\n        onEnd(() => {\n          // Delete the active operation handle\n          dispatched.delete(operation.key);\n          replays.delete(operation.key);\n          active.delete(operation.key);\n          // Interrupt active queue\n          isOperationBatchActive = false;\n          // Delete all queued up operations of the same key on end\n          for (let i = queue.length - 1; i >= 0; i--)\n            if (queue[i].key === operation.key) queue.splice(i, 1);\n          // Dispatch a teardown signal for the stopped operation\n          nextOperation(\n            makeOperation('teardown', operation, operation.context)\n          );\n        })\n      );\n    } else {\n      result$ = pipe(\n        result$,\n        // Send mutation operation on start\n        onStart(() => {\n          nextOperation(operation);\n        })\n      );\n    }\n\n    return share(result$);\n  };\n\n  const instance: Client =\n    this instanceof Client ? this : Object.create(Client.prototype);\n  const client: Client = Object.assign(instance, {\n    suspense: !!opts.suspense,\n    operations$: operations.source,\n\n    reexecuteOperation(operation: Operation) {\n      // Reexecute operation only if any subscribers are still subscribed to the\n      // operation's exchange results\n      if (operation.kind === 'teardown') {\n        dispatchOperation(operation);\n      } else if (operation.kind === 'mutation' || active.has(operation.key)) {\n        let queued = false;\n        for (let i = 0; i < queue.length; i++)\n          queued = queued || queue[i].key === operation.key;\n        if (!queued) dispatched.delete(operation.key);\n        queue.push(operation);\n        Promise.resolve().then(dispatchOperation);\n      }\n    },\n\n    createRequestOperation(kind, request, opts) {\n      if (!opts) opts = {};\n\n      let requestOperationType: string | undefined;\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        kind !== 'teardown' &&\n        (requestOperationType = getOperationType(request.query)) !== kind\n      ) {\n        throw new Error(\n          `Expected operation of type \"${kind}\" but found \"${requestOperationType}\"`\n        );\n      }\n\n      return makeOperation(kind, request, {\n        _instance:\n          kind === 'mutation'\n            ? ((ids = (ids + 1) | 0) as OperationInstance)\n            : undefined,\n        ...baseOpts,\n        ...opts,\n        requestPolicy: opts.requestPolicy || baseOpts.requestPolicy,\n        suspense: opts.suspense || (opts.suspense !== false && client.suspense),\n      });\n    },\n\n    executeRequestOperation(operation) {\n      if (operation.kind === 'mutation') {\n        return withPromise(makeResultSource(operation));\n      }\n\n      return withPromise(\n        lazy<OperationResult>(() => {\n          let source = active.get(operation.key);\n          if (!source) {\n            active.set(operation.key, (source = makeResultSource(operation)));\n          }\n\n          source = pipe(\n            source,\n            onStart(() => {\n              dispatchOperation(operation);\n            })\n          );\n\n          const replay = replays.get(operation.key);\n          if (\n            operation.kind === 'query' &&\n            replay &&\n            (replay.stale || replay.hasNext)\n          ) {\n            return pipe(\n              merge([\n                source,\n                pipe(\n                  fromValue(replay),\n                  filter(replay => replay === replays.get(operation.key))\n                ),\n              ]),\n              switchMap(fromValue)\n            );\n          } else {\n            return source;\n          }\n        })\n      );\n    },\n\n    executeQuery(query, opts) {\n      const operation = client.createRequestOperation('query', query, opts);\n      return client.executeRequestOperation(operation);\n    },\n\n    executeSubscription(query, opts) {\n      const operation = client.createRequestOperation(\n        'subscription',\n        query,\n        opts\n      );\n      return client.executeRequestOperation(operation);\n    },\n\n    executeMutation(query, opts) {\n      const operation = client.createRequestOperation('mutation', query, opts);\n      return client.executeRequestOperation(operation);\n    },\n\n    readQuery(query, variables, context) {\n      let result: OperationResult | null = null;\n\n      pipe(\n        client.query(query, variables, context),\n        subscribe(res => {\n          result = res;\n        })\n      ).unsubscribe();\n\n      return result;\n    },\n\n    query(query, variables, context) {\n      return client.executeQuery(createRequest(query, variables), context);\n    },\n\n    subscription(query, variables, context) {\n      return client.executeSubscription(\n        createRequest(query, variables),\n        context\n      );\n    },\n\n    mutation(query, variables, context) {\n      return client.executeMutation(createRequest(query, variables), context);\n    },\n  } as Client);\n\n  let dispatchDebug: ExchangeInput['dispatchDebug'] = noop;\n  if (process.env.NODE_ENV !== 'production') {\n    const { next, source } = makeSubject<DebugEvent>();\n    client.subscribeToDebugTarget = (onEvent: (e: DebugEvent) => void) =>\n      pipe(source, subscribe(onEvent));\n    dispatchDebug = next as ExchangeInput['dispatchDebug'];\n  }\n\n  // All exchange are composed into a single one and are called using the constructed client\n  // and the fallback exchange stream\n  const composedExchange = composeExchanges(opts.exchanges);\n\n  // All exchanges receive inputs using which they can forward operations to the next exchange\n  // and receive a stream of results in return, access the client, or dispatch debugging events\n  // All operations then run through the Exchange IOs in a pipeline-like fashion\n  const results$ = share(\n    composedExchange({\n      client,\n      dispatchDebug,\n      forward: fallbackExchange({ dispatchDebug }),\n    })(operations.source)\n  );\n\n  // Prevent the `results$` exchange pipeline from being closed by active\n  // cancellations cascading up from components\n  pipe(results$, publish);\n\n  return client;\n} as any;\n\n/** Accepts `ClientOptions` and creates a `Client`.\n * @param opts - A {@link ClientOptions} objects with options for the `Client`.\n * @returns A {@link Client} instantiated with `opts`.\n */\nexport const createClient = Client as any as (opts: ClientOptions) => Client;\n", "import * as React from 'react';\nimport type { Client } from '@urql/core';\n\nconst OBJ = {};\n\n/** `urql`'s React Context.\n *\n * @remarks\n * The React Context that `urql`’s {@link Client} will be provided with.\n * You may use the reexported {@link Provider} to provide a `Client` as well.\n */\nexport const Context: import('react').Context<Client | object> =\n  React.createContext(OBJ);\n\n/** Provider for `urql`'s {@link Client} to GraphQL hooks.\n *\n * @remarks\n * `Provider` accepts a {@link Client} and provides it to all GraphQL hooks,\n * and {@link useClient}.\n *\n * You should make sure to create a {@link Client} and provide it with the\n * `Provider` to parts of your component tree that use GraphQL hooks.\n *\n * @example\n * ```tsx\n * import { Provider } from 'urql';\n * // All of `@urql/core` is also re-exported by `urql`:\n * import { Client, cacheExchange, fetchExchange } from '@urql/core';\n *\n * const client = new Client({\n *   url: 'https://API',\n *   exchanges: [cacheExchange, fetchExchange],\n * });\n *\n * const App = () => (\n *   <Provider value={client}>\n *     <Component />\n *   </Provider>\n * );\n * ```\n */\nexport const Provider: React.Provider<Client | object> = Context.Provider;\n\n/** React Consumer component, providing the {@link Client} provided on a parent component.\n * @remarks\n * This is an alias for {@link Context.Consumer}.\n */\nexport const Consumer: React.Consumer<Client | object> = Context.Consumer;\n\nContext.displayName = 'UrqlContext';\n\n/** Hook returning a {@link Client} from {@link Context}.\n *\n * @remarks\n * `useClient` is a convenience hook, which accesses `urql`'s {@link Context}\n * and returns the {@link Client} defined on it.\n *\n * This will be the {@link Client} you passed to a {@link Provider}\n * you wrapped your elements containing this hook with.\n *\n * @throws\n * In development, if the component you call `useClient()` in is\n * not wrapped in a {@link Provider}, an error is thrown.\n */\nexport const useClient = (): Client => {\n  const client = React.useContext(Context);\n\n  if (client === OBJ && process.env.NODE_ENV !== 'production') {\n    const error =\n      \"No client has been specified using urql's Provider. please create a client and add a Provider.\";\n\n    console.error(error);\n    throw new Error(error);\n  }\n\n  return client as Client;\n};\n", "import * as React from 'react';\n\nexport const initialState = {\n  fetching: false,\n  stale: false,\n  error: undefined,\n  data: undefined,\n  extensions: undefined,\n  operation: undefined,\n};\n\n// Two operations are considered equal if they have the same key\nconst areOperationsEqual = (\n  a: { key: number } | undefined,\n  b: { key: number } | undefined\n) => {\n  return a === b || !!(a && b && a.key === b.key);\n};\n\n/**\n * Checks if two objects are shallowly different with a special case for\n * 'operation' where it compares the key if they are not the otherwise equal\n */\nconst isShallowDifferent = <T extends Record<string, any>>(a: T, b: T) => {\n  for (const key in a) if (!(key in b)) return true;\n  for (const key in b) {\n    if (\n      key === 'operation'\n        ? !areOperationsEqual(a[key], b[key])\n        : a[key] !== b[key]\n    ) {\n      return true;\n    }\n  }\n  return false;\n};\n\ninterface Stateish {\n  data?: any;\n  error?: any;\n  fetching: boolean;\n  stale: boolean;\n}\n\nexport const computeNextState = <T extends Stateish>(\n  prevState: T,\n  result: Partial<T>\n): T => {\n  const newState: T = {\n    ...prevState,\n    ...result,\n    data:\n      result.data !== undefined || result.error ? result.data : prevState.data,\n    fetching: !!result.fetching,\n    stale: !!result.stale,\n  };\n\n  return isShallowDifferent(prevState, newState) ? newState : prevState;\n};\n\nexport const hasDepsChanged = <T extends { length: number }>(a: T, b: T) => {\n  for (let i = 0, l = b.length; i < l; i++) if (a[i] !== b[i]) return true;\n  return false;\n};\n\nconst reactSharedInternals = (React as any)\n  .__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nexport function deferDispatch<Dispatch extends React.Dispatch<any>>(\n  setState: Dispatch,\n  value: Dispatch extends React.Dispatch<infer State> ? State : void\n) {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    !!reactSharedInternals &&\n    !!reactSharedInternals.ReactCurrentOwner &&\n    !!reactSharedInternals.ReactCurrentOwner.current\n  ) {\n    Promise.resolve(value).then(setState);\n  } else {\n    setState(value);\n  }\n}\n", "import * as React from 'react';\nimport { pipe, onPush, filter, toPromise, take } from 'wonka';\n\nimport type {\n  AnyVariables,\n  DocumentInput,\n  OperationResult,\n  OperationContext,\n  CombinedError,\n  Operation,\n} from '@urql/core';\nimport { createRequest } from '@urql/core';\n\nimport { useClient } from '../context';\nimport { deferDispatch, initialState } from './state';\n\n/** State of the last mutation executed by your {@link useMutation} hook.\n *\n * @remarks\n * `UseMutationState` is returned (in a tuple) by {@link useMutation} and\n * gives you the {@link OperationResult} of the last mutation executed\n * with {@link UseMutationExecute}.\n *\n * Even if the mutation document passed to {@link useMutation} changes,\n * the state isn’t reset, so you can keep displaying the previous result.\n */\nexport interface UseMutationState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> {\n  /** Indicates whether `useMutation` is currently executing a mutation. */\n  fetching: boolean;\n  /** Indicates that the mutation result is not fresh.\n   *\n   * @remarks\n   * The `stale` flag is set to `true` when a new result for the mutation\n   * is expected.\n   * This is mostly unused for mutations and will rarely affect you, and\n   * is more relevant for queries.\n   *\n   * @see {@link OperationResult.stale} for the source of this value.\n   */\n  stale: boolean;\n  /** The {@link OperationResult.data} for the executed mutation. */\n  data?: Data;\n  /** The {@link OperationResult.error} for the executed mutation. */\n  error?: CombinedError;\n  /** The {@link OperationResult.extensions} for the executed mutation. */\n  extensions?: Record<string, any>;\n  /** The {@link Operation} that the current state is for.\n   *\n   * @remarks\n   * This is the mutation {@link Operation} that has last been executed.\n   * When {@link UseQueryState.fetching} is `true`, this is the\n   * last `Operation` that the current state was for.\n   */\n  operation?: Operation<Data, Variables>;\n}\n\n/** Triggers {@link useMutation} to execute its GraphQL mutation operation.\n *\n * @param variables - variables using which the mutation will be executed.\n * @param context - optionally, context options that will be merged with the hook's\n * {@link UseQueryArgs.context} options and the `Client`’s options.\n * @returns the {@link OperationResult} of the mutation.\n *\n * @remarks\n * When called, {@link useMutation} will start the GraphQL mutation\n * it currently holds and use the `variables` passed to it.\n *\n * Once the mutation response comes back from the API, its\n * returned promise will resolve to the mutation’s {@link OperationResult}\n * and the {@link UseMutationState} will be updated with the result.\n *\n * @example\n * ```ts\n * const [result, executeMutation] = useMutation(UpdateTodo);\n * const start = async ({ id, title }) => {\n *   const result = await executeMutation({ id, title });\n * };\n */\nexport type UseMutationExecute<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> = (\n  variables: Variables,\n  context?: Partial<OperationContext>\n) => Promise<OperationResult<Data, Variables>>;\n\n/** Result tuple returned by the {@link useMutation} hook.\n *\n * @remarks\n * Similarly to a `useState` hook’s return value,\n * the first element is the {@link useMutation}’s state, updated\n * as mutations are executed with the second value, which is\n * used to start mutations and is a {@link UseMutationExecute}\n * function.\n */\nexport type UseMutationResponse<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> = [UseMutationState<Data, Variables>, UseMutationExecute<Data, Variables>];\n\n/** Hook to create a GraphQL mutation, run by passing variables to the returned execute function.\n *\n * @param query - a GraphQL mutation document which `useMutation` will execute.\n * @returns a {@link UseMutationResponse} tuple of a {@link UseMutationState} result,\n * and an execute function to start the mutation.\n *\n * @remarks\n * `useMutation` allows GraphQL mutations to be defined and keeps its state\n * after the mutation is started with the returned execute function.\n *\n * Given a GraphQL mutation document it returns state to keep track of the\n * mutation state and a {@link UseMutationExecute} function, which accepts\n * variables for the mutation to be executed.\n * Once called, the mutation executes and the state will be updated with\n * the mutation’s result.\n *\n * @see {@link https://urql.dev/goto/urql/docs/basics/react-preact/#mutations} for `useMutation` docs.\n *\n * @example\n * ```ts\n * import { gql, useMutation } from 'urql';\n *\n * const UpdateTodo = gql`\n *   mutation ($id: ID!, $title: String!) {\n *     updateTodo(id: $id, title: $title) {\n *       id, title\n *     }\n *   }\n * `;\n *\n * const UpdateTodo = () => {\n *   const [result, executeMutation] = useMutation(UpdateTodo);\n *   const start = async ({ id, title }) => {\n *     const result = await executeMutation({ id, title });\n *   };\n *   // ...\n * };\n * ```\n */\nexport function useMutation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(query: DocumentInput<Data, Variables>): UseMutationResponse<Data, Variables> {\n  const isMounted = React.useRef(true);\n  const client = useClient();\n\n  const [state, setState] =\n    React.useState<UseMutationState<Data, Variables>>(initialState);\n\n  const executeMutation = React.useCallback(\n    (variables: Variables, context?: Partial<OperationContext>) => {\n      deferDispatch(setState, { ...initialState, fetching: true });\n      return pipe(\n        client.executeMutation<Data, Variables>(\n          createRequest<Data, Variables>(query, variables),\n          context || {}\n        ),\n        onPush(result => {\n          if (isMounted.current) {\n            deferDispatch(setState, {\n              fetching: false,\n              stale: result.stale,\n              data: result.data,\n              error: result.error,\n              extensions: result.extensions,\n              operation: result.operation,\n            });\n          }\n        }),\n        filter(result => !result.hasNext),\n        take(1),\n        toPromise\n      );\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [client, query, setState]\n  );\n\n  React.useEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  return [state, executeMutation];\n}\n", "import * as React from 'react';\nimport type { AnyVariables, DocumentInput, GraphQLRequest } from '@urql/core';\nimport { createRequest } from '@urql/core';\n\n/** Creates a request from a query and variables but preserves reference equality if the key isn't changing\n * @internal\n */\nexport function useRequest<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  query: DocumentInput<Data, Variables>,\n  variables: Variables\n): GraphQLRequest<Data, Variables> {\n  const prev = React.useRef<undefined | GraphQLRequest<Data, Variables>>(\n    undefined\n  );\n\n  return React.useMemo(() => {\n    const request = createRequest<Data, Variables>(query, variables);\n    // We manually ensure reference equality if the key hasn't changed\n    if (prev.current !== undefined && prev.current.key === request.key) {\n      return prev.current;\n    } else {\n      prev.current = request;\n      return request;\n    }\n  }, [query, variables]);\n}\n", "import { pipe, subscribe } from 'wonka';\nimport type { Client, OperationResult } from '@urql/core';\n\ntype CacheEntry = OperationResult | Promise<unknown> | undefined;\n\ninterface Cache {\n  get(key: number): CacheEntry;\n  set(key: number, value: CacheEntry): void;\n  dispose(key: number): void;\n}\n\ninterface ClientWithCache extends Client {\n  _react?: Cache;\n}\n\nexport const getCacheForClient = (client: Client): Cache => {\n  if (!(client as Client<PERSON>ith<PERSON>ache)._react) {\n    const reclaim = new Set();\n    const map = new Map<number, CacheEntry>();\n\n    if (client.operations$ /* not available in mocks */) {\n      pipe(\n        client.operations$,\n        subscribe(operation => {\n          if (operation.kind === 'teardown' && reclaim.has(operation.key)) {\n            reclaim.delete(operation.key);\n            map.delete(operation.key);\n          }\n        })\n      );\n    }\n\n    (client as Client<PERSON>ith<PERSON>ache)._react = {\n      get(key) {\n        return map.get(key);\n      },\n      set(key, value) {\n        reclaim.delete(key);\n        map.set(key, value);\n      },\n      dispose(key) {\n        reclaim.add(key);\n      },\n    };\n  }\n\n  return (client as Client<PERSON><PERSON><PERSON><PERSON>)._react!;\n};\n", "/* eslint-disable react-hooks/exhaustive-deps */\n\nimport type { Source } from 'wonka';\nimport { pipe, subscribe, onEnd, onPush, takeWhile } from 'wonka';\nimport * as React from 'react';\n\nimport type {\n  GraphQLRequestParams,\n  AnyVariables,\n  Client,\n  CombinedError,\n  OperationContext,\n  RequestPolicy,\n  OperationResult,\n  Operation,\n} from '@urql/core';\n\nimport { useClient } from '../context';\nimport { useRequest } from './useRequest';\nimport { getCacheForClient } from './cache';\n\nimport {\n  deferDispatch,\n  initialState,\n  computeNextState,\n  hasDepsChanged,\n} from './state';\n\n/** Input arguments for the {@link useQuery} hook.\n *\n * @param query - The GraphQL query that `useQuery` executes.\n * @param variables - The variables for the GraphQL query that `useQuery` executes.\n */\nexport type UseQueryArgs<\n  Variables extends AnyVariables = AnyVariables,\n  Data = any,\n> = {\n  /** Updates the {@link RequestPolicy} for the executed GraphQL query operation.\n   *\n   * @remarks\n   * `requestPolicy` modifies the {@link RequestPolicy} of the GraphQL query operation\n   * that `useQuery` executes, and indicates a caching strategy for cache exchanges.\n   *\n   * For example, when set to `'cache-and-network'`, {@link useQuery} will\n   * receive a cached result with `stale: true` and an API request will be\n   * sent in the background.\n   *\n   * @see {@link OperationContext.requestPolicy} for where this value is set.\n   */\n  requestPolicy?: RequestPolicy;\n  /** Updates the {@link OperationContext} for the executed GraphQL query operation.\n   *\n   * @remarks\n   * `context` may be passed to {@link useQuery}, to update the {@link OperationContext}\n   * of a query operation. This may be used to update the `context` that exchanges\n   * will receive for a single hook.\n   *\n   * Hint: This should be wrapped in a `useMemo` hook, to make sure that your\n   * component doesn’t infinitely update.\n   *\n   * @example\n   * ```ts\n   * const [result, reexecute] = useQuery({\n   *   query,\n   *   context: useMemo(() => ({\n   *     additionalTypenames: ['Item'],\n   *   }), [])\n   * });\n   * ```\n   */\n  context?: Partial<OperationContext>;\n  /** Prevents {@link useQuery} from automatically executing GraphQL query operations.\n   *\n   * @remarks\n   * `pause` may be set to `true` to stop {@link useQuery} from executing\n   * automatically. The hook will stop receiving updates from the {@link Client}\n   * and won’t execute the query operation, until either it’s set to `false`\n   * or the {@link UseQueryExecute} function is called.\n   *\n   * @see {@link https://urql.dev/goto/docs/basics/react-preact/#pausing-usequery} for\n   * documentation on the `pause` option.\n   */\n  pause?: boolean;\n} & GraphQLRequestParams<Data, Variables>;\n\n/** State of the current query, your {@link useQuery} hook is executing.\n *\n * @remarks\n * `UseQueryState` is returned (in a tuple) by {@link useQuery} and\n * gives you the updating {@link OperationResult} of GraphQL queries.\n *\n * Even when the query and variables passed to {@link useQuery} change,\n * this state preserves the prior state and sets the `fetching` flag to\n * `true`.\n * This allows you to display the previous state, while implementing\n * a separate loading indicator separately.\n */\nexport interface UseQueryState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> {\n  /** Indicates whether `useQuery` is waiting for a new result.\n   *\n   * @remarks\n   * When `useQuery` is passed a new query and/or variables, it will\n   * start executing the new query operation and `fetching` is set to\n   * `true` until a result arrives.\n   *\n   * Hint: This is subtly different than whether the query is actually\n   * fetching, and doesn’t indicate whether a query is being re-executed\n   * in the background. For this, see {@link UseQueryState.stale}.\n   */\n  fetching: boolean;\n  /** Indicates that the state is not fresh and a new result will follow.\n   *\n   * @remarks\n   * The `stale` flag is set to `true` when a new result for the query\n   * is expected and `useQuery` is waiting for it. This may indicate that\n   * a new request is being requested in the background.\n   *\n   * @see {@link OperationResult.stale} for the source of this value.\n   */\n  stale: boolean;\n  /** The {@link OperationResult.data} for the executed query. */\n  data?: Data;\n  /** The {@link OperationResult.error} for the executed query. */\n  error?: CombinedError;\n  /** The {@link OperationResult.extensions} for the executed query. */\n  extensions?: Record<string, any>;\n  /** The {@link Operation} that the current state is for.\n   *\n   * @remarks\n   * This is the {@link Operation} that is currently being executed.\n   * When {@link UseQueryState.fetching} is `true`, this is the\n   * last `Operation` that the current state was for.\n   */\n  operation?: Operation<Data, Variables>;\n}\n\n/** Triggers {@link useQuery} to execute a new GraphQL query operation.\n *\n * @param opts - optionally, context options that will be merged with the hook's\n * {@link UseQueryArgs.context} options and the `Client`’s options.\n *\n * @remarks\n * When called, {@link useQuery} will re-execute the GraphQL query operation\n * it currently holds, even if {@link UseQueryArgs.pause} is set to `true`.\n *\n * This is useful for executing a paused query or re-executing a query\n * and get a new network result, by passing a new request policy.\n *\n * ```ts\n * const [result, reexecuteQuery] = useQuery({ query });\n *\n * const refresh = () => {\n *   // Re-execute the query with a network-only policy, skipping the cache\n *   reexecuteQuery({ requestPolicy: 'network-only' });\n * };\n * ```\n */\nexport type UseQueryExecute = (opts?: Partial<OperationContext>) => void;\n\n/** Result tuple returned by the {@link useQuery} hook.\n *\n * @remarks\n * Similarly to a `useState` hook’s return value,\n * the first element is the {@link useQuery}’s result and state,\n * a {@link UseQueryState} object,\n * and the second is used to imperatively re-execute the query\n * via a {@link UseQueryExecute} function.\n */\nexport type UseQueryResponse<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> = [UseQueryState<Data, Variables>, UseQueryExecute];\n\nconst isSuspense = (client: Client, context?: Partial<OperationContext>) =>\n  context && context.suspense !== undefined\n    ? !!context.suspense\n    : client.suspense;\n\n/** Hook to run a GraphQL query and get updated GraphQL results.\n *\n * @param args - a {@link UseQueryArgs} object, to pass a `query`, `variables`, and options.\n * @returns a {@link UseQueryResponse} tuple of a {@link UseQueryState} result, and re-execute function.\n *\n * @remarks\n * `useQuery` allows GraphQL queries to be defined and executed.\n * Given {@link UseQueryArgs.query}, it executes the GraphQL query with the\n * context’s {@link Client}.\n *\n * The returned result updates when the `Client` has new results\n * for the query, and changes when your input `args` change.\n *\n * Additionally, if the `suspense` option is enabled on the `Client`,\n * the `useQuery` hook will suspend instead of indicating that it’s\n * waiting for a result via {@link UseQueryState.fetching}.\n *\n * @see {@link https://urql.dev/goto/urql/docs/basics/react-preact/#queries} for `useQuery` docs.\n *\n * @example\n * ```ts\n * import { gql, useQuery } from 'urql';\n *\n * const TodosQuery = gql`\n *   query { todos { id, title } }\n * `;\n *\n * const Todos = () => {\n *   const [result, reexecuteQuery] = useQuery({\n *     query: TodosQuery,\n *     variables: {},\n *   });\n *   // ...\n * };\n * ```\n */\nexport function useQuery<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(args: UseQueryArgs<Variables, Data>): UseQueryResponse<Data, Variables> {\n  const client = useClient();\n  const cache = getCacheForClient(client);\n  const suspense = isSuspense(client, args.context);\n  const request = useRequest(args.query, args.variables as Variables);\n\n  const source = React.useMemo(() => {\n    if (args.pause) return null;\n\n    const source = client.executeQuery(request, {\n      requestPolicy: args.requestPolicy,\n      ...args.context,\n    });\n\n    return suspense\n      ? pipe(\n          source,\n          onPush(result => {\n            cache.set(request.key, result);\n          })\n        )\n      : source;\n  }, [\n    cache,\n    client,\n    request,\n    suspense,\n    args.pause,\n    args.requestPolicy,\n    args.context,\n  ]);\n\n  const getSnapshot = React.useCallback(\n    (\n      source: Source<OperationResult<Data, Variables>> | null,\n      suspense: boolean\n    ): Partial<UseQueryState<Data, Variables>> => {\n      if (!source) return { fetching: false };\n\n      let result = cache.get(request.key);\n      if (!result) {\n        let resolve: (value: unknown) => void;\n\n        const subscription = pipe(\n          source,\n          takeWhile(() => (suspense && !resolve) || !result),\n          subscribe(_result => {\n            result = _result;\n            if (resolve) resolve(result);\n          })\n        );\n\n        if (result == null && suspense) {\n          const promise = new Promise(_resolve => {\n            resolve = _resolve;\n          });\n\n          cache.set(request.key, promise);\n          throw promise;\n        } else {\n          subscription.unsubscribe();\n        }\n      } else if (suspense && result != null && 'then' in result) {\n        throw result;\n      }\n\n      return (result as OperationResult<Data, Variables>) || { fetching: true };\n    },\n    [cache, request]\n  );\n\n  const deps = [\n    client,\n    request,\n    args.requestPolicy,\n    args.context,\n    args.pause,\n  ] as const;\n\n  const [state, setState] = React.useState(\n    () =>\n      [\n        source,\n        computeNextState(initialState, getSnapshot(source, suspense)),\n        deps,\n      ] as const\n  );\n\n  let currentResult = state[1];\n  if (source !== state[0] && hasDepsChanged(state[2], deps)) {\n    setState([\n      source,\n      (currentResult = computeNextState(\n        state[1],\n        getSnapshot(source, suspense)\n      )),\n      deps,\n    ]);\n  }\n\n  React.useEffect(() => {\n    const source = state[0];\n    const request = state[2][1];\n\n    let hasResult = false;\n\n    const updateResult = (result: Partial<UseQueryState<Data, Variables>>) => {\n      hasResult = true;\n      deferDispatch(setState, state => {\n        const nextResult = computeNextState(state[1], result);\n        return state[1] !== nextResult\n          ? [state[0], nextResult, state[2]]\n          : state;\n      });\n    };\n\n    if (source) {\n      const subscription = pipe(\n        source,\n        onEnd(() => {\n          updateResult({ fetching: false });\n        }),\n        subscribe(updateResult)\n      );\n\n      if (!hasResult) updateResult({ fetching: true });\n\n      return () => {\n        cache.dispose(request.key);\n        subscription.unsubscribe();\n      };\n    } else {\n      updateResult({ fetching: false });\n    }\n  }, [cache, state[0], state[2][1]]);\n\n  const executeQuery = React.useCallback(\n    (opts?: Partial<OperationContext>) => {\n      const context = {\n        requestPolicy: args.requestPolicy,\n        ...args.context,\n        ...opts,\n      };\n\n      deferDispatch(setState, state => {\n        const source = suspense\n          ? pipe(\n              client.executeQuery(request, context),\n              onPush(result => {\n                cache.set(request.key, result);\n              })\n            )\n          : client.executeQuery(request, context);\n        return [source, state[1], deps];\n      });\n    },\n    [\n      client,\n      cache,\n      request,\n      suspense,\n      args.requestPolicy,\n      args.context,\n      args.pause,\n    ]\n  );\n\n  return [currentResult, executeQuery];\n}\n", "/* eslint-disable react-hooks/exhaustive-deps */\n\nimport { pipe, subscribe, onEnd } from 'wonka';\nimport * as React from 'react';\n\nimport type {\n  GraphQLRequestParams,\n  AnyVariables,\n  CombinedError,\n  OperationContext,\n  Operation,\n} from '@urql/core';\n\nimport { useClient } from '../context';\nimport { useRequest } from './useRequest';\n\nimport {\n  deferDispatch,\n  initialState,\n  computeNextState,\n  hasDepsChanged,\n} from './state';\n\n/** Input arguments for the {@link useSubscription} hook.\n *\n * @param query - The GraphQL subscription document that `useSubscription` executes.\n * @param variables - The variables for the GraphQL subscription that `useSubscription` executes.\n */\nexport type UseSubscriptionArgs<\n  Variables extends AnyVariables = AnyVariables,\n  Data = any,\n> = {\n  /** Prevents {@link useSubscription} from automatically starting GraphQL subscriptions.\n   *\n   * @remarks\n   * `pause` may be set to `true` to stop {@link useSubscription} from starting its subscription\n   * automatically. The hook will stop receiving updates from the {@link Client}\n   * and won’t start the subscription operation, until either it’s set to `false`\n   * or the {@link UseSubscriptionExecute} function is called.\n   */\n  pause?: boolean;\n  /** Updates the {@link OperationContext} for the executed GraphQL subscription operation.\n   *\n   * @remarks\n   * `context` may be passed to {@link useSubscription}, to update the {@link OperationContext}\n   * of a subscription operation. This may be used to update the `context` that exchanges\n   * will receive for a single hook.\n   *\n   * Hint: This should be wrapped in a `useMemo` hook, to make sure that your\n   * component doesn’t infinitely update.\n   *\n   * @example\n   * ```ts\n   * const [result, reexecute] = useSubscription({\n   *   query,\n   *   context: useMemo(() => ({\n   *     additionalTypenames: ['Item'],\n   *   }), [])\n   * });\n   * ```\n   */\n  context?: Partial<OperationContext>;\n} & GraphQLRequestParams<Data, Variables>;\n\n/** Combines previous data with an incoming subscription result’s data.\n *\n * @remarks\n * A `SubscriptionHandler` may be passed to {@link useSubscription} to\n * aggregate subscription results into a combined {@link UseSubscriptionState.data}\n * value.\n *\n * This is useful when a subscription event delivers a single item, while\n * you’d like to display a list of events.\n *\n * @example\n * ```ts\n * const NotificationsSubscription = gql`\n *   subscription { newNotification { id, text } }\n * `;\n *\n * const combineNotifications = (notifications = [], data) => {\n *   return [...notifications, data.newNotification];\n * };\n *\n * const [result, executeSubscription] = useSubscription(\n *   { query: NotificationsSubscription },\n *   combineNotifications,\n * );\n * ```\n */\nexport type SubscriptionHandler<T, R> = (prev: R | undefined, data: T) => R;\n\n/** State of the current subscription, your {@link useSubscription} hook is executing.\n *\n * @remarks\n * `UseSubscriptionState` is returned (in a tuple) by {@link useSubscription} and\n * gives you the updating {@link OperationResult} of GraphQL subscriptions.\n *\n * If a {@link SubscriptionHandler} has been passed to `useSubscription` then\n * {@link UseSubscriptionState.data} is instead the updated data as returned\n * by the handler, otherwise it’s the latest result’s data.\n *\n * Hint: Even when the query and variables passed to {@link useSubscription} change,\n * this state preserves the prior state.\n */\nexport interface UseSubscriptionState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> {\n  /** Indicates whether `useSubscription`’s subscription is active.\n   *\n   * @remarks\n   * When `useSubscription` starts a subscription, the `fetching` flag\n   * is set to `true` and will remain `true` until the subscription\n   * completes on the API, or the {@link UseSubscriptionArgs.pause}\n   * flag is set to `true`.\n   */\n  fetching: boolean;\n  /** Indicates that the subscription result is not fresh.\n   *\n   * @remarks\n   * This is mostly unused for subscriptions and will rarely affect you, and\n   * is more relevant for queries.\n   *\n   * @see {@link OperationResult.stale} for the source of this value.\n   */\n  stale: boolean;\n  /** The {@link OperationResult.data} for the executed subscription, or data returned by a handler.\n   *\n   * @remarks\n   * `data` will be set to the last {@link OperationResult.data} value\n   * received for the subscription.\n   *\n   * It will instead be set to the values that {@link SubscriptionHandler}\n   * returned, if a handler has been passed to {@link useSubscription}.\n   */\n  data?: Data;\n  /** The {@link OperationResult.error} for the executed subscription. */\n  error?: CombinedError;\n  /** The {@link OperationResult.extensions} for the executed mutation. */\n  extensions?: Record<string, any>;\n  /** The {@link Operation} that the current state is for.\n   *\n   * @remarks\n   * This is the subscription {@link Operation} that is currently active.\n   * When {@link UseSubscriptionState.fetching} is `true`, this is the\n   * last `Operation` that the current state was for.\n   */\n  operation?: Operation<Data, Variables>;\n}\n\n/** Triggers {@link useSubscription} to reexecute a GraphQL subscription operation.\n *\n * @param opts - optionally, context options that will be merged with the hook's\n * {@link UseSubscriptionArgs.context} options and the `Client`’s options.\n *\n * @remarks\n * When called, {@link useSubscription} will restart the GraphQL subscription\n * operation it currently holds. If {@link UseSubscriptionArgs.pause} is set\n * to `true`, it will start executing the subscription.\n *\n * ```ts\n * const [result, executeSubscription] = useSubscription({\n *   query,\n *   pause: true,\n * });\n *\n * const start = () => {\n *   executeSubscription();\n * };\n * ```\n */\nexport type UseSubscriptionExecute = (opts?: Partial<OperationContext>) => void;\n\n/** Result tuple returned by the {@link useSubscription} hook.\n *\n * @remarks\n * Similarly to a `useState` hook’s return value,\n * the first element is the {@link useSubscription}’s state,\n * a {@link UseSubscriptionState} object,\n * and the second is used to imperatively re-execute or start the subscription\n * via a {@link UseMutationExecute} function.\n */\nexport type UseSubscriptionResponse<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> = [UseSubscriptionState<Data, Variables>, UseSubscriptionExecute];\n\n/** Hook to run a GraphQL subscription and get updated GraphQL results.\n *\n * @param args - a {@link UseSubscriptionArgs} object, to pass a `query`, `variables`, and options.\n * @param handler - optionally, a {@link SubscriptionHandler} function to combine multiple subscription results.\n * @returns a {@link UseSubscriptionResponse} tuple of a {@link UseSubscriptionState} result, and an execute function.\n *\n * @remarks\n * `useSubscription` allows GraphQL subscriptions to be defined and executed.\n * Given {@link UseSubscriptionArgs.query}, it executes the GraphQL subscription with the\n * context’s {@link Client}.\n *\n * The returned result updates when the `Client` has new results\n * for the subscription, and `data` is updated with the result’s data\n * or with the `data` that a `handler` returns.\n *\n * @example\n * ```ts\n * import { gql, useSubscription } from 'urql';\n *\n * const NotificationsSubscription = gql`\n *   subscription { newNotification { id, text } }\n * `;\n *\n * const combineNotifications = (notifications = [], data) => {\n *   return [...notifications, data.newNotification];\n * };\n *\n * const Notifications = () => {\n *   const [result, executeSubscription] = useSubscription(\n *     { query: NotificationsSubscription },\n *     combineNotifications,\n *   );\n *   // ...\n * };\n * ```\n */\nexport function useSubscription<\n  Data = any,\n  Result = Data,\n  Variables extends AnyVariables = AnyVariables,\n>(\n  args: UseSubscriptionArgs<Variables, Data>,\n  handler?: SubscriptionHandler<Data, Result>\n): UseSubscriptionResponse<Result, Variables> {\n  const client = useClient();\n  const request = useRequest(args.query, args.variables as Variables);\n\n  const handlerRef = React.useRef<\n    SubscriptionHandler<Data, Result> | undefined\n  >(handler);\n  handlerRef.current = handler;\n\n  const source = React.useMemo(\n    () =>\n      !args.pause ? client.executeSubscription(request, args.context) : null,\n    [client, request, args.pause, args.context]\n  );\n\n  const deps = [client, request, args.context, args.pause] as const;\n\n  const [state, setState] = React.useState(\n    () => [source, { ...initialState, fetching: !!source }, deps] as const\n  );\n\n  let currentResult = state[1];\n  if (source !== state[0] && hasDepsChanged(state[2], deps)) {\n    setState([\n      source,\n      (currentResult = computeNextState(state[1], { fetching: !!source })),\n      deps,\n    ]);\n  }\n\n  React.useEffect(() => {\n    const updateResult = (\n      result: Partial<UseSubscriptionState<Data, Variables>>\n    ) => {\n      deferDispatch(setState, state => {\n        const nextResult = computeNextState(state[1], result);\n        if (state[1] === nextResult) return state;\n        if (handlerRef.current && state[1].data !== nextResult.data) {\n          nextResult.data = handlerRef.current(\n            state[1].data,\n            nextResult.data!\n          ) as any;\n        }\n\n        return [state[0], nextResult as any, state[2]];\n      });\n    };\n\n    if (state[0]) {\n      return pipe(\n        state[0],\n        onEnd(() => {\n          updateResult({ fetching: !!source });\n        }),\n        subscribe(updateResult)\n      ).unsubscribe;\n    } else {\n      updateResult({ fetching: false });\n    }\n  }, [state[0]]);\n\n  // This is the imperative execute function passed to the user\n  const executeSubscription = React.useCallback(\n    (opts?: Partial<OperationContext>) => {\n      const source = client.executeSubscription(request, {\n        ...args.context,\n        ...opts,\n      });\n\n      deferDispatch(setState, state => [source, state[1], deps]);\n    },\n    [client, request, args.context, args.pause]\n  );\n\n  return [currentResult, executeSubscription];\n}\n", "import type { ReactElement } from 'react';\nimport type { AnyVariables, DocumentInput } from '@urql/core';\n\nimport type { UseMutationState, UseMutationExecute } from '../hooks';\nimport { useMutation } from '../hooks';\n\n/** Props accepted by {@link Mutation}.\n *\n * @remarks\n * `MutationProps` are the props accepted by the {@link Mutation} component.\n *\n * The result, the {@link MutationState} object, will be passed to\n * a {@link MutationProps.children} function, passed as children\n * to the `Mutation` component.\n */\nexport interface MutationProps<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> {\n  /* The GraphQL mutation document that {@link useMutation} will execute. */\n  query: DocumentInput<Data, Variables>;\n  children(arg: MutationState<Data, Variables>): ReactElement<any>;\n}\n\n/** Object that {@link MutationProps.children} is called with.\n *\n * @remarks\n * This is an extented {@link UseMutationstate} with an added\n * {@link MutationState.executeMutation} method, which is usually\n * part of a tuple returned by {@link useMutation}.\n */\nexport interface MutationState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> extends UseMutationState<Data, Variables> {\n  /** Alias to {@link useMutation}’s `executeMutation` function. */\n  executeMutation: UseMutationExecute<Data, Variables>;\n}\n\n/** Component Wrapper around {@link useMutation} to run a GraphQL query.\n *\n * @remarks\n * `Mutation` is a component wrapper around the {@link useMutation} hook\n * that calls the {@link MutationProps.children} prop, as a function,\n * with the {@link MutationState} object.\n */\nexport function Mutation<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(props: MutationProps<Data, Variables>): ReactElement<any> {\n  const mutation = useMutation<Data, Variables>(props.query);\n  return props.children({ ...mutation[0], executeMutation: mutation[1] });\n}\n", "import type { ReactElement } from 'react';\nimport type { AnyVariables } from '@urql/core';\n\nimport type { UseQueryArgs, UseQueryState, UseQueryExecute } from '../hooks';\nimport { useQuery } from '../hooks';\n\n/** Props accepted by {@link Query}.\n *\n * @remarks\n * `QueryProps` are the props accepted by the {@link Query} component,\n * which is identical to {@link UseQueryArgs}.\n *\n * The result, the {@link QueryState} object, will be passed to\n * a {@link QueryProps.children} function, passed as children\n * to the `Query` component.\n */\nexport type QueryProps<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> = UseQueryArgs<Variables, Data> & {\n  children(arg: QueryState<Data, Variables>): ReactElement<any>;\n};\n\n/** Object that {@link QueryProps.children} is called with.\n *\n * @remarks\n * This is an extented {@link UseQueryState} with an added\n * {@link QueryState.executeQuery} method, which is usually\n * part of a tuple returned by {@link useQuery}.\n */\nexport interface QueryState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> extends UseQueryState<Data, Variables> {\n  /** Alias to {@link useQuery}’s `executeQuery` function. */\n  executeQuery: UseQueryExecute;\n}\n\n/** Component Wrapper around {@link useQuery} to run a GraphQL query.\n *\n * @remarks\n * `Query` is a component wrapper around the {@link useQuery} hook\n * that calls the {@link QueryProps.children} prop, as a function,\n * with the {@link QueryState} object.\n */\nexport function Query<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n>(props: QueryProps<Data, Variables>): ReactElement<any> {\n  const query = useQuery<Data, Variables>(props);\n  return props.children({ ...query[0], executeQuery: query[1] });\n}\n", "import type { ReactElement } from 'react';\nimport type { AnyVariables } from '@urql/core';\n\nimport type {\n  UseSubscriptionArgs,\n  UseSubscriptionState,\n  UseSubscriptionExecute,\n  SubscriptionHandler,\n} from '../hooks';\nimport { useSubscription } from '../hooks';\n\n/** Props accepted by {@link Subscription}.\n *\n * @remarks\n * `SubscriptionProps` are the props accepted by the {@link Subscription} component,\n * which is identical to {@link UseSubscriptionArgs} with an added\n * {@link SubscriptionProps.handler} prop, which {@link useSubscription} usually\n * accepts as an additional argument.\n *\n * The result, the {@link SubscriptionState} object, will be passed to\n * a {@link SubscriptionProps.children} function, passed as children\n * to the `Subscription` component.\n */\nexport type SubscriptionProps<\n  Data = any,\n  Result = Data,\n  Variables extends AnyVariables = AnyVariables,\n> = UseSubscriptionArgs<Variables, Data> & {\n  handler?: SubscriptionHandler<Data, Result>;\n  children(arg: SubscriptionState<Result, Variables>): ReactElement<any>;\n};\n\n/** Object that {@link SubscriptionProps.children} is called with.\n *\n * @remarks\n * This is an extented {@link UseSubscriptionState} with an added\n * {@link SubscriptionState.executeSubscription} method, which is usually\n * part of a tuple returned by {@link useSubscription}.\n */\nexport interface SubscriptionState<\n  Data = any,\n  Variables extends AnyVariables = AnyVariables,\n> extends UseSubscriptionState<Data, Variables> {\n  /** Alias to {@link useSubscription}’s `executeMutation` function. */\n  executeSubscription: UseSubscriptionExecute;\n}\n\n/** Component Wrapper around {@link useSubscription} to run a GraphQL subscription.\n *\n * @remarks\n * `Subscription` is a component wrapper around the {@link useSubscription} hook\n * that calls the {@link SubscriptionProps.children} prop, as a function,\n * with the {@link SubscriptionState} object.\n */\nexport function Subscription<\n  Data = any,\n  Result = Data,\n  Variables extends AnyVariables = AnyVariables,\n>(props: SubscriptionProps<Data, Result, Variables>): ReactElement<any> {\n  const subscription = useSubscription<Data, Result, Variables>(\n    props,\n    props.handler\n  );\n\n  return props.children({\n    ...subscription[0],\n    executeSubscription: subscription[1],\n  });\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,IAAA;EACAC,MAAA;EACAC,UAAA;EACAC,sBAAA;EACAC,qBAAA;EACAC,eAAA;EACAC,OAAA;EACAC,UAAA;EACAC,iBAAA;EACAC,iBAAA;EACAC,qBAAA;EACAC,UAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;EACAC,SAAA;EACAC,MAAA;EACAC,MAAA;EACAC,MAAA;EACAC,QAAA;EACAC,cAAA;EACAC,WAAA;EACAC,YAAA;EACAC,WAAA;EACAC,eAAA;;ACrBA,IAAAC,eAAA,cAAAC,MAAAA;EASAC,YACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAAAA;AAEAC,UAAAP,EAAAA;AAEAQ,SAAAC,OAAA;AACAD,SAAAR,UAAAA;AAEA,QAAAI,IAAAA;AAAAI,WAAAJ,OAAAA;;AACA,QAAAH,IAAAA;AAAAO,WAAAP,QAAAS,MAAAC,QAAAV,EAAAA,IAAAA,KAAA,CAAAA,EAAAA;;AACA,QAAAC,IAAAA;AAAAM,WAAAN,SAAAA;;AACA,QAAAC,IAAAA;AAAAK,WAAAL,YAAAA;;AACA,QAAAE,IAAAA;AAAAG,WAAAH,gBAAAA;;AAEAO,QAAAC,KAAAP;AACA,QAAA,CAAAO,MAAAR,IAAA;AACAS,UAAAC,KAAAC,GAAAV;AACA,UAAAS,MAAA,YAAA,OAAAA,IAAAA;AACAF,QAAAA,KAAAE;;IAEA;AAEAP,SAAAF,aAAAO,MAAA,CAAA;EACA;EAEAI,SAAAA;AACA,WAAA;SAAAT;MAAAR,SAAAQ,KAAAR;;EACA;EAEAkB,WAAAA;AACA,WAAAV,KAAAR;EACA;EAEAmB,KAAAC,OAAAD,WAAAA,IAAAA;AACA,WAAA;EACA;;AC1CAP,IAAAS;AACAT,IAAAU;AAEA,SAAAC,MAAAC,IAAAA;AACA,SAAA,IAAA3B,aAAA,qCAAAyB,CAAAA,OAAAE,EAAAA,EAAAA;AACA;AAEA,SAAAC,QAAAC,IAAAA;AACAA,EAAAA,GAAAC,YAAAL;AACA,MAAAI,GAAAE,KAAAP,CAAAA,GAAA;AAEA,WADAA,EAAAQ,MAAAP,GAAAA,IAAAI,GAAAC,SAAAA;EAEA;AACA;AAEAb,IAAAgB,IAAA;AACA,SAAAC,YAAAC,IAAAA;AACAlB,MAAAmB,KAAAD,GAAAE,MAAA,IAAA;AACAtB,MAAAuB,KAAA;AACAvB,MAAAwB,KAAA;AACAxB,MAAAyB,KAAA;AACAzB,MAAA0B,KAAAL,GAAAM,SAAA;AACA,WAAAC,KAAA,GAAAA,KAAAP,GAAAM,QAAAC,MAAA;AACAV,MAAAH,YAAA;AACA,QAAAG,EAAAF,KAAAK,GAAAO,EAAAA,CAAAA,GAAA;AACA,UAAAA,OAAAA,CAAAJ,MAAAN,EAAAH,YAAAS,KAAAA;AACAA,QAAAA,KAAAN,EAAAH;;AACAU,MAAAA,KAAAA,MAAAG;AACAF,MAAAA,KAAAE;IACA;EACA;AACA,WAAAA,KAAAH,IAAAG,MAAAF,IAAAE,MAAA;AACA,QAAAA,OAAAH,IAAAA;AAAAF,MAAAA,MAAA;;AACAA,IAAAA,MAAAF,GAAAO,EAAAA,EAAAX,MAAAO,EAAAA,EAAAK,QAAA,UAAA,KAAA;EACA;AACA,SAAAN;AACA;AAGA,SAAAO,UAAAA;AACA,WACAC,KAAA,IAAAtB,EAAAuB,WAAAtB,GAAAA,GACA,MAAAqB,MACA,OAAAA,MACA,OAAAA,MACA,OAAAA,MACA,OAAAA,MACA,OAAAA,MACA,UAAAA,IACAA,KAAA,IAAAtB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAEA,QAAA,OAAAqB,IAAAA;AAAA,aAAA,QAAAA,KAAAtB,EAAAuB,WAAAtB,GAAAA,MAAA,OAAAqB,IAAAA;MAAAA;;;AAEArB;AACA;AAEAR,IAAA+B,IAAA;AACA,SAAApC,OAAAA;AACAG,MAAAkC;AACA,MAAAA,KAAArB,QAAAoB,CAAAA,GAAAA;AACA,WAAA;MACArB,MAAA;MACAuB,OAAAD;;;AAGA;AAGAhC,IAAAkC,IAAA;AAEAlC,IAAAmC,IAAA;AACAnC,IAAAoC,IAAA;AAGApC,IAAAqC,IAAA;AAEArC,IAAAsC,IAAA;AACAtC,IAAAuC,IAAA;AACAvC,IAAAwC,IAAA;AAKA,SAAAP,MAAAQ,IAAAA;AACA3C,MAAAuB;AACAvB,MAAAkC;AACA,MAAAA,KAAArB,QAAAuB,CAAAA,GAAAA;AACAb,IAAAA,KACA,WAAAW,KACA;MACAtB,MAAA;QAEA;MACAA,MAAA;MACAuB,OAAA,WAAAD;;aAEA,CAAAS,OAAAT,KAAArB,QAAAwB,CAAAA,IAAAA;AACAd,IAAAA,KAAA;MACAX,MAAA;MACAf,MAAA;QACAe,MAAA;QACAuB,OAAAD,GAAAjB,MAAA,CAAA;;;aAGAiB,KAAArB,QAAAyB,CAAAA,GAAA;AACApC,QAAA0C,KAAAV;AACA,QAAAA,KAAArB,QAAA0B,CAAAA,GAAAA;AACAhB,MAAAA,KAAA;QACAX,MAAA;QACAuB,OAAAS,KAAAV;;;AAGAX,MAAAA,KAAA;QACAX,MAAA;QACAuB,OAAAS;;;EAGA,WAAAV,KAAArB,QAAAoB,CAAAA,GAAAA;AACAV,IAAAA,KAAA;MACAX,MAAA;MACAuB,OAAAD;;aAEAA,KAAArB,QAAA4B,CAAAA,GAAAA;AACAlB,IAAAA,KAAA;MACAX,MAAA;MACAuB,OAAAhB,YAAAe,GAAAjB,MAAA,GAAA,EAAA,CAAA;MACA4B,OAAAA;;aAEAX,KAAArB,QAAA6B,CAAAA,GAAAA;AACAnB,IAAAA,KAAA;MACAX,MAAA;MACAuB,OAAAK,EAAAxB,KAAAkB,EAAAA,IAAAY,KAAAC,MAAAb,EAAAA,IAAAA,GAAAjB,MAAA,GAAA,EAAA;MACA4B,OAAAA;;aAEAtB,KAQA,SAAAyB,KAAAL,IAAAA;AACA3C,QAAAkC;AACA,QAAA,OAAAzB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,cAAAA;AACA5B,UAAA+C,KAAA,CAAA;AACA,aAAAf,KAAAC,MAAAQ,EAAAA,GAAAA;AAAAM,QAAAA,GAAAC,KAAAhB,EAAAA;;AACA,UAAA,OAAAzB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,cAAAC,MAAA,WAAA;;AACAmB,cAAAA;AACA,aAAA;QACAlB,MAAA;QACAqC,QAAAA;;IAEA;EACA,EAtBAN,EAAAA,KAwBA,SAAAQ,OAAAR,IAAAA;AACA,QAAA,QAAAlC,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,cAAAA;AACA5B,UAAAkD,KAAA,CAAA;AACApD,UAAAqD;AACA,aAAAA,KAAAxD,KAAAA,GAAA;AACAiC,gBAAAA;AACA,YAAA,OAAArB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,gBAAAC,MAAA,aAAA;;AACAmB,gBAAAA;AACA5B,YAAAoD,KAAAnB,MAAAQ,EAAAA;AACA,YAAA,CAAAW,IAAAA;AAAA,gBAAA3C,MAAA,aAAA;;AACAyC,QAAAA,GAAAF,KAAA;UACAtC,MAAA;UACAf,MAAAwD;UACAlB,OAAAmB;;MAEA;AACA,UAAA,QAAA7C,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,cAAAC,MAAA,aAAA;;AACAmB,cAAAA;AACA,aAAA;QACAlB,MAAA;QACAwC,QAAAA;;IAEA;EACA,EAjDAT,EAAAA,GAAAA;AACA,WAAApB;;AAGAO,UAAAA;AACA,SAAAP;AACA;AA6CA,SAAAgC,WAAAZ,IAAAA;AACAzC,MAAAsD,KAAA,CAAA;AACA1B,UAAAA;AACA,MAAA,OAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,YAAAA;AACA9B,QAAAqD;AACA,WAAAA,KAAAxD,KAAAA,GAAA;AACAiC,cAAAA;AACA,UAAA,OAAArB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,cAAAC,MAAA,UAAA;;AACAmB,cAAAA;AACA5B,UAAAoD,KAAAnB,MAAAQ,EAAAA;AACA,UAAA,CAAAW,IAAAA;AAAA,cAAA3C,MAAA,UAAA;;AACA6C,MAAAA,GAAAN,KAAA;QACAtC,MAAA;QACAf,MAAAwD;QACAlB,OAAAmB;;IAEA;AACA,QAAA,CAAAE,GAAA7B,UAAA,OAAAlB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,YAAAC,MAAA,UAAA;;AACAmB,YAAAA;EACA;AACA,SAAA0B;AACA;AAKA,SAAAC,WAAAd,IAAAA;AACAzC,MAAAuD,KAAA,CAAA;AACA3B,UAAAA;AACA,SAAA,OAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAR,QAAAmD,KAAAxD,KAAAA;AACA,QAAA,CAAAwD,IAAAA;AAAA,YAAA1C,MAAA,WAAA;;AACAmB,YAAAA;AACA2B,IAAAA,GAAAP,KAAA;MACAtC,MAAA;MACAf,MAAAwD;MACAK,WAAAH,WAAAZ,EAAAA;;EAEA;AACA,SAAAc;AACA;AAEA,SAAAE,QAAAA;AACA3D,MAAAqD,KAAAxD,KAAAA;AACA,MAAAwD,IAAA;AACAvB,YAAAA;AACA9B,QAAA4D;AACA,QAAA,OAAAnD,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,cAAAA;AACA8B,MAAAA,KAAAP;AAEA,UAAA,EADAA,KAAAxD,KAAAA,IAAAA;AACA,cAAAc,MAAA,OAAA;;AACAmB,cAAAA;IACA;AACA,WAAA;MACAlB,MAAA;MACAiD,OAAAD;MACA/D,MAAAwD;MACAK,WAAAH,WAAAA,KAAA;MACAE,YAAAA,WAAAA,KAAA;MACAK,cAAAA,aAAAA;;EAEA;AACA;AAEA,SAAAC,OAAAA;AACA/D,MAAAkC;AACAJ,UAAAA;AACA,MAAA,OAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,YAAAA;AACA5B,QAAA8D,KAAAD,KAAAA;AACA,QAAA,CAAAC,MAAA,OAAAvD,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,YAAAC,MAAA,UAAA;;AACAuB,IAAAA,KAAA;MACAtB,MAAA;MACAmD,MAAAC;;EAEA,WAAA9B,KAAArC,KAAAA,GAAAA;AACAqC,IAAAA,KAAA;MACAtB,MAAA;MACAf,MAAAqC;;;AAGA,UAAAvB,MAAA,WAAA;;AAGAmB,UAAAA;AACA,MAAA,OAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,YAAAA;AACA,WAAA;MACAlB,MAAA;MACAmD,MAAA7B;;EAEA,OAAA;AACA,WAAAA;;AAEA;AAEAhC,IAAA+D,IAAA;AACA,SAAAC,gBAAAA;AACA,MAAArD,QAAAoD,CAAAA,GAAA;AACAnC,YAAAA;AACA5B,QAAAmD,KAAAxD,KAAAA;AACA,QAAA,CAAAwD,IAAAA;AAAA,YAAA1C,MAAA,WAAA;;AACAmB,YAAAA;AACA,WAAA;MACAlB,MAAA;MACAf,MAAAwD;;EAEA;AACA;AAEAnD,IAAAiE,IAAA;AAEA,SAAAC,iBAAAA;AACA,MAAAvD,QAAAsD,CAAAA,GAAA;AACArC,YAAAA;AACA5B,QAAAmE,KAAA3D;AACAV,QAAAqD;AACA,SAAAA,KAAAxD,KAAAA,MAAA,SAAAwD,GAAAlB,OAAAA;AACA,aAAA;QACAvB,MAAA;QACAf,MAAAwD;QACAI,YAAAA,WAAAA,KAAA;;WAEA;AACA/C,UAAA2D;AACAnE,UAAAoE,KAAAJ,cAAAA;AACAhE,UAAAqE,KAAAd,WAAAA,KAAA;AACAvD,UAAAsE,KAAAV,aAAAA;AACA,UAAA,CAAAU,IAAAA;AAAA,cAAA7D,MAAA,gBAAA;;AACA,aAAA;QACAC,MAAA;QACAsD,eAAAI;QACAb,YAAAc;QACAT,cAAAU;;IAEA;EACA;AACA;AAEA,SAAAV,eAAAA;AACA9D,MAAAkC;AACAJ,UAAAA;AACA,MAAA,QAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,YAAAA;AACA5B,QAAAuE,KAAA,CAAA;AACA,WAAAvC,KAAAkC,eAAAA,KAAAT,MAAAA,GAAAA;AAAAc,MAAAA,GAAAvB,KAAAhB,EAAAA;;AACA,QAAA,CAAAuC,GAAA9C,UAAA,QAAAlB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,YAAAC,MAAA,cAAA;;AACAmB,YAAAA;AACA,WAAA;MACAlB,MAAA;MACA6D,YAAAA;;EAEA;AACA;AAyCAvE,IAAAwE,IAAA;AACA,SAAAC,qBAAAA;AACA,MAAA9D,QAAA6D,CAAAA,GAAA;AACA5C,YAAAA;AACA5B,QAAAmD,KAAAxD,KAAAA;AACA,QAAA,CAAAwD,IAAAA;AAAA,YAAA1C,MAAA,oBAAA;;AACAmB,YAAAA;AACA5B,QAAAoE,KAAAJ,cAAAA;AACA,QAAA,CAAAI,IAAAA;AAAA,YAAA3D,MAAA,oBAAA;;AACAT,QAAAqE,KAAAd,WAAAA,KAAA;AACAvD,QAAAsE,KAAAV,aAAAA;AACA,QAAA,CAAAU,IAAAA;AAAA,YAAA7D,MAAA,oBAAA;;AACA,WAAA;MACAC,MAAA;MACAf,MAAAwD;MACAa,eAAAI;MACAb,YAAAc;MACAT,cAAAU;;EAEA;AACA;AAGAtE,IAAA0E,IAAA;AAEA,SAAAC,sBAAAA;AACA7E,MAAA8E;AACA9E,MAAAqD;AACArD,MAAA+E,KAAA,CAAA;AACA/E,MAAAuE,KAAA,CAAA;AACA,MAAAO,KAAAjE,QAAA+D,CAAAA,GAAA;AACA9C,YAAAA;AACAuB,IAAAA,KAAAxD,KAAAA;AACAkF,IAAAA,KAxEA,SAAAC,sBAAAA;AACAhF,UAAAkC;AACAhC,UAAA+E,KAAA,CAAA;AACAnD,cAAAA;AACA,UAAA,OAAArB,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,gBAAAA;AACA,eAAAI,KAAArB,QAAAwB,CAAAA,GAAA;AACAP,kBAAAA;AACA,cAAA,OAAArB,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,kBAAAC,MAAA,oBAAA;;AACAT,cAAA8D,KAAAD,KAAAA;AACA/D,cAAAkF,KAAAA;AACA,cAAA,OAAAzE,EAAAuB,WAAAtB,CAAAA,GAAA;AACAA;AACAoB,oBAAAA;AAEA,gBAAA,EADAoD,KAAA/C,MAAAA,IAAA,IAAA;AACA,oBAAAxB,MAAA,oBAAA;;UACA;AACAmB,kBAAAA;AACAmD,UAAAA,GAAA/B,KAAA;YACAtC,MAAA;YACAuE,UAAA;cACAvE,MAAA;cACAf,MAAA;gBACAe,MAAA;gBACAuB,OAAAD,GAAAjB,MAAA,CAAA;;;YAGA8C,MAAAC;YACAoB,cAAAF;YACAzB,YAAAA,WAAAA,IAAA;;QAEA;AACA,YAAA,OAAAhD,EAAAuB,WAAAtB,GAAAA,GAAAA;AAAA,gBAAAC,MAAA,oBAAA;;AACAmB,gBAAAA;MACA;AACA,aAAAmD;IACA,EAmCAD;AACAT,IAAAA,KAAAd,WAAAA,KAAA;EACA;AACAvD,MAAAsE,KAAAV,aAAAA;AACA,MAAAU,IAAAA;AACA,WAAA;MACA5D,MAAA;MACAyE,WAAAP,MAAA;MACAjF,MAAAwD;MACA2B,qBAAAD;MACAtB,YAAAc;MACAT,cAAAU;;;AAGA;AAiBA,SAAAzB,MACA3B,IACAkE,IAAAA;AAEA7E,MAAA,YAAA,OAAAW,GAAAmE,OAAAnE,GAAAmE,OAAAnE;AACAV,MAAA;AACA,SArBA,SAAA8E,WAAAA;AACAxF,QAAAkC;AACAJ,YAAAA;AACA5B,QAAAuF,KAAA,CAAA;AACA,WAAAvD,KAAAyC,mBAAAA,KAAAE,oBAAAA,GAAAA;AAAAY,MAAAA,GAAAvC,KAAAhB,EAAAA;;AACA,WAAA;MACAtB,MAAA;MACA6E,aAAAA;;EAEA,EAYAD;AACA;AEndA,SAAAE,YAAAC,IAAAA;AACA,SAAAC,KAAAC,UAAAF,EAAAA;AACA;AAEA,SAAAG,iBAAAH,IAAAA;AACA,SAAA,UAAAA,GAAAI,QAAA,QAAA,OAAA,IAAA;AACA;AAEAC,IAAAC,WAAAC,CAAAA,OAAAA,EAAAA,CACAA,MAAAA,CAAAA,GAAAC;AAIAH,IAAAI,IAEA;EACAC,oBAAAC,IAAAA;AACA,QACA,YAAAA,GAAAC,aAAAA,CACAD,GAAAE,QAAAA,CACAP,SAAAK,GAAAG,mBAAAA,KAAAA,CACAR,SAAAK,GAAAI,UAAAA,GAAAA;AAEA,aAAAN,EAAAO,aAAAL,GAAAM,YAAAA;;AAEAC,QAAAC,KAAAR,GAAAC;AACA,QAAAD,GAAAE,MAAAA;AAAAM,MAAAA,MAAA,MAAAR,GAAAE,KAAAO;;AACA,QAAAd,SAAAK,GAAAG,mBAAAA,GAAA;AACA,UAAA,CAAAH,GAAAE,MAAAA;AAAAM,QAAAA,MAAA;;AACAA,MAAAA,MAAA,MAAAR,GAAAG,oBAAAO,IAAAZ,EAAAa,kBAAAA,EAAAC,KAAA,IAAA,IAAA;IACA;AACA,QAAAjB,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAJ,KAAA,MAAAV,EAAAO,aAAAL,GAAAM,YAAAA;EACA;EACAK,mBAAAX,IAAAA;AACAO,QAAAC,KAAAV,EAAAgB,SAAAd,GAAAe,QAAAA,IAAA,OAAAC,MAAAhB,GAAAiB,IAAAA;AACA,QAAAjB,GAAAkB,cAAAA;AAAAV,MAAAA,MAAA,QAAAQ,MAAAhB,GAAAkB,YAAAA;;AACA,QAAAvB,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAJ;EACA;EACAW,MAAAnB,IAAAA;AACAO,QAAAC,MAAAR,GAAAoB,QAAApB,GAAAoB,MAAAX,QAAA,OAAA,MAAAT,GAAAE,KAAAO;AACA,QAAAd,SAAAK,GAAAqB,SAAAA,GAAA;AACA3B,UAAA4B,KAAAtB,GAAAqB,UAAAX,IAAAZ,EAAAyB,QAAAA;AACA7B,UAAA8B,KAAAhB,KAAA,MAAAc,GAAAV,KAAA,IAAA,IAAA;AACAJ,MAAAA,KACAgB,GAAA3B,SAnCA,KAoCAW,KAAA,UAAAc,GAAAV,KAAA,IAAA,EAAAnB,QAAA,OAAA,MAAA,IAAA,QACA+B;IACA;AACA,QAAA7B,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAZ,GAAAM,eAAAE,KAAA,MAAAV,EAAAO,aAAAL,GAAAM,YAAAA,IAAAE;EACA;EACAiB,aAAAzB,CAAAA,OACAA,GAAA0B,QAAAlC,iBAAAQ,GAAAS,KAAAA,IAAArB,YAAAY,GAAAS,KAAAA;EAEAkB,cAAA3B,CAAAA,OACA,KAAAA,GAAAS;EAEAmB,WAAAC,CAAAA,OACA;EAEAC,UAAA9B,CAAAA,OACAA,GAAAS;EAEAsB,YAAA/B,CAAAA,OACAA,GAAAS;EAEAuB,WAAAhC,CAAAA,OACAA,GAAAS;EAEAwB,MAAAjC,CAAAA,OACAA,GAAAS;EAEAK,UAAAd,CAAAA,OACA,MAAAA,GAAAE,KAAAO;EAEAyB,WAAAlC,CAAAA,OACA,MAAAA,GAAAmC,OAAAzB,IAAAM,KAAAA,EAAAJ,KAAA,IAAA,IAAA;EAEAwB,aAAApC,CAAAA,OACA,MAAAA,GAAAqC,OAAA3B,IAAAZ,EAAAwC,WAAAA,EAAA1B,KAAA,IAAA,IAAA;EAEA0B,aAAAtC,CAAAA,OACAA,GAAAE,KAAAO,QAAA,OAAAO,MAAAhB,GAAAS,KAAAA;EAEA8B,UAAAvC,CAAAA,OACAL,SAAAK,GAAAwC,WAAAA,IAAAxC,GAAAwC,YAAA9B,IAAAM,KAAAA,EAAAJ,KAAA,MAAA,IAAA;EAEAP,cAAAL,CAAAA,OACA,UAAAA,GAAAyC,WAAA/B,IAAAM,KAAAA,EAAAJ,KAAA,IAAA,EAAAnB,QAAA,OAAA,MAAA,IAAA;EAEA8B,UAAAvB,CAAAA,OACAA,GAAAE,KAAAO,QAAA,OAAAO,MAAAhB,GAAAS,KAAAA;EAEAiC,eAAA1C,IAAAA;AACAO,QAAAC,KAAA,QAAAR,GAAAE,KAAAO;AACA,QAAAd,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAJ;EACA;EACAmC,eAAA3C,IAAAA;AACAO,QAAAC,KAAA;AACA,QAAAR,GAAA4C,eAAAA;AAAApC,MAAAA,MAAA,SAAAR,GAAA4C,cAAA1C,KAAAO;;AACA,QAAAd,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAJ,KAAA,MAAAQ,MAAAhB,GAAAM,YAAAA;EACA;EACAuC,mBAAA7C,IAAAA;AACAO,QAAAC,KAAA,cAAAR,GAAAE,KAAAO;AACAD,IAAAA,MAAA,SAAAR,GAAA4C,cAAA1C,KAAAO;AACA,QAAAd,SAAAK,GAAAI,UAAAA,GAAAA;AAAAI,MAAAA,MAAA,MAAAR,GAAAI,WAAAM,IAAAZ,EAAAe,SAAAA,EAAAD,KAAA,GAAA;;AACA,WAAAJ,KAAA,MAAAQ,MAAAhB,GAAAM,YAAAA;EACA;EACAO,UAAAb,IAAAA;AACAO,QAAAC,KAAA,MAAAR,GAAAE,KAAAO;AACA,QAAAd,SAAAK,GAAAqB,SAAAA,GAAAA;AAAAb,MAAAA,MAAA,MAAAR,GAAAqB,UAAAX,IAAAZ,EAAAyB,QAAAA,EAAAX,KAAA,IAAA,IAAA;;AACA,WAAAJ;EACA;EACAsC,WAAA9C,CAAAA,OACAA,GAAAE,KAAAO;EAEAsC,UAAA/C,CAAAA,OACA,MAAAgB,MAAAhB,GAAAiB,IAAAA,IAAA;EAEA+B,aAAAhD,CAAAA,OACAgB,MAAAhB,GAAAiB,IAAAA,IAAA;;AAIA,SAAAD,MAAAhB,IAAAA;AACA,SAAAF,EAAAE,GAAAiD,IAAAA,IAAA,EAAAjD,GAAAiD,IAAAA,EAAAjD,EAAAA,IAAA;AACA;;;AEpIA,IAAI,sBAAsB,MAAM;AAAC;AAEjC,IAAIkD,KAAI;AAER,SAAS,MAAMA,IAAG;AAChB,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAGA;AAAA,EACL;AACF;AAEA,SAAS,KAAKA,IAAG;AACf,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAGA;AAAA,EACL;AACF;AAEA,IAAI,sBAAsB,MAAM,cAAc,OAAO,UAAU,OAAO,iBAAiB;AAIvF,IAAI,WAAW,CAAAC,OAAKA;AAkJpB,SAAS,OAAOC,IAAG;AACjB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,IAAAH,GAAG,CAAAG,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,QAAAF,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,QAAAD,KAAIC,GAAE,CAAC;AACP,QAAAF,GAAEE,EAAC;AAAA,MACL,WAAW,CAACJ,GAAEI,GAAE,CAAC,CAAC,GAAG;AACnB,QAAAD,GAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAD,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,IAAIA,IAAG;AACd,SAAO,CAAAJ,OAAK,CAAAC,OAAKD,GAAG,CAAAA,OAAK;AACvB,QAAI,MAAMA,MAAK,MAAMA,GAAE,KAAK;AAC1B,MAAAC,GAAED,EAAC;AAAA,IACL,OAAO;AACL,MAAAC,GAAE,KAAKG,GAAEJ,GAAE,CAAC,CAAC,CAAC,CAAC;AAAA,IACjB;AAAA,EACF,CAAE;AACJ;AAEA,SAAS,SAASA,IAAG;AACnB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI,CAAC;AACT,QAAIE,KAAID;AACR,QAAIE,KAAI;AACR,QAAIC,KAAI;AACR,IAAAN,GAAG,CAAAA,OAAK;AACN,UAAIM,IAAG;AAAA,MAAC,WAAW,MAAMN,IAAG;AAC1B,QAAAM,KAAI;AACJ,YAAI,CAACJ,GAAE,QAAQ;AACb,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,MAAMD,GAAE,KAAK;AACtB,QAAAI,KAAIJ,GAAE,CAAC;AAAA,MACT,OAAO;AACL,QAAAK,KAAI;AACJ,SAAC,SAAS,iBAAiBN,IAAG;AAC5B,cAAIC,KAAIG;AACR,UAAAJ,GAAG,CAAAI,OAAK;AACN,gBAAI,MAAMA,IAAG;AACX,kBAAID,GAAE,QAAQ;AACZ,oBAAIH,KAAIG,GAAE,QAAQF,EAAC;AACnB,oBAAID,KAAI,IAAI;AACV,mBAACG,KAAIA,GAAE,MAAM,GAAG,OAAOH,IAAG,CAAC;AAAA,gBAC7B;AACA,oBAAI,CAACG,GAAE,QAAQ;AACb,sBAAII,IAAG;AACL,oBAAAL,GAAE,CAAC;AAAA,kBACL,WAAW,CAACI,IAAG;AACb,oBAAAA,KAAI;AACJ,oBAAAD,GAAE,CAAC;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF,WAAW,MAAMD,GAAE,KAAK;AACtB,cAAAD,GAAE,KAAKF,KAAIG,GAAE,CAAC,CAAC;AACf,cAAAH,GAAE,CAAC;AAAA,YACL,WAAWE,GAAE,QAAQ;AACnB,cAAAD,GAAEE,EAAC;AACH,cAAAH,GAAE,CAAC;AAAA,YACL;AAAA,UACF,CAAE;AAAA,QACJ,EAAED,GAAEC,GAAE,CAAC,CAAC,CAAC;AACT,YAAI,CAACK,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAE;AACF,IAAAH,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAI,CAACG,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAF,GAAE,CAAC;AAAA,QACL;AACA,iBAASL,KAAI,GAAGC,KAAIE,IAAGD,KAAIC,GAAE,QAAQH,KAAIE,IAAGF,MAAK;AAC/C,UAAAC,GAAED,EAAC,EAAE,CAAC;AAAA,QACR;AACA,QAAAG,GAAE,SAAS;AAAA,MACb,OAAO;AACL,YAAI,CAACI,MAAK,CAACD,IAAG;AACZ,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL,OAAO;AACL,UAAAC,KAAI;AAAA,QACN;AACA,iBAASE,KAAI,GAAGC,KAAIN,IAAGO,KAAIP,GAAE,QAAQK,KAAIE,IAAGF,MAAK;AAC/C,UAAAC,GAAED,EAAC,EAAE,CAAC;AAAA,QACR;AAAA,MACF;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,SAASJ,IAAG;AACnB,SAAO,SAAS,QAAQ,EAAEA,EAAC;AAC7B;AAEA,SAAS,MAAMA,IAAG;AAChB,SAAO,SAAS,EAAEA,EAAC,CAAC;AACtB;AAEA,SAAS,MAAMA,IAAG;AAChB,SAAO,CAAAJ,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI;AACR,IAAAF,GAAG,CAAAA,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AACH,QAAAG,GAAE;AAAA,MACJ,WAAW,MAAMJ,GAAE,KAAK;AACtB,YAAIG,KAAIH,GAAE,CAAC;AACX,QAAAC,GAAE,MAAO,CAAAD,OAAK;AACZ,cAAI,MAAMA,IAAG;AACX,YAAAE,KAAI;AACJ,YAAAC,GAAE,CAAC;AACH,YAAAC,GAAE;AAAA,UACJ,OAAO;AACL,YAAAD,GAAEH,EAAC;AAAA,UACL;AAAA,QACF,CAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAC,GAAED,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,OAAOI,IAAG;AACjB,SAAO,CAAAJ,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAI;AACR,IAAAF,GAAG,CAAAA,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AAAA,MACL,WAAW,MAAMD,GAAE,KAAK;AACtB,YAAIG,KAAIH,GAAE,CAAC;AACX,QAAAC,GAAE,MAAO,CAAAG,OAAK;AACZ,cAAI,MAAMA,IAAG;AACX,YAAAF,KAAI;AAAA,UACN;AACA,UAAAC,GAAEC,EAAC;AAAA,QACL,CAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAA,GAAEJ,GAAE,CAAC,CAAC;AACN,QAAAC,GAAED,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAEA,SAAS,QAAQI,IAAG;AAClB,SAAO,CAAAJ,OAAK,CAAAC,OAAKD,GAAG,CAAAA,OAAK;AACvB,QAAI,MAAMA,IAAG;AACX,MAAAC,GAAE,CAAC;AAAA,IACL,WAAW,MAAMD,GAAE,KAAK;AACtB,MAAAC,GAAED,EAAC;AACH,MAAAI,GAAE;AAAA,IACJ,OAAO;AACL,MAAAH,GAAED,EAAC;AAAA,IACL;AAAA,EACF,CAAE;AACJ;AAqEA,SAAS,MAAMW,IAAG;AAChB,MAAIC,KAAI,CAAC;AACT,MAAIC,KAAIC;AACR,MAAIC,KAAI;AACR,SAAO,CAAAD,OAAK;AACV,IAAAF,GAAE,KAAKE,EAAC;AACR,QAAI,MAAMF,GAAE,QAAQ;AAClB,MAAAD,GAAG,CAAAG,OAAK;AACN,YAAI,MAAMA,IAAG;AACX,mBAASH,KAAI,GAAGK,KAAIJ,IAAGK,KAAIL,GAAE,QAAQD,KAAIM,IAAGN,MAAK;AAC/C,YAAAK,GAAEL,EAAC,EAAE,CAAC;AAAA,UACR;AACA,UAAAC,GAAE,SAAS;AAAA,QACb,WAAW,MAAME,GAAE,KAAK;AACtB,UAAAD,KAAIC,GAAE,CAAC;AAAA,QACT,OAAO;AACL,UAAAC,KAAI;AACJ,mBAASG,KAAI,GAAGC,KAAIP,IAAGQ,KAAIR,GAAE,QAAQM,KAAIE,IAAGF,MAAK;AAC/C,YAAAC,GAAED,EAAC,EAAEJ,EAAC;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAE;AAAA,IACJ;AACA,IAAAA,GAAE,MAAO,CAAAH,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAIK,KAAIJ,GAAE,QAAQE,EAAC;AACnB,YAAIE,KAAI,IAAI;AACV,WAACJ,KAAIA,GAAE,MAAM,GAAG,OAAOI,IAAG,CAAC;AAAA,QAC7B;AACA,YAAI,CAACJ,GAAE,QAAQ;AACb,UAAAC,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,CAACE,IAAG;AACb,QAAAA,KAAI;AACJ,QAAAF,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAuGA,SAAS,UAAUQ,IAAG;AACpB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAID;AACR,QAAIE,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,IAAAR,GAAG,CAAAA,OAAK;AACN,UAAIQ,IAAG;AAAA,MAAC,WAAW,MAAMR,IAAG;AAC1B,QAAAQ,KAAI;AACJ,YAAI,CAACD,IAAG;AACN,UAAAN,GAAE,CAAC;AAAA,QACL;AAAA,MACF,WAAW,MAAMD,GAAE,KAAK;AACtB,QAAAE,KAAIF,GAAE,CAAC;AAAA,MACT,OAAO;AACL,YAAIO,IAAG;AACL,UAAAH,GAAE,CAAC;AACH,UAAAA,KAAID;AAAA,QACN;AACA,YAAI,CAACE,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL,OAAO;AACL,UAAAG,KAAI;AAAA,QACN;AACA,SAAC,SAAS,iBAAiBF,IAAG;AAC5B,UAAAI,KAAI;AACJ,UAAAJ,GAAG,CAAAA,OAAK;AACN,gBAAI,CAACI,IAAG;AAAA,YAAC,WAAW,MAAMJ,IAAG;AAC3B,cAAAI,KAAI;AACJ,kBAAIC,IAAG;AACL,gBAAAP,GAAE,CAAC;AAAA,cACL,WAAW,CAACI,IAAG;AACb,gBAAAA,KAAI;AACJ,gBAAAH,GAAE,CAAC;AAAA,cACL;AAAA,YACF,WAAW,MAAMC,GAAE,KAAK;AACtB,cAAAG,KAAI;AACJ,eAACF,KAAID,GAAE,CAAC,GAAG,CAAC;AAAA,YACd,OAAO;AACL,cAAAF,GAAEE,EAAC;AACH,kBAAI,CAACG,IAAG;AACN,gBAAAF,GAAE,CAAC;AAAA,cACL,OAAO;AACL,gBAAAE,KAAI;AAAA,cACN;AAAA,YACF;AAAA,UACF,CAAE;AAAA,QACJ,EAAEP,GAAEC,GAAE,CAAC,CAAC,CAAC;AAAA,MACX;AAAA,IACF,CAAE;AACF,IAAAC,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,YAAI,CAACK,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAN,GAAE,CAAC;AAAA,QACL;AACA,YAAIK,IAAG;AACL,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL;AAAA,MACF,OAAO;AACL,YAAI,CAACI,MAAK,CAACH,IAAG;AACZ,UAAAA,KAAI;AACJ,UAAAH,GAAE,CAAC;AAAA,QACL;AACA,YAAIK,MAAK,CAACD,IAAG;AACX,UAAAA,KAAI;AACJ,UAAAF,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAMA,SAAS,KAAKK,IAAG;AACf,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,IAAAL,GAAG,CAAAG,OAAK;AACN,UAAIC,IAAG;AAAA,MAAC,WAAW,MAAMD,IAAG;AAC1B,QAAAC,KAAI;AACJ,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,YAAIJ,MAAK,GAAG;AACV,UAAAK,KAAI;AACJ,UAAAH,GAAE,CAAC;AACH,UAAAE,GAAE,CAAC,EAAE,CAAC;AAAA,QACR,OAAO;AACL,UAAAD,KAAIC,GAAE,CAAC;AAAA,QACT;AAAA,MACF,WAAWE,OAAMN,IAAG;AAClB,QAAAE,GAAEE,EAAC;AACH,YAAI,CAACC,MAAKC,MAAKN,IAAG;AAChB,UAAAK,KAAI;AACJ,UAAAH,GAAE,CAAC;AACH,UAAAC,GAAE,CAAC;AAAA,QACL;AAAA,MACF,OAAO;AACL,QAAAD,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,IAAAF,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACC,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAF,GAAE,CAAC;AAAA,MACL,WAAW,MAAMC,MAAK,CAACC,MAAKC,KAAIN,IAAG;AACjC,QAAAG,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AA2BA,SAAS,UAAUI,IAAG;AACpB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIC,KAAIC;AACR,QAAIC,KAAID;AACR,QAAIE,KAAI;AACR,IAAAL,GAAG,CAAAG,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAD,GAAE,CAAC;AACH,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAME,GAAE,KAAK;AACtB,QAAAD,KAAIC,GAAE,CAAC;AACP,QAAAJ,GAAG,CAAAI,OAAK;AACN,cAAI,MAAMA,IAAG;AAAA,UAAC,WAAW,MAAMA,GAAE,KAAK;AACpC,aAACC,KAAID,GAAE,CAAC,GAAG,CAAC;AAAA,UACd,OAAO;AACL,YAAAE,KAAI;AACJ,YAAAD,GAAE,CAAC;AACH,YAAAF,GAAE,CAAC;AACH,YAAAD,GAAE,CAAC;AAAA,UACL;AAAA,QACF,CAAE;AAAA,MACJ,OAAO;AACL,QAAAA,GAAEE,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,IAAAF,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACE,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAH,GAAE,CAAC;AACH,QAAAE,GAAE,CAAC;AAAA,MACL,WAAW,CAACC,IAAG;AACb,QAAAH,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,UAAUH,IAAGC,IAAG;AACvB,SAAO,CAAAC,OAAK,CAAAC,OAAK;AACf,QAAIE,KAAID;AACR,QAAIE,KAAI;AACR,IAAAJ,GAAG,CAAAE,OAAK;AACN,UAAIE,IAAG;AAAA,MAAC,WAAW,MAAMF,IAAG;AAC1B,QAAAE,KAAI;AACJ,QAAAH,GAAE,CAAC;AAAA,MACL,WAAW,MAAMC,GAAE,KAAK;AACtB,QAAAC,KAAID,GAAE,CAAC;AACP,QAAAD,GAAEC,EAAC;AAAA,MACL,WAAW,CAACJ,GAAEI,GAAE,CAAC,CAAC,GAAG;AACnB,QAAAE,KAAI;AACJ,YAAIL,IAAG;AACL,UAAAE,GAAEC,EAAC;AAAA,QACL;AACA,QAAAD,GAAE,CAAC;AACH,QAAAE,GAAE,CAAC;AAAA,MACL,OAAO;AACL,QAAAF,GAAEC,EAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ;AACF;AAqGA,SAAS,KAAKG,IAAG;AACf,SAAO,CAAAC,OAAKD,GAAE,EAAEC,EAAC;AACnB;AAEA,SAAS,kBAAkBD,IAAG;AAC5B,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIF,GAAE,oBAAoB,CAAC,KAAKA,GAAE,oBAAoB,CAAC,EAAE,KAAKA;AAClE,QAAIG,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC;AACJ,IAAAL,GAAE,MAAO,OAAMD,OAAK;AAClB,UAAI,MAAMA,IAAG;AACX,QAAAG,KAAI;AACJ,YAAID,GAAE,QAAQ;AACZ,UAAAA,GAAE,OAAO;AAAA,QACX;AAAA,MACF,WAAWE,IAAG;AACZ,QAAAC,KAAI;AAAA,MACN,OAAO;AACL,aAAKA,KAAID,KAAI,MAAIC,MAAK,CAACF,MAAK;AAC1B,eAAKG,KAAI,MAAMJ,GAAE,KAAK,GAAG,MAAM;AAC7B,YAAAC,KAAI;AACJ,gBAAID,GAAE,QAAQ;AACZ,oBAAMA,GAAE,OAAO;AAAA,YACjB;AACA,YAAAD,GAAE,CAAC;AAAA,UACL,OAAO;AACL,gBAAI;AACF,cAAAI,KAAI;AACJ,cAAAJ,GAAE,KAAKK,GAAE,KAAK,CAAC;AAAA,YACjB,SAASN,IAAG;AACV,kBAAIE,GAAE,OAAO;AACX,oBAAIC,KAAI,CAAC,EAAE,MAAMD,GAAE,MAAMF,EAAC,GAAG,MAAM;AACjC,kBAAAC,GAAE,CAAC;AAAA,gBACL;AAAA,cACF,OAAO;AACL,sBAAMD;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAI,KAAI;AAAA,MACN;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,aAAaJ,IAAG;AACvB,MAAIA,GAAE,OAAO,aAAa,GAAG;AAC3B,WAAO,kBAAkBA,EAAC;AAAA,EAC5B;AACA,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIF,GAAE,OAAO,QAAQ,EAAE;AAC3B,QAAIG,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC,KAAI;AACR,QAAIC;AACJ,IAAAL,GAAE,MAAO,CAAAD,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,QAAAG,KAAI;AACJ,YAAID,GAAE,QAAQ;AACZ,UAAAA,GAAE,OAAO;AAAA,QACX;AAAA,MACF,WAAWE,IAAG;AACZ,QAAAC,KAAI;AAAA,MACN,OAAO;AACL,aAAKA,KAAID,KAAI,MAAIC,MAAK,CAACF,MAAK;AAC1B,eAAKG,KAAIJ,GAAE,KAAK,GAAG,MAAM;AACvB,YAAAC,KAAI;AACJ,gBAAID,GAAE,QAAQ;AACZ,cAAAA,GAAE,OAAO;AAAA,YACX;AACA,YAAAD,GAAE,CAAC;AAAA,UACL,OAAO;AACL,gBAAI;AACF,cAAAI,KAAI;AACJ,cAAAJ,GAAE,KAAKK,GAAE,KAAK,CAAC;AAAA,YACjB,SAASN,IAAG;AACV,kBAAIE,GAAE,OAAO;AACX,oBAAIC,KAAI,CAAC,CAACD,GAAE,MAAMF,EAAC,EAAE,MAAM;AACzB,kBAAAC,GAAE,CAAC;AAAA,gBACL;AAAA,cACF,OAAO;AACL,sBAAMD;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAI,KAAI;AAAA,MACN;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,IAAI,IAAI;AAER,SAAS,UAAUJ,IAAG;AACpB,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAI;AACR,IAAAD,GAAE,MAAO,CAAAE,OAAK;AACZ,UAAI,MAAMA,IAAG;AACX,QAAAD,KAAI;AAAA,MACN,WAAW,CAACA,IAAG;AACb,QAAAA,KAAI;AACJ,QAAAD,GAAE,KAAKD,EAAC,CAAC;AACT,QAAAC,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,KAAKD,IAAG;AACf,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAI;AACR,QAAIC,KAAIH,GAAE;AAAA,MACR,KAAKA,IAAG;AACN,YAAI,CAACE,IAAG;AACN,UAAAD,GAAE,KAAKD,EAAC,CAAC;AAAA,QACX;AAAA,MACF;AAAA,MACA,WAAW;AACT,YAAI,CAACE,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAAA,GAAE,MAAO,CAAAD,OAAK;AACZ,UAAI,MAAMA,MAAK,CAACE,IAAG;AACjB,QAAAA,KAAI;AACJ,QAAAC,GAAE;AAAA,MACJ;AAAA,IACF,CAAE,CAAC;AAAA,EACL;AACF;AAEA,SAAS,cAAc;AACrB,MAAIH;AACJ,MAAIC;AACJ,SAAO;AAAA,IACL,QAAQ,MAAM,KAAM,CAAAC,OAAK;AACvB,MAAAF,KAAIE,GAAE;AACN,MAAAD,KAAIC,GAAE;AACN,aAAO;AAAA,IACT,CAAE,CAAC;AAAA,IACH,KAAKD,IAAG;AACN,UAAID,IAAG;AACL,QAAAA,GAAEC,EAAC;AAAA,MACL;AAAA,IACF;AAAA,IACA,WAAW;AACT,UAAIA,IAAG;AACL,QAAAA,GAAE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAiCA,SAAS,YAAYM,IAAG;AACtB,SAAO,KAAM,CAAAC,OAAK;AAChB,IAAAD,GAAE,KAAM,CAAAA,OAAK;AACX,cAAQ,QAAQA,EAAC,EAAE,KAAM,MAAM;AAC7B,QAAAC,GAAE,KAAKD,EAAC;AACR,QAAAC,GAAE,SAAS;AAAA,MACb,CAAE;AAAA,IACJ,CAAE;AACF,WAAO;AAAA,EACT,CAAE;AACJ;AAEA,SAAS,UAAUA,IAAG;AACpB,SAAO,CAAAC,OAAK;AACV,QAAIC,KAAIH;AACR,QAAII,KAAI;AACR,IAAAF,GAAG,CAAAF,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,QAAAI,KAAI;AAAA,MACN,WAAW,MAAMJ,GAAE,KAAK;AACtB,SAACG,KAAIH,GAAE,CAAC,GAAG,CAAC;AAAA,MACd,WAAW,CAACI,IAAG;AACb,QAAAH,GAAED,GAAE,CAAC,CAAC;AACN,QAAAG,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE;AACF,WAAO;AAAA,MACL,cAAc;AACZ,YAAI,CAACC,IAAG;AACN,UAAAA,KAAI;AACJ,UAAAD,GAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,QAAQE,IAAG;AAClB,YAAW,CAAAA,OAAK;AAAA,EAAC,CAAE,EAAEA,EAAC;AACxB;AAmFA,SAAS,UAAUC,IAAG;AACpB,SAAO,IAAI,QAAS,CAAAC,OAAK;AACvB,QAAIC,KAAIC;AACR,QAAIC;AACJ,IAAAJ,GAAG,CAAAG,OAAK;AACN,UAAI,MAAMA,IAAG;AACX,gBAAQ,QAAQC,EAAC,EAAE,KAAKH,EAAC;AAAA,MAC3B,WAAW,MAAME,GAAE,KAAK;AACtB,SAACD,KAAIC,GAAE,CAAC,GAAG,CAAC;AAAA,MACd,OAAO;AACL,QAAAC,KAAID,GAAE,CAAC;AACP,QAAAD,GAAE,CAAC;AAAA,MACL;AAAA,IACF,CAAE;AAAA,EACJ,CAAE;AACJ;;;AClpCA,IAAMG,wBAAyBC,CAAAA,OAAAA;AAC7B,MACEA,MACAA,GAAMC,YACLD,GAAME,cAA6B,mBAAfF,GAAMG,OAAAA;AAE3B,WAAOH;aACmB,YAAA,OAAVA,MAAsBA,GAAMC,SAAAA;AAC5C,WAAO,IAAIG,aACTJ,GAAMC,SACND,GAAMK,OACNL,GAAMM,QACNN,GAAMO,WACNP,GAAMQ,MACNR,IACAA,GAAME,cAAc,CAAA,CAAA;;AAGtB,WAAO,IAAIE,aAAaJ,EAAAA;;AAC1B;AAiBK,IAAMS,gBAAN,cAA4BC,MAAAA;EAwCjCC,YAAYC,IAAAA;AAKV,QAAMC,MAA2BD,GAAME,iBAAiB,CAAA,GAAIC,IAC1DhB,qBAAAA;AAEF,QAAME,MAnGmBe,CAC3BC,IACAC,OAAAA;AAEA,UAAIlB,KAAQ;AACZ,UAAIiB,IAAAA;AAAY,eAAQ,aAAYA,GAAWhB,OAAAA;;AAC/C,UAAIiB,IAAAA;AACF,iBAAWC,MAAOD,IAAa;AAC7B,cAAIlB,IAAAA;AAAOA,YAAAA,MAAS;;AACpBA,UAAAA,MAAU,aAAYmB,GAAIlB,OAAAA;QAC5B;;AAEF,aAAOD;IAAK,GAwFRY,GAAMQ,cACNP,EAAAA;AAGFQ,UAAMpB,EAAAA;AAENqB,SAAKnB,OAAO;AACZmB,SAAKrB,UAAUA;AACfqB,SAAKR,gBAAgBD;AACrBS,SAAKF,eAAeR,GAAMQ;AAC1BE,SAAKC,WAAWX,GAAMW;EACxB;EAEAC,WAAAA;AACE,WAAOF,KAAKrB;EACd;;ACvFK,IAAMwB,QAAQA,CAACC,IAAWC,OAAAA;AAC/B,MAAIC,KAAqB,KAAhBD,MAAQ;AACjB,WAASE,KAAI,GAAGC,KAAe,IAAXJ,GAAEK,QAAYF,KAAIC,IAAGD,MAAAA;AACvCD,IAAAA,MAAKA,MAAK,KAAKA,KAAIF,GAAEM,WAAWH,EAAAA;;AAClC,SAAOD;AAAC;ACjCV,IAAMK,KAAO,oBAAIC;AACjB,IAAMC,KAAQ,oBAAIC;AAElB,IAAMC,YAAaX,CAAAA,OAAAA;AACjB,MAAU,SAANA,MAAcO,GAAKK,IAAIZ,EAAAA,GAAAA;AACzB,WAAO;aACe,YAAA,OAANA,IAAAA;AAChB,WAAOa,KAAKF,UAAUX,EAAAA,KAAM;aACnBA,GAAEc,QAAAA;AACX,WAAOH,UAAUX,GAAEc,OAAAA,CAAAA;aACVC,MAAMC,QAAQhB,EAAAA,GAAI;AAC3B,QAAIiB,KAAM;AACV,aAAWC,MAASlB,IAAG;AACrB,UAAIiB,GAAIZ,SAAS,GAAA;AAAGY,QAAAA,MAAO;;AAC3BA,MAAAA,MAAON,UAAUO,EAAAA,KAAU;IAC7B;AAEA,WADAD,MAAO;EAET,WACGE,OAAoBC,mBAAmBpB,cAAamB,MACpDE,OAAoBD,mBAAmBpB,cAAaqB,IAAAA;AAErD,WAAO;;AAGT,MAAMC,KAAOC,OAAOD,KAAKtB,EAAAA,EAAGwB,KAAAA;AAC5B,MAAA,CACGF,GAAKjB,UACNL,GAAEf,eACFsC,OAAOE,eAAezB,EAAAA,EAAGf,gBAAgBsC,OAAOG,UAAUzC,aAC1D;AACA,QAAM0C,KAAMlB,GAAMmB,IAAI5B,EAAAA,KAAM6B,KAAKC,OAAAA,EAAShC,SAAS,EAAA,EAAIiC,MAAM,CAAA;AAC7DtB,IAAAA,GAAMuB,IAAIhC,IAAG2B,EAAAA;AACb,WAAOhB,UAAU;MAAEsB,OAAON;;EAC5B;AAEApB,EAAAA,GAAK2B,IAAIlC,EAAAA;AACT,MAAIiB,KAAM;AACV,WAAWU,MAAOL,IAAM;AACtB,QAAMJ,KAAQP,UAAUX,GAAE2B,EAAAA,CAAAA;AAC1B,QAAIT,IAAO;AACT,UAAID,GAAIZ,SAAS,GAAA;AAAGY,QAAAA,MAAO;;AAC3BA,MAAAA,MAAON,UAAUgB,EAAAA,IAAO,MAAMT;IAChC;EACF;AAEAX,EAAAA,GAAK4B,OAAOnC,EAAAA;AAEZ,SADAiB,MAAO;AACG;AAGZ,IAAMmB,UAAUA,CAAC/C,IAAcP,IAAckB,OAAAA;AAC3C,MAAS,QAALA,MAA0B,YAAA,OAANA,MAAkBA,GAAEc,UAAUP,GAAKK,IAAIZ,EAAAA,GAAAA;EAAAA,WAEpDe,MAAMC,QAAQhB,EAAAA,GAAAA;AACvB,aAASG,KAAI,GAAGC,KAAIJ,GAAEK,QAAQF,KAAIC,IAAGD,MAAAA;AACnCiC,cAAQ/C,IAAM,GAAEP,EAAAA,IAAQqB,EAAAA,IAAKH,GAAEG,EAAAA,CAAAA;;aACxBH,cAAamB,MAAmBnB,cAAaqB,IAAAA;AACtDhC,IAAAA,GAAI2C,IAAIlD,IAAMkB,EAAAA;SACT;AACLO,IAAAA,GAAK2B,IAAIlC,EAAAA;AACT,aAAW2B,MAAOJ,OAAOD,KAAKtB,EAAAA,GAAAA;AAAIoC,cAAQ/C,IAAM,GAAEP,EAAAA,IAAQ6C,EAAAA,IAAO3B,GAAE2B,EAAAA,CAAAA;;EACrE;AAAA;AAiBWU,IAAAA,qBAAsBrC,CAAAA,OAAAA;AACjCO,EAAAA,GAAK+B,MAAAA;AACL,SAAO3B,UAAUX,EAAAA;AAAE;AAGrB,IAAMoB,kBAAN,MAAMA;AAAAA;AACN,IAAMD,KAAkC,eAAA,OAAToB,OAAuBA,OAAOnB;AAC7D,IAAMC,KAAkC,eAAA,OAATmB,OAAuBA,OAAOpB;AClE7D,IAAMqB,KAAoB;AAC1B,IAAMC,KAAkB;AAExB,IAAMC,wBAAwBA,CAACC,IAAaC,OAC1CA,KAAM,KAAM,IAAID,GAAIE,QAAQJ,IAAiB,IAAA,IAAQE;AAGvD,IAAMG,mBAAoBC,CAAAA,OACxBA,GAAKC,MAAMR,EAAAA,EAAmBpD,IAAIsD,qBAAAA,EAAuBO,KAAK,EAAA,EAAIC,KAAAA;AAEpE,IAAMC,KAAS,oBAAIC;AACnB,IAAMC,KAAO,oBAAID;AAgBJE,IAAAA,oBACXP,CAAAA,OAAAA;AAEA,MAAIQ;AACJ,MAAoB,YAAA,OAATR,IAAAA;AACTQ,IAAAA,KAAUT,iBAAiBC,EAAAA;aAClBA,GAAKS,OAAOH,GAAK1B,IAAKoB,GAA2Bf,KAAAA,MAAWe,IAAAA;AACrEQ,IAAAA,KAAUR,GAAKS,IAAI7E,OAAO8E;SACrB;AACLF,IAAAA,KAAUJ,GAAOxB,IAAIoB,EAAAA,KAASD,iBAAiBY,MAAMX,EAAAA,CAAAA;AACrDI,IAAAA,GAAOpB,IAAIgB,IAAMQ,EAAAA;EACnB;AAEA,MAAoB,YAAA,OAATR,MAAAA,CAAsBA,GAAKS,KAAAA;AACnCT,IAAAA,GAAaS,MAAM;MAClBG,OAAO;MACPC,KAAKL,GAAQnD;MACbzB,QAAQ;QACN8E,MAAMF;QACN/E,MA/CY;QAgDZqF,gBAAgB;UAAEC,MAAM;UAAGC,QAAQ;;;;;AAKzC,SAAOR;AAAO;AAehB,IAAMS,eACJjB,CAAAA,OAAAA;AAEA,MAAIrB,KAAM5B,MAAMwD,kBAAkBP,EAAAA,CAAAA;AAElC,MAAKA,GAAsBkB,aAAa;AACtC,QAAMC,KAAgBC,iBAAiBpB,EAAAA;AACvC,QAAImB,IAAAA;AAAexC,MAAAA,KAAM5B,MAAO;IAAMoE,EAAAA,IAAiBxC,EAAAA;;EACzD;AACA,SAAOA;AAAG;AAeC0C,IAAAA,cAAerB,CAAAA,OAAAA;AAC1B,MAAIrB;AACJ,MAAI2C;AACJ,MAAoB,YAAA,OAATtB,IAAmB;AAC5BrB,IAAAA,KAAMsC,aAAajB,EAAAA;AACnBsB,IAAAA,KAAQhB,GAAK1B,IAAID,EAAAA,KAAQ4C,MAAMvB,IAAM;MAAEwB,YAAAA;;EACzC,OAAO;AACL7C,IAAAA,KAAOqB,GAA2Bf,SAASgC,aAAajB,EAAAA;AACxDsB,IAAAA,KAAQhB,GAAK1B,IAAID,EAAAA,KAAQqB;EAC3B;AAGA,MAAA,CAAKsB,GAAMb,KAAAA;AAAKF,sBAAkBe,EAAAA;;AAEjCA,EAAAA,GAA4BrC,QAAQN;AACrC2B,EAAAA,GAAKtB,IAAIL,IAAK2C,EAAAA;AACd,SAAOA;AAAK;AAiBP,IAAMG,gBAAgBA,CAI3BC,IACAC,IACAnG,OAAAA;AAEA,MAAMoG,KAAYD,MAAe,CAAA;AACjC,MAAML,KAAQD,YAAYK,EAAAA;AAC1B,MAAMG,KAAcxC,mBAAmBuC,EAAAA;AACvC,MAAIjD,KAAM2C,GAAMrC;AAChB,MAAoB,SAAhB4C,IAAAA;AAAsBlD,IAAAA,KAAM5B,MAAM8E,IAAalD,EAAAA;;AACnD,SAAO;IAAEA,KAAAA;IAAK2C,OAAAA;IAAOM,WAAAA;IAAWpG,YAAAA;;AAAY;AAOvC,IAAM4F,mBAAoBE,CAAAA,OAAAA;AAC/B,WAAWtB,MAAQsB,GAAMJ,aAAAA;AACvB,QAAIlB,GAAK8B,SAASC,EAAKC,sBAAAA;AACrB,aAAOhC,GAAKvE,OAAOuE,GAAKvE,KAAKyC,QAAAA;;;AAEjC;AAOW+D,IAAAA,mBAAoBX,CAAAA,OAAAA;AAC/B,WAAWtB,MAAQsB,GAAMJ,aAAAA;AACvB,QAAIlB,GAAK8B,SAASC,EAAKC,sBAAAA;AACrB,aAAOhC,GAAKkC;;;AAEhB;AC9JK,IAAMC,aAAaA,CACxBD,IACAE,IACAvF,OAAAA;AAEA,MAAA,EACI,UAAUuF,MACT,YAAYA,MAAYrE,MAAMC,QAAQoE,GAAOC,MAAAA,IAAAA;AAEhD,UAAM,IAAIrG,MAAM,YAAA;;AAGlB,MAAMsG,KAAoC,mBAAnBJ,GAAUJ;AACjC,SAAO;IACLI,WAAAA;IACAK,MAAMH,GAAOG;IACbjH,OAAOyC,MAAMC,QAAQoE,GAAOC,MAAAA,IACxB,IAAItG,cAAc;MAChBK,eAAegG,GAAOC;MACtBxF,UAAAA;;IAGNrB,YAAY4G,GAAO5G,aAAa;SAAK4G,GAAO5G;;IAC5CgH,SAA2B,QAAlBJ,GAAOI,UAAkBF,KAAiBF,GAAOI;IAC1DC,OAAAA;;AACD;AAGH,IAAMC,YAAYA,CAACC,IAAa/G,OAAAA;AAC9B,MAAsB,YAAA,OAAX+G,MAAiC,QAAVA,IAAAA;AAChC,QAAA,CACGA,GAAO1G,eACR0G,GAAO1G,gBAAgBsC,UACvBR,MAAMC,QAAQ2E,EAAAA,GACd;AACAA,MAAAA,KAAS5E,MAAMC,QAAQ2E,EAAAA,IAAU,CAAA,GAAIA,EAAAA,IAAU;WAAKA;;AACpD,eAAWhE,MAAOJ,OAAOD,KAAK1C,EAAAA,GAAAA;AAC5B+G,QAAAA,GAAOhE,EAAAA,IAAO+D,UAAUC,GAAOhE,EAAAA,GAAM/C,GAAO+C,EAAAA,CAAAA;;AAC9C,aAAOgE;IACT;;AAEF,SAAO/G;AAAM;AAqBR,IAAMgH,mBAAmBA,CAC9BC,IACAC,IACAjG,IACAkG,OAAAA;AAEA,MAAIV,KAASQ,GAAWvH,QAAQuH,GAAWvH,MAAMc,gBAAgB,CAAA;AACjE,MAAI4G,KAAAA,CAAAA,CACAH,GAAWrH,cAAAA,CAAAA,EAAiBsH,GAAWG,WAAWH,IAAYtH;AAClE,MAAMA,KAAa;OACdqH,GAAWrH;QACVsH,GAAWG,WAAWH,IAAYtH;;AAGxC,MAAI0H,KAAcJ,GAAWI;AAG7B,MAAI,UAAUJ,IAAAA;AACZI,IAAAA,KAAc,CAACJ,EAAAA;;AAGjB,MAAMK,KAAW;IAAEZ,MAAMM,GAAWN;;AACpC,MAAIW,IAAa;AAAA,QAAAE,QAAA,SAAAC,IAAAA;AAEb,UAAItF,MAAMC,QAAQqF,GAAMhB,MAAAA,GAAAA;AACtBA,QAAAA,GAAOiB,KAAAA,GAASD,GAAMhB,MAAAA;;AAGxB,UAAIgB,GAAM7H,YAAY;AACpB+C,eAAOgF,OAAO/H,IAAY6H,GAAM7H,UAAAA;AAChCwH,QAAAA,KAAAA;MACF;AAEA,UAAIQ,KAAwB;AAC5B,UAAIC,KAAyCN;AAC7C,UAAIrH,KAAqC,CAAA;AACzC,UAAIuH,GAAMvH,MAAAA;AACRA,QAAAA,KAAOuH,GAAMvH;iBACJiH,IAAS;AAClB,YAAMW,KAAMX,GAAQY,KAAKC,CAAAA,OAAcA,GAAWC,OAAOR,GAAMQ,EAAAA;AAC/D,YAAIR,GAAMS,SAAAA;AACRhI,UAAAA,KAAO,CAAA,GAAI4H,GAAK5H,MAAAA,GAASuH,GAAMS,OAAAA;;AAE/BhI,UAAAA,KAAO4H,GAAK5H;;MAEhB;AAEA,eAASqB,KAAI,GAAGC,KAAItB,GAAKuB,QAAQF,KAAIC,IAAGoG,KAAO1H,GAAKqB,IAAAA,GAAAA;AAClDsG,QAAAA,KAAOA,GAAKD,EAAAA,IAAQzF,MAAMC,QAAQyF,GAAKD,EAAAA,CAAAA,IACnC,CAAA,GAAIC,GAAKD,EAAAA,CAAAA,IACT;aAAKC,GAAKD,EAAAA;;;AAGhB,UAAIH,GAAMU,OAAO;AACf,YAAMC,KAAAA,CAAcR,MAAQ,IAAKA,KAAkB;AACnD,iBAASrG,KAAI,GAAGC,KAAIiG,GAAMU,MAAM1G,QAAQF,KAAIC,IAAGD,MAAAA;AAC7CsG,UAAAA,GAAKO,KAAa7G,EAAAA,IAAKuF,UACrBe,GAAKO,KAAa7G,EAAAA,GAClBkG,GAAMU,MAAM5G,EAAAA,CAAAA;;MAElB,WAAO,WAAIkG,GAAMd,MAAAA;AACfkB,QAAAA,GAAKD,EAAAA,IAAQd,UAAUe,GAAKD,EAAAA,GAAOH,GAAMd,IAAAA;;;AAtC7C,aAAWc,MAASH,IAAAA;AAAWE,YAAAC,EAAAA;;EAyCjC,OAAO;AACLF,IAAAA,GAASZ,QAAQO,GAAWG,WAAWH,IAAYP,QAAQM,GAAWN;AACtEF,IAAAA,KACGS,GAAWT,UACXS,GAAWG,WAAWH,GAAWG,QAAQZ,UAC1CA;EACJ;AAEA,SAAO;IACLH,WAAWW,GAAWX;IACtBK,MAAMY,GAASZ;IACfjH,OAAO+G,GAAOhF,SACV,IAAItB,cAAc;MAAEK,eAAeiG;MAAQxF,UAAAA;;IAE/CrB,YAAYwH,KAAgBxH,KAAAA;IAC5BgH,SACwB,QAAtBM,GAAWN,UAAkBM,GAAWN,UAAUK,GAAWL;IAC/DC,OAAAA;;AACD;AAgBI,IAAMwB,kBAAkBA,CAC7B/B,IACA5G,IACAuB,QACqB;EACrBqF,WAAAA;EACAK,MAAAA;EACAjH,OAAO,IAAIS,cAAc;IACvBW,cAAcpB;IACduB,UAAAA;;EAEFrB,YAAAA;EACAgH,SAAAA;EACAC,OAAAA;;AC/KK,SAASyB,cAGdC,IAAAA;AACA,MAAMzD,KAAkB;IACtBY,OAAAA;IACA8C,YAAAA;IACAjD,eAAeC,iBAAiB+C,GAAQ7C,KAAAA;IACxCM,WAAWuC,GAAQvC,aAAAA;IACnBpG,YAAY2I,GAAQ3I;;AAGtB,MACE,gBAAgB2I,GAAQ7C,SACxB6C,GAAQ7C,MAAM8C,eAAAA,CAGZD,GAAQ7C,MAAMJ,eAAAA,CAAgBiD,GAAQ7C,MAAMJ,YAAY7D,SAAAA;AAE1DqD,IAAAA,GAAK0D,aAAaD,GAAQ7C,MAAM8C;aAC3B,CACJD,GAAQ3I,cAAAA,CACR2I,GAAQ3I,WAAW6I,kBAClBF,GAAQ3I,WAAW6I,eAAeC,MAAAA;AAEpC5D,IAAAA,GAAKY,QAAQf,kBAAkB4D,GAAQ7C,KAAAA;;AAGzC,SAAOZ;AACT;IAaa6D,eAAeA,CAC1BrC,IACAxB,OAAAA;AAEA,MAAM8D,KACe,YAAnBtC,GAAUJ,QAAoBI,GAAUuC,QAAQC;AAClD,MAAA,CAAKF,MAAAA,CAAiB9D,IAAAA;AAAM,WAAOwB,GAAUuC,QAAQE;;AAErD,MAAMC,KAAWC,qBAAqB3C,GAAUuC,QAAQE,GAAAA;AACxD,WAAWhG,MAAO+B,IAAM;AACtB,QAAMxC,KAAQwC,GAAK/B,EAAAA;AACnB,QAAIT,IAAAA;AACF0G,MAAAA,GAAS,CAAA,EAAG5F,IACVL,IACiB,YAAA,OAAVT,KAAqBmB,mBAAmBnB,EAAAA,IAASA,EAAAA;;EAG9D;AACA,MAAM4G,KAAWF,GAAS1E,KAAK,GAAA;AAC/B,MAAI4E,GAASzH,SAAS,QAAyB,YAAjBmH,IAA0B;AACtDtC,IAAAA,GAAUuC,QAAQC,kBAAAA;AAClB,WAAOxC,GAAUuC,QAAQE;EAC3B;AAEA,SAAOG;AAAQ;AAGjB,IAAMD,uBACJF,CAAAA,OAAAA;AAEA,MAAM/D,KAAQ+D,GAAII,QAAQ,GAAA;AAC1B,SAAOnE,KAAAA,KACH,CAAC+D,GAAI5F,MAAM,GAAG6B,EAAAA,GAAQ,IAAIoE,gBAAgBL,GAAI5F,MAAM6B,KAAQ,CAAA,CAAA,CAAA,IAC5D,CAAC+D,IAAK,IAAIK,iBAAAA;AAAkB;AAIlC,IAAMC,gBAAgBA,CACpB/C,IACAxB,OAAAA;AAIA,MAAIA,MAAAA,EADiB,YAAnBwB,GAAUJ,QAAAA,CAAAA,CAAsBI,GAAUuC,QAAQC,kBAC7B;AACrB,QAAMQ,KAAO7F,mBAAmBqB,EAAAA;AAChC,QAAMyE,MHpBmBnI,CAAAA,OAAAA;AAC3B,UAAMX,KAAe,oBAAIgE;AACzB,UACElC,OAAoBC,mBACpBC,OAAoBD,iBACpB;AACAb,QAAAA,GAAK+B,MAAAA;AACLF,gBAAQ/C,IAAK,aAAaW,EAAAA;MAC5B;AACA,aAAOX;IAAG,GGWmBqE,GAAKkB,SAAAA;AAChC,QAAIuD,GAAMC,MAAM;AACd,UAAMC,KAAO,IAAIC;AACjBD,MAAAA,GAAKE,OAAO,cAAcL,EAAAA;AAC1BG,MAAAA,GAAKE,OACH,OACAlG,mBAAmB;WACd,CAAA,GAAI8F,GAAM7G,KAAAA,CAAAA,EAAQjC,IAAI6B,CAAAA,OAAS,CAACA,EAAAA,CAAAA;;AAGvC,UAAIsH,KAAQ;AACZ,eAAWC,MAAQN,GAAMO,OAAAA,GAAAA;AAAUL,QAAAA,GAAKE,OAAQ,KAAEC,MAAWC,EAAAA;;AAC7D,aAAOJ;IACT;AACA,WAAOH;EACT;AAAA;IAmBWS,mBAAmBA,CAC9BzD,IACAxB,OAAAA;AAEA,MAAMkF,KAAuB;IAC3BC,QACqB,mBAAnB3D,GAAUJ,OACN,uCACA;;AAER,MAAMgE,MACuC,cAAA,OAAnC5D,GAAUuC,QAAQsB,eACtB7D,GAAUuC,QAAQsB,aAAAA,IAClB7D,GAAUuC,QAAQsB,iBAAiB,CAAA;AACzC,MAAID,GAAaF,SAAAA;AACf,SA/BeA,CAAAA,OACjB,SAASA,MAAAA,CAAYrH,OAAOD,KAAKsH,EAAAA,EAASvI,QA8B1ByI,GAAaF,OAAAA,GAAAA;AACzBE,MAAAA,GAAaF,QAAQI,QAAQ,CAAC9H,IAAOS,OAAAA;AACnCiH,QAAAA,GAAQjH,EAAAA,IAAOT;MAAK,CAAA;eAEbH,MAAMC,QAAQ8H,GAAaF,OAAAA,GAAAA;AACnCE,MAAAA,GAAaF,QAAoCI,QAChD,CAAC9H,IAAOS,OAAAA;AACN,YAAIZ,MAAMC,QAAQE,EAAAA,GAAAA;AAChB,cAAI0H,GAAQ1H,GAAM,CAAA,CAAA,GAAA;AAChB0H,YAAAA,GAAQ1H,GAAM,CAAA,CAAA,IAAO,GAAE0H,GAAQ1H,GAAM,CAAA,CAAA,CAAA,IAAOA,GAAM,CAAA,CAAA;;AAElD0H,YAAAA,GAAQ1H,GAAM,CAAA,CAAA,IAAMA,GAAM,CAAA;;;AAG5B0H,UAAAA,GAAQjH,EAAAA,IAAOT;;MACjB,CAAA;;AAIJ,eAAWS,MAAOmH,GAAaF,SAAAA;AAC7BA,QAAAA,GAAQjH,GAAIsH,YAAAA,CAAAA,IAAiBH,GAAaF,QAAQjH,EAAAA;;;;AAKxD,MAAMuH,KAAiBjB,cAAc/C,IAAWxB,EAAAA;AAChD,MAA8B,YAAA,OAAnBwF,MAAAA,CAAgCN,GAAQ,cAAA,GAAA;AACjDA,IAAAA,GAAQ,cAAA,IAAkB;;AAC5B,SAAO;OACFE;IACHK,QAAQD,KAAiB,SAAS;IAClCxF,MAAMwF;IACNN,SAAAA;;AACD;AC/IH,IAAMQ,KAAiC,eAAA,OAAhBC,cAA8B,IAAIA,gBAAgB;AACzE,IAAMC,IAAmB;AACzB,IAAMC,KAAgB;AAMtB,IAAMzJ,WAAYZ,CAAAA,OACW,aAA3BA,GAAMD,YAAYR,OACbS,GAAiBY,SAAAA,IAClBsJ,GAASI,OAAOtK,EAAAA;AAEtBuK,gBAAgBC,WAAW7J,IAAAA;AACzB,MAAIA,GAAS6D,KAAMiG,OAAOC,aAAAA,GAAAA;AACxB,mBAAiBC,MAAShK,GAAS6D,MAAAA;YAC3B5D,SAAS+J,EAAAA;;SACZ;AACL,QAAMC,KAASjK,GAAS6D,KAAMqG,UAAAA;AAC9B,QAAI3E;AACJ,QAAA;AACE,aAAA,EAASA,KAAAA,MAAe0E,GAAOE,KAAAA,GAAQC,MAAAA;cAAYnK,SAASsF,GAAOlE,KAAAA;;IACrE,UAAU;AACR4I,MAAAA,GAAOI,OAAAA;IACT;EACF;AACF;AAEAT,gBAAgBxG,MACdkH,IACAC,IAAAA;AAEA,MAAIC,KAAS;AACb,MAAIC;AACJ,iBAAiBT,MAASM,IAAQ;AAChCE,IAAAA,MAAUR;AACV,YAAQS,KAAgBD,GAAOtC,QAAQqC,EAAAA,KAAAA,IAAiB;YAChDC,GAAOtI,MAAM,GAAGuI,EAAAA;AACtBD,MAAAA,KAASA,GAAOtI,MAAMuI,KAAgBF,GAAS/J,MAAAA;IACjD;EACF;AACF;AA4EAoJ,gBAAgBc,eACdrF,IACAyC,IACAoB,IAAAA;AAEA,MAAIyB,KAAAA;AACJ,MAAIpF,KAAiC;AACrC,MAAIvF;AAEJ,MAAA;gBAGc4K,QAAQC,QAAAA;AAGpB,QAAMC,MADN9K,KAAAA,OAAkBqF,GAAUuC,QAAQmD,SAASA,OAAOjD,IAAKoB,EAAAA,GAC5BH,QAAQhH,IAAI,cAAA,KAAmB;AAE5D,QAAIiJ;AACJ,QAAI,oBAAoBC,KAAKH,EAAAA,GAAAA;AAC3BE,MAAAA,KAlENpB,gBAAgBsB,oBACdJ,IACA9K,IAAAA;AAEA,YAAMmL,KAAiBL,GAAYM,MAAM3B,CAAAA;AACzC,YAAMc,KAAW,QAAQY,KAAiBA,GAAe,CAAA,IAAK;AAC9D,YAAIE,KAAAA;AACJ,YAAIjF;AACJ,uBAAe4D,MAAS5G,MAAMyG,WAAW7J,EAAAA,GAAW,SAASuK,EAAAA,GAAW;AACtE,cAAIc,IAAY;AACdA,YAAAA,KAAAA;AACA,gBAAMC,KAAgBtB,GAAM9B,QAAQqC,EAAAA;AACpC,gBAAIe,KAAAA,IAAiB;AACnBtB,cAAAA,KAAQA,GAAM9H,MAAMoJ,KAAgBf,GAAS/J,MAAAA;;AAE7C;;UAEJ;AACA,cAAA;kBACS4F,KAAUpF,KAAK0D,MAAMsF,GAAM9H,MAAM8H,GAAM9B,QAAQ,UAAA,IAAc,CAAA,CAAA;UACrE,SAAQzJ,IAAAA;AACP,gBAAA,CAAK2H,IAAAA;AAAS,oBAAM3H;;UACtB;AACA,cAAI2H,MAAAA,UAAWA,GAAQT,SAAAA;AAAmB;;QAC5C;AACA,YAAIS,MAAAA,UAAWA,GAAQT,SAAAA;gBACf;YAAEA,SAAAA;;;MAEZ,EAsCoCmF,IAAa9K,EAAAA;eAClC,sBAAsBiL,KAAKH,EAAAA,GAAAA;AACpCE,MAAAA,KAzFNpB,gBAAgB2B,iBACdvL,IAAAA;AAEA,YAAIoG;AACJ,uBAAiB4D,MAAS5G,MAAMyG,WAAW7J,EAAAA,GAAW,MAAA,GAAS;AAC7D,cAAMoL,KAAQpB,GAAMoB,MAAM1B,EAAAA;AAC1B,cAAI0B,IAAO;AACT,gBAAMpB,KAAQoB,GAAM,CAAA;AACpB,gBAAA;oBACShF,KAAUpF,KAAK0D,MAAMsF,EAAAA;YAC7B,SAAQvL,IAAAA;AACP,kBAAA,CAAK2H,IAAAA;AAAS,sBAAM3H;;YACtB;AACA,gBAAI2H,MAAAA,UAAWA,GAAQT,SAAAA;AAAmB;;UAC5C;QACF;AACA,YAAIS,MAAAA,UAAWA,GAAQT,SAAAA;gBACf;YAAEA,SAAAA;;;MAEZ,EAsEiC3F,EAAAA;eACtB,CAAK,UAAUiL,KAAKH,EAAAA,GAAAA;AACzBE,MAAAA,KAjGNpB,gBAAgB4B,UACdxL,IAAAA;cAEMgB,KAAK0D,MAAAA,MAAY1E,GAASyL,KAAAA,CAAAA;MAClC,EA6F0BzL,EAAAA;;AAEpBgL,MAAAA,KA1CNpB,gBAAgB8B,eACd1L,IAAAA;AAEA,YAAMyL,KAAAA,MAAazL,GAASyL,KAAAA;AAC5B,YAAA;AACE,cAAMlG,KAASvE,KAAK0D,MAAM+G,EAAAA;AAC1B,cAA6B,MAAbE;AACdC,oBAAQC,KACL,+FAAA;;gBAGCtG;QACP,SAAQuG,IAAAA;AACP,gBAAM,IAAI3M,MAAMsM,EAAAA;QAClB;MACF,EA2B+BzL,EAAAA;;AAG3B,QAAIkG;AACJ,mBAAiBE,MAAW4E,IAAS;AACnC,UAAI5E,GAAQF,WAAAA,CAAYX,IAAAA;AACtBW,QAAAA,KAAUE,GAAQF;iBACTE,GAAQF,SAAAA;AACjBA,QAAAA,KAAU,CAAA,GAAIA,IAAAA,GAAaE,GAAQF,OAAAA;;AAErCX,MAAAA,KAASA,KACLQ,iBAAiBR,IAAQa,IAASpG,IAAUkG,EAAAA,IAC5CZ,WAAWD,IAAWe,IAASpG,EAAAA;AACnC2K,MAAAA,KAAAA;YACMpF;AACNoF,MAAAA,KAAAA;IACF;AAEA,QAAA,CAAKpF,IAAAA;YACIA,KAASD,WAAWD,IAAW,CAAE,GAAErF,EAAAA;;EAE7C,SAAQvB,IAAAA;AACP,QAAA,CAAKkM,IAAAA;AACH,YAAMlM;;UAGF2I,gBACJ/B,IACArF,OACGA,GAAS+L,SAAS,OAAO/L,GAAS+L,UAAU,QAC7C/L,GAASgM,aACP,IAAI7M,MAAMa,GAASgM,UAAAA,IACnBvN,IACJuB,EAAAA;EAEJ;AACF;AA6BO,SAASiM,gBACd5G,IACAyC,IACAoB,IAAAA;AAEA,MAAIgD;AACJ,MAA+B,eAAA,OAApBC,iBAAAA;AACTjD,IAAAA,GAAakD,UAAUF,KAAkB,IAAIC,mBAAmBC;;AAElE,SAGEC,MAAM,MAAA;AACJ,QAAIH,IAAAA;AAAiBA,MAAAA,GAAgBI,MAAAA;;EAAO,CAAA,EAF9CC,OAAQhH,CAAAA,OAAAA,CAAAA,CAAwCA,EAAAA,EADhDiH,kBAAkB9B,eAAerF,IAAWyC,IAAKoB,EAAAA,CAAAA,CAAAA,CAAAA;AAMrD;;;AC3QA,IAAMuD,eAAeA,CAACC,IAAgCC,OAAAA;AACpD,MAAIC,MAAMC,QAAQH,EAAAA,GAAAA;AAChB,aAAWI,MAAQJ,IAAAA;AAAKD,mBAAaK,IAAMH,EAAAA;;aACnB,YAAA,OAARD,MAA4B,SAARA,IAAAA;AACpC,aAAWK,MAAOL,IAAAA;AAChB,UAAY,iBAARK,MAA4C,YAAA,OAAbL,GAAIK,EAAAA,GAAAA;AACrCJ,QAAAA,GAAMK,IAAIN,GAAIK,EAAAA,CAAAA;;AAEdN,qBAAaC,GAAIK,EAAAA,GAAMJ,EAAAA;;;;AAK7B,SAAOA;AAAK;ACPd,IAAMM,aAGJC,CAAAA,OAAAA;AAEA,MAAI,iBAAiBA,IAAM;AACzB,QAAMC,KAA+C,CAAA;AACrD,aAAWC,MAAcF,GAAKC,aAAa;AACzC,UAAME,KAAgBJ,WAAWG,EAAAA;AACjCD,MAAAA,GAAYG,KAAKD,EAAAA;IACnB;AAEA,WAAO;SAAKH;MAAMC,aAAAA;;EACpB;AAEA,MAAI,gBAAgBD,MAAQA,GAAKK,cAAcL,GAAKK,WAAWC,QAAQ;AACrE,QAAMD,KAA8B,CAAA;AACpC,QAAME,KAAc,CAAA;AACpB,aAAWC,MAAaR,GAAKK,YAAY;AACvC,UAAII,KAAOD,GAAUC,KAAKC;AAC1B,UAAgB,QAAZD,GAAK,CAAA,GAAA;AACPJ,QAAAA,GAAWD,KAAKI,EAAAA;;AAEhBC,QAAAA,KAAOA,GAAKE,MAAM,CAAA;;AAEpBJ,MAAAA,GAAYE,EAAAA,IAAQD;IACtB;AACAR,IAAAA,KAAO;SAAKA;MAAMK,YAAAA;MAAYE,aAAAA;;EAChC;AAEA,MAAI,kBAAkBP,IAAM;AAC1B,QAAMY,KAA6C,CAAA;AACnD,QAAIC,KAAcb,GAAKc,SAASC,EAAKC;AACrC,QAAIhB,GAAKiB,cAAc;AACrB,eAAWC,MAAalB,GAAKiB,aAAaL,cAAc,CAAA,GAAI;AAC1DC,QAAAA,KACEA,MACCK,GAAUJ,SAASC,EAAKI,SACE,iBAAzBD,GAAUT,KAAKC,SAAAA,CACdQ,GAAUE;AACf,YAAMC,KAAetB,WAAWmB,EAAAA;AAChCN,QAAAA,GAAWR,KAAKiB,EAAAA;MAClB;AAEA,UAAA,CAAKR,IAAAA;AACHD,QAAAA,GAAWR,KAAK;UACdU,MAAMC,EAAKI;UACXV,MAAM;YACJK,MAAMC,EAAKO;YACXZ,OAAO;;UAETa,YAAAA;;;AAIJ,aAAO;WACFvB;QACHiB,cAAc;aAAKjB,GAAKiB;UAAcL,YAAAA;;;IAE1C;EACF;AAEA,SAAOZ;AAAI;AAGb,IAAMwB,IAAgB,oBAAIC;AAwBbC,IAAAA,iBACX1B,CAAAA,OAAAA;AAEA,MAAM2B,KAAQC,YAAY5B,EAAAA;AAE1B,MAAI6B,KAASL,EAAcM,IAAIH,GAAMI,KAAAA;AACrC,MAAA,CAAKF,IAAQ;AACXL,MAAcQ,IACZL,GAAMI,OACLF,KAAS9B,WAAW4B,EAAAA,CAAAA;AAMvBM,WAAOC,eAAeL,IAAQ,SAAS;MACrCnB,OAAOiB,GAAMI;MACbI,YAAAA;;EAEJ;AAEA,SAAON;AAAM;IC3GFO,eAAeA,CAACC,IAAWC,OAAAA;AACtC,MAAA,CAAKD,MAAwB,YAAA,OAATA,IAAAA;AAClB,WAAOA;aACE3C,MAAMC,QAAQ0C,EAAAA,GAAAA;AACvB,WAAOA,GAAKE,IAAIC,CAAAA,OAAKJ,aAAaI,EAAAA,CAAAA;aAElCH,MACgB,YAAA,OAATA,OACNC,MAAU,gBAAgBD,KAC3B;AACA,QAAMI,KAAM,CAAA;AACZ,aAAW5C,MAAOwC,IAAAA;AAChB,UAAY,iBAARxC,IAAAA;AACFoC,eAAOC,eAAeO,IAAK,cAAc;UACvCN,YAAAA;UACAzB,OAAO2B,GAAKK;;;AAGdD,QAAAA,GAAI5C,EAAAA,IAAOuC,aAAaC,GAAKxC,EAAAA,CAAAA;;;AAGjC,WAAO4C;EACT,OAAA;AACE,WAAOJ;;AACT;AC7BK,SAASM,YACdC,IAAAA;AAEA,MAAMC,UAAYC,CAAAA,OAChBF,GAASE,EAAAA;AACXD,UAAQE,YAAY,MAKhBA,UADAC,KAAK,CAAA,EADLC,OAAOpB,CAAAA,OAAAA,CAAWA,GAAOqB,SAAAA,CAAUrB,GAAOsB,OAAAA,EAD1CN,OAAAA,CAAAA,CAAAA;AAKJA,UAAQO,OAAO,CAACC,IAAWC,OACzBT,QAAQE,UAAAA,EAAYK,KAAKC,IAAWC,EAAAA;AACtCT,UAAQU,YAAYC,CAAAA,OAAYD,UAAUC,EAAAA,EAAUX,OAAAA;AACpD,SAAOA;AACT;AC2BA,SAASY,cAAc3C,IAAM4C,IAASC,IAAAA;AACpC,SAAO;OACFD;IACH5C,MAAAA;IACA6C,SAASD,GAAQC,UACb;SACKD,GAAQC;SACRA;QAELA,MAAWD,GAAQC;;AAE3B;AAOO,IAAMC,cAAcA,CACzBC,IACAC,OAEOL,cAAcI,GAAU/C,MAAM+C,IAAW;EAC9CC,MAAM;OACDD,GAAUF,QAAQG;OAClBA;;;ACnEF,IAAMC,OAAOA,MAAAA;AAAAA;ACmDpB,SAASC,IAAIC,IAAAA;AACX,MAAMC,KAAgB,oBAAIzC;AAC1B,MAAMxB,KAAgC,CAAA;AACtC,MAAMkE,KAAyB,CAAA;AAG/B,MAAIC,KAAe1E,MAAMC,QAAQsE,EAAAA,IAASA,GAAM,CAAA,IAAKA,MAAS;AAC9D,WAASI,KAAI,GAAGA,KAAIC,UAAUhE,QAAQ+D,MAAK;AACzC,QAAM3D,KAAQ4D,UAAUD,EAAAA;AACxB,QAAI3D,MAASA,GAAMT,aAAAA;AACjBkE,MAAAA,GAAO/D,KAAKM,EAAAA;;AAEZ0D,MAAAA,MAAQ1D;;AAGV0D,IAAAA,MAAQE,UAAU,CAAA,EAAGD,EAAAA;EACvB;AAEAF,EAAAA,GAAOI,QAAQ3C,YAAYwC,EAAAA,CAAAA;AAC3B,WAAWI,MAAYL,IAAAA;AACrB,aAAWjE,MAAcsE,GAASvE,aAAAA;AAChC,UAAIC,GAAWY,SAASC,EAAK0D,qBAAqB;AAChD,YAAMhE,KAAOP,GAAWO,KAAKC;AAC7B,YAAMA,KAAQgE,kBAAkBxE,EAAAA;AAEhC,YAAA,CAAKgE,GAAcS,IAAIlE,EAAAA,GAAO;AAC5ByD,UAAAA,GAAclC,IAAIvB,IAAMC,EAAAA;AACxBT,UAAAA,GAAYG,KAAKF,EAAAA;QACnB,WAEEgE,GAAcpC,IAAIrB,EAAAA,MAAUC,IAAAA;AAG5BkE,kBAAQC,KACN,yDACEpE,KADF,uIAAA;;MAMN,OAAA;AACER,QAAAA,GAAYG,KAAKF,EAAAA;;;;AAKvB,SAAO0B,YAAY;IACjBd,MAAMC,EAAK+D;IACX7E,aAAAA;;AAEJ;AC9FA,IAAM8E,aAAaA,CAAAA,EAAGjE,MAAAA,GAAAA,MACX,eAATA,MAAgC,YAATA;AAGlB,IAAMkE,eAAgBnB,CAAAA,OAAAA;AAC3B,MAAMlC,KAAQD,eAAemC,GAAUlC,KAAAA;AACvC,MAAIA,OAAUkC,GAAUlC,OAAO;AAC7B,QAAMsD,KAAqBxB,cAAcI,GAAU/C,MAAM+C,EAAAA;AACzDoB,IAAAA,GAAmBtD,QAAQA;AAC3B,WAAOsD;EACT,OAAA;AACE,WAAOpB;;AACT;AAuBK,IAAMqB,gBAA0BA,CAAAA,EAAGC,SAAAA,IAASC,QAAAA,IAAQC,eAAAA,GAAAA,MAAAA;AACzD,MAAMC,KAA2B,oBAAI7D;AACrC,MAAM8D,KAAiC,oBAAI9D;AAE3C,MAAM+D,oBAAqB3B,CAAAA,OACN,YAAnBA,GAAU/C,QAC0B,mBAApC+C,GAAUF,QAAQ8B,kBACmB,iBAApC5B,GAAUF,QAAQ8B,iBACjBH,GAAYX,IAAId,GAAUhE,GAAAA;AAE9B,SAAO6F,CAAAA,OAAAA;AACL,QAAMC,KAGJpD,IAAIsB,CAAAA,OAAAA;AACF,UAAM+B,KAAeN,GAAYxD,IAAI+B,GAAUhE,GAAAA;AAE/C,MAAAwF,GAAc;QACZxB,WAAAA;WACI+B,KACA;UACEC,MAAM;UACNC,SAAS;YAEX;UACED,MAAM;UACNC,SAAS;;QACT3B,QAAA;;AAGR,UAAItC,KACF+D,MACAG,WAAWlC,IAAW;QACpBxB,MAAM;;AAGVR,MAAAA,KAAS;WACJA;QACHgC,WAAWD,OAAAA,YAAYC,IAAW;UAChCmC,cAAcJ,KAAe,QAAQ;aADhB/B;;AAKzB,UAAwC,wBAApCA,GAAUF,QAAQ8B,eAAuC;AAC3D5D,QAAAA,GAAOqB,QAAAA;AACP+C,2BAAmBb,IAAQvB,EAAAA;MAC7B;AAEA,aAAOhC;IAAM,CAAA,EAnCfoB,OAAOiD,CAAAA,OAAAA,CAAOnB,WAAWmB,EAAAA,KAAOV,kBAAkBU,EAAAA,CAAAA,EADlDR,EAAAA,CAAAA;AAwCF,QAAMS,KAiBJC,OAAIC,CAAAA,OAAAA;AACF,UAAA,EAAIxC,WAAEA,GAAAA,IAAcwC;AACpB,UAAA,CAAKxC,IAAAA;AAAW;;AAEhB,UAAIyC,KAAYzC,GAAUF,QAAQ4C,uBAAuB,CAAA;AAMzD,UAAgC,mBAA5BF,GAASxC,UAAU/C,MAAAA;AACrBwF,QAAAA,MPzGuBD,CAAAA,OAA+B,CAAA,GAC3D9G,aAAa8G,IAAwB,oBAAIG,KAAAA,CAAAA,GOwGPH,GAAShE,IAAAA,EAAMoE,OAAOH,EAAAA;;AAIrD,UAC8B,eAA5BD,GAASxC,UAAU/C,QACS,mBAA5BuF,GAASxC,UAAU/C,MACnB;AACA,YAAM4F,KAAoB,oBAAIF;AAE9B,QAAAnB,GAAc;UACZQ,MAAM;UACNC,SAAU,kDAAiDQ,EAAAA;UAC3DzC,WAAAA;UACAxB,MAAM;YAAEiE,WAAAA;YAAWD,UAAAA;;UAAUlC,QAAA;;AAG/B,iBAASE,KAAI,GAAGA,KAAIiC,GAAUhG,QAAQ+D,MAAK;AACzC,cAAMsC,KAAWL,GAAUjC,EAAAA;AAC3B,cAAIuC,KAAarB,GAAezD,IAAI6E,EAAAA;AACpC,cAAA,CAAKC,IAAAA;AACHrB,YAAAA,GAAevD,IAAI2E,IAAWC,KAAa,oBAAIJ,KAAAA;;AACjD,mBAAW3G,MAAO+G,GAAWC,OAAAA,GAAAA;AAAUH,YAAAA,GAAkB5G,IAAID,EAAAA;;AAC7D+G,UAAAA,GAAWE,MAAAA;QACb;AAEA,iBAAWjH,MAAO6G,GAAkBG,OAAAA,GAAAA;AAClC,cAAIvB,GAAYX,IAAI9E,EAAAA,GAAM;AACxBgE,YAAAA,KAAayB,GAAYxD,IAAIjC,EAAAA,EAAyBgE;AACtDyB,YAAAA,GAAYyB,OAAOlH,EAAAA;AACnBoG,+BAAmBb,IAAQvB,EAAAA;UAC7B;;MAEH,WAA6B,YAAnBA,GAAU/C,QAAoBuF,GAAShE,MAAM;AACtDiD,QAAAA,GAAYtD,IAAI6B,GAAUhE,KAAKwG,EAAAA;AAC/B,iBAAShC,KAAI,GAAGA,KAAIiC,GAAUhG,QAAQ+D,MAAK;AACzC,cAAMsC,KAAWL,GAAUjC,EAAAA;AAC3B,cAAIuC,KAAarB,GAAezD,IAAI6E,EAAAA;AACpC,cAAA,CAAKC,IAAAA;AACHrB,YAAAA,GAAevD,IAAI2E,IAAWC,KAAa,oBAAIJ,KAAAA;;AACjDI,UAAAA,GAAW9G,IAAI+D,GAAUhE,GAAAA;QAC3B;MACF;IAAA,CAAA,EAtDFsF,GAHAlC,OACEiD,CAAAA,OAAkB,YAAZA,GAAGpF,QAAiD,iBAA7BoF,GAAGvC,QAAQ8B,aAAAA,EAF1ClD,IAAI2D,CAAAA,OAAE,OAAItC,YAAYsC,IAAI;MAAEF,cAAc;SAApBE,EAAAA,EAXtBc,MAAM,CAIFzE,IAAIyC,YAAAA,EADJ/B,OAAOiD,CAAAA,OAAAA,CAAOnB,WAAWmB,EAAAA,KAAAA,CAAQV,kBAAkBU,EAAAA,CAAAA,EADnDR,EAAAA,CAAAA,GAMAzC,OAAOiD,CAAAA,OAAMnB,WAAWmB,EAAAA,CAAAA,EADxBR,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAkEN,WAAOsB,MAAM,CAACrB,IAAYQ,EAAAA,CAAAA;EAAe;AAC1C;AAMI,IAAMF,qBAAqBA,CAACb,IAAgBvB,OAC1CuB,GAAOa,mBACZxC,cAAcI,GAAU/C,MAAM+C,IAAW;EACvC4B,eAAe;;AClBrB,IAAMwB,IAAc,oBAAIT;AAkBjB,IAAMU,cAAcA,CAACC,KAA4B,CAAA,MAAA;AACtD,MAAMC,KAAAA,CAAAA,CAAyBD,GAAOC;AACtC,MAAMC,KAAAA,CAAAA,CAAsBF,GAAOE;AACnC,MAAMhF,KAAgD,CAAA;AAItD,MAAMiF,KAA4B,CAAA;AAClC,MAAMC,aAAc1F,CAAAA,OAAAA;AAClByF,IAAAA,GAAgBlH,KAAKyB,GAAOgC,UAAUhE,GAAAA;AACtC,QAA+B,MAA3ByH,GAAgBhH,QAAAA;AAClBkH,cAAQC,QAAAA,EAAUrE,KAAK,MAAA;AACrB,YAAIvD;AACJ,eAAQA,KAAMyH,GAAgBI,MAAAA,GAAAA;AAC5BrF,UAAAA,GAAKxC,EAAAA,IAAO;;MACd,CAAA;;EAEJ;AAKF,MAAM8H,MACJA,CAAAA,EAAGvC,QAAAA,IAAQD,SAAAA,GAAAA,MACXO,CAAAA,OAAAA;AAGE,QAAMkC,KACJT,MAAqC,aAAA,OAApBA,GAAOS,WAAAA,CAAAA,CAClBT,GAAOS,WAAAA,CACRxC,GAAOyC;AAEd,QAAI1B,KAUFhB,GADA5C,IAAIyC,YAAAA,EAPJ/B,OACEY,CAAAA,OACqB,eAAnBA,GAAU/C,QAAAA,CACTuB,GAAKwB,GAAUhE,GAAAA,KAAAA,CAAAA,CACdwC,GAAKwB,GAAUhE,GAAAA,EAAMsD,WACa,mBAApCU,GAAUF,QAAQ8B,aAAAA,EANtBC,EAAAA,CAAAA,CAAAA;AAcF,QAAIC,KAQFpD,IAAI2D,CAAAA,OAAAA;AAEF,UAAMN,MAlGUkC,CACxBjE,IACAhC,IACAwF,QACqB;QACrBxD,WAAAA;QACAxB,MAAMR,GAAOQ,OAAO0F,KAAKC,MAAMnG,GAAOQ,IAAAA,IAAAA;QACtC4F,YACEZ,MAAqBxF,GAAOoG,aACxBF,KAAKC,MAAMnG,GAAOoG,UAAAA,IAAAA;QAExBC,OAAOrG,GAAOqG,QACV,IAAIC,cAAc;UAChBC,cAAcvG,GAAOqG,MAAME,eACvB,IAAIC,MAAMxG,GAAOqG,MAAME,YAAAA,IAAAA;UAE3BE,eAAezG,GAAOqG,MAAMI;;QAGlCpF,OAAAA;QACAC,SAAAA,CAAAA,CAAWtB,GAAOsB;UA+ER+C,IAFiB7D,GAAK6D,GAAGrG,GAAAA,GAIzBwH,EAAAA;AAGF,UAAID,MAAAA,CAAyBH,EAAYtC,IAAIuB,GAAGrG,GAAAA,GAAM;AACpD+F,QAAAA,GAAa1C,QAAAA;AACb+D,UAAYnH,IAAIoG,GAAGrG,GAAAA;AACnBoG,2BAAmBb,IAAQc,EAAAA;MAC7B;AAQA,aANgC;WAC3BN;QACH/B,WAAWD,OAAAA,YAAYsC,IAAI;UACzBF,cAAc;aADOE;;IAIZ,CAAA,EA1BfjD,OACEY,CAAAA,OACqB,eAAnBA,GAAU/C,QAAAA,CAAAA,CACRuB,GAAKwB,GAAUhE,GAAAA,KACmB,mBAApCgE,GAAUF,QAAQ8B,aAAAA,EALtBC,EAAAA,CAAAA;AA+BF,QAAA,CAAKkC,IAAAA;AAEHzB,MAAAA,KAEEC,OAAKvE,CAAAA,OAAAA;AACH,YAAA,EAAMgC,WAAEA,GAAAA,IAAchC;AACtB,YAAuB,eAAnBgC,GAAU/C,MAAqB;AACjC,cAAMyH,MAxKIC,CACtB3G,IACAwF,OAAAA;AAEA,gBAAMkB,KAA+B;cACnClG,MAAM0F,KAAKU,UAAU5G,GAAOQ,IAAAA;cAC5Bc,SAAStB,GAAOsB;;AAGlB,gBAAA,WAAItB,GAAOQ,MAAAA;AACTkG,cAAAA,GAAWlG,OAAO0F,KAAKU,UAAU5G,GAAOQ,IAAAA;;AAG1C,gBAAIgF,MAAAA,WAAqBxF,GAAOoG,YAAAA;AAC9BM,cAAAA,GAAWN,aAAaF,KAAKU,UAAU5G,GAAOoG,UAAAA;;AAGhD,gBAAIpG,GAAOqG,OAAO;AAChBK,cAAAA,GAAWL,QAAQ;gBACjBI,eAAezG,GAAOqG,MAAMI,cAAc/F,IAAI2F,CAAAA,OAAAA;AAC5C,sBAAA,CAAKA,GAAMQ,QAAAA,CAASR,GAAMD,YAAAA;AAAY,2BAAOC,GAAMpC;;AAEnD,yBAAO;oBACLA,SAASoC,GAAMpC;oBACf4C,MAAMR,GAAMQ;oBACZT,YAAYC,GAAMD;;gBACnB,CAAA;;AAIL,kBAAIpG,GAAOqG,MAAME,cAAAA;AACfG,gBAAAA,GAAWL,MAAME,eAAe,KAAKvG,GAAOqG,MAAME;;YAEtD;AAEA,mBAAOG;UAAU,GAqI8B1G,IAAQwF,EAAAA;AAC3ChF,UAAAA,GAAKwB,GAAUhE,GAAAA,IAAO0I;QACxB;MAAA,CAAA,EANFpC,EAAAA;;AAWFR,MAAAA,KAA8BS,OAAImB,UAAAA,EAAhB5B,EAAAA;;AAGpB,WAAOqB,MAAM,CAACb,IAAeR,EAAAA,CAAAA;EAAY;AAG7CgC,MAAIgB,cAAeC,CAAAA,OAAAA;AACjB,aAAW/I,MAAO+I,IAAAA;AAEhB,UAAkB,SAAdvG,GAAKxC,EAAAA,GAAAA;AACPwC,QAAAA,GAAKxC,EAAAA,IAAO+I,GAAQ/I,EAAAA;;;EAExB;AAGF8H,MAAIkB,cAAc,MAAA;AAChB,QAAMhH,KAAkB,CAAA;AACxB,aAAWhC,MAAOwC,IAAAA;AAAM,UAAiB,QAAbA,GAAKxC,EAAAA,GAAAA;AAAcgC,QAAAA,GAAOhC,EAAAA,IAAOwC,GAAKxC,EAAAA;;;AAClE,WAAOgC;EAAM;AAGf,MAAIsF,MAAUA,GAAO2B,cAAAA;AACnBnB,QAAIgB,YAAYxB,GAAO2B,YAAAA;;AAGzB,SAAOnB;AAAG;ACtLL,IAAMoB,uBACXA,CAAAA,EACEC,qBAAAA,IACAC,qBAAAA,IACAC,yBAAAA,GAAAA,MAEF,CAAA,EAAG9D,QAAAA,IAAQD,SAAAA,GAAAA,MAAAA;AA+DT,MAAMgE,KACJD,OACCrF,CAAAA,OACoB,mBAAnBA,GAAU/C,QAAAA,CAAAA,CACPmI,OACmB,YAAnBpF,GAAU/C,QAAuC,eAAnB+C,GAAU/C;AAE/C,SAAO4E,CAAAA,OAAAA;AACL,QAAM0D,KAOJC,SAASxF,CAAAA,OAAAA;AACP,UAAA,EAAMhE,KAAEA,GAAAA,IAAQgE;AAChB,UAAMyF,KAEJrG,OAAOiD,CAAAA,OAAkB,eAAZA,GAAGpF,QAAuBoF,GAAGrG,QAAQA,EAAAA,EADlD6F,EAAAA;AAIF,aAEE6D,UAAUD,EAAAA,GArFhBzF,CAAAA,OAAAA;AAEA,YAAM2F,KAAgBR,GACpBS,cAAc5F,EAAAA,GACdA,EAAAA;AAGF,eAAO6F,KAAsBC,CAAAA,OAAAA;AAC3B,cAAIC,KAAAA;AACJ,cAAIC;AACJ,cAAIhI;AAEJ,mBAASiI,WAAWpJ,IAAAA;AAClBiJ,YAAAA,GAASI,KACNlI,KAASA,KACNmI,iBAAiBnI,IAAQnB,EAAAA,IACzBqF,WAAWlC,IAAWnD,EAAAA,CAAAA;UAE9B;AAEA8G,kBAAQC,QAAAA,EAAUrE,KAAK,MAAA;AACrB,gBAAIwG,IAAAA;AAAY;;AAEhBC,YAAAA,KAAML,GAAcjG,UAAU;cAC5BwG,MAAMD;cACN5B,MAAMA,IAAAA;AACJ,oBAAIxI,MAAMC,QAAQuI,EAAAA,GAAAA;AAKhB4B,6BAAW;oBAAEG,QAAQ/B;;;AAErByB,kBAAAA,GAASI,KAAKG,gBAAgBrG,IAAWqE,EAAAA,CAAAA;;AAE3CyB,gBAAAA,GAASQ,SAAAA;cACV;cACDA,WAAAA;AACE,oBAAA,CAAKP,IAAY;AACfA,kBAAAA,KAAAA;AACA,sBAAuB,mBAAnB/F,GAAU/C,MAAAA;AACZsE,oBAAAA,GAAOa,mBACLxC,cAAc,YAAYI,IAAWA,GAAUF,OAAAA,CAAAA;;AAGnD,sBAAI9B,MAAUA,GAAOsB,SAAAA;AACnB2G,+BAAW;sBAAE3G,SAAAA;;;AAEfwG,kBAAAA,GAASQ,SAAAA;gBACX;cACF;;UACA,CAAA;AAGJ,iBAAO,MAAA;AACLP,YAAAA,KAAAA;AACA,gBAAIC,IAAAA;AAAKA,cAAAA,GAAIO,YAAAA;;UAAa;QAC3B,CAAA;MACD,GA0B6BvG,EAAAA,CAAAA;IAAU,CAAA,EAbvCZ,OACEY,CAAAA,OACqB,eAAnBA,GAAU/C,QACVqI,GAA0BtF,EAAAA,CAAAA,EAJ9B6B,EAAAA,CAAAA;AAoBF,QAAM2E,KAOJlF,GALAlC,OACEY,CAAAA,OACqB,eAAnBA,GAAU/C,QAAAA,CACTqI,GAA0BtF,EAAAA,CAAAA,EAJ/B6B,EAAAA,CAAAA;AASF,WAAOsB,MAAM,CAACoC,IAAsBiB,EAAAA,CAAAA;EAAU;AAC/C;ACxNE,IAAMC,gBAA0BA,CAAAA,EAAGnF,SAAAA,GAAAA,MAAAA;AACxC,MAA6B,OAAboF;AACd,WAAO7E,CAAAA,OAAQP,GAAQO,EAAAA;;AAEvB,WAAOA,CAAAA,OAMHU,OAAIvE,CAAAA,OAEF+C,QAAQ4F,IAAI,2CAA2C3I,EAAAA,CAAAA,EAHzDsD,GADAiB,OAAIF,CAAAA,OAAMtB,QAAQ4F,IAAI,0CAA0CtE,EAAAA,CAAAA,EAFhER,EAAAA,CAAAA,CAAAA;;AASN;AC1BK,IAAM+E,gBACXA,CAAAA,EAAGtF,SAAAA,GAAAA,MACHO,CAAAA,OACEP,GAAQO,EAAAA;ACmBL,IAAMgF,gBAA0BA,CAAAA,EAAGvF,SAAAA,IAASE,eAAAA,GAAAA,MAC1CK,CAAAA,OAAAA;AACL,MAAMiF,KASJtB,SAASxF,CAAAA,OAAAA;AACP,QAAMO,KAAOqF,cAAc5F,EAAAA;AAC3B,QAAM+G,KAAMC,aAAahH,IAAWO,EAAAA;AACpC,QAAM0G,KAAeC,iBAAiBlH,IAAWO,EAAAA;AAEjD,IAAAiB,GAAc;MACZQ,MAAM;MACNC,SAAS;MACTjC,WAAAA;MACAxB,MAAM;QACJuI,KAAAA;QACAE,cAAAA;;MACD3G,QAAA;;AAGH,QAAMA,KAEJoF,UAGItG,OAAOiD,CAAAA,OAAkB,eAAZA,GAAGpF,QAAuBoF,GAAGrG,QAAQgE,GAAUhE,GAAAA,EAD5D6F,EAAAA,CAAAA,EAHJsF,gBAAgBnH,IAAW+G,IAAKE,EAAAA,CAAAA;AASlC,QAA6B,MAAbP;AACd,aAEEU,OAAOpJ,CAAAA,OAAAA;AACL,YAAMqG,KAAAA,CAASrG,GAAOQ,OAAOR,GAAOqG,QAAAA;AAEpC,QAAA7C,GAAc;UACZQ,MAAMqC,KAAQ,eAAe;UAC7BpC,SAAU,KACRoC,KAAQ,WAAW,YAAA;UAErBrE,WAAAA;UACAxB,MAAM;YACJuI,KAAAA;YACAE,cAAAA;YACApK,OAAOwH,MAASrG;;UACjBsC,QAAA;;MACD,CAAA,EAfJA,EAAAA;;AAoBJ,WAAOA;EAAM,CAAA,EAtDflB,OAAOY,CAAAA,OAEgB,eAAnBA,GAAU/C,SACU,mBAAnB+C,GAAU/C,QAAAA,CAAAA,CACP+C,GAAUF,QAAQuH,mBAAAA,EAL1BxF,EAAAA,CAAAA;AA2DF,MAAM2E,KASJlF,GAPAlC,OAAOY,CAAAA,OAEgB,eAAnBA,GAAU/C,QACU,mBAAnB+C,GAAU/C,QAAAA,CACR+C,GAAUF,QAAQuH,kBAAAA,EALzBxF,EAAAA,CAAAA;AAWF,SAAOsB,MAAM,CAAC2D,IAAeN,EAAAA,CAAAA;AAAU;AChF9Bc,IAAAA,mBACVC,CAAAA,OACD,CAAA,EAAGhG,QAAAA,IAAQD,SAAAA,IAASE,eAAAA,GAAAA,MAClB+F,GAAUC,YAAY,CAAClG,IAASmG,OAAAA;AAC9B,MAAIC,KAAAA;AACJ,SAAOD,GAAS;IACdlG,QAAAA;IACAD,QAAQqG,IAAAA;AACN,UAA6B,MAAc;AACzC,YAAID,IAAAA;AACF,gBAAM,IAAIlD,MACR,sDAAA;;AAEJkD,QAAAA,KAAAA;MACF;AACA,aAAOE,MAAMtG,GAAQsG,MAAMD,EAAAA,CAAAA,CAAAA;IAC5B;IACDnG,cAAcqG,IAAAA;AACZ,MAAArG,GAAc;QACZsG,WAAWC,KAAKC,IAAAA;QAChB1H,QAAQmH,GAAS7K;WACdiL;;IAEP;;AACA,GACDvG,EAAAA;ACqBA,IAAM2G,cAAcA,CAAAA,EACzBC,aAAAA,IACAvI,UAAAA,IACAwI,SAAAA,GAAAA,MAEO,CAAA,EAAG7G,SAAAA,GAAAA,MACRO,CAAAA,OAaI2D,SAASxH,CAAAA,OAAAA;AACP,MAAImK,MAAWnK,GAAOqG,OAAAA;AAAO8D,IAAAA,GAAQnK,GAAOqG,OAAOrG,GAAOgC,SAAAA;;AAC1D,MAAMoI,KAAazI,MAAYA,GAAS3B,EAAAA,KAAYA;AACpD,SAAO,UAAUoK,KACbC,YAAYD,EAAAA,IACZE,UAAUF,EAAAA;AAAU,CAAA,EAN1B9G,GAREkE,SAASxF,CAAAA,OAAAA;AACP,MAAMuI,KACHL,MAAeA,GAAYlI,EAAAA,KAAeA;AAC7C,SAAO,UAAUuI,KACbF,YAAYE,EAAAA,IACZD,UAAUC,EAAAA;AAAa,CAAA,EAN7B1G,EAAAA,CAAAA,CAAAA;ACjEH,IAAM2G,mBAGXA,CAAAA,EAAGhH,eAAAA,GAAAA,MACHK,CAAAA,OAAAA;AACE,MAA6B,MAAb6E;AACd7E,IAAAA,KAEEU,OAAIvC,CAAAA,OAAAA;AACF,UACqB,eAAnBA,GAAU/C,QACe,MACzB;AACA,YAAMgF,KAAW,+CAA8CjC,GAAU/C,IAAAA;AAEzE,QAAAuE,GAAc;UACZQ,MAAM;UACNC,SAAAA;UACAjC,WAAAA;UAASM,QAAA;;AAEXS,gBAAQC,KAAKiB,EAAAA;MACf;IAAA,CAAA,EAdFJ,EAAAA;;AAoBJ,SAAOzC,OAAQqJ,CAAAA,OAAAA,KAAoB,EAAO5G,EAAAA;AAAK;ICmgBtC6G,IAA8C,SAASA,OAElEC,IAAAA;AAEA,MAAgBjC,CAA8BiC,GAAK5B,KAAAA;AACjD,UAAM,IAAIvC,MAAM,gDAAA;;AAGlB,MAAIoE,KAAM;AAEV,MAAMC,KAAU,oBAAIjL;AACpB,MAAMkL,KAA+C,oBAAIlL;AACzD,MAAMmL,KAAa,oBAAIpG;AACvB,MAAMqG,KAAqB,CAAA;AAE3B,MAAMC,KAAW;IACflC,KAAK4B,GAAK5B;IACVM,oBAAoBsB,GAAKtB;IACzBJ,cAAc0B,GAAK1B;IACnBiC,OAAOP,GAAKO;IACZC,iBAAiBR,GAAKQ;IACtBvH,eAAe+G,GAAK/G,iBAAiB;;AAKvC,MAAMmB,KAAaqG,YAAAA;AAEnB,WAASC,cAAcrJ,IAAAA;AACrB,QACqB,eAAnBA,GAAU/C,QACS,eAAnB+C,GAAU/C,QAAAA,CACT8L,GAAWjI,IAAId,GAAUhE,GAAAA,GAC1B;AACA,UAAuB,eAAnBgE,GAAU/C,MAAAA;AACZ8L,QAAAA,GAAW7F,OAAOlD,GAAUhE,GAAAA;iBACA,eAAnBgE,GAAU/C,MAAAA;AACnB8L,QAAAA,GAAW9M,IAAI+D,GAAUhE,GAAAA;;AAE3B+G,MAAAA,GAAWmD,KAAKlG,EAAAA;IAClB;EACF;AAIA,MAAIsJ,KAAAA;AACJ,WAASC,kBAAkBvJ,IAAAA;AACzB,QAAIA,IAAAA;AAAWqJ,oBAAcrJ,EAAAA;;AAE7B,QAAA,CAAKsJ,IAAwB;AAC3BA,MAAAA,KAAAA;AACA,aAAOA,OAA2BtJ,KAAYgJ,GAAMnF,MAAAA,IAAAA;AAClDwF,sBAAcrJ,EAAAA;;AAChBsJ,MAAAA,KAAAA;IACF;EACF;AAGA,MAAME,mBAAoBxJ,CAAAA,OAAAA;AACxB,QAAIyJ,KAWF/D,UAGItG,OAAOiD,CAAAA,OAAkB,eAAZA,GAAGpF,QAAuBoF,GAAGrG,QAAQgE,GAAUhE,GAAAA,EAD5D+G,GAAWzC,MAAAA,CAAAA,EAVflB,OACGsK,CAAAA,OACCA,GAAI1J,UAAU/C,SAAS+C,GAAU/C,QACjCyM,GAAI1J,UAAUhE,QAAQgE,GAAUhE,QAAAA,CAC9B0N,GAAI1J,UAAUF,QAAQ6J,aACtBD,GAAI1J,UAAUF,QAAQ6J,cAAc3J,GAAUF,QAAQ6J,UAAAA,EAP5DC,CAAAA,CAAAA;AAmBF,QAAIjB,GAAKpK,cAAAA;AACPkL,MAAAA,KAEE/K,IAAIgL,CAAAA,QAAQ;WAAKA;QAAKlL,MAAMD,aAAamL,GAAIlL,MAAAA,IAAM;UADnDiL,EAAAA;;AAKJ,QAAuB,YAAnBzJ,GAAU/C,MAAAA;AAEZwM,MAAAA,KAEEI,UAAU7L,CAAAA,OAAAA,CAAAA,CAAYA,GAAOsB,SAAAA,IAAS,EADtCmK,EAAAA;;AAIFA,MAAAA,KAGEK,UAAU9L,CAAAA,OAAAA;AACR,YAAM+L,KAASzB,UAAUtK,EAAAA;AACzB,eAAOA,GAAOqB,SAASrB,GAAOsB,UAC1ByK,KACA5G,MAAM,CACJ4G,IAKErL,IAAI,MAAA;AACFV,UAAAA,GAAOqB,QAAAA;AACP,iBAAOrB;QAAM,CAAA,EAHfmB,KAAK,CAAA,EADLC,OAAOiD,CAAAA,OAAMA,GAAGrG,QAAQgE,GAAUhE,GAAAA,EADlC+G,GAAWzC,MAAAA,CAAAA,CAAAA,CAAAA,CAAAA;MAQb,CAAA,EAjBRmJ,EAAAA;;AAsBJ,QAAuB,eAAnBzJ,GAAU/C,MAAAA;AACZwM,MAAAA,KAmBEO,MAAM,MAAA;AAEJjB,QAAAA,GAAW7F,OAAOlD,GAAUhE,GAAAA;AAC5B6M,QAAAA,GAAQ3F,OAAOlD,GAAUhE,GAAAA;AACzB8M,QAAAA,GAAO5F,OAAOlD,GAAUhE,GAAAA;AAExBsN,QAAAA,KAAAA;AAEA,iBAAS9I,KAAIwI,GAAMvM,SAAS,GAAG+D,MAAK,GAAGA,MAAAA;AACrC,cAAIwI,GAAMxI,EAAAA,EAAGxE,QAAQgE,GAAUhE,KAAAA;AAAKgN,YAAAA,GAAMiB,OAAOzJ,IAAG,CAAA;;;AAEtD6I,sBACEzJ,cAAc,YAAYI,IAAWA,GAAUF,OAAAA,CAAAA;MAChD,CAAA,EA7BHsH,OAAOpJ,CAAAA,OAAAA;AACL,YAAIA,GAAOqB,OAAAA;AAGT,mBAAWW,MAAagJ,IAAAA;AACtB,gBAAIhJ,GAAUhE,QAAQgC,GAAOgC,UAAUhE,KAAK;AAC1C+M,cAAAA,GAAW7F,OAAOlD,GAAUhE,GAAAA;AAC5B;YACF;;mBAEG,CAAKgC,GAAOsB,SAAAA;AACjByJ,UAAAA,GAAW7F,OAAOlD,GAAUhE,GAAAA;;AAE9B6M,QAAAA,GAAQ1K,IAAI6B,GAAUhE,KAAKgC,EAAAA;MAAO,CAAA,EAfpCyL,EAAAA,CAAAA;;AAmCFA,MAAAA,KAGES,QAAQ,MAAA;AACNb,sBAAcrJ,EAAAA;MAAU,CAAA,EAH1ByJ,EAAAA;;AAQJ,WAAO7B,MAAM6B,EAAAA;EAAQ;AAGvB,MAAMU,KACJC,gBAAgB1B,SAAS0B,OAAOhM,OAAOiM,OAAO3B,OAAO4B,SAAAA;AACvD,MAAM/I,KAAiBnD,OAAOmM,OAAOJ,IAAU;IAC7CnG,UAAAA,CAAAA,CAAY2E,GAAK3E;IACjB2D,aAAa5E,GAAWzC;IAExB8B,mBAAmBpC,IAAAA;AAGjB,UAAuB,eAAnBA,GAAU/C,MAAAA;AACZsM,0BAAkBvJ,EAAAA;iBACU,eAAnBA,GAAU/C,QAAuB6L,GAAOhI,IAAId,GAAUhE,GAAAA,GAAM;AACrE,YAAIwO,KAAAA;AACJ,iBAAShK,KAAI,GAAGA,KAAIwI,GAAMvM,QAAQ+D,MAAAA;AAChCgK,UAAAA,KAASA,MAAUxB,GAAMxI,EAAAA,EAAGxE,QAAQgE,GAAUhE;;AAChD,YAAA,CAAKwO,IAAAA;AAAQzB,UAAAA,GAAW7F,OAAOlD,GAAUhE,GAAAA;;AACzCgN,QAAAA,GAAMzM,KAAKyD,EAAAA;AACX2D,gBAAQC,QAAAA,EAAUrE,KAAKgK,iBAAAA;MACzB;IACD;IAEDkB,uBAAuBxN,IAAM4C,IAAS8I,IAAAA;AACpC,UAAA,CAAKA,IAAAA;AAAMA,QAAAA,KAAO,CAAA;;AAElB,UAAI+B;AACJ,UAEW,eAATzN,OACCyN,KAAuBC,iBAAiB9K,GAAQ/B,KAAAA,OAAYb,IAAAA;AAE7D,cAAM,IAAIuH,MACP,+BAA8BvH,EAAAA,gBAAoByN,EAAAA,GAAAA;;AAIvD,aAAO9K,cAAc3C,IAAM4C,IAAS;QAClC8J,WACW,eAAT1M,KACM2L,KAAOA,KAAM,IAAK,IAAA;WAEvBK;WACAN;QACH/G,eAAe+G,GAAK/G,iBAAiBqH,GAASrH;QAC9CoC,UAAU2E,GAAK3E,YAAAA,UAAa2E,GAAK3E,YAAsBzC,GAAOyC;;IAEjE;IAED4G,wBAAwB5K,IAAAA;AACtB,UAAuB,eAAnBA,GAAU/C,MAAAA;AACZ,eAAO6B,YAAY0K,iBAAiBxJ,EAAAA,CAAAA;;AAGtC,aAAOlB,YACL+L,KAAsB,MAAA;AACpB,YAAIvK,KAASwI,GAAO7K,IAAI+B,GAAUhE,GAAAA;AAClC,YAAA,CAAKsE,IAAAA;AACHwI,UAAAA,GAAO3K,IAAI6B,GAAUhE,KAAMsE,KAASkJ,iBAAiBxJ,EAAAA,CAAAA;;AAGvDM,QAAAA,KAEE4J,QAAQ,MAAA;AACNX,4BAAkBvJ,EAAAA;QAAU,CAAA,EAF9BM,EAAAA;AAMF,YAAMwK,KAASjC,GAAQ5K,IAAI+B,GAAUhE,GAAAA;AACrC,YACqB,YAAnBgE,GAAU/C,QACV6N,OACCA,GAAOzL,SAASyL,GAAOxL,UAAAA;AAExB,iBAQEwK,UAAUxB,SAAAA,EAPVnF,MAAM,CACJ7C,IAGElB,OAAO0L,CAAAA,OAAUA,OAAWjC,GAAQ5K,IAAI+B,GAAUhE,GAAAA,CAAAA,EADlDsM,UAAUwC,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA;;AAOhB,iBAAOxK;;MACT,CAAA,CAAA;IAGL;IAEDyK,aAAajN,IAAO6K,IAAAA;AAClB,UAAM3I,KAAYuB,GAAOkJ,uBAAuB,SAAS3M,IAAO6K,EAAAA;AAChE,aAAOpH,GAAOqJ,wBAAwB5K,EAAAA;IACvC;IAEDgL,oBAAoBlN,IAAO6K,IAAAA;AACzB,UAAM3I,KAAYuB,GAAOkJ,uBACvB,gBACA3M,IACA6K,EAAAA;AAEF,aAAOpH,GAAOqJ,wBAAwB5K,EAAAA;IACvC;IAEDiL,gBAAgBnN,IAAO6K,IAAAA;AACrB,UAAM3I,KAAYuB,GAAOkJ,uBAAuB,YAAY3M,IAAO6K,EAAAA;AACnE,aAAOpH,GAAOqJ,wBAAwB5K,EAAAA;IACvC;IAEDkL,UAAUpN,IAAOqN,IAAWrL,IAAAA;AAC1B,UAAI9B,KAAiC;AAInC0B,gBAAUgK,CAAAA,OAAAA;AACR1L,QAAAA,KAAS0L;MAAG,CAAA,EAFdnI,GAAOzD,MAAMA,IAAOqN,IAAWrL,EAAAA,CAAAA,EAI/ByG,YAAAA;AAEF,aAAOvI;IACR;IAEDF,OAAKA,CAACA,IAAOqN,IAAWrL,OACfyB,GAAOwJ,aAAaK,cAActN,IAAOqN,EAAAA,GAAYrL,EAAAA;IAG9DuL,cAAYA,CAACvN,IAAOqN,IAAWrL,OACtByB,GAAOyJ,oBACZI,cAActN,IAAOqN,EAAAA,GACrBrL,EAAAA;IAIJwL,UAAQA,CAACxN,IAAOqN,IAAWrL,OAClByB,GAAO0J,gBAAgBG,cAActN,IAAOqN,EAAAA,GAAYrL,EAAAA;;AAInE,MAAI0B,KAAgDtB;AACpD,MAA6B,MAAc;AACzC,QAAA,EAAMgG,MAAEA,IAAI5F,QAAEA,EAAAA,IAAW8I,YAAAA;AACzB7H,IAAAA,GAAOgK,yBAA0BC,CAAAA,OAClB9L,UAAU8L,EAAAA,EAAlBlL,CAAAA;AACPkB,IAAAA,KAAgB0E;EAClB;AAIA,MAAMuF,KAAmBnE,iBAAiBqB,GAAKpB,SAAAA;AAK/C,MAAMqC,IAAWhC,MACf6D,GAAiB;IACflK,QAAAA;IACAC,eAAAA;IACAF,SAASkH,iBAAiB;MAAEhH,eAAAA;;KAC3BuB,GAAWzC,MAAAA,CAAAA;AAKDoL,UAAV9B,CAAAA;AAEL,SAAOrI;AACT;AAMO,IAAMoK,IAAejD;;;;ACv3B5B,IAAMkD,KAAM,CAAA;AAQL,IAAMC,KACLC,iBAAcF,EAAAA;AA6BTG,IAAAA,KAA4CF,GAAQE;AAMpDC,IAAAA,KAA4CH,GAAQG;AAEjEH,GAAQI,cAAc;AAeTC,IAAAA,YAAYA,MAAAA;AACvB,MAAMC,KAAeC,cAAWP,EAAAA;AAEhC,MAAIM,OAAWP,MAAgC,MAAc;AAC3D,QAAMS,KACJ;AAEFC,YAAQD,MAAMA,EAAAA;AACd,UAAM,IAAIE,MAAMF,EAAAA;EAClB;AAEA,SAAOF;AAAM;ACzER,IAAMK,KAAe;EAC1BC,UAAAA;EACAC,OAAAA;EACAL,OAAAA;EACAM,MAAAA;EACAC,YAAAA;EACAC,WAAAA;;AAIF,IAAMC,qBAAqBA,CACzBC,IACAC,OAEOD,OAAMC,MAAAA,EAAAA,CAAQD,MAAAA,CAAKC,MAAKD,GAAEE,QAAQD,GAAEC;AA4BtC,IAAMC,mBAAmBA,CAC9BC,IACAC,OAAAA;AAEA,MAAMC,KAAc;OACfF;OACAC;IACHT,MAAAA,WACES,GAAOT,QAAsBS,GAAOf,QAAQe,GAAOT,OAAOQ,GAAUR;IACtEF,UAAAA,CAAAA,CAAYW,GAAOX;IACnBC,OAAAA,CAAAA,CAASU,GAAOV;;AAGlB,UAlCyBY,CAAgCP,IAAMC,OAAAA;AAC/D,aAAWC,MAAOF,IAAAA;AAAG,UAAA,EAAME,MAAOD,KAAAA;AAAI,eAAA;;;AACtC,aAAWC,MAAOD,IAAAA;AAChB,UACU,gBAARC,KAAAA,CACKH,mBAAmBC,GAAEE,EAAAA,GAAMD,GAAEC,EAAAA,CAAAA,IAC9BF,GAAEE,EAAAA,MAASD,GAAEC,EAAAA,GAAAA;AAEjB,eAAA;;;AAGJ,WAAA;EAAY,GAuBcE,IAAWE,EAAAA,IAAYA,KAAWF;AAAS;AAGhE,IAAMI,iBAAiBA,CAA+BR,IAAMC,OAAAA;AACjE,WAASQ,KAAI,GAAGC,KAAIT,GAAEU,QAAQF,KAAIC,IAAGD,MAAAA;AAAK,QAAIT,GAAES,EAAAA,MAAOR,GAAEQ,EAAAA,GAAAA;AAAI,aAAA;;;AAC7D,SAAA;AAAY;AAGd,IAAMG,KACHC;AAEI,SAASC,cACdC,IACAC,IAAAA;AAEA,MAEIJ,MACAA,GAAqBK,qBACrBL,GAAqBK,kBAAkBC,SAAAA;AAEzCC,YAAQC,QAAQJ,EAAAA,EAAOK,KAAKN,EAAAA;;AAE5BA,IAAAA,GAASC,EAAAA;;AAEb;AC4DO,SAASM,YAGdC,IAAAA;AACA,MAAMC,KAAkBC,UAAAA,IAAO;AAC/B,MAAMrC,KAASD,UAAAA;AAEf,MAAA,CAAOuC,IAAOX,EAAAA,IACNY,YAA4ClC,EAAAA;AAEpD,MAAMmC,KAAwBC,eAC5B,CAACC,IAAsBC,OAAAA;AACrBjB,kBAAcC,IAAU;SAAKtB;MAAcC,UAAAA;;AAC3C,WAmBEsC,UADAC,KAAK,CAAA,EADLC,OAAO7B,CAAAA,OAAAA,CAAWA,GAAO8B,OAAAA,EAZzBC,OAAO/B,CAAAA,OAAAA;AACL,UAAImB,GAAUN,SAAAA;AACZJ,sBAAcC,IAAU;UACtBrB,UAAAA;UACAC,OAAOU,GAAOV;UACdC,MAAMS,GAAOT;UACbN,OAAOe,GAAOf;UACdO,YAAYQ,GAAOR;UACnBC,WAAWO,GAAOP;;;IAEtB,CAAA,EAdFV,GAAOwC,gBACLS,cAA+Bd,IAAOO,EAAAA,GACtCC,MAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACZ,GAmBL,CAAC3C,IAAQmC,IAAOR,EAAAA,CAAAA;AAGlBuB,EAAMC,aAAU,MAAA;AACdf,IAAAA,GAAUN,UAAAA;AACV,WAAO,MAAA;AACLM,MAAAA,GAAUN,UAAAA;IAAe;EAC1B,GACA,CAAA,CAAA;AAEH,SAAO,CAACQ,IAAOE,EAAAA;AACjB;ACtLO,SAASY,WAIdjB,IACAO,IAAAA;AAEA,MAAMW,KAAahB,UAAAA,MACjBiB;AAGF,SAAaC,WAAQ,MAAA;AACnB,QAAMC,KAAUP,cAA+Bd,IAAOO,EAAAA;AAEtD,QAAA,WAAIW,GAAKvB,WAAyBuB,GAAKvB,QAAQhB,QAAQ0C,GAAQ1C,KAAAA;AAC7D,aAAOuC,GAAKvB;WACP;AACLuB,MAAAA,GAAKvB,UAAU0B;AACf,aAAOA;IACT;EAAA,GACC,CAACrB,IAAOO,EAAAA,CAAAA;AACb;ACbO,IAAMe,oBAAqBzD,CAAAA,OAAAA;AAChC,MAAA,CAAMA,GAA2B0D,QAAQ;AACvC,QAAMC,KAAU,oBAAIC;AACpB,QAAMC,KAAM,oBAAIC;AAEhB,QAAI9D,GAAO+D,aAAAA;AAGPC,gBAAUtD,CAAAA,OAAAA;AACR,YAAuB,eAAnBA,GAAUuD,QAAuBN,GAAQO,IAAIxD,GAAUI,GAAAA,GAAM;AAC/D6C,UAAAA,GAAQQ,OAAOzD,GAAUI,GAAAA;AACzB+C,UAAAA,GAAIM,OAAOzD,GAAUI,GAAAA;QACvB;MAAA,CAAA,EALFd,GAAO+D,WAAAA;;AAUV/D,IAAAA,GAA2B0D,SAAS;MACnCU,KAAItD,CAAAA,OACK+C,GAAIO,IAAItD,EAAAA;MAEjBuD,IAAIvD,IAAKc,IAAAA;AACP+B,QAAAA,GAAQQ,OAAOrD,EAAAA;AACf+C,QAAAA,GAAIQ,IAAIvD,IAAKc,EAAAA;MACd;MACD0C,QAAQxD,IAAAA;AACN6C,QAAAA,GAAQY,IAAIzD,EAAAA;MACd;;EAEJ;AAEA,SAAQd,GAA2B0D;AAAM;ACkI3C,IAAMc,aAAaA,CAACxE,IAAgB2C,OAClCA,MAAAA,WAAWA,GAAQ8B,WAAAA,CAAAA,CACb9B,GAAQ8B,WACVzE,GAAOyE;AAsCN,SAASC,SAGdC,IAAAA;AACA,MAAM3E,KAASD,UAAAA;AACf,MAAM6E,KAAQnB,kBAAkBzD,EAAAA;AAChC,MAAMyE,KAAWD,WAAWxE,IAAQ2E,GAAKhC,OAAAA;AACzC,MAAMa,KAAUJ,WAAWuB,GAAKxC,OAAOwC,GAAKjC,SAAAA;AAE5C,MAAMmC,KAAetB,WAAQ,MAAA;AAC3B,QAAIoB,GAAKG,OAAAA;AAAO,aAAO;;AAEvB,QAAMD,KAAS7E,GAAO+E,aAAavB,IAAS;MAC1CwB,eAAeL,GAAKK;SACjBL,GAAKhC;;AAGV,WAAO8B,KAGDzB,OAAO/B,CAAAA,OAAAA;AACL2D,MAAAA,GAAMP,IAAIb,GAAQ1C,KAAKG,EAAAA;IAAO,CAAA,EAFhC4D,EAAAA,IAKFA;EAAM,GACT,CACDD,IACA5E,IACAwD,IACAiB,IACAE,GAAKG,OACLH,GAAKK,eACLL,GAAKhC,OAAAA,CAAAA;AAGP,MAAMsC,KAAoBxC,eACxB,CACEoC,IACAJ,OAAAA;AAEA,QAAA,CAAKI,IAAAA;AAAQ,aAAO;QAAEvE,UAAAA;;;AAEtB,QAAIW,KAAS2D,GAAMR,IAAIZ,GAAQ1C,GAAAA;AAC/B,QAAA,CAAKG,IAAQ;AACX,UAAIe;AAEJ,UAAMkD,KAGJlB,UAAUmB,CAAAA,OAAAA;AACRlE,QAAAA,KAASkE;AACT,YAAInD,IAAAA;AAASA,UAAAA,GAAQf,EAAAA;;MAAO,CAAA,EAH9BmE,UAAU,MAAOX,MAAAA,CAAazC,MAAAA,CAAaf,EAAAA,EAD3C4D,EAAAA,CAAAA;AAQF,UAAc,QAAV5D,MAAkBwD,IAAU;AAC9B,YAAMY,KAAU,IAAItD,QAAQuD,CAAAA,OAAAA;AAC1BtD,UAAAA,KAAUsD;QAAQ,CAAA;AAGpBV,QAAAA,GAAMP,IAAIb,GAAQ1C,KAAKuE,EAAAA;AACvB,cAAMA;MACR,OAAA;AACEH,QAAAA,GAAaK,YAAAA;;IAEhB,WAAUd,MAAsB,QAAVxD,MAAkB,UAAUA,IAAAA;AACjD,YAAMA;;AAGR,WAAQA,MAA+C;MAAEX,UAAAA;;EAAgB,GAE3E,CAACsE,IAAOpB,EAAAA,CAAAA;AAGV,MAAMgC,KAAO,CACXxF,IACAwD,IACAmB,GAAKK,eACLL,GAAKhC,SACLgC,GAAKG,KAAAA;AAGP,MAAA,CAAOxC,IAAOX,EAAAA,IAAkBY,YAC9B,MACE,CACEsC,IACA9D,iBAAiBV,IAAc4E,GAAYJ,IAAQJ,EAAAA,CAAAA,GACnDe,EAAAA,CAAAA;AAIN,MAAIC,KAAgBnD,GAAM,CAAA;AAC1B,MAAIuC,OAAWvC,GAAM,CAAA,KAAMlB,eAAekB,GAAM,CAAA,GAAIkD,EAAAA,GAAAA;AAClD7D,IAAAA,GAAS,CACPkD,IACCY,KAAgB1E,iBACfuB,GAAM,CAAA,GACN2C,GAAYJ,IAAQJ,EAAAA,CAAAA,GAEtBe,EAAAA,CAAAA;;AAIJtC,EAAMC,aAAU,MAAA;AACd,QAAM0B,KAASvC,GAAM,CAAA;AACrB,QAAMkB,KAAUlB,GAAM,CAAA,EAAG,CAAA;AAEzB,QAAIoD,KAAAA;AAEJ,QAAMC,eAAgB1E,CAAAA,OAAAA;AACpByE,MAAAA,KAAAA;AACAhE,oBAAcC,IAAUW,CAAAA,OAAAA;AACtB,YAAMsD,KAAa7E,iBAAiBuB,GAAM,CAAA,GAAIrB,EAAAA;AAC9C,eAAOqB,GAAM,CAAA,MAAOsD,KAChB,CAACtD,GAAM,CAAA,GAAIsD,IAAYtD,GAAM,CAAA,CAAA,IAC7BA;MAAK,CAAA;IACT;AAGJ,QAAIuC,IAAQ;AACV,UAAMK,KAKJlB,UAAU2B,YAAAA,EAHVE,MAAM,MAAA;AACJF,qBAAa;UAAErF,UAAAA;;MAAkB,CAAA,EAFnCuE,EAAAA,CAAAA;AAOF,UAAA,CAAKa,IAAAA;AAAWC,qBAAa;UAAErF,UAAAA;;;AAE/B,aAAO,MAAA;AACLsE,QAAAA,GAAMN,QAAQd,GAAQ1C,GAAAA;AACtBoE,QAAAA,GAAaK,YAAAA;MAAa;IAE9B,OAAA;AACEI,mBAAa;QAAErF,UAAAA;;;EACjB,GACC,CAACsE,IAAOtC,GAAM,CAAA,GAAIA,GAAM,CAAA,EAAG,CAAA,CAAA,CAAA;AAE9B,MAAMyC,IAAqBtC,eACxBqD,CAAAA,OAAAA;AACC,QAAMnD,KAAU;MACdqC,eAAeL,GAAKK;SACjBL,GAAKhC;SACLmD;;AAGLpE,kBAAcC,IAAUW,CAAAA,OASf,CARQmC,KAGTzB,OAAO/B,CAAAA,OAAAA;AACL2D,MAAAA,GAAMP,IAAIb,GAAQ1C,KAAKG,EAAAA;IAAO,CAAA,EAFhCjB,GAAO+E,aAAavB,IAASb,EAAAA,CAAAA,IAK/B3C,GAAO+E,aAAavB,IAASb,EAAAA,GACjBL,GAAM,CAAA,GAAIkD,EAAAA,CAAAA;EAC1B,GAEJ,CACExF,IACA4E,IACApB,IACAiB,IACAE,GAAKK,eACLL,GAAKhC,SACLgC,GAAKG,KAAAA,CAAAA;AAIT,SAAO,CAACW,IAAeV,CAAAA;AACzB;ACpKO,SAASgB,gBAKdpB,IACAqB,IAAAA;AAEA,MAAMhG,KAASD,UAAAA;AACf,MAAMyD,KAAUJ,WAAWuB,GAAKxC,OAAOwC,GAAKjC,SAAAA;AAE5C,MAAMuD,KAAmB5D,UAEvB2D,EAAAA;AACFC,EAAAA,GAAWnE,UAAUkE;AAErB,MAAMnB,KAAetB,WACnB,MAAA,CACGoB,GAAKG,QAAQ9E,GAAOkG,oBAAoB1C,IAASmB,GAAKhC,OAAAA,IAAW,MACpE,CAAC3C,IAAQwD,IAASmB,GAAKG,OAAOH,GAAKhC,OAAAA,CAAAA;AAGrC,MAAM6C,KAAO,CAACxF,IAAQwD,IAASmB,GAAKhC,SAASgC,GAAKG,KAAAA;AAElD,MAAA,CAAOxC,IAAOX,EAAAA,IAAkBY,YAC9B,MAAM,CAACsC,IAAQ;OAAKxE;IAAcC,UAAAA,CAAAA,CAAYuE;KAAUW,EAAAA,CAAAA;AAG1D,MAAIC,KAAgBnD,GAAM,CAAA;AAC1B,MAAIuC,OAAWvC,GAAM,CAAA,KAAMlB,eAAekB,GAAM,CAAA,GAAIkD,EAAAA,GAAAA;AAClD7D,IAAAA,GAAS,CACPkD,IACCY,KAAgB1E,iBAAiBuB,GAAM,CAAA,GAAI;MAAEhC,UAAAA,CAAAA,CAAYuE;QAC1DW,EAAAA,CAAAA;;AAIJtC,EAAMC,aAAU,MAAA;AACd,QAAMwC,eACJ1E,CAAAA,OAAAA;AAEAS,oBAAcC,IAAUW,CAAAA,OAAAA;AACtB,YAAMsD,KAAa7E,iBAAiBuB,GAAM,CAAA,GAAIrB,EAAAA;AAC9C,YAAIqB,GAAM,CAAA,MAAOsD,IAAAA;AAAY,iBAAOtD;;AACpC,YAAI2D,GAAWnE,WAAWQ,GAAM,CAAA,EAAG9B,SAASoF,GAAWpF,MAAAA;AACrDoF,UAAAA,GAAWpF,OAAOyF,GAAWnE,QAC3BQ,GAAM,CAAA,EAAG9B,MACToF,GAAWpF,IAAAA;;AAIf,eAAO,CAAC8B,GAAM,CAAA,GAAIsD,IAAmBtD,GAAM,CAAA,CAAA;MAAG,CAAA;IAC9C;AAGJ,QAAIA,GAAM,CAAA,GAAA;AACR,aAKE0B,UAAU2B,YAAAA,EAHVE,MAAM,MAAA;AACJF,qBAAa;UAAErF,UAAAA,CAAAA,CAAYuE;;MAAS,CAAA,EAFtCvC,GAAM,CAAA,CAAA,CAAA,EAKNiD;;AAEFI,mBAAa;QAAErF,UAAAA;;;EACjB,GACC,CAACgC,GAAM,CAAA,CAAA,CAAA;AAGV,MAAM4D,KAA4BzD,eAC/BqD,CAAAA,OAAAA;AACC,QAAMjB,KAAS7E,GAAOkG,oBAAoB1C,IAAS;SAC9CmB,GAAKhC;SACLmD;;AAGLpE,kBAAcC,IAAUW,CAAAA,OAAS,CAACuC,IAAQvC,GAAM,CAAA,GAAIkD,EAAAA,CAAAA;EAAM,GAE5D,CAACxF,IAAQwD,IAASmB,GAAKhC,SAASgC,GAAKG,KAAAA,CAAAA;AAGvC,SAAO,CAACW,IAAeS,EAAAA;AACzB;ACpQO,SAASC,SAGdC,IAAAA;AACA,MAAMC,KAAWnE,YAA6BkE,GAAMjE,KAAAA;AACpD,SAAOiE,GAAME,SAAS;OAAKD,GAAS,CAAA;IAAI7D,iBAAiB6D,GAAS,CAAA;;AACpE;ACPO,SAASE,MAGdH,IAAAA;AACA,MAAMjE,KAAQuC,SAA0B0B,EAAAA;AACxC,SAAOA,GAAME,SAAS;OAAKnE,GAAM,CAAA;IAAI4C,cAAc5C,GAAM,CAAA;;AAC3D;ACGO,SAASqE,aAIdJ,IAAAA;AACA,MAAMlB,KAAea,gBACnBK,IACAA,GAAMJ,OAAAA;AAGR,SAAOI,GAAME,SAAS;OACjBpB,GAAa,CAAA;IAChBgB,qBAAqBhB,GAAa,CAAA;;AAEtC;", "names": ["Kind", "NAME", "DOCUMENT", "OPERATION_DEFINITION", "VARIABLE_DEFINITION", "SELECTION_SET", "FIELD", "ARGUMENT", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "VARIABLE", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "LIST", "OBJECT", "OBJECT_FIELD", "DIRECTIVE", "NAMED_TYPE", "LIST_TYPE", "NON_NULL_TYPE", "GraphQLError", "Error", "constructor", "message", "nodes", "source", "positions", "path", "originalError", "extensions", "super", "this", "name", "Array", "isArray", "let", "_extensions", "const", "originalExtensions", "t", "toJSON", "toString", "toStringTag", "Symbol", "input", "idx", "error", "kind", "advance", "pattern", "lastIndex", "test", "slice", "leadingRe", "blockString", "string", "lines", "split", "out", "commonIndent", "firstNonEmptyLine", "lastNonEmptyLine", "length", "i", "replace", "ignored", "char", "charCodeAt", "nameRe", "match", "value", "constRe", "variableRe", "intRe", "floatPartRe", "complexStringRe", "blockStringRe", "stringRe", "constant", "intPart", "block", "JSON", "parse", "list", "values", "push", "object", "fields", "_name", "_value", "arguments_", "args", "directives", "arguments", "field", "_alias", "alias", "selectionSet", "type", "_type", "typeConditionRe", "typeCondition", "fragmentSpreadRe", "fragmentSpread", "_idx", "_typeCondition", "_directives", "_selectionSet", "selections", "fragmentDefinitionRe", "fragmentDefinition", "operationDefinitionRe", "operationDefinition", "_operation", "_variableDefinitions", "variableDefinitions", "vars", "_defaultValue", "variable", "defaultValue", "operation", "_options", "body", "document", "definitions", "printString", "string", "JSON", "stringify", "printBlockString", "replace", "const", "hasItems", "array", "length", "nodes", "OperationDefinition", "node", "operation", "name", "variableDefinitions", "directives", "SelectionSet", "selectionSet", "let", "out", "value", "map", "VariableDefinition", "join", "Directive", "Variable", "variable", "print", "type", "defaultValue", "Field", "alias", "arguments", "args", "Argument", "argsLine", "StringValue", "block", "BooleanValue", "Null<PERSON><PERSON>ue", "_node", "IntValue", "FloatValue", "EnumValue", "Name", "ListValue", "values", "ObjectValue", "fields", "ObjectField", "Document", "definitions", "selections", "FragmentSpread", "InlineFragment", "typeCondition", "FragmentDefinition", "NamedType", "ListType", "NonNullType", "kind", "e", "e", "r", "t", "i", "a", "e", "f", "n", "s", "l", "u", "o", "r", "t", "i", "e", "a", "f", "n", "s", "l", "u", "r", "t", "i", "a", "e", "f", "n", "s", "l", "u", "r", "t", "i", "a", "e", "f", "n", "r", "t", "i", "a", "e", "f", "n", "e", "r", "t", "i", "a", "f", "n", "e", "r", "t", "i", "a", "e", "r", "t", "i", "e", "a", "rehydrateGraphQlError", "error", "message", "extensions", "name", "GraphQLError", "nodes", "source", "positions", "path", "CombinedError", "Error", "constructor", "input", "normalizedGraphQLErrors", "graphQLErrors", "map", "generateErrorMessage", "networkErr", "graphQlErrs", "err", "networkError", "super", "this", "response", "toString", "phash", "x", "seed", "h", "i", "l", "length", "charCodeAt", "seen", "Set", "cache", "WeakMap", "stringify", "has", "JSON", "toJSON", "Array", "isArray", "out", "value", "FileConstructor", "NoopConstructor", "BlobConstructor", "keys", "Object", "sort", "getPrototypeOf", "prototype", "key", "get", "Math", "random", "slice", "set", "__key", "add", "delete", "extract", "stringifyVariables", "clear", "File", "Blob", "GRAPHQL_STRING_RE", "REPLACE_CHAR_RE", "replaceOutsideStrings", "str", "idx", "replace", "sanitizeDocument", "node", "split", "join", "trim", "prints", "Map", "docs", "stringifyDocument", "printed", "loc", "body", "print", "start", "end", "locationOffset", "line", "column", "hashDocument", "definitions", "operationName", "getOperationName", "keyDocument", "query", "parse", "noLocation", "createRequest", "_query", "_variables", "variables", "printedVars", "kind", "Kind", "OPERATION_DEFINITION", "getOperationType", "operation", "makeResult", "result", "errors", "defaultHasNext", "data", "hasNext", "stale", "deepMerge", "target", "mergeResultPatch", "prevResult", "nextResult", "pending", "hasExtensions", "payload", "incremental", "withData", "_loop", "patch", "push", "assign", "prop", "part", "res", "find", "pendingRes", "id", "subPath", "items", "startIndex", "makeErrorResult", "makeFetchBody", "request", "documentId", "<PERSON><PERSON><PERSON><PERSON>", "miss", "makeFetchURL", "useGETMethod", "context", "preferGetMethod", "url", "urlParts", "splitOutSearchParams", "finalUrl", "indexOf", "URLSearchParams", "serializeBody", "json", "files", "size", "form", "FormData", "append", "index", "file", "values", "makeFetchOptions", "headers", "accept", "extraOptions", "fetchOptions", "for<PERSON>ach", "toLowerCase", "serializedBody", "method", "decoder", "TextDecoder", "boundaryHeaderRe", "eventStreamRe", "decode", "async", "streamBody", "Symbol", "asyncIterator", "chunk", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "cancel", "chunks", "boundary", "buffer", "boundaryIndex", "fetchOperation", "networkMode", "Promise", "resolve", "contentType", "fetch", "results", "test", "parseMultipartMixed", "<PERSON><PERSON><PERSON><PERSON>", "match", "isPreamble", "preambleIndex", "parseEventStream", "parseJSON", "text", "parseMaybeJSON", "NODE_ENV", "console", "warn", "e", "status", "statusText", "makeFetchSource", "abortController", "AbortController", "signal", "onEnd", "abort", "filter", "fromAsyncIterable", "collectTypes", "obj", "types", "Array", "isArray", "item", "key", "add", "formatNode", "node", "definitions", "definition", "newDefinition", "push", "directives", "length", "_directives", "directive", "name", "value", "slice", "selections", "hasTypename", "kind", "Kind", "OPERATION_DEFINITION", "selectionSet", "selection", "FIELD", "alias", "newSelection", "NAME", "_generated", "formattedDocs", "Map", "formatDocument", "query", "keyDocument", "result", "get", "__key", "set", "Object", "defineProperty", "enumerable", "maskTypename", "data", "isRoot", "map", "d", "acc", "__typename", "with<PERSON><PERSON><PERSON>", "_source$", "source$", "sink", "to<PERSON>romise", "take", "filter", "stale", "hasNext", "then", "onResolve", "onReject", "subscribe", "onResult", "makeOperation", "request", "context", "addMetadata", "operation", "meta", "noop", "gql", "parts", "fragmentNames", "source", "body", "i", "arguments", "unshift", "document", "FRAGMENT_DEFINITION", "stringifyDocument", "has", "console", "warn", "DOCUMENT", "shouldSkip", "mapTypeNames", "formattedOperation", "cacheExchange", "forward", "client", "dispatchDebug", "resultCache", "operationCache", "isOperationCached", "requestPolicy", "ops$", "cachedOps$", "cachedResult", "type", "message", "makeResult", "cacheOutcome", "reexecuteOperation", "op", "forwardedOps$", "tap", "response", "typenames", "additionalTypenames", "Set", "concat", "pendingOperations", "typeName", "operations", "values", "clear", "delete", "merge", "revalidated", "ssrExchange", "params", "staleWhileRevalidate", "includeExtensions", "invalidate<PERSON><PERSON><PERSON>", "invalidate", "Promise", "resolve", "shift", "ssr", "isClient", "suspense", "deserializeResult", "JSON", "parse", "extensions", "error", "CombinedError", "networkError", "Error", "graphQLErrors", "serialized", "serializeResult", "stringify", "path", "restoreData", "restore", "extractData", "initialState", "subscriptionExchange", "forwardSubscription", "enableAllOperations", "isSubscriptionOperation", "isSubscriptionOperationFn", "subscriptionResults$", "mergeMap", "teardown$", "takeUntil", "observableish", "makeFetchBody", "make", "observer", "isComplete", "sub", "nextResult", "next", "mergeResultPatch", "errors", "makeErrorResult", "complete", "unsubscribe", "forward$", "debugExchange", "NODE_ENV", "log", "dedupExchange", "fetchExchange", "fetchResults$", "url", "makeFetchURL", "fetchOptions", "makeFetchOptions", "makeFetchSource", "onPush", "fetchSubscriptions", "composeExchanges", "exchanges", "reduceRight", "exchange", "forwarded", "operations$", "share", "event", "timestamp", "Date", "now", "mapExchange", "onOperation", "onError", "newResult", "fromPromise", "fromValue", "newOperation", "fallbackExchange", "_x", "Client", "opts", "ids", "replays", "active", "dispatched", "queue", "baseOpts", "fetch", "preferGetMethod", "makeSubject", "nextOperation", "isOperationBatchActive", "dispatchOperation", "makeResultSource", "result$", "res", "_instance", "results$", "<PERSON><PERSON><PERSON><PERSON>", "switchMap", "value$", "onEnd", "splice", "onStart", "instance", "this", "create", "prototype", "assign", "queued", "createRequestOperation", "requestOperationType", "getOperationType", "executeRequestOperation", "lazy", "replay", "execute<PERSON>uery", "executeSubscription", "executeMutation", "readQuery", "variables", "createRequest", "subscription", "mutation", "subscribeToDebugTarget", "onEvent", "composedExchange", "publish", "createClient", "OBJ", "Context", "createContext", "Provider", "Consumer", "displayName", "useClient", "client", "useContext", "error", "console", "Error", "initialState", "fetching", "stale", "data", "extensions", "operation", "areOperationsEqual", "a", "b", "key", "computeNextState", "prevState", "result", "newState", "isShallowDifferent", "hasDepsChanged", "i", "l", "length", "reactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "deferDispatch", "setState", "value", "ReactCurrentOwner", "current", "Promise", "resolve", "then", "useMutation", "query", "isMounted", "useRef", "state", "useState", "executeMutation", "useCallback", "variables", "context", "to<PERSON>romise", "take", "filter", "hasNext", "onPush", "createRequest", "React", "useEffect", "useRequest", "prev", "undefined", "useMemo", "request", "getCacheForClient", "_react", "reclaim", "Set", "map", "Map", "operations$", "subscribe", "kind", "has", "delete", "get", "set", "dispose", "add", "isSuspense", "suspense", "useQuery", "args", "cache", "source", "pause", "execute<PERSON>uery", "requestPolicy", "getSnapshot", "subscription", "_result", "<PERSON><PERSON><PERSON><PERSON>", "promise", "_resolve", "unsubscribe", "deps", "currentResult", "hasResult", "updateResult", "nextResult", "onEnd", "opts", "useSubscription", "handler", "handler<PERSON>ef", "executeSubscription", "Mutation", "props", "mutation", "children", "Query", "Subscription"]}