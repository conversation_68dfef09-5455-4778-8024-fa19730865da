package providers

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/semanser/ai-coder/config"
	"github.com/semanser/ai-coder/database"
	"github.com/semanser/ai-coder/templates"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
)

type MistralProvider struct {
	llm llms.Model
}

func (p MistralProvider) New() Provider {
	apiKey := os.Getenv("MISTRAL_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.MistralAPIKey
	}
	
	llm, err := openai.New(
		openai.WithAPIKey(apiKey),
		openai.WithBaseURL("https://api.mistral.ai/v1"),
		openai.WithModel(config.Config.MistralModel),
	)
	if err != nil {
		log.Fatalf("Failed to create Mistral client: %v", err)
	}
	return MistralProvider{llm: llm}
}

func (p MistralProvider) Name() ProviderType {
	return ProviderMistral
}

func (p MistralProvider) Summary(query string, n int) (string, error) {
	apiKey := os.Getenv("MISTRAL_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.MistralAPIKey
	}
	
	llm, err := openai.New(
		openai.WithAPIKey(apiKey),
		openai.WithBaseURL("https://api.mistral.ai/v1"),
		openai.WithModel(config.Config.MistralModel),
	)
	if err != nil {
		return "", fmt.Errorf("failed to create Mistral client for summary: %v", err)
	}

	prompt := templates.SummaryPrompt(query, n)
	completion, err := llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get summary from Mistral: %v", err)
	}
	return completion, nil
}

func (p MistralProvider) DockerImageName(task string) (string, error) {
	prompt := templates.DockerPrompt(task)
	completion, err := p.llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get docker image name from Mistral: %v", err)
	}
	return completion, nil
}

func (p MistralProvider) NextTask(args NextTaskOptions) *database.Task {
	var messages []llms.MessageContent

	apiKey := os.Getenv("MISTRAL_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.MistralAPIKey
	}
	
	llm, err := openai.New(
		openai.WithAPIKey(apiKey),
		openai.WithBaseURL("https://api.mistral.ai/v1"),
		openai.WithModel(config.Config.MistralModel),
	)
	if err != nil {
		log.Printf("Failed to create Mistral client for next task: %v", err)
		return defaultAskTask("Failed to initialize Mistral model for next task generation.")
	}

	prompt := templates.AgentPrompt(args.DockerImage)
	messages = tasksToMessages(args.Tasks, prompt)

	options := []llms.CallOption{
		llms.WithTools(Tools),
		llms.WithStreamingFunc(func(ctx context.Context, chunk []byte) error {
			if StreamFunc != nil {
				StreamFunc(chunk)
			}
			return nil
		}),
	}

	content, err := llm.GenerateContent(context.Background(), messages, options...)
	if err != nil {
		log.Printf("Failed to generate content from Mistral, asking user: %v", err)
		return defaultAskTask("There was an error generating the next task.")
	}

	if len(content.Choices) == 0 {
		return defaultAskTask("No choices found for the next task.")
	}

	task, err := toolToTask(content.Choices)
	if err != nil {
		log.Printf("Failed to convert tool call to task, asking user: %v", err)
		return defaultAskTask(fmt.Sprintf("There was an error processing the tool call: %v", err))
	}

	return task
}
