package providers

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/semanser/ai-coder/database"
	"github.com/semanser/ai-coder/templates" // Import the templates package
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/googleai"
)

type GeminiProvider struct {
	llm llms.Model // Change to llms.Model interface
}

func (p GeminiProvider) New() Provider {
	llm, err := googleai.New(
		context.Background(),
		googleai.WithAPIKey(os.Getenv("GEMINI_API_KEY")),
	)
	if err != nil {
		log.Fatalf("Failed to create Gemini client: %v", err)
	}
	return GeminiProvider{llm: llm}
}

func (p GeminiProvider) Name() ProviderType {
	return ProviderGemini
}

func (p GeminiProvider) Summary(query string, n int) (string, error) {
	// For summary, we can use a more capable model if needed, or stick to flash
	llm, err := googleai.New(
		context.Background(),
		googleai.WithAPIKey(os.Getenv("GEMINI_API_KEY")),
	)
	if err != nil {
		return "", fmt.Errorf("failed to create Gemini client for summary: %v", err)
	}

	prompt := templates.SummaryPrompt(query, n) // Use templates.SummaryPrompt
	completion, err := llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get summary from Gemini: %v", err)
	}
	return completion, nil
}

func (p GeminiProvider) DockerImageName(task string) (string, error) {
	prompt := templates.DockerPrompt(task) // Use templates.DockerPrompt
	completion, err := p.llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get docker image name from Gemini: %v", err)
	}
	return completion, nil
}

func (p GeminiProvider) NextTask(args NextTaskOptions) *database.Task {
	var messages []llms.MessageContent

	// Use gemini-2.0-flash for next task generation as it's more recent and potentially better for agentic tasks
	llm, err := googleai.New(
		context.Background(),
		googleai.WithAPIKey(os.Getenv("GEMINI_API_KEY")),
	)
	if err != nil {
		log.Printf("Failed to create Gemini client for next task: %v", err)
		return defaultAskTask("Failed to initialize Gemini model for next task generation.")
	}

	prompt := templates.AgentPrompt(args.DockerImage) // Use templates.AgentPrompt
	messages = tasksToMessages(args.Tasks, prompt)

	options := []llms.CallOption{
		llms.WithTools(Tools),
		llms.WithStreamingFunc(func(ctx context.Context, chunk []byte) error {
			if StreamFunc != nil {
				StreamFunc(chunk)
			}
			return nil
		}),
	}

	content, err := llm.GenerateContent(context.Background(), messages, options...)
	if err != nil {
		log.Printf("Failed to generate content from Gemini, asking user: %v", err)
		return defaultAskTask("There was an error generating the next task.")
	}

	if len(content.Choices) == 0 {
		return defaultAskTask("No choices found for the next task.")
	}

	task, err := toolToTask(content.Choices)
	if err != nil {
		log.Printf("Failed to convert tool call to task, asking user: %v", err)
		return defaultAskTask(fmt.Sprintf("There was an error processing the tool call: %v", err))
	}

	return task
}
