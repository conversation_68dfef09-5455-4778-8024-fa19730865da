import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Icon } from "@/components/Icon/Icon";
import {
  Dropdown,
  dropdownMenuContentStyles,
  dropdownMenuItemStyles,
} from "@/components/Dropdown/Dropdown";
import { dropdownMenuItemIconStyles } from "@/components/Dropdown/Dropdown.css";

import { buttonStyles } from "./ModelSelector.css";

type ModelSelectorProps = {
  activeProvider: string;
  onProviderSwitch: (id: string) => void;
};

export const ModelSelector = ({
  activeProvider,
  onProviderSwitch
}: ModelSelectorProps) => {
  const providers = [
    { id: "gemini", name: "<PERSON>" },
    { id: "openai", name: "OpenA<PERSON>" },
    { id: "ollama", name: "Olla<PERSON>" },
  ];

  const dropdownContent = (
    <DropdownMenu.Content className={dropdownMenuContentStyles} sideOffset={5}>
      <DropdownMenu.RadioGroup
        value={activeProvider}
        onValueChange={onProviderSwitch}
      >
        {providers.map((provider) => (
          <DropdownMenu.RadioItem
            key={provider.id}
            className={dropdownMenuItemStyles}
            value={provider.id}
          >
            <DropdownMenu.ItemIndicator
              className={dropdownMenuItemIconStyles}
            >
              <Icon.Check />
            </DropdownMenu.ItemIndicator>
            {provider.name}
          </DropdownMenu.RadioItem>
        ))}
      </DropdownMenu.RadioGroup>
    </DropdownMenu.Content>
  );

  const activeProviderName = providers.find(p => p.id === activeProvider)?.name || "Gemini";

  return (
    <Dropdown content={dropdownContent}>
      <div className={buttonStyles}>{activeProviderName}</div>
    </Dropdown>
  );
};
