// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: flows.sql

package database

import (
	"context"
	"database/sql"
)

const createFlow = `-- name: CreateFlow :one
INSERT INTO flows (
  name, status, container_id, model, model_provider
)
VALUES (
  ?, ?, ?, ?, ?
)
RETURNING id, created_at, updated_at, name, status, container_id, model, model_provider
`

type CreateFlowParams struct {
	Name          sql.NullString
	Status        sql.NullString
	ContainerID   sql.NullInt64
	Model         sql.NullString
	ModelProvider sql.NullString
}

func (q *Queries) CreateFlow(ctx context.Context, arg CreateFlowParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, createFlow,
		arg.Name,
		arg.Status,
		arg.ContainerID,
		arg.Model,
		arg.ModelProvider,
	)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Name,
		&i.Status,
		&i.ContainerID,
		&i.Model,
		&i.<PERSON>,
	)
	return i, err
}

const readAllFlows = `-- name: ReadAllFlows :many
SELECT
  f.id, f.created_at, f.updated_at, f.name, f.status, f.container_id, f.model, f.model_provider,
  c.name AS container_name
FROM flows f
LEFT JOIN containers c ON f.container_id = c.id
ORDER BY f.created_at DESC
`

type ReadAllFlowsRow struct {
	ID            int64
	CreatedAt     sql.NullTime
	UpdatedAt     sql.NullTime
	Name          sql.NullString
	Status        sql.NullString
	ContainerID   sql.NullInt64
	Model         sql.NullString
	ModelProvider sql.NullString
	ContainerName sql.NullString
}

func (q *Queries) ReadAllFlows(ctx context.Context) ([]ReadAllFlowsRow, error) {
	rows, err := q.db.QueryContext(ctx, readAllFlows)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ReadAllFlowsRow
	for rows.Next() {
		var i ReadAllFlowsRow
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Name,
			&i.Status,
			&i.ContainerID,
			&i.Model,
			&i.ModelProvider,
			&i.ContainerName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const readFlow = `-- name: ReadFlow :one
SELECT
  f.id, f.created_at, f.updated_at, f.name, f.status, f.container_id, f.model, f.model_provider,
  c.name AS container_name,
  c.image AS container_image,
  c.status AS container_status,
  c.local_id AS container_local_id
FROM flows f
LEFT JOIN containers c ON f.container_id = c.id
WHERE f.id = ?
`

type ReadFlowRow struct {
	ID               int64
	CreatedAt        sql.NullTime
	UpdatedAt        sql.NullTime
	Name             sql.NullString
	Status           sql.NullString
	ContainerID      sql.NullInt64
	Model            sql.NullString
	ModelProvider    sql.NullString
	ContainerName    sql.NullString
	ContainerImage   sql.NullString
	ContainerStatus  sql.NullString
	ContainerLocalID sql.NullString
}

func (q *Queries) ReadFlow(ctx context.Context, id int64) (ReadFlowRow, error) {
	row := q.db.QueryRowContext(ctx, readFlow, id)
	var i ReadFlowRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Name,
		&i.Status,
		&i.ContainerID,
		&i.Model,
		&i.ModelProvider,
		&i.ContainerName,
		&i.ContainerImage,
		&i.ContainerStatus,
		&i.ContainerLocalID,
	)
	return i, err
}

const updateFlowContainer = `-- name: UpdateFlowContainer :one
UPDATE flows
SET container_id = ?
WHERE id = ?
RETURNING id, created_at, updated_at, name, status, container_id, model, model_provider
`

type UpdateFlowContainerParams struct {
	ContainerID sql.NullInt64
	ID          int64
}

func (q *Queries) UpdateFlowContainer(ctx context.Context, arg UpdateFlowContainerParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowContainer, arg.ContainerID, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Name,
		&i.Status,
		&i.ContainerID,
		&i.Model,
		&i.ModelProvider,
	)
	return i, err
}

const updateFlowName = `-- name: UpdateFlowName :one
UPDATE flows
SET name = ?
WHERE id = ?
RETURNING id, created_at, updated_at, name, status, container_id, model, model_provider
`

type UpdateFlowNameParams struct {
	Name sql.NullString
	ID   int64
}

func (q *Queries) UpdateFlowName(ctx context.Context, arg UpdateFlowNameParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowName, arg.Name, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Name,
		&i.Status,
		&i.ContainerID,
		&i.Model,
		&i.ModelProvider,
	)
	return i, err
}

const updateFlowStatus = `-- name: UpdateFlowStatus :one
UPDATE flows
SET status = ?
WHERE id = ?
RETURNING id, created_at, updated_at, name, status, container_id, model, model_provider
`

type UpdateFlowStatusParams struct {
	Status sql.NullString
	ID     int64
}

func (q *Queries) UpdateFlowStatus(ctx context.Context, arg UpdateFlowStatusParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowStatus, arg.Status, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Name,
		&i.Status,
		&i.ContainerID,
		&i.Model,
		&i.ModelProvider,
	)
	return i, err
}
