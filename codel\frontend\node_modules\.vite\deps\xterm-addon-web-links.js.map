{"version": 3, "sources": ["../../xterm-addon-web-links/lib/webpack:/WebLinksAddon/webpack/universalModuleDefinition", "../../xterm-addon-web-links/lib/webpack:/WebLinksAddon/src/WebLinkProvider.ts", "../../xterm-addon-web-links/lib/webpack:/WebLinksAddon/webpack/bootstrap", "../../xterm-addon-web-links/lib/webpack:/WebLinksAddon/src/WebLinksAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"WebLinksAddon\"] = factory();\n\telse\n\t\troot[\"WebLinksAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ILinkProvider, ILink, Terminal, IViewportRange, IBufferLine } from 'xterm';\n\nexport interface ILinkProviderOptions {\n  hover?(event: MouseEvent, text: string, location: IViewportRange): void;\n  leave?(event: MouseEvent, text: string): void;\n  urlRegex?: RegExp;\n}\n\nexport class WebLinkProvider implements ILinkProvider {\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _regex: RegExp,\n    private readonly _handler: (event: MouseEvent, uri: string) => void,\n    private readonly _options: ILinkProviderOptions = {}\n  ) {\n\n  }\n\n  public provideLinks(y: number, callback: (links: ILink[] | undefined) => void): void {\n    const links = LinkComputer.computeLink(y, this._regex, this._terminal, this._handler);\n    callback(this._addCallbacks(links));\n  }\n\n  private _addCallbacks(links: ILink[]): ILink[] {\n    return links.map(link => {\n      link.leave = this._options.leave;\n      link.hover = (event: MouseEvent, uri: string): void => {\n        if (this._options.hover) {\n          const { range } = link;\n          this._options.hover(event, uri, range);\n        }\n      };\n      return link;\n    });\n  }\n}\n\nexport class LinkComputer {\n  public static computeLink(y: number, regex: RegExp, terminal: Terminal, activate: (event: MouseEvent, uri: string) => void): ILink[] {\n    const rex = new RegExp(regex.source, (regex.flags || '') + 'g');\n\n    const [lines, startLineIndex] = LinkComputer._getWindowedLineStrings(y - 1, terminal);\n    const line = lines.join('');\n\n    let match;\n    const result: ILink[] = [];\n\n    while (match = rex.exec(line)) {\n      const text = match[0];\n\n      // check via URL if the matched text would form a proper url\n      // NOTE: This outsources the ugly url parsing to the browser.\n      // To avoid surprising auto expansion from URL we additionally\n      // check afterwards if the provided string resembles the parsed\n      // one close enough:\n      // - decodeURI  decode path segement back to byte repr\n      //              to detect unicode auto conversion correctly\n      // - append /   also match domain urls w'o any path notion\n      try {\n        const url = new URL(text);\n        const urlText = decodeURI(url.toString());\n        if (text !== urlText && text + '/' !== urlText) {\n          continue;\n        }\n      } catch (e) {\n        continue;\n      }\n\n      // map string positions back to buffer positions\n      // values are 0-based right side excluding\n      const [startY, startX] = LinkComputer._mapStrIdx(terminal, startLineIndex, 0, match.index);\n      const [endY, endX] = LinkComputer._mapStrIdx(terminal, startY, startX, text.length);\n\n      if (startY === -1 || startX === -1 || endY === -1 || endX === -1) {\n        continue;\n      }\n\n      // range expects values 1-based right side including, thus +1 except for endX\n      const range = {\n        start: {\n          x: startX + 1,\n          y: startY + 1\n        },\n        end: {\n          x: endX,\n          y: endY + 1\n        }\n      };\n\n      result.push({ range, text, activate });\n    }\n\n    return result;\n  }\n\n  /**\n   * Get wrapped content lines for the current line index.\n   * The top/bottom line expansion stops at whitespaces or length > 2048.\n   * Returns an array with line strings and the top line index.\n   *\n   * NOTE: We pull line strings with trimRight=true on purpose to make sure\n   * to correctly match urls with early wrapped wide chars. This corrupts the string index\n   * for 1:1 backmapping to buffer positions, thus needs an additional correction in _mapStrIdx.\n   */\n  private static _getWindowedLineStrings(lineIndex: number, terminal: Terminal): [string[], number] {\n    let line: IBufferLine | undefined;\n    let topIdx = lineIndex;\n    let bottomIdx = lineIndex;\n    let length = 0;\n    let content = '';\n    const lines: string[] = [];\n\n    if ((line = terminal.buffer.active.getLine(lineIndex))) {\n      const currentContent = line.translateToString(true);\n\n      // expand top, stop on whitespaces or length > 2048\n      if (line.isWrapped && currentContent[0] !== ' ') {\n        length = 0;\n        while ((line = terminal.buffer.active.getLine(--topIdx)) && length < 2048) {\n          content = line.translateToString(true);\n          length += content.length;\n          lines.push(content);\n          if (!line.isWrapped || content.indexOf(' ') !== -1) {\n            break;\n          }\n        }\n        lines.reverse();\n      }\n\n      // append current line\n      lines.push(currentContent);\n\n      // expand bottom, stop on whitespaces or length > 2048\n      length = 0;\n      while ((line = terminal.buffer.active.getLine(++bottomIdx)) && line.isWrapped && length < 2048) {\n        content = line.translateToString(true);\n        length += content.length;\n        lines.push(content);\n        if (content.indexOf(' ') !== -1) {\n          break;\n        }\n      }\n    }\n    return [lines, topIdx];\n  }\n\n  /**\n   * Map a string index back to buffer positions.\n   * Returns buffer position as [lineIndex, columnIndex] 0-based,\n   * or [-1, -1] in case the lookup ran into a non-existing line.\n   */\n  private static _mapStrIdx(terminal: Terminal, lineIndex: number, rowIndex: number, stringIndex: number): [number, number] {\n    const buf = terminal.buffer.active;\n    const cell = buf.getNullCell();\n    let start = rowIndex;\n    while (stringIndex) {\n      const line = buf.getLine(lineIndex);\n      if (!line) {\n        return [-1, -1];\n      }\n      for (let i = start; i < line.length; ++i) {\n        line.getCell(i, cell);\n        const chars = cell.getChars();\n        const width = cell.getWidth();\n        if (width) {\n          stringIndex -= chars.length || 1;\n\n          // correct stringIndex for early wrapped wide chars:\n          // - currently only happens at last cell\n          // - cells to the right are reset with chars='' and width=1 in InputHandler.print\n          // - follow-up line must be wrapped and contain wide char at first cell\n          // --> if all these conditions are met, correct stringIndex by +1\n          if (i === line.length - 1 && chars === '') {\n            const line = buf.getLine(lineIndex + 1);\n            if (line && line.isWrapped) {\n              line.getCell(0, cell);\n              if (cell.getWidth() === 2) {\n                stringIndex += 1;\n              }\n            }\n          }\n        }\n        if (stringIndex < 0) {\n          return [lineIndex, i];\n        }\n      }\n      lineIndex++;\n      start = 0;\n    }\n    return [lineIndex, start];\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Terminal, ITerminalAddon, IDisposable } from 'xterm';\nimport { ILinkProviderOptions, WebLinkProvider } from './WebLinkProvider';\n\n// consider everthing starting with http:// or https://\n// up to first whitespace, `\"` or `'` as url\n// NOTE: The repeated end clause is needed to not match a dangling `:`\n// resembling the old (...)*([^:\"\\'\\\\s]) final path clause\n// additionally exclude early + final:\n// - unsafe from rfc3986: !*'()\n// - unsafe chars from rfc1738: {}|\\^~[]` (minus [] as we need them for ipv6 adresses, also allow ~)\n// also exclude as finals:\n// - final interpunction like ,.!?\n// - any sort of brackets <>()[]{} (not spec conform, but often used to enclose urls)\n// - unsafe chars from rfc1738: {}|\\^~[]`\nconst strictUrlRegex = /https?:[/]{2}[^\\s\"'!*(){}|\\\\\\^<>`]*[^\\s\"':,.!?{}|\\\\\\^~\\[\\]`()<>]/;\n\n\nfunction handleLink(event: MouseEvent, uri: string): void {\n  const newWindow = window.open();\n  if (newWindow) {\n    try {\n      newWindow.opener = null;\n    } catch {\n      // no-op, Electron can throw\n    }\n    newWindow.location.href = uri;\n  } else {\n    console.warn('Opening link blocked as opener could not be cleared');\n  }\n}\n\nexport class WebLinksAddon implements ITerminalAddon {\n  private _terminal: Terminal | undefined;\n  private _linkProvider: IDisposable | undefined;\n\n  constructor(\n    private _handler: (event: MouseEvent, uri: string) => void = handleLink,\n    private _options: ILinkProviderOptions = {}\n  ) {\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    const options = this._options as ILinkProviderOptions;\n    const regex = options.urlRegex || strictUrlRegex;\n    this._linkProvider = this._terminal.registerLinkProvider(new WebLinkProvider(this._terminal, regex, this._handler, options));\n  }\n\n  public dispose(): void {\n    this._linkProvider?.dispose();\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAuB,gBAAID,EAAAA,IAE3BD,EAAoB,gBAAIC,EAAAA;IACzB,EAAEK,MAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,EAAA,GAAA,CAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,eAAAA,GAAA,kBAAA,QCITA,GAAA,kBAAA,MAAA;UAEE,YACmBC,IACAC,IACAC,IACAC,KAAiC,CAAC,GAAA;AAHlC,iBAAAH,YAAAA,IACA,KAAAC,SAAAA,IACA,KAAAC,WAAAA,IACA,KAAAC,WAAAA;UAGnB;UAEO,aAAaC,IAAWC,IAAAA;AAC7B,kBAAMC,KAAQC,GAAaC,YAAYJ,IAAGK,KAAKR,QAAQQ,KAAKT,WAAWS,KAAKP,QAAAA;AAC5EG,YAAAA,GAASI,KAAKC,cAAcJ,EAAAA,CAAAA;UAC9B;UAEQ,cAAcA,IAAAA;AACpB,mBAAOA,GAAMK,IAAIC,CAAAA,QACfA,GAAKC,QAAQJ,KAAKN,SAASU,OAC3BD,GAAKE,QAAQ,CAACC,IAAmBC,OAAAA;AAC/B,kBAAIP,KAAKN,SAASW,OAAO;AACvB,sBAAA,EAAM,OAAEG,GAAAA,IAAUL;AAClBH,qBAAKN,SAASW,MAAMC,IAAOC,IAAKC,EAAAA;cAAAA;YAAAA,GAG7BL,GAAAA;UAEX;QAAA;QAGF,MAAaL,GAAAA;UACJ,OAAA,YAAmBH,IAAWc,IAAeC,IAAoBC,GAAAA;AACtE,kBAAMC,IAAM,IAAIC,OAAOJ,GAAMK,SAASL,GAAMM,SAAS,MAAM,GAAA,GAAA,CAEpDC,GAAOC,CAAAA,IAAkBnB,GAAaoB,wBAAwBvB,KAAI,GAAGe,EAAAA,GACtES,IAAOH,EAAMI,KAAK,EAAA;AAExB,gBAAIC;AACJ,kBAAMC,IAAkB,CAAA;AAExB,mBAAOD,IAAQT,EAAIW,KAAKJ,CAAAA,KAAO;AAC7B,oBAAMK,KAAOH,EAAM,CAAA;AAUnB,kBAAA;AACE,sBAAMI,KAAM,IAAIC,IAAIF,EAAAA,GACdG,KAAUC,UAAUH,GAAII,SAAAA,CAAAA;AAC9B,oBAAIL,OAASG,MAAWH,KAAO,QAAQG;AACrC;cAAA,SAEKtC,IAAAA;AACP;cAAA;AAKF,oBAAA,CAAOyC,IAAQC,EAAAA,IAAUjC,GAAakC,WAAWtB,IAAUO,GAAgB,GAAGI,EAAMY,KAAAA,GAAAA,CAC7EC,IAAMC,EAAAA,IAAQrC,GAAakC,WAAWtB,IAAUoB,IAAQC,IAAQP,GAAKY,MAAAA;AAE5E,kBAAA,OAAIN,MAAAA,OAAiBC,MAAAA,OAAiBG,MAAAA,OAAeC;AACnD;AAIF,oBAAM3B,IAAQ,EACZ6B,OAAO,EACLC,GAAGP,KAAS,GACZpC,GAAGmC,KAAS,EAAA,GAEdS,KAAK,EACHD,GAAGH,IACHxC,GAAGuC,KAAO,EAAA,EAAA;AAIdZ,gBAAOkB,KAAK,EAAEhC,OAAAA,GAAOgB,MAAAA,IAAMb,UAAAA,EAAAA,CAAAA;YAAAA;AAG7B,mBAAOW;UACT;UAWQ,OAAA,wBAA+BmB,IAAmB/B,IAAAA;AACxD,gBAAIS,IACAuB,KAASD,IACTE,IAAYF,IACZL,IAAS,GACTQ,IAAU;AACd,kBAAM5B,IAAkB,CAAA;AAExB,gBAAKG,KAAOT,GAASmC,OAAOC,OAAOC,QAAQN,EAAAA,GAAa;AACtD,oBAAMO,KAAiB7B,GAAK8B,kBAAAA,IAAkB;AAG9C,kBAAI9B,GAAK+B,aAAmC,QAAtBF,GAAe,CAAA,GAAY;AAE/C,qBADAZ,IAAS,IACDjB,KAAOT,GAASmC,OAAOC,OAAOC,QAAAA,EAAUL,EAAAA,MAAYN,IAAS,SACnEQ,IAAUzB,GAAK8B,kBAAAA,IAAkB,GACjCb,KAAUQ,EAAQR,QAClBpB,EAAMwB,KAAKI,CAAAA,GACNzB,GAAK+B,aAAAA,OAAaN,EAAQO,QAAQ,GAAA;AAAA;AAIzCnC,kBAAMoC,QAAAA;cAAAA;AAQR,mBAJApC,EAAMwB,KAAKQ,EAAAA,GAGXZ,IAAS,IACDjB,KAAOT,GAASmC,OAAOC,OAAOC,QAAAA,EAAUJ,CAAAA,MAAexB,GAAK+B,aAAad,IAAS,SACxFQ,IAAUzB,GAAK8B,kBAAAA,IAAkB,GACjCb,KAAUQ,EAAQR,QAClBpB,EAAMwB,KAAKI,CAAAA,GAAAA,OACPA,EAAQO,QAAQ,GAAA;AAAA;YAAA;AAKxB,mBAAO,CAACnC,GAAO0B,EAAAA;UACjB;UAOQ,OAAA,WAAkBhC,IAAoB+B,IAAmBY,IAAkBC,IAAAA;AACjF,kBAAMC,IAAM7C,GAASmC,OAAOC,QACtBU,IAAOD,EAAIE,YAAAA;AACjB,gBAAIpB,IAAQgB;AACZ,mBAAOC,MAAa;AAClB,oBAAMnC,KAAOoC,EAAIR,QAAQN,EAAAA;AACzB,kBAAA,CAAKtB;AACH,uBAAO,CAAA,IAAE,EAAI;AAEf,uBAASuC,KAAIrB,GAAOqB,KAAIvC,GAAKiB,QAAAA,EAAUsB,IAAG;AACxCvC,gBAAAA,GAAKwC,QAAQD,IAAGF,CAAAA;AAChB,sBAAMI,KAAQJ,EAAKK,SAAAA;AAEnB,oBADcL,EAAKM,SAAAA,MAEjBR,MAAeM,GAAMxB,UAAU,GAO3BsB,OAAMvC,GAAKiB,SAAS,KAAe,OAAVwB,KAAc;AACzC,wBAAMzC,KAAOoC,EAAIR,QAAQN,KAAY,CAAA;AACjCtB,kBAAAA,MAAQA,GAAK+B,cACf/B,GAAKwC,QAAQ,GAAGH,CAAAA,GACQ,MAApBA,EAAKM,SAAAA,MACPR,MAAe;gBAAA;AAKvB,oBAAIA,KAAc;AAChB,yBAAO,CAACb,IAAWiB,EAAAA;cAAAA;AAGvBjB,cAAAA,MACAJ,IAAQ;YAAA;AAEV,mBAAO,CAACI,IAAWJ,CAAAA;UACrB;QAAA;AAzJF,QAAA/C,GAAA,eAAAyE;MAAA,EAAA,GC1CIC,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC;AACH,iBAAOA,EAAanF;AAGrB,YAAIC,IAAS+E,EAAyBE,EAAAA,IAAY,EAGjDlF,SAAS,CAAC,EAAA;AAOX,eAHAoF,EAAoBF,EAAAA,EAAUjF,GAAQA,EAAOD,SAASiF,CAAAA,GAG/ChF,EAAOD;MACf;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAAK,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAA;AChBA,cAAAC,KAAA,EAAA,CAAA,GAaM+E,IAAiB;AAGvB,iBAASC,EAAWhE,IAAmBC,IAAAA;AACrC,gBAAMgE,KAAYC,OAAOC,KAAAA;AACzB,cAAIF,IAAW;AACb,gBAAA;AACEA,cAAAA,GAAUG,SAAS;YAAA,SACnBrF,IAAA;YAAA;AAGFkF,YAAAA,GAAUI,SAASC,OAAOrE;UAAAA;AAE1BsE,oBAAQC,KAAK,qDAAA;QAEjB;AAEA,QAAAzF,GAAA,gBAAA,MAAA;UAIE,YACUI,KAAqD6E,GACrD5E,KAAiC,CAAC,GAAA;AADlC,iBAAAD,WAAAA,IACA,KAAAC,WAAAA;UAEV;UAEO,SAASgB,IAAAA;AACdV,iBAAKT,YAAYmB;AACjB,kBAAMqE,KAAU/E,KAAKN,UACfe,KAAQsE,GAAQC,YAAYX;AAClCrE,iBAAKiF,gBAAgBjF,KAAKT,UAAU2F,qBAAqB,IAAI5F,GAAA6F,gBAAgBnF,KAAKT,WAAWkB,IAAOT,KAAKP,UAAUsF,EAAAA,CAAAA;UACrH;UAEO,UAAAK;AAAAA,gBAAAA;AACa,sBAAlB/F,KAAAW,KAAKiF,kBAAAA,WAAa5F,MAAAA,GAAE+F,QAAAA;UACtB;QAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "e", "t", "_terminal", "_regex", "_handler", "_options", "y", "callback", "links", "LinkComputer", "computeLink", "this", "_addCallbacks", "map", "link", "leave", "hover", "event", "uri", "range", "regex", "terminal", "activate", "rex", "RegExp", "source", "flags", "lines", "startLineIndex", "_getWindowedLineStrings", "line", "join", "match", "result", "exec", "text", "url", "URL", "urlText", "decodeURI", "toString", "startY", "startX", "_mapStrIdx", "index", "endY", "endX", "length", "start", "x", "end", "push", "lineIndex", "topIdx", "bottomIdx", "content", "buffer", "active", "getLine", "currentC<PERSON>nt", "translateToString", "isWrapped", "indexOf", "reverse", "rowIndex", "stringIndex", "buf", "cell", "getNullCell", "i", "getCell", "chars", "getChars", "getWidth", "n", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "strictUrlRegex", "handleLink", "newWindow", "window", "open", "opener", "location", "href", "console", "warn", "options", "urlRegex", "_linkProvider", "registerLinkProvider", "WebLinkProvider", "dispose"]}