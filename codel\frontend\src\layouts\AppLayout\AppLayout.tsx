import { Outlet } from "react-router-dom";
import { useState } from "react";

import { Sidebar } from "@/components/Sidebar/Sidebar";

import { wrapperStyles } from "./AppLayout.css";

export const AppLayout = () => {
  const [provider, setProvider] = useState(
    localStorage.getItem('activeProvider') || 'gemini'
  );
  
  const handleSwitch = (id: string) => {
    localStorage.setItem('activeProvider', id);
    fetch('/api/provider-switch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ provider: id })
    });
    setProvider(id);
  };

  const sidebarItems = [
    { id: "1", title: "Flow 1", done: false },
    { id: "2", title: "Flow 2", done: true },
  ];
  const availableModelsData = [
    { id: "gemini", name: "<PERSON>" },
    { id: "openai", name: "OpenAI" },
  ];

  return (
    <div className={wrapperStyles}>
      <Sidebar
        items={sidebarItems}
        availableModels={availableModelsData ?? []}
        activeProvider={provider}
        onProviderSwitch={handleSwitch}
      />
      <Outlet />
    </div>
  );
};
