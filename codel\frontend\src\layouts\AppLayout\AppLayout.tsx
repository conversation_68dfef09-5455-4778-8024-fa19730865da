import { Outlet } from "react-router-dom";
import { useState } from "react";

import { Sidebar } from "@/components/Sidebar/Sidebar";

import { wrapperStyles } from "./AppLayout.css";

export const AppLayout = () => {
  const [provider, setProvider] = useState(
    localStorage.getItem('activeProvider') || 'gemini'
  );
  
  const handleSwitch = (id: string) => {
    localStorage.setItem('activeProvider', id);
    fetch('/api/switch-provider', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ provider: id })
    }).then(response => {
      if (response.ok) {
        setProvider(id);
      } else {
        console.error('Failed to switch provider');
      }
    }).catch(error => {
      console.error('Error switching provider:', error);
    });
  };

  const sidebarItems = [
    { id: "1", title: "Flow 1", done: false },
    { id: "2", title: "Flow 2", done: true },
  ];
  const availableModelsData = [
    { id: "gemini-2.0-flash-exp", provider: "gemini" },
    { id: "gpt-4-0125-preview", provider: "openai" },
    { id: "mistral-large-latest", provider: "mistral" },
    { id: "deepseek-chat", provider: "deepseek" },
  ];

  return (
    <div className={wrapperStyles}>
      <Sidebar
        items={sidebarItems}
        availableModels={availableModelsData ?? []}
        activeProvider={provider}
        onProviderSwitch={handleSwitch}
      />
      <Outlet />
    </div>
  );
};
