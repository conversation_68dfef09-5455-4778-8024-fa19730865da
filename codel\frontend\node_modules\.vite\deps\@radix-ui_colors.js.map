{"version": 3, "sources": ["../../@radix-ui/colors/index.mjs"], "sourcesContent": ["const grayDark = {\n    gray1: \"#111111\",\n    gray2: \"#191919\",\n    gray3: \"#222222\",\n    gray4: \"#2a2a2a\",\n    gray5: \"#313131\",\n    gray6: \"#3a3a3a\",\n    gray7: \"#484848\",\n    gray8: \"#606060\",\n    gray9: \"#6e6e6e\",\n    gray10: \"#7b7b7b\",\n    gray11: \"#b4b4b4\",\n    gray12: \"#eeeeee\",\n};\nconst grayDarkA = {\n    grayA1: \"#00000000\",\n    grayA2: \"#ffffff09\",\n    grayA3: \"#ffffff12\",\n    grayA4: \"#ffffff1b\",\n    grayA5: \"#ffffff22\",\n    grayA6: \"#ffffff2c\",\n    grayA7: \"#ffffff3b\",\n    grayA8: \"#ffffff55\",\n    grayA9: \"#ffffff64\",\n    grayA10: \"#ffffff72\",\n    grayA11: \"#ffffffaf\",\n    grayA12: \"#ffffffed\",\n};\nconst grayDarkP3 = {\n    gray1: \"color(display-p3 0.067 0.067 0.067)\",\n    gray2: \"color(display-p3 0.098 0.098 0.098)\",\n    gray3: \"color(display-p3 0.135 0.135 0.135)\",\n    gray4: \"color(display-p3 0.163 0.163 0.163)\",\n    gray5: \"color(display-p3 0.192 0.192 0.192)\",\n    gray6: \"color(display-p3 0.228 0.228 0.228)\",\n    gray7: \"color(display-p3 0.283 0.283 0.283)\",\n    gray8: \"color(display-p3 0.375 0.375 0.375)\",\n    gray9: \"color(display-p3 0.431 0.431 0.431)\",\n    gray10: \"color(display-p3 0.484 0.484 0.484)\",\n    gray11: \"color(display-p3 0.706 0.706 0.706)\",\n    gray12: \"color(display-p3 0.933 0.933 0.933)\",\n};\nconst grayDarkP3A = {\n    grayA1: \"color(display-p3 0 0 0 / 0)\",\n    grayA2: \"color(display-p3 1 1 1 / 0.034)\",\n    grayA3: \"color(display-p3 1 1 1 / 0.071)\",\n    grayA4: \"color(display-p3 1 1 1 / 0.105)\",\n    grayA5: \"color(display-p3 1 1 1 / 0.134)\",\n    grayA6: \"color(display-p3 1 1 1 / 0.172)\",\n    grayA7: \"color(display-p3 1 1 1 / 0.231)\",\n    grayA8: \"color(display-p3 1 1 1 / 0.332)\",\n    grayA9: \"color(display-p3 1 1 1 / 0.391)\",\n    grayA10: \"color(display-p3 1 1 1 / 0.445)\",\n    grayA11: \"color(display-p3 1 1 1 / 0.685)\",\n    grayA12: \"color(display-p3 1 1 1 / 0.929)\",\n};\nconst mauveDark = {\n    mauve1: \"#121113\",\n    mauve2: \"#1a191b\",\n    mauve3: \"#232225\",\n    mauve4: \"#2b292d\",\n    mauve5: \"#323035\",\n    mauve6: \"#3c393f\",\n    mauve7: \"#49474e\",\n    mauve8: \"#625f69\",\n    mauve9: \"#6f6d78\",\n    mauve10: \"#7c7a85\",\n    mauve11: \"#b5b2bc\",\n    mauve12: \"#eeeef0\",\n};\nconst mauveDarkA = {\n    mauveA1: \"#00000000\",\n    mauveA2: \"#f5f4f609\",\n    mauveA3: \"#ebeaf814\",\n    mauveA4: \"#eee5f81d\",\n    mauveA5: \"#efe6fe25\",\n    mauveA6: \"#f1e6fd30\",\n    mauveA7: \"#eee9ff40\",\n    mauveA8: \"#eee7ff5d\",\n    mauveA9: \"#eae6fd6e\",\n    mauveA10: \"#ece9fd7c\",\n    mauveA11: \"#f5f1ffb7\",\n    mauveA12: \"#fdfdffef\",\n};\nconst mauveDarkP3 = {\n    mauve1: \"color(display-p3 0.07 0.067 0.074)\",\n    mauve2: \"color(display-p3 0.101 0.098 0.105)\",\n    mauve3: \"color(display-p3 0.138 0.134 0.144)\",\n    mauve4: \"color(display-p3 0.167 0.161 0.175)\",\n    mauve5: \"color(display-p3 0.196 0.189 0.206)\",\n    mauve6: \"color(display-p3 0.232 0.225 0.245)\",\n    mauve7: \"color(display-p3 0.286 0.277 0.302)\",\n    mauve8: \"color(display-p3 0.383 0.373 0.408)\",\n    mauve9: \"color(display-p3 0.434 0.428 0.467)\",\n    mauve10: \"color(display-p3 0.487 0.48 0.519)\",\n    mauve11: \"color(display-p3 0.707 0.7 0.735)\",\n    mauve12: \"color(display-p3 0.933 0.933 0.94)\",\n};\nconst mauveDarkP3A = {\n    mauveA1: \"color(display-p3 0 0 0 / 0)\",\n    mauveA2: \"color(display-p3 0.996 0.992 1 / 0.034)\",\n    mauveA3: \"color(display-p3 0.937 0.933 0.992 / 0.077)\",\n    mauveA4: \"color(display-p3 0.957 0.918 0.996 / 0.111)\",\n    mauveA5: \"color(display-p3 0.937 0.906 0.996 / 0.145)\",\n    mauveA6: \"color(display-p3 0.953 0.925 0.996 / 0.183)\",\n    mauveA7: \"color(display-p3 0.945 0.929 1 / 0.246)\",\n    mauveA8: \"color(display-p3 0.937 0.918 1 / 0.361)\",\n    mauveA9: \"color(display-p3 0.933 0.918 1 / 0.424)\",\n    mauveA10: \"color(display-p3 0.941 0.925 1 / 0.479)\",\n    mauveA11: \"color(display-p3 0.965 0.961 1 / 0.712)\",\n    mauveA12: \"color(display-p3 0.992 0.992 1 / 0.937)\",\n};\nconst slateDark = {\n    slate1: \"#111113\",\n    slate2: \"#18191b\",\n    slate3: \"#212225\",\n    slate4: \"#272a2d\",\n    slate5: \"#2e3135\",\n    slate6: \"#363a3f\",\n    slate7: \"#43484e\",\n    slate8: \"#5a6169\",\n    slate9: \"#696e77\",\n    slate10: \"#777b84\",\n    slate11: \"#b0b4ba\",\n    slate12: \"#edeef0\",\n};\nconst slateDarkA = {\n    slateA1: \"#00000000\",\n    slateA2: \"#d8f4f609\",\n    slateA3: \"#ddeaf814\",\n    slateA4: \"#d3edf81d\",\n    slateA5: \"#d9edfe25\",\n    slateA6: \"#d6ebfd30\",\n    slateA7: \"#d9edff40\",\n    slateA8: \"#d9edff5d\",\n    slateA9: \"#dfebfd6d\",\n    slateA10: \"#e5edfd7b\",\n    slateA11: \"#f1f7feb5\",\n    slateA12: \"#fcfdffef\",\n};\nconst slateDarkP3 = {\n    slate1: \"color(display-p3 0.067 0.067 0.074)\",\n    slate2: \"color(display-p3 0.095 0.098 0.105)\",\n    slate3: \"color(display-p3 0.13 0.135 0.145)\",\n    slate4: \"color(display-p3 0.156 0.163 0.176)\",\n    slate5: \"color(display-p3 0.183 0.191 0.206)\",\n    slate6: \"color(display-p3 0.215 0.226 0.244)\",\n    slate7: \"color(display-p3 0.265 0.28 0.302)\",\n    slate8: \"color(display-p3 0.357 0.381 0.409)\",\n    slate9: \"color(display-p3 0.415 0.431 0.463)\",\n    slate10: \"color(display-p3 0.469 0.483 0.514)\",\n    slate11: \"color(display-p3 0.692 0.704 0.728)\",\n    slate12: \"color(display-p3 0.93 0.933 0.94)\",\n};\nconst slateDarkP3A = {\n    slateA1: \"color(display-p3 0 0 0 / 0)\",\n    slateA2: \"color(display-p3 0.875 0.992 1 / 0.034)\",\n    slateA3: \"color(display-p3 0.882 0.933 0.992 / 0.077)\",\n    slateA4: \"color(display-p3 0.882 0.953 0.996 / 0.111)\",\n    slateA5: \"color(display-p3 0.878 0.929 0.996 / 0.145)\",\n    slateA6: \"color(display-p3 0.882 0.949 0.996 / 0.183)\",\n    slateA7: \"color(display-p3 0.882 0.929 1 / 0.246)\",\n    slateA8: \"color(display-p3 0.871 0.937 1 / 0.361)\",\n    slateA9: \"color(display-p3 0.898 0.937 1 / 0.42)\",\n    slateA10: \"color(display-p3 0.918 0.945 1 / 0.475)\",\n    slateA11: \"color(display-p3 0.949 0.969 0.996 / 0.708)\",\n    slateA12: \"color(display-p3 0.988 0.992 1 / 0.937)\",\n};\nconst sageDark = {\n    sage1: \"#101211\",\n    sage2: \"#171918\",\n    sage3: \"#202221\",\n    sage4: \"#272a29\",\n    sage5: \"#2e3130\",\n    sage6: \"#373b39\",\n    sage7: \"#444947\",\n    sage8: \"#5b625f\",\n    sage9: \"#63706b\",\n    sage10: \"#717d79\",\n    sage11: \"#adb5b2\",\n    sage12: \"#eceeed\",\n};\nconst sageDarkA = {\n    sageA1: \"#00000000\",\n    sageA2: \"#f0f2f108\",\n    sageA3: \"#f3f5f412\",\n    sageA4: \"#f2fefd1a\",\n    sageA5: \"#f1fbfa22\",\n    sageA6: \"#edfbf42d\",\n    sageA7: \"#edfcf73c\",\n    sageA8: \"#ebfdf657\",\n    sageA9: \"#dffdf266\",\n    sageA10: \"#e5fdf674\",\n    sageA11: \"#f4fefbb0\",\n    sageA12: \"#fdfffeed\",\n};\nconst sageDarkP3 = {\n    sage1: \"color(display-p3 0.064 0.07 0.067)\",\n    sage2: \"color(display-p3 0.092 0.098 0.094)\",\n    sage3: \"color(display-p3 0.128 0.135 0.131)\",\n    sage4: \"color(display-p3 0.155 0.164 0.159)\",\n    sage5: \"color(display-p3 0.183 0.193 0.188)\",\n    sage6: \"color(display-p3 0.218 0.23 0.224)\",\n    sage7: \"color(display-p3 0.269 0.285 0.277)\",\n    sage8: \"color(display-p3 0.362 0.382 0.373)\",\n    sage9: \"color(display-p3 0.398 0.438 0.421)\",\n    sage10: \"color(display-p3 0.453 0.49 0.474)\",\n    sage11: \"color(display-p3 0.685 0.709 0.697)\",\n    sage12: \"color(display-p3 0.927 0.933 0.93)\",\n};\nconst sageDarkP3A = {\n    sageA1: \"color(display-p3 0 0 0 / 0)\",\n    sageA2: \"color(display-p3 0.976 0.988 0.984 / 0.03)\",\n    sageA3: \"color(display-p3 0.992 0.945 0.941 / 0.072)\",\n    sageA4: \"color(display-p3 0.988 0.996 0.992 / 0.102)\",\n    sageA5: \"color(display-p3 0.992 1 0.996 / 0.131)\",\n    sageA6: \"color(display-p3 0.973 1 0.976 / 0.173)\",\n    sageA7: \"color(display-p3 0.957 1 0.976 / 0.233)\",\n    sageA8: \"color(display-p3 0.957 1 0.984 / 0.334)\",\n    sageA9: \"color(display-p3 0.902 1 0.957 / 0.397)\",\n    sageA10: \"color(display-p3 0.929 1 0.973 / 0.452)\",\n    sageA11: \"color(display-p3 0.969 1 0.988 / 0.688)\",\n    sageA12: \"color(display-p3 0.992 1 0.996 / 0.929)\",\n};\nconst oliveDark = {\n    olive1: \"#111210\",\n    olive2: \"#181917\",\n    olive3: \"#212220\",\n    olive4: \"#282a27\",\n    olive5: \"#2f312e\",\n    olive6: \"#383a36\",\n    olive7: \"#454843\",\n    olive8: \"#5c625b\",\n    olive9: \"#687066\",\n    olive10: \"#767d74\",\n    olive11: \"#afb5ad\",\n    olive12: \"#eceeec\",\n};\nconst oliveDarkA = {\n    oliveA1: \"#00000000\",\n    oliveA2: \"#f1f2f008\",\n    oliveA3: \"#f4f5f312\",\n    oliveA4: \"#f3fef21a\",\n    oliveA5: \"#f2fbf122\",\n    oliveA6: \"#f4faed2c\",\n    oliveA7: \"#f2fced3b\",\n    oliveA8: \"#edfdeb57\",\n    oliveA9: \"#ebfde766\",\n    oliveA10: \"#f0fdec74\",\n    oliveA11: \"#f6fef4b0\",\n    oliveA12: \"#fdfffded\",\n};\nconst oliveDarkP3 = {\n    olive1: \"color(display-p3 0.067 0.07 0.063)\",\n    olive2: \"color(display-p3 0.095 0.098 0.091)\",\n    olive3: \"color(display-p3 0.131 0.135 0.126)\",\n    olive4: \"color(display-p3 0.158 0.163 0.153)\",\n    olive5: \"color(display-p3 0.186 0.192 0.18)\",\n    olive6: \"color(display-p3 0.221 0.229 0.215)\",\n    olive7: \"color(display-p3 0.273 0.284 0.266)\",\n    olive8: \"color(display-p3 0.365 0.382 0.359)\",\n    olive9: \"color(display-p3 0.414 0.438 0.404)\",\n    olive10: \"color(display-p3 0.467 0.49 0.458)\",\n    olive11: \"color(display-p3 0.69 0.709 0.682)\",\n    olive12: \"color(display-p3 0.927 0.933 0.926)\",\n};\nconst oliveDarkP3A = {\n    oliveA1: \"color(display-p3 0 0 0 / 0)\",\n    oliveA2: \"color(display-p3 0.984 0.988 0.976 / 0.03)\",\n    oliveA3: \"color(display-p3 0.992 0.996 0.988 / 0.068)\",\n    oliveA4: \"color(display-p3 0.953 0.996 0.949 / 0.102)\",\n    oliveA5: \"color(display-p3 0.969 1 0.965 / 0.131)\",\n    oliveA6: \"color(display-p3 0.973 1 0.969 / 0.169)\",\n    oliveA7: \"color(display-p3 0.98 1 0.961 / 0.228)\",\n    oliveA8: \"color(display-p3 0.961 1 0.957 / 0.334)\",\n    oliveA9: \"color(display-p3 0.949 1 0.922 / 0.397)\",\n    oliveA10: \"color(display-p3 0.953 1 0.941 / 0.452)\",\n    oliveA11: \"color(display-p3 0.976 1 0.965 / 0.688)\",\n    oliveA12: \"color(display-p3 0.992 1 0.992 / 0.929)\",\n};\nconst sandDark = {\n    sand1: \"#111110\",\n    sand2: \"#191918\",\n    sand3: \"#222221\",\n    sand4: \"#2a2a28\",\n    sand5: \"#31312e\",\n    sand6: \"#3b3a37\",\n    sand7: \"#494844\",\n    sand8: \"#62605b\",\n    sand9: \"#6f6d66\",\n    sand10: \"#7c7b74\",\n    sand11: \"#b5b3ad\",\n    sand12: \"#eeeeec\",\n};\nconst sandDarkA = {\n    sandA1: \"#00000000\",\n    sandA2: \"#f4f4f309\",\n    sandA3: \"#f6f6f513\",\n    sandA4: \"#fefef31b\",\n    sandA5: \"#fbfbeb23\",\n    sandA6: \"#fffaed2d\",\n    sandA7: \"#fffbed3c\",\n    sandA8: \"#fff9eb57\",\n    sandA9: \"#fffae965\",\n    sandA10: \"#fffdee73\",\n    sandA11: \"#fffcf4b0\",\n    sandA12: \"#fffffded\",\n};\nconst sandDarkP3 = {\n    sand1: \"color(display-p3 0.067 0.067 0.063)\",\n    sand2: \"color(display-p3 0.098 0.098 0.094)\",\n    sand3: \"color(display-p3 0.135 0.135 0.129)\",\n    sand4: \"color(display-p3 0.164 0.163 0.156)\",\n    sand5: \"color(display-p3 0.193 0.192 0.183)\",\n    sand6: \"color(display-p3 0.23 0.229 0.217)\",\n    sand7: \"color(display-p3 0.285 0.282 0.267)\",\n    sand8: \"color(display-p3 0.384 0.378 0.357)\",\n    sand9: \"color(display-p3 0.434 0.428 0.403)\",\n    sand10: \"color(display-p3 0.487 0.481 0.456)\",\n    sand11: \"color(display-p3 0.707 0.703 0.68)\",\n    sand12: \"color(display-p3 0.933 0.933 0.926)\",\n};\nconst sandDarkP3A = {\n    sandA1: \"color(display-p3 0 0 0 / 0)\",\n    sandA2: \"color(display-p3 0.992 0.992 0.988 / 0.034)\",\n    sandA3: \"color(display-p3 0.996 0.996 0.992 / 0.072)\",\n    sandA4: \"color(display-p3 0.992 0.992 0.953 / 0.106)\",\n    sandA5: \"color(display-p3 1 1 0.965 / 0.135)\",\n    sandA6: \"color(display-p3 1 0.976 0.929 / 0.177)\",\n    sandA7: \"color(display-p3 1 0.984 0.929 / 0.236)\",\n    sandA8: \"color(display-p3 1 0.976 0.925 / 0.341)\",\n    sandA9: \"color(display-p3 1 0.98 0.925 / 0.395)\",\n    sandA10: \"color(display-p3 1 0.992 0.933 / 0.45)\",\n    sandA11: \"color(display-p3 1 0.996 0.961 / 0.685)\",\n    sandA12: \"color(display-p3 1 1 0.992 / 0.929)\",\n};\nconst tomatoDark = {\n    tomato1: \"#181111\",\n    tomato2: \"#1f1513\",\n    tomato3: \"#391714\",\n    tomato4: \"#4e1511\",\n    tomato5: \"#5e1c16\",\n    tomato6: \"#6e2920\",\n    tomato7: \"#853a2d\",\n    tomato8: \"#ac4d39\",\n    tomato9: \"#e54d2e\",\n    tomato10: \"#ec6142\",\n    tomato11: \"#ff977d\",\n    tomato12: \"#fbd3cb\",\n};\nconst tomatoDarkA = {\n    tomatoA1: \"#f1121208\",\n    tomatoA2: \"#ff55330f\",\n    tomatoA3: \"#ff35232b\",\n    tomatoA4: \"#fd201142\",\n    tomatoA5: \"#fe332153\",\n    tomatoA6: \"#ff4f3864\",\n    tomatoA7: \"#fd644a7d\",\n    tomatoA8: \"#fe6d4ea7\",\n    tomatoA9: \"#fe5431e4\",\n    tomatoA10: \"#ff6847eb\",\n    tomatoA11: \"#ff977d\",\n    tomatoA12: \"#ffd6cefb\",\n};\nconst tomatoDarkP3 = {\n    tomato1: \"color(display-p3 0.09 0.068 0.067)\",\n    tomato2: \"color(display-p3 0.115 0.084 0.076)\",\n    tomato3: \"color(display-p3 0.205 0.097 0.083)\",\n    tomato4: \"color(display-p3 0.282 0.099 0.077)\",\n    tomato5: \"color(display-p3 0.339 0.129 0.101)\",\n    tomato6: \"color(display-p3 0.398 0.179 0.141)\",\n    tomato7: \"color(display-p3 0.487 0.245 0.194)\",\n    tomato8: \"color(display-p3 0.629 0.322 0.248)\",\n    tomato9: \"color(display-p3 0.831 0.345 0.231)\",\n    tomato10: \"color(display-p3 0.862 0.415 0.298)\",\n    tomato11: \"color(display-p3 1 0.585 0.455)\",\n    tomato12: \"color(display-p3 0.959 0.833 0.802)\",\n};\nconst tomatoDarkP3A = {\n    tomatoA1: \"color(display-p3 0.973 0.071 0.071 / 0.026)\",\n    tomatoA2: \"color(display-p3 0.992 0.376 0.224 / 0.051)\",\n    tomatoA3: \"color(display-p3 0.996 0.282 0.176 / 0.148)\",\n    tomatoA4: \"color(display-p3 1 0.204 0.118 / 0.232)\",\n    tomatoA5: \"color(display-p3 1 0.286 0.192 / 0.29)\",\n    tomatoA6: \"color(display-p3 1 0.392 0.278 / 0.353)\",\n    tomatoA7: \"color(display-p3 1 0.459 0.349 / 0.45)\",\n    tomatoA8: \"color(display-p3 1 0.49 0.369 / 0.601)\",\n    tomatoA9: \"color(display-p3 1 0.408 0.267 / 0.82)\",\n    tomatoA10: \"color(display-p3 1 0.478 0.341 / 0.853)\",\n    tomatoA11: \"color(display-p3 1 0.585 0.455)\",\n    tomatoA12: \"color(display-p3 0.959 0.833 0.802)\",\n};\nconst redDark = {\n    red1: \"#191111\",\n    red2: \"#201314\",\n    red3: \"#3b1219\",\n    red4: \"#500f1c\",\n    red5: \"#611623\",\n    red6: \"#72232d\",\n    red7: \"#8c333a\",\n    red8: \"#b54548\",\n    red9: \"#e5484d\",\n    red10: \"#ec5d5e\",\n    red11: \"#ff9592\",\n    red12: \"#ffd1d9\",\n};\nconst redDarkA = {\n    redA1: \"#f4121209\",\n    redA2: \"#f22f3e11\",\n    redA3: \"#ff173f2d\",\n    redA4: \"#fe0a3b44\",\n    redA5: \"#ff204756\",\n    redA6: \"#ff3e5668\",\n    redA7: \"#ff536184\",\n    redA8: \"#ff5d61b0\",\n    redA9: \"#fe4e54e4\",\n    redA10: \"#ff6465eb\",\n    redA11: \"#ff9592\",\n    redA12: \"#ffd1d9\",\n};\nconst redDarkP3 = {\n    red1: \"color(display-p3 0.093 0.068 0.067)\",\n    red2: \"color(display-p3 0.118 0.077 0.079)\",\n    red3: \"color(display-p3 0.211 0.081 0.099)\",\n    red4: \"color(display-p3 0.287 0.079 0.113)\",\n    red5: \"color(display-p3 0.348 0.11 0.142)\",\n    red6: \"color(display-p3 0.414 0.16 0.183)\",\n    red7: \"color(display-p3 0.508 0.224 0.236)\",\n    red8: \"color(display-p3 0.659 0.298 0.297)\",\n    red9: \"color(display-p3 0.83 0.329 0.324)\",\n    red10: \"color(display-p3 0.861 0.403 0.387)\",\n    red11: \"color(display-p3 1 0.57 0.55)\",\n    red12: \"color(display-p3 0.971 0.826 0.852)\",\n};\nconst redDarkP3A = {\n    redA1: \"color(display-p3 0.984 0.071 0.071 / 0.03)\",\n    redA2: \"color(display-p3 0.996 0.282 0.282 / 0.055)\",\n    redA3: \"color(display-p3 1 0.169 0.271 / 0.156)\",\n    redA4: \"color(display-p3 1 0.118 0.267 / 0.236)\",\n    redA5: \"color(display-p3 1 0.212 0.314 / 0.303)\",\n    redA6: \"color(display-p3 1 0.318 0.38 / 0.374)\",\n    redA7: \"color(display-p3 1 0.4 0.424 / 0.475)\",\n    redA8: \"color(display-p3 1 0.431 0.431 / 0.635)\",\n    redA9: \"color(display-p3 1 0.388 0.384 / 0.82)\",\n    redA10: \"color(display-p3 1 0.463 0.447 / 0.853)\",\n    redA11: \"color(display-p3 1 0.57 0.55)\",\n    redA12: \"color(display-p3 0.971 0.826 0.852)\",\n};\nconst rubyDark = {\n    ruby1: \"#191113\",\n    ruby2: \"#1e1517\",\n    ruby3: \"#3a141e\",\n    ruby4: \"#4e1325\",\n    ruby5: \"#5e1a2e\",\n    ruby6: \"#6f2539\",\n    ruby7: \"#883447\",\n    ruby8: \"#b3445a\",\n    ruby9: \"#e54666\",\n    ruby10: \"#ec5a72\",\n    ruby11: \"#ff949d\",\n    ruby12: \"#fed2e1\",\n};\nconst rubyDarkA = {\n    rubyA1: \"#f4124a09\",\n    rubyA2: \"#fe5a7f0e\",\n    rubyA3: \"#ff235d2c\",\n    rubyA4: \"#fd195e42\",\n    rubyA5: \"#fe2d6b53\",\n    rubyA6: \"#ff447665\",\n    rubyA7: \"#ff577d80\",\n    rubyA8: \"#ff5c7cae\",\n    rubyA9: \"#fe4c70e4\",\n    rubyA10: \"#ff617beb\",\n    rubyA11: \"#ff949d\",\n    rubyA12: \"#ffd3e2fe\",\n};\nconst rubyDarkP3 = {\n    ruby1: \"color(display-p3 0.093 0.068 0.074)\",\n    ruby2: \"color(display-p3 0.113 0.083 0.089)\",\n    ruby3: \"color(display-p3 0.208 0.088 0.117)\",\n    ruby4: \"color(display-p3 0.279 0.092 0.147)\",\n    ruby5: \"color(display-p3 0.337 0.12 0.18)\",\n    ruby6: \"color(display-p3 0.401 0.166 0.223)\",\n    ruby7: \"color(display-p3 0.495 0.224 0.281)\",\n    ruby8: \"color(display-p3 0.652 0.295 0.359)\",\n    ruby9: \"color(display-p3 0.83 0.323 0.408)\",\n    ruby10: \"color(display-p3 0.857 0.392 0.455)\",\n    ruby11: \"color(display-p3 1 0.57 0.59)\",\n    ruby12: \"color(display-p3 0.968 0.83 0.88)\",\n};\nconst rubyDarkP3A = {\n    rubyA1: \"color(display-p3 0.984 0.071 0.329 / 0.03)\",\n    rubyA2: \"color(display-p3 0.992 0.376 0.529 / 0.051)\",\n    rubyA3: \"color(display-p3 0.996 0.196 0.404 / 0.152)\",\n    rubyA4: \"color(display-p3 1 0.173 0.416 / 0.227)\",\n    rubyA5: \"color(display-p3 1 0.259 0.459 / 0.29)\",\n    rubyA6: \"color(display-p3 1 0.341 0.506 / 0.358)\",\n    rubyA7: \"color(display-p3 1 0.412 0.541 / 0.458)\",\n    rubyA8: \"color(display-p3 1 0.431 0.537 / 0.627)\",\n    rubyA9: \"color(display-p3 1 0.376 0.482 / 0.82)\",\n    rubyA10: \"color(display-p3 1 0.447 0.522 / 0.849)\",\n    rubyA11: \"color(display-p3 1 0.57 0.59)\",\n    rubyA12: \"color(display-p3 0.968 0.83 0.88)\",\n};\nconst crimsonDark = {\n    crimson1: \"#191114\",\n    crimson2: \"#201318\",\n    crimson3: \"#381525\",\n    crimson4: \"#4d122f\",\n    crimson5: \"#5c1839\",\n    crimson6: \"#6d2545\",\n    crimson7: \"#873356\",\n    crimson8: \"#b0436e\",\n    crimson9: \"#e93d82\",\n    crimson10: \"#ee518a\",\n    crimson11: \"#ff92ad\",\n    crimson12: \"#fdd3e8\",\n};\nconst crimsonDarkA = {\n    crimsonA1: \"#f4126709\",\n    crimsonA2: \"#f22f7a11\",\n    crimsonA3: \"#fe2a8b2a\",\n    crimsonA4: \"#fd158741\",\n    crimsonA5: \"#fd278f51\",\n    crimsonA6: \"#fe459763\",\n    crimsonA7: \"#fd559b7f\",\n    crimsonA8: \"#fe5b9bab\",\n    crimsonA9: \"#fe418de8\",\n    crimsonA10: \"#ff5693ed\",\n    crimsonA11: \"#ff92ad\",\n    crimsonA12: \"#ffd5eafd\",\n};\nconst crimsonDarkP3 = {\n    crimson1: \"color(display-p3 0.093 0.068 0.078)\",\n    crimson2: \"color(display-p3 0.117 0.078 0.095)\",\n    crimson3: \"color(display-p3 0.203 0.091 0.143)\",\n    crimson4: \"color(display-p3 0.277 0.087 0.182)\",\n    crimson5: \"color(display-p3 0.332 0.115 0.22)\",\n    crimson6: \"color(display-p3 0.394 0.162 0.268)\",\n    crimson7: \"color(display-p3 0.489 0.222 0.336)\",\n    crimson8: \"color(display-p3 0.638 0.289 0.429)\",\n    crimson9: \"color(display-p3 0.843 0.298 0.507)\",\n    crimson10: \"color(display-p3 0.864 0.364 0.539)\",\n    crimson11: \"color(display-p3 1 0.56 0.66)\",\n    crimson12: \"color(display-p3 0.966 0.834 0.906)\",\n};\nconst crimsonDarkP3A = {\n    crimsonA1: \"color(display-p3 0.984 0.071 0.463 / 0.03)\",\n    crimsonA2: \"color(display-p3 0.996 0.282 0.569 / 0.055)\",\n    crimsonA3: \"color(display-p3 0.996 0.227 0.573 / 0.148)\",\n    crimsonA4: \"color(display-p3 1 0.157 0.569 / 0.227)\",\n    crimsonA5: \"color(display-p3 1 0.231 0.604 / 0.286)\",\n    crimsonA6: \"color(display-p3 1 0.337 0.643 / 0.349)\",\n    crimsonA7: \"color(display-p3 1 0.416 0.663 / 0.454)\",\n    crimsonA8: \"color(display-p3 0.996 0.427 0.651 / 0.614)\",\n    crimsonA9: \"color(display-p3 1 0.345 0.596 / 0.832)\",\n    crimsonA10: \"color(display-p3 1 0.42 0.62 / 0.853)\",\n    crimsonA11: \"color(display-p3 1 0.56 0.66)\",\n    crimsonA12: \"color(display-p3 0.966 0.834 0.906)\",\n};\nconst pinkDark = {\n    pink1: \"#191117\",\n    pink2: \"#21121d\",\n    pink3: \"#37172f\",\n    pink4: \"#4b143d\",\n    pink5: \"#591c47\",\n    pink6: \"#692955\",\n    pink7: \"#833869\",\n    pink8: \"#a84885\",\n    pink9: \"#d6409f\",\n    pink10: \"#de51a8\",\n    pink11: \"#ff8dcc\",\n    pink12: \"#fdd1ea\",\n};\nconst pinkDarkA = {\n    pinkA1: \"#f412bc09\",\n    pinkA2: \"#f420bb12\",\n    pinkA3: \"#fe37cc29\",\n    pinkA4: \"#fc1ec43f\",\n    pinkA5: \"#fd35c24e\",\n    pinkA6: \"#fd51c75f\",\n    pinkA7: \"#fd62c87b\",\n    pinkA8: \"#ff68c8a2\",\n    pinkA9: \"#fe49bcd4\",\n    pinkA10: \"#ff5cc0dc\",\n    pinkA11: \"#ff8dcc\",\n    pinkA12: \"#ffd3ecfd\",\n};\nconst pinkDarkP3 = {\n    pink1: \"color(display-p3 0.093 0.068 0.089)\",\n    pink2: \"color(display-p3 0.121 0.073 0.11)\",\n    pink3: \"color(display-p3 0.198 0.098 0.179)\",\n    pink4: \"color(display-p3 0.271 0.095 0.231)\",\n    pink5: \"color(display-p3 0.32 0.127 0.273)\",\n    pink6: \"color(display-p3 0.382 0.177 0.326)\",\n    pink7: \"color(display-p3 0.477 0.238 0.405)\",\n    pink8: \"color(display-p3 0.612 0.304 0.51)\",\n    pink9: \"color(display-p3 0.775 0.297 0.61)\",\n    pink10: \"color(display-p3 0.808 0.356 0.645)\",\n    pink11: \"color(display-p3 1 0.535 0.78)\",\n    pink12: \"color(display-p3 0.964 0.826 0.912)\",\n};\nconst pinkDarkP3A = {\n    pinkA1: \"color(display-p3 0.984 0.071 0.855 / 0.03)\",\n    pinkA2: \"color(display-p3 1 0.2 0.8 / 0.059)\",\n    pinkA3: \"color(display-p3 1 0.294 0.886 / 0.139)\",\n    pinkA4: \"color(display-p3 1 0.192 0.82 / 0.219)\",\n    pinkA5: \"color(display-p3 1 0.282 0.827 / 0.274)\",\n    pinkA6: \"color(display-p3 1 0.396 0.835 / 0.337)\",\n    pinkA7: \"color(display-p3 1 0.459 0.831 / 0.442)\",\n    pinkA8: \"color(display-p3 1 0.478 0.827 / 0.585)\",\n    pinkA9: \"color(display-p3 1 0.373 0.784 / 0.761)\",\n    pinkA10: \"color(display-p3 1 0.435 0.792 / 0.795)\",\n    pinkA11: \"color(display-p3 1 0.535 0.78)\",\n    pinkA12: \"color(display-p3 0.964 0.826 0.912)\",\n};\nconst plumDark = {\n    plum1: \"#181118\",\n    plum2: \"#201320\",\n    plum3: \"#351a35\",\n    plum4: \"#451d47\",\n    plum5: \"#512454\",\n    plum6: \"#5e3061\",\n    plum7: \"#734079\",\n    plum8: \"#92549c\",\n    plum9: \"#ab4aba\",\n    plum10: \"#b658c4\",\n    plum11: \"#e796f3\",\n    plum12: \"#f4d4f4\",\n};\nconst plumDarkA = {\n    plumA1: \"#f112f108\",\n    plumA2: \"#f22ff211\",\n    plumA3: \"#fd4cfd27\",\n    plumA4: \"#f646ff3a\",\n    plumA5: \"#f455ff48\",\n    plumA6: \"#f66dff56\",\n    plumA7: \"#f07cfd70\",\n    plumA8: \"#ee84ff95\",\n    plumA9: \"#e961feb6\",\n    plumA10: \"#ed70ffc0\",\n    plumA11: \"#f19cfef3\",\n    plumA12: \"#feddfef4\",\n};\nconst plumDarkP3 = {\n    plum1: \"color(display-p3 0.09 0.068 0.092)\",\n    plum2: \"color(display-p3 0.118 0.077 0.121)\",\n    plum3: \"color(display-p3 0.192 0.105 0.202)\",\n    plum4: \"color(display-p3 0.25 0.121 0.271)\",\n    plum5: \"color(display-p3 0.293 0.152 0.319)\",\n    plum6: \"color(display-p3 0.343 0.198 0.372)\",\n    plum7: \"color(display-p3 0.424 0.262 0.461)\",\n    plum8: \"color(display-p3 0.54 0.341 0.595)\",\n    plum9: \"color(display-p3 0.624 0.313 0.708)\",\n    plum10: \"color(display-p3 0.666 0.365 0.748)\",\n    plum11: \"color(display-p3 0.86 0.602 0.933)\",\n    plum12: \"color(display-p3 0.936 0.836 0.949)\",\n};\nconst plumDarkP3A = {\n    plumA1: \"color(display-p3 0.973 0.071 0.973 / 0.026)\",\n    plumA2: \"color(display-p3 0.933 0.267 1 / 0.059)\",\n    plumA3: \"color(display-p3 0.918 0.333 0.996 / 0.148)\",\n    plumA4: \"color(display-p3 0.91 0.318 1 / 0.219)\",\n    plumA5: \"color(display-p3 0.914 0.388 1 / 0.269)\",\n    plumA6: \"color(display-p3 0.906 0.463 1 / 0.328)\",\n    plumA7: \"color(display-p3 0.906 0.529 1 / 0.425)\",\n    plumA8: \"color(display-p3 0.906 0.553 1 / 0.568)\",\n    plumA9: \"color(display-p3 0.875 0.427 1 / 0.69)\",\n    plumA10: \"color(display-p3 0.886 0.471 0.996 / 0.732)\",\n    plumA11: \"color(display-p3 0.86 0.602 0.933)\",\n    plumA12: \"color(display-p3 0.936 0.836 0.949)\",\n};\nconst purpleDark = {\n    purple1: \"#18111b\",\n    purple2: \"#1e1523\",\n    purple3: \"#301c3b\",\n    purple4: \"#3d224e\",\n    purple5: \"#48295c\",\n    purple6: \"#54346b\",\n    purple7: \"#664282\",\n    purple8: \"#8457aa\",\n    purple9: \"#8e4ec6\",\n    purple10: \"#9a5cd0\",\n    purple11: \"#d19dff\",\n    purple12: \"#ecd9fa\",\n};\nconst purpleDarkA = {\n    purpleA1: \"#b412f90b\",\n    purpleA2: \"#b744f714\",\n    purpleA3: \"#c150ff2d\",\n    purpleA4: \"#bb53fd42\",\n    purpleA5: \"#be5cfd51\",\n    purpleA6: \"#c16dfd61\",\n    purpleA7: \"#c378fd7a\",\n    purpleA8: \"#c47effa4\",\n    purpleA9: \"#b661ffc2\",\n    purpleA10: \"#bc6fffcd\",\n    purpleA11: \"#d19dff\",\n    purpleA12: \"#f1ddfffa\",\n};\nconst purpleDarkP3 = {\n    purple1: \"color(display-p3 0.09 0.068 0.103)\",\n    purple2: \"color(display-p3 0.113 0.082 0.134)\",\n    purple3: \"color(display-p3 0.175 0.112 0.224)\",\n    purple4: \"color(display-p3 0.224 0.137 0.297)\",\n    purple5: \"color(display-p3 0.264 0.167 0.349)\",\n    purple6: \"color(display-p3 0.311 0.208 0.406)\",\n    purple7: \"color(display-p3 0.381 0.266 0.496)\",\n    purple8: \"color(display-p3 0.49 0.349 0.649)\",\n    purple9: \"color(display-p3 0.523 0.318 0.751)\",\n    purple10: \"color(display-p3 0.57 0.373 0.791)\",\n    purple11: \"color(display-p3 0.8 0.62 1)\",\n    purple12: \"color(display-p3 0.913 0.854 0.971)\",\n};\nconst purpleDarkP3A = {\n    purpleA1: \"color(display-p3 0.686 0.071 0.996 / 0.038)\",\n    purpleA2: \"color(display-p3 0.722 0.286 0.996 / 0.072)\",\n    purpleA3: \"color(display-p3 0.718 0.349 0.996 / 0.169)\",\n    purpleA4: \"color(display-p3 0.702 0.353 1 / 0.248)\",\n    purpleA5: \"color(display-p3 0.718 0.404 1 / 0.303)\",\n    purpleA6: \"color(display-p3 0.733 0.455 1 / 0.366)\",\n    purpleA7: \"color(display-p3 0.753 0.506 1 / 0.458)\",\n    purpleA8: \"color(display-p3 0.749 0.522 1 / 0.622)\",\n    purpleA9: \"color(display-p3 0.686 0.408 1 / 0.736)\",\n    purpleA10: \"color(display-p3 0.71 0.459 1 / 0.778)\",\n    purpleA11: \"color(display-p3 0.8 0.62 1)\",\n    purpleA12: \"color(display-p3 0.913 0.854 0.971)\",\n};\nconst violetDark = {\n    violet1: \"#14121f\",\n    violet2: \"#1b1525\",\n    violet3: \"#291f43\",\n    violet4: \"#33255b\",\n    violet5: \"#3c2e69\",\n    violet6: \"#473876\",\n    violet7: \"#56468b\",\n    violet8: \"#6958ad\",\n    violet9: \"#6e56cf\",\n    violet10: \"#7d66d9\",\n    violet11: \"#baa7ff\",\n    violet12: \"#e2ddfe\",\n};\nconst violetDarkA = {\n    violetA1: \"#4422ff0f\",\n    violetA2: \"#853ff916\",\n    violetA3: \"#8354fe36\",\n    violetA4: \"#7d51fd50\",\n    violetA5: \"#845ffd5f\",\n    violetA6: \"#8f6cfd6d\",\n    violetA7: \"#9879ff83\",\n    violetA8: \"#977dfea8\",\n    violetA9: \"#8668ffcc\",\n    violetA10: \"#9176fed7\",\n    violetA11: \"#baa7ff\",\n    violetA12: \"#e3defffe\",\n};\nconst violetDarkP3 = {\n    violet1: \"color(display-p3 0.077 0.071 0.118)\",\n    violet2: \"color(display-p3 0.101 0.084 0.141)\",\n    violet3: \"color(display-p3 0.154 0.123 0.256)\",\n    violet4: \"color(display-p3 0.191 0.148 0.345)\",\n    violet5: \"color(display-p3 0.226 0.182 0.396)\",\n    violet6: \"color(display-p3 0.269 0.223 0.449)\",\n    violet7: \"color(display-p3 0.326 0.277 0.53)\",\n    violet8: \"color(display-p3 0.399 0.346 0.656)\",\n    violet9: \"color(display-p3 0.417 0.341 0.784)\",\n    violet10: \"color(display-p3 0.477 0.402 0.823)\",\n    violet11: \"color(display-p3 0.72 0.65 1)\",\n    violet12: \"color(display-p3 0.883 0.867 0.986)\",\n};\nconst violetDarkP3A = {\n    violetA1: \"color(display-p3 0.282 0.141 0.996 / 0.055)\",\n    violetA2: \"color(display-p3 0.51 0.263 1 / 0.08)\",\n    violetA3: \"color(display-p3 0.494 0.337 0.996 / 0.202)\",\n    violetA4: \"color(display-p3 0.49 0.345 1 / 0.299)\",\n    violetA5: \"color(display-p3 0.525 0.392 1 / 0.353)\",\n    violetA6: \"color(display-p3 0.569 0.455 1 / 0.408)\",\n    violetA7: \"color(display-p3 0.588 0.494 1 / 0.496)\",\n    violetA8: \"color(display-p3 0.596 0.51 1 / 0.631)\",\n    violetA9: \"color(display-p3 0.522 0.424 1 / 0.769)\",\n    violetA10: \"color(display-p3 0.576 0.482 1 / 0.811)\",\n    violetA11: \"color(display-p3 0.72 0.65 1)\",\n    violetA12: \"color(display-p3 0.883 0.867 0.986)\",\n};\nconst irisDark = {\n    iris1: \"#13131e\",\n    iris2: \"#171625\",\n    iris3: \"#202248\",\n    iris4: \"#262a65\",\n    iris5: \"#303374\",\n    iris6: \"#3d3e82\",\n    iris7: \"#4a4a95\",\n    iris8: \"#5958b1\",\n    iris9: \"#5b5bd6\",\n    iris10: \"#6e6ade\",\n    iris11: \"#b1a9ff\",\n    iris12: \"#e0dffe\",\n};\nconst irisDarkA = {\n    irisA1: \"#3636fe0e\",\n    irisA2: \"#564bf916\",\n    irisA3: \"#525bff3b\",\n    irisA4: \"#4d58ff5a\",\n    irisA5: \"#5b62fd6b\",\n    irisA6: \"#6d6ffd7a\",\n    irisA7: \"#7777fe8e\",\n    irisA8: \"#7b7afeac\",\n    irisA9: \"#6a6afed4\",\n    irisA10: \"#7d79ffdc\",\n    irisA11: \"#b1a9ff\",\n    irisA12: \"#e1e0fffe\",\n};\nconst irisDarkP3 = {\n    iris1: \"color(display-p3 0.075 0.075 0.114)\",\n    iris2: \"color(display-p3 0.089 0.086 0.14)\",\n    iris3: \"color(display-p3 0.128 0.134 0.272)\",\n    iris4: \"color(display-p3 0.153 0.165 0.382)\",\n    iris5: \"color(display-p3 0.192 0.201 0.44)\",\n    iris6: \"color(display-p3 0.239 0.241 0.491)\",\n    iris7: \"color(display-p3 0.291 0.289 0.565)\",\n    iris8: \"color(display-p3 0.35 0.345 0.673)\",\n    iris9: \"color(display-p3 0.357 0.357 0.81)\",\n    iris10: \"color(display-p3 0.428 0.416 0.843)\",\n    iris11: \"color(display-p3 0.685 0.662 1)\",\n    iris12: \"color(display-p3 0.878 0.875 0.986)\",\n};\nconst irisDarkP3A = {\n    irisA1: \"color(display-p3 0.224 0.224 0.992 / 0.051)\",\n    irisA2: \"color(display-p3 0.361 0.314 1 / 0.08)\",\n    irisA3: \"color(display-p3 0.357 0.373 1 / 0.219)\",\n    irisA4: \"color(display-p3 0.325 0.361 1 / 0.337)\",\n    irisA5: \"color(display-p3 0.38 0.4 1 / 0.4)\",\n    irisA6: \"color(display-p3 0.447 0.447 1 / 0.454)\",\n    irisA7: \"color(display-p3 0.486 0.486 1 / 0.534)\",\n    irisA8: \"color(display-p3 0.502 0.494 1 / 0.652)\",\n    irisA9: \"color(display-p3 0.431 0.431 1 / 0.799)\",\n    irisA10: \"color(display-p3 0.502 0.486 1 / 0.832)\",\n    irisA11: \"color(display-p3 0.685 0.662 1)\",\n    irisA12: \"color(display-p3 0.878 0.875 0.986)\",\n};\nconst indigoDark = {\n    indigo1: \"#11131f\",\n    indigo2: \"#141726\",\n    indigo3: \"#182449\",\n    indigo4: \"#1d2e62\",\n    indigo5: \"#253974\",\n    indigo6: \"#304384\",\n    indigo7: \"#3a4f97\",\n    indigo8: \"#435db1\",\n    indigo9: \"#3e63dd\",\n    indigo10: \"#5472e4\",\n    indigo11: \"#9eb1ff\",\n    indigo12: \"#d6e1ff\",\n};\nconst indigoDarkA = {\n    indigoA1: \"#1133ff0f\",\n    indigoA2: \"#3354fa17\",\n    indigoA3: \"#2f62ff3c\",\n    indigoA4: \"#3566ff57\",\n    indigoA5: \"#4171fd6b\",\n    indigoA6: \"#5178fd7c\",\n    indigoA7: \"#5a7fff90\",\n    indigoA8: \"#5b81feac\",\n    indigoA9: \"#4671ffdb\",\n    indigoA10: \"#5c7efee3\",\n    indigoA11: \"#9eb1ff\",\n    indigoA12: \"#d6e1ff\",\n};\nconst indigoDarkP3 = {\n    indigo1: \"color(display-p3 0.068 0.074 0.118)\",\n    indigo2: \"color(display-p3 0.081 0.089 0.144)\",\n    indigo3: \"color(display-p3 0.105 0.141 0.275)\",\n    indigo4: \"color(display-p3 0.129 0.18 0.369)\",\n    indigo5: \"color(display-p3 0.163 0.22 0.439)\",\n    indigo6: \"color(display-p3 0.203 0.262 0.5)\",\n    indigo7: \"color(display-p3 0.245 0.309 0.575)\",\n    indigo8: \"color(display-p3 0.285 0.362 0.674)\",\n    indigo9: \"color(display-p3 0.276 0.384 0.837)\",\n    indigo10: \"color(display-p3 0.354 0.445 0.866)\",\n    indigo11: \"color(display-p3 0.63 0.69 1)\",\n    indigo12: \"color(display-p3 0.848 0.881 0.99)\",\n};\nconst indigoDarkP3A = {\n    indigoA1: \"color(display-p3 0.071 0.212 0.996 / 0.055)\",\n    indigoA2: \"color(display-p3 0.251 0.345 0.988 / 0.085)\",\n    indigoA3: \"color(display-p3 0.243 0.404 1 / 0.223)\",\n    indigoA4: \"color(display-p3 0.263 0.42 1 / 0.324)\",\n    indigoA5: \"color(display-p3 0.314 0.451 1 / 0.4)\",\n    indigoA6: \"color(display-p3 0.361 0.49 1 / 0.467)\",\n    indigoA7: \"color(display-p3 0.388 0.51 1 / 0.547)\",\n    indigoA8: \"color(display-p3 0.404 0.518 1 / 0.652)\",\n    indigoA9: \"color(display-p3 0.318 0.451 1 / 0.824)\",\n    indigoA10: \"color(display-p3 0.404 0.506 1 / 0.858)\",\n    indigoA11: \"color(display-p3 0.63 0.69 1)\",\n    indigoA12: \"color(display-p3 0.848 0.881 0.99)\",\n};\nconst blueDark = {\n    blue1: \"#0d1520\",\n    blue2: \"#111927\",\n    blue3: \"#0d2847\",\n    blue4: \"#003362\",\n    blue5: \"#004074\",\n    blue6: \"#104d87\",\n    blue7: \"#205d9e\",\n    blue8: \"#2870bd\",\n    blue9: \"#0090ff\",\n    blue10: \"#3b9eff\",\n    blue11: \"#70b8ff\",\n    blue12: \"#c2e6ff\",\n};\nconst blueDarkA = {\n    blueA1: \"#004df211\",\n    blueA2: \"#1166fb18\",\n    blueA3: \"#0077ff3a\",\n    blueA4: \"#0075ff57\",\n    blueA5: \"#0081fd6b\",\n    blueA6: \"#0f89fd7f\",\n    blueA7: \"#2a91fe98\",\n    blueA8: \"#3094feb9\",\n    blueA9: \"#0090ff\",\n    blueA10: \"#3b9eff\",\n    blueA11: \"#70b8ff\",\n    blueA12: \"#c2e6ff\",\n};\nconst blueDarkP3 = {\n    blue1: \"color(display-p3 0.057 0.081 0.122)\",\n    blue2: \"color(display-p3 0.072 0.098 0.147)\",\n    blue3: \"color(display-p3 0.078 0.154 0.27)\",\n    blue4: \"color(display-p3 0.033 0.197 0.37)\",\n    blue5: \"color(display-p3 0.08 0.245 0.441)\",\n    blue6: \"color(display-p3 0.14 0.298 0.511)\",\n    blue7: \"color(display-p3 0.195 0.361 0.6)\",\n    blue8: \"color(display-p3 0.239 0.434 0.72)\",\n    blue9: \"color(display-p3 0.247 0.556 0.969)\",\n    blue10: \"color(display-p3 0.344 0.612 0.973)\",\n    blue11: \"color(display-p3 0.49 0.72 1)\",\n    blue12: \"color(display-p3 0.788 0.898 0.99)\",\n};\nconst blueDarkP3A = {\n    blueA1: \"color(display-p3 0 0.333 1 / 0.059)\",\n    blueA2: \"color(display-p3 0.114 0.435 0.988 / 0.085)\",\n    blueA3: \"color(display-p3 0.122 0.463 1 / 0.219)\",\n    blueA4: \"color(display-p3 0 0.467 1 / 0.324)\",\n    blueA5: \"color(display-p3 0.098 0.51 1 / 0.4)\",\n    blueA6: \"color(display-p3 0.224 0.557 1 / 0.475)\",\n    blueA7: \"color(display-p3 0.294 0.584 1 / 0.572)\",\n    blueA8: \"color(display-p3 0.314 0.592 1 / 0.702)\",\n    blueA9: \"color(display-p3 0.251 0.573 0.996 / 0.967)\",\n    blueA10: \"color(display-p3 0.357 0.631 1 / 0.971)\",\n    blueA11: \"color(display-p3 0.49 0.72 1)\",\n    blueA12: \"color(display-p3 0.788 0.898 0.99)\",\n};\nconst cyanDark = {\n    cyan1: \"#0b161a\",\n    cyan2: \"#101b20\",\n    cyan3: \"#082c36\",\n    cyan4: \"#003848\",\n    cyan5: \"#004558\",\n    cyan6: \"#045468\",\n    cyan7: \"#12677e\",\n    cyan8: \"#11809c\",\n    cyan9: \"#00a2c7\",\n    cyan10: \"#23afd0\",\n    cyan11: \"#4ccce6\",\n    cyan12: \"#b6ecf7\",\n};\nconst cyanDarkA = {\n    cyanA1: \"#0091f70a\",\n    cyanA2: \"#02a7f211\",\n    cyanA3: \"#00befd28\",\n    cyanA4: \"#00baff3b\",\n    cyanA5: \"#00befd4d\",\n    cyanA6: \"#00c7fd5e\",\n    cyanA7: \"#14cdff75\",\n    cyanA8: \"#11cfff95\",\n    cyanA9: \"#00cfffc3\",\n    cyanA10: \"#28d6ffcd\",\n    cyanA11: \"#52e1fee5\",\n    cyanA12: \"#bbf3fef7\",\n};\nconst cyanDarkP3 = {\n    cyan1: \"color(display-p3 0.053 0.085 0.098)\",\n    cyan2: \"color(display-p3 0.072 0.105 0.122)\",\n    cyan3: \"color(display-p3 0.073 0.168 0.209)\",\n    cyan4: \"color(display-p3 0.063 0.216 0.277)\",\n    cyan5: \"color(display-p3 0.091 0.267 0.336)\",\n    cyan6: \"color(display-p3 0.137 0.324 0.4)\",\n    cyan7: \"color(display-p3 0.186 0.398 0.484)\",\n    cyan8: \"color(display-p3 0.23 0.496 0.6)\",\n    cyan9: \"color(display-p3 0.282 0.627 0.765)\",\n    cyan10: \"color(display-p3 0.331 0.675 0.801)\",\n    cyan11: \"color(display-p3 0.446 0.79 0.887)\",\n    cyan12: \"color(display-p3 0.757 0.919 0.962)\",\n};\nconst cyanDarkP3A = {\n    cyanA1: \"color(display-p3 0 0.647 0.992 / 0.034)\",\n    cyanA2: \"color(display-p3 0.133 0.733 1 / 0.059)\",\n    cyanA3: \"color(display-p3 0.122 0.741 0.996 / 0.152)\",\n    cyanA4: \"color(display-p3 0.051 0.725 1 / 0.227)\",\n    cyanA5: \"color(display-p3 0.149 0.757 1 / 0.29)\",\n    cyanA6: \"color(display-p3 0.267 0.792 1 / 0.358)\",\n    cyanA7: \"color(display-p3 0.333 0.808 1 / 0.446)\",\n    cyanA8: \"color(display-p3 0.357 0.816 1 / 0.572)\",\n    cyanA9: \"color(display-p3 0.357 0.82 1 / 0.748)\",\n    cyanA10: \"color(display-p3 0.4 0.839 1 / 0.786)\",\n    cyanA11: \"color(display-p3 0.446 0.79 0.887)\",\n    cyanA12: \"color(display-p3 0.757 0.919 0.962)\",\n};\nconst tealDark = {\n    teal1: \"#0d1514\",\n    teal2: \"#111c1b\",\n    teal3: \"#0d2d2a\",\n    teal4: \"#023b37\",\n    teal5: \"#084843\",\n    teal6: \"#145750\",\n    teal7: \"#1c6961\",\n    teal8: \"#207e73\",\n    teal9: \"#12a594\",\n    teal10: \"#0eb39e\",\n    teal11: \"#0bd8b6\",\n    teal12: \"#adf0dd\",\n};\nconst tealDarkA = {\n    tealA1: \"#00deab05\",\n    tealA2: \"#12fbe60c\",\n    tealA3: \"#00ffe61e\",\n    tealA4: \"#00ffe92d\",\n    tealA5: \"#00ffea3b\",\n    tealA6: \"#1cffe84b\",\n    tealA7: \"#2efde85f\",\n    tealA8: \"#32ffe775\",\n    tealA9: \"#13ffe49f\",\n    tealA10: \"#0dffe0ae\",\n    tealA11: \"#0afed5d6\",\n    tealA12: \"#b8ffebef\",\n};\nconst tealDarkP3 = {\n    teal1: \"color(display-p3 0.059 0.083 0.079)\",\n    teal2: \"color(display-p3 0.075 0.11 0.107)\",\n    teal3: \"color(display-p3 0.087 0.175 0.165)\",\n    teal4: \"color(display-p3 0.087 0.227 0.214)\",\n    teal5: \"color(display-p3 0.12 0.277 0.261)\",\n    teal6: \"color(display-p3 0.162 0.335 0.314)\",\n    teal7: \"color(display-p3 0.205 0.406 0.379)\",\n    teal8: \"color(display-p3 0.245 0.489 0.453)\",\n    teal9: \"color(display-p3 0.297 0.637 0.581)\",\n    teal10: \"color(display-p3 0.319 0.69 0.62)\",\n    teal11: \"color(display-p3 0.388 0.835 0.719)\",\n    teal12: \"color(display-p3 0.734 0.934 0.87)\",\n};\nconst tealDarkP3A = {\n    tealA1: \"color(display-p3 0 0.992 0.761 / 0.017)\",\n    tealA2: \"color(display-p3 0.235 0.988 0.902 / 0.047)\",\n    tealA3: \"color(display-p3 0.235 1 0.898 / 0.118)\",\n    tealA4: \"color(display-p3 0.18 0.996 0.929 / 0.173)\",\n    tealA5: \"color(display-p3 0.31 1 0.933 / 0.227)\",\n    tealA6: \"color(display-p3 0.396 1 0.933 / 0.286)\",\n    tealA7: \"color(display-p3 0.443 1 0.925 / 0.366)\",\n    tealA8: \"color(display-p3 0.459 1 0.925 / 0.454)\",\n    tealA9: \"color(display-p3 0.443 0.996 0.906 / 0.61)\",\n    tealA10: \"color(display-p3 0.439 0.996 0.89 / 0.669)\",\n    tealA11: \"color(display-p3 0.388 0.835 0.719)\",\n    tealA12: \"color(display-p3 0.734 0.934 0.87)\",\n};\nconst jadeDark = {\n    jade1: \"#0d1512\",\n    jade2: \"#121c18\",\n    jade3: \"#0f2e22\",\n    jade4: \"#0b3b2c\",\n    jade5: \"#114837\",\n    jade6: \"#1b5745\",\n    jade7: \"#246854\",\n    jade8: \"#2a7e68\",\n    jade9: \"#29a383\",\n    jade10: \"#27b08b\",\n    jade11: \"#1fd8a4\",\n    jade12: \"#adf0d4\",\n};\nconst jadeDarkA = {\n    jadeA1: \"#00de4505\",\n    jadeA2: \"#27fba60c\",\n    jadeA3: \"#02f99920\",\n    jadeA4: \"#00ffaa2d\",\n    jadeA5: \"#11ffb63b\",\n    jadeA6: \"#34ffc24b\",\n    jadeA7: \"#45fdc75e\",\n    jadeA8: \"#48ffcf75\",\n    jadeA9: \"#38feca9d\",\n    jadeA10: \"#31fec7ab\",\n    jadeA11: \"#21fec0d6\",\n    jadeA12: \"#b8ffe1ef\",\n};\nconst jadeDarkP3 = {\n    jade1: \"color(display-p3 0.059 0.083 0.071)\",\n    jade2: \"color(display-p3 0.078 0.11 0.094)\",\n    jade3: \"color(display-p3 0.091 0.176 0.138)\",\n    jade4: \"color(display-p3 0.102 0.228 0.177)\",\n    jade5: \"color(display-p3 0.133 0.279 0.221)\",\n    jade6: \"color(display-p3 0.174 0.334 0.273)\",\n    jade7: \"color(display-p3 0.219 0.402 0.335)\",\n    jade8: \"color(display-p3 0.263 0.488 0.411)\",\n    jade9: \"color(display-p3 0.319 0.63 0.521)\",\n    jade10: \"color(display-p3 0.338 0.68 0.555)\",\n    jade11: \"color(display-p3 0.4 0.835 0.656)\",\n    jade12: \"color(display-p3 0.734 0.934 0.838)\",\n};\nconst jadeDarkP3A = {\n    jadeA1: \"color(display-p3 0 0.992 0.298 / 0.017)\",\n    jadeA2: \"color(display-p3 0.318 0.988 0.651 / 0.047)\",\n    jadeA3: \"color(display-p3 0.267 1 0.667 / 0.118)\",\n    jadeA4: \"color(display-p3 0.275 0.996 0.702 / 0.173)\",\n    jadeA5: \"color(display-p3 0.361 1 0.741 / 0.227)\",\n    jadeA6: \"color(display-p3 0.439 1 0.796 / 0.286)\",\n    jadeA7: \"color(display-p3 0.49 1 0.804 / 0.362)\",\n    jadeA8: \"color(display-p3 0.506 1 0.835 / 0.45)\",\n    jadeA9: \"color(display-p3 0.478 0.996 0.816 / 0.606)\",\n    jadeA10: \"color(display-p3 0.478 1 0.816 / 0.656)\",\n    jadeA11: \"color(display-p3 0.4 0.835 0.656)\",\n    jadeA12: \"color(display-p3 0.734 0.934 0.838)\",\n};\nconst greenDark = {\n    green1: \"#0e1512\",\n    green2: \"#121b17\",\n    green3: \"#132d21\",\n    green4: \"#113b29\",\n    green5: \"#174933\",\n    green6: \"#20573e\",\n    green7: \"#28684a\",\n    green8: \"#2f7c57\",\n    green9: \"#30a46c\",\n    green10: \"#33b074\",\n    green11: \"#3dd68c\",\n    green12: \"#b1f1cb\",\n};\nconst greenDarkA = {\n    greenA1: \"#00de4505\",\n    greenA2: \"#29f99d0b\",\n    greenA3: \"#22ff991e\",\n    greenA4: \"#11ff992d\",\n    greenA5: \"#2bffa23c\",\n    greenA6: \"#44ffaa4b\",\n    greenA7: \"#50fdac5e\",\n    greenA8: \"#54ffad73\",\n    greenA9: \"#44ffa49e\",\n    greenA10: \"#43fea4ab\",\n    greenA11: \"#46fea5d4\",\n    greenA12: \"#bbffd7f0\",\n};\nconst greenDarkP3 = {\n    green1: \"color(display-p3 0.062 0.083 0.071)\",\n    green2: \"color(display-p3 0.079 0.106 0.09)\",\n    green3: \"color(display-p3 0.1 0.173 0.133)\",\n    green4: \"color(display-p3 0.115 0.229 0.166)\",\n    green5: \"color(display-p3 0.147 0.282 0.206)\",\n    green6: \"color(display-p3 0.185 0.338 0.25)\",\n    green7: \"color(display-p3 0.227 0.403 0.298)\",\n    green8: \"color(display-p3 0.27 0.479 0.351)\",\n    green9: \"color(display-p3 0.332 0.634 0.442)\",\n    green10: \"color(display-p3 0.357 0.682 0.474)\",\n    green11: \"color(display-p3 0.434 0.828 0.573)\",\n    green12: \"color(display-p3 0.747 0.938 0.807)\",\n};\nconst greenDarkP3A = {\n    greenA1: \"color(display-p3 0 0.992 0.298 / 0.017)\",\n    greenA2: \"color(display-p3 0.341 0.98 0.616 / 0.043)\",\n    greenA3: \"color(display-p3 0.376 0.996 0.655 / 0.114)\",\n    greenA4: \"color(display-p3 0.341 0.996 0.635 / 0.173)\",\n    greenA5: \"color(display-p3 0.408 1 0.678 / 0.232)\",\n    greenA6: \"color(display-p3 0.475 1 0.706 / 0.29)\",\n    greenA7: \"color(display-p3 0.514 1 0.706 / 0.362)\",\n    greenA8: \"color(display-p3 0.529 1 0.718 / 0.442)\",\n    greenA9: \"color(display-p3 0.502 0.996 0.682 / 0.61)\",\n    greenA10: \"color(display-p3 0.506 1 0.682 / 0.66)\",\n    greenA11: \"color(display-p3 0.434 0.828 0.573)\",\n    greenA12: \"color(display-p3 0.747 0.938 0.807)\",\n};\nconst grassDark = {\n    grass1: \"#0e1511\",\n    grass2: \"#141a15\",\n    grass3: \"#1b2a1e\",\n    grass4: \"#1d3a24\",\n    grass5: \"#25482d\",\n    grass6: \"#2d5736\",\n    grass7: \"#366740\",\n    grass8: \"#3e7949\",\n    grass9: \"#46a758\",\n    grass10: \"#53b365\",\n    grass11: \"#71d083\",\n    grass12: \"#c2f0c2\",\n};\nconst grassDarkA = {\n    grassA1: \"#00de1205\",\n    grassA2: \"#5ef7780a\",\n    grassA3: \"#70fe8c1b\",\n    grassA4: \"#57ff802c\",\n    grassA5: \"#68ff8b3b\",\n    grassA6: \"#71ff8f4b\",\n    grassA7: \"#77fd925d\",\n    grassA8: \"#77fd9070\",\n    grassA9: \"#65ff82a1\",\n    grassA10: \"#72ff8dae\",\n    grassA11: \"#89ff9fcd\",\n    grassA12: \"#ceffceef\",\n};\nconst grassDarkP3 = {\n    grass1: \"color(display-p3 0.062 0.083 0.067)\",\n    grass2: \"color(display-p3 0.083 0.103 0.085)\",\n    grass3: \"color(display-p3 0.118 0.163 0.122)\",\n    grass4: \"color(display-p3 0.142 0.225 0.15)\",\n    grass5: \"color(display-p3 0.178 0.279 0.186)\",\n    grass6: \"color(display-p3 0.217 0.337 0.224)\",\n    grass7: \"color(display-p3 0.258 0.4 0.264)\",\n    grass8: \"color(display-p3 0.302 0.47 0.305)\",\n    grass9: \"color(display-p3 0.38 0.647 0.378)\",\n    grass10: \"color(display-p3 0.426 0.694 0.426)\",\n    grass11: \"color(display-p3 0.535 0.807 0.542)\",\n    grass12: \"color(display-p3 0.797 0.936 0.776)\",\n};\nconst grassDarkP3A = {\n    grassA1: \"color(display-p3 0 0.992 0.071 / 0.017)\",\n    grassA2: \"color(display-p3 0.482 0.996 0.584 / 0.038)\",\n    grassA3: \"color(display-p3 0.549 0.992 0.588 / 0.106)\",\n    grassA4: \"color(display-p3 0.51 0.996 0.557 / 0.169)\",\n    grassA5: \"color(display-p3 0.553 1 0.588 / 0.227)\",\n    grassA6: \"color(display-p3 0.584 1 0.608 / 0.29)\",\n    grassA7: \"color(display-p3 0.604 1 0.616 / 0.358)\",\n    grassA8: \"color(display-p3 0.608 1 0.62 / 0.433)\",\n    grassA9: \"color(display-p3 0.573 1 0.569 / 0.622)\",\n    grassA10: \"color(display-p3 0.6 0.996 0.6 / 0.673)\",\n    grassA11: \"color(display-p3 0.535 0.807 0.542)\",\n    grassA12: \"color(display-p3 0.797 0.936 0.776)\",\n};\nconst brownDark = {\n    brown1: \"#12110f\",\n    brown2: \"#1c1816\",\n    brown3: \"#28211d\",\n    brown4: \"#322922\",\n    brown5: \"#3e3128\",\n    brown6: \"#4d3c2f\",\n    brown7: \"#614a39\",\n    brown8: \"#7c5f46\",\n    brown9: \"#ad7f58\",\n    brown10: \"#b88c67\",\n    brown11: \"#dbb594\",\n    brown12: \"#f2e1ca\",\n};\nconst brownDarkA = {\n    brownA1: \"#91110002\",\n    brownA2: \"#fba67c0c\",\n    brownA3: \"#fcb58c19\",\n    brownA4: \"#fbbb8a24\",\n    brownA5: \"#fcb88931\",\n    brownA6: \"#fdba8741\",\n    brownA7: \"#ffbb8856\",\n    brownA8: \"#ffbe8773\",\n    brownA9: \"#feb87da8\",\n    brownA10: \"#ffc18cb3\",\n    brownA11: \"#fed1aad9\",\n    brownA12: \"#feecd4f2\",\n};\nconst brownDarkP3 = {\n    brown1: \"color(display-p3 0.071 0.067 0.059)\",\n    brown2: \"color(display-p3 0.107 0.095 0.087)\",\n    brown3: \"color(display-p3 0.151 0.13 0.115)\",\n    brown4: \"color(display-p3 0.191 0.161 0.138)\",\n    brown5: \"color(display-p3 0.235 0.194 0.162)\",\n    brown6: \"color(display-p3 0.291 0.237 0.192)\",\n    brown7: \"color(display-p3 0.365 0.295 0.232)\",\n    brown8: \"color(display-p3 0.469 0.377 0.287)\",\n    brown9: \"color(display-p3 0.651 0.505 0.368)\",\n    brown10: \"color(display-p3 0.697 0.557 0.423)\",\n    brown11: \"color(display-p3 0.835 0.715 0.597)\",\n    brown12: \"color(display-p3 0.938 0.885 0.802)\",\n};\nconst brownDarkP3A = {\n    brownA1: \"color(display-p3 0.855 0.071 0 / 0.005)\",\n    brownA2: \"color(display-p3 0.98 0.706 0.525 / 0.043)\",\n    brownA3: \"color(display-p3 0.996 0.745 0.576 / 0.093)\",\n    brownA4: \"color(display-p3 1 0.765 0.592 / 0.135)\",\n    brownA5: \"color(display-p3 1 0.761 0.588 / 0.181)\",\n    brownA6: \"color(display-p3 1 0.773 0.592 / 0.24)\",\n    brownA7: \"color(display-p3 0.996 0.776 0.58 / 0.32)\",\n    brownA8: \"color(display-p3 1 0.78 0.573 / 0.433)\",\n    brownA9: \"color(display-p3 1 0.769 0.549 / 0.627)\",\n    brownA10: \"color(display-p3 1 0.792 0.596 / 0.677)\",\n    brownA11: \"color(display-p3 0.835 0.715 0.597)\",\n    brownA12: \"color(display-p3 0.938 0.885 0.802)\",\n};\nconst bronzeDark = {\n    bronze1: \"#141110\",\n    bronze2: \"#1c1917\",\n    bronze3: \"#262220\",\n    bronze4: \"#302a27\",\n    bronze5: \"#3b3330\",\n    bronze6: \"#493e3a\",\n    bronze7: \"#5a4c47\",\n    bronze8: \"#6f5f58\",\n    bronze9: \"#a18072\",\n    bronze10: \"#ae8c7e\",\n    bronze11: \"#d4b3a5\",\n    bronze12: \"#ede0d9\",\n};\nconst bronzeDarkA = {\n    bronzeA1: \"#d1110004\",\n    bronzeA2: \"#fbbc910c\",\n    bronzeA3: \"#faceb817\",\n    bronzeA4: \"#facdb622\",\n    bronzeA5: \"#ffd2c12d\",\n    bronzeA6: \"#ffd1c03c\",\n    bronzeA7: \"#fdd0c04f\",\n    bronzeA8: \"#ffd6c565\",\n    bronzeA9: \"#fec7b09b\",\n    bronzeA10: \"#fecab5a9\",\n    bronzeA11: \"#ffd7c6d1\",\n    bronzeA12: \"#fff1e9ec\",\n};\nconst bronzeDarkP3 = {\n    bronze1: \"color(display-p3 0.076 0.067 0.063)\",\n    bronze2: \"color(display-p3 0.106 0.097 0.093)\",\n    bronze3: \"color(display-p3 0.147 0.132 0.125)\",\n    bronze4: \"color(display-p3 0.185 0.166 0.156)\",\n    bronze5: \"color(display-p3 0.227 0.202 0.19)\",\n    bronze6: \"color(display-p3 0.278 0.246 0.23)\",\n    bronze7: \"color(display-p3 0.343 0.302 0.281)\",\n    bronze8: \"color(display-p3 0.426 0.374 0.347)\",\n    bronze9: \"color(display-p3 0.611 0.507 0.455)\",\n    bronze10: \"color(display-p3 0.66 0.556 0.504)\",\n    bronze11: \"color(display-p3 0.81 0.707 0.655)\",\n    bronze12: \"color(display-p3 0.921 0.88 0.854)\",\n};\nconst bronzeDarkP3A = {\n    bronzeA1: \"color(display-p3 0.941 0.067 0 / 0.009)\",\n    bronzeA2: \"color(display-p3 0.98 0.8 0.706 / 0.043)\",\n    bronzeA3: \"color(display-p3 0.988 0.851 0.761 / 0.085)\",\n    bronzeA4: \"color(display-p3 0.996 0.839 0.78 / 0.127)\",\n    bronzeA5: \"color(display-p3 0.996 0.863 0.773 / 0.173)\",\n    bronzeA6: \"color(display-p3 1 0.863 0.796 / 0.227)\",\n    bronzeA7: \"color(display-p3 1 0.867 0.8 / 0.295)\",\n    bronzeA8: \"color(display-p3 1 0.859 0.788 / 0.387)\",\n    bronzeA9: \"color(display-p3 1 0.82 0.733 / 0.585)\",\n    bronzeA10: \"color(display-p3 1 0.839 0.761 / 0.635)\",\n    bronzeA11: \"color(display-p3 0.81 0.707 0.655)\",\n    bronzeA12: \"color(display-p3 0.921 0.88 0.854)\",\n};\nconst goldDark = {\n    gold1: \"#121211\",\n    gold2: \"#1b1a17\",\n    gold3: \"#24231f\",\n    gold4: \"#2d2b26\",\n    gold5: \"#38352e\",\n    gold6: \"#444039\",\n    gold7: \"#544f46\",\n    gold8: \"#696256\",\n    gold9: \"#978365\",\n    gold10: \"#a39073\",\n    gold11: \"#cbb99f\",\n    gold12: \"#e8e2d9\",\n};\nconst goldDarkA = {\n    goldA1: \"#91911102\",\n    goldA2: \"#f9e29d0b\",\n    goldA3: \"#f8ecbb15\",\n    goldA4: \"#ffeec41e\",\n    goldA5: \"#feecc22a\",\n    goldA6: \"#feebcb37\",\n    goldA7: \"#ffedcd48\",\n    goldA8: \"#fdeaca5f\",\n    goldA9: \"#ffdba690\",\n    goldA10: \"#fedfb09d\",\n    goldA11: \"#fee7c6c8\",\n    goldA12: \"#fef7ede7\",\n};\nconst goldDarkP3 = {\n    gold1: \"color(display-p3 0.071 0.071 0.067)\",\n    gold2: \"color(display-p3 0.104 0.101 0.09)\",\n    gold3: \"color(display-p3 0.141 0.136 0.122)\",\n    gold4: \"color(display-p3 0.177 0.17 0.152)\",\n    gold5: \"color(display-p3 0.217 0.207 0.185)\",\n    gold6: \"color(display-p3 0.265 0.252 0.225)\",\n    gold7: \"color(display-p3 0.327 0.31 0.277)\",\n    gold8: \"color(display-p3 0.407 0.384 0.342)\",\n    gold9: \"color(display-p3 0.579 0.517 0.41)\",\n    gold10: \"color(display-p3 0.628 0.566 0.463)\",\n    gold11: \"color(display-p3 0.784 0.728 0.635)\",\n    gold12: \"color(display-p3 0.906 0.887 0.855)\",\n};\nconst goldDarkP3A = {\n    goldA1: \"color(display-p3 0.855 0.855 0.071 / 0.005)\",\n    goldA2: \"color(display-p3 0.98 0.89 0.616 / 0.043)\",\n    goldA3: \"color(display-p3 1 0.949 0.753 / 0.08)\",\n    goldA4: \"color(display-p3 1 0.933 0.8 / 0.118)\",\n    goldA5: \"color(display-p3 1 0.949 0.804 / 0.16)\",\n    goldA6: \"color(display-p3 1 0.925 0.8 / 0.215)\",\n    goldA7: \"color(display-p3 1 0.945 0.831 / 0.278)\",\n    goldA8: \"color(display-p3 1 0.937 0.82 / 0.366)\",\n    goldA9: \"color(display-p3 0.996 0.882 0.69 / 0.551)\",\n    goldA10: \"color(display-p3 1 0.894 0.725 / 0.601)\",\n    goldA11: \"color(display-p3 0.784 0.728 0.635)\",\n    goldA12: \"color(display-p3 0.906 0.887 0.855)\",\n};\nconst skyDark = {\n    sky1: \"#0d141f\",\n    sky2: \"#111a27\",\n    sky3: \"#112840\",\n    sky4: \"#113555\",\n    sky5: \"#154467\",\n    sky6: \"#1b537b\",\n    sky7: \"#1f6692\",\n    sky8: \"#197cae\",\n    sky9: \"#7ce2fe\",\n    sky10: \"#a8eeff\",\n    sky11: \"#75c7f0\",\n    sky12: \"#c2f3ff\",\n};\nconst skyDarkA = {\n    skyA1: \"#0044ff0f\",\n    skyA2: \"#1171fb18\",\n    skyA3: \"#1184fc33\",\n    skyA4: \"#128fff49\",\n    skyA5: \"#1c9dfd5d\",\n    skyA6: \"#28a5ff72\",\n    skyA7: \"#2badfe8b\",\n    skyA8: \"#1db2fea9\",\n    skyA9: \"#7ce3fffe\",\n    skyA10: \"#a8eeff\",\n    skyA11: \"#7cd3ffef\",\n    skyA12: \"#c2f3ff\",\n};\nconst skyDarkP3 = {\n    sky1: \"color(display-p3 0.056 0.078 0.116)\",\n    sky2: \"color(display-p3 0.075 0.101 0.149)\",\n    sky3: \"color(display-p3 0.089 0.154 0.244)\",\n    sky4: \"color(display-p3 0.106 0.207 0.323)\",\n    sky5: \"color(display-p3 0.135 0.261 0.394)\",\n    sky6: \"color(display-p3 0.17 0.322 0.469)\",\n    sky7: \"color(display-p3 0.205 0.394 0.557)\",\n    sky8: \"color(display-p3 0.232 0.48 0.665)\",\n    sky9: \"color(display-p3 0.585 0.877 0.983)\",\n    sky10: \"color(display-p3 0.718 0.925 0.991)\",\n    sky11: \"color(display-p3 0.536 0.772 0.924)\",\n    sky12: \"color(display-p3 0.799 0.947 0.993)\",\n};\nconst skyDarkP3A = {\n    skyA1: \"color(display-p3 0 0.282 0.996 / 0.055)\",\n    skyA2: \"color(display-p3 0.157 0.467 0.992 / 0.089)\",\n    skyA3: \"color(display-p3 0.192 0.522 0.996 / 0.19)\",\n    skyA4: \"color(display-p3 0.212 0.584 1 / 0.274)\",\n    skyA5: \"color(display-p3 0.259 0.631 1 / 0.349)\",\n    skyA6: \"color(display-p3 0.302 0.655 1 / 0.433)\",\n    skyA7: \"color(display-p3 0.329 0.686 1 / 0.526)\",\n    skyA8: \"color(display-p3 0.325 0.71 1 / 0.643)\",\n    skyA9: \"color(display-p3 0.592 0.894 1 / 0.984)\",\n    skyA10: \"color(display-p3 0.722 0.933 1 / 0.992)\",\n    skyA11: \"color(display-p3 0.536 0.772 0.924)\",\n    skyA12: \"color(display-p3 0.799 0.947 0.993)\",\n};\nconst mintDark = {\n    mint1: \"#0e1515\",\n    mint2: \"#0f1b1b\",\n    mint3: \"#092c2b\",\n    mint4: \"#003a38\",\n    mint5: \"#004744\",\n    mint6: \"#105650\",\n    mint7: \"#1e685f\",\n    mint8: \"#277f70\",\n    mint9: \"#86ead4\",\n    mint10: \"#a8f5e5\",\n    mint11: \"#58d5ba\",\n    mint12: \"#c4f5e1\",\n};\nconst mintDarkA = {\n    mintA1: \"#00dede05\",\n    mintA2: \"#00f9f90b\",\n    mintA3: \"#00fff61d\",\n    mintA4: \"#00fff42c\",\n    mintA5: \"#00fff23a\",\n    mintA6: \"#0effeb4a\",\n    mintA7: \"#34fde55e\",\n    mintA8: \"#41ffdf76\",\n    mintA9: \"#92ffe7e9\",\n    mintA10: \"#aefeedf5\",\n    mintA11: \"#67ffded2\",\n    mintA12: \"#cbfee9f5\",\n};\nconst mintDarkP3 = {\n    mint1: \"color(display-p3 0.059 0.082 0.081)\",\n    mint2: \"color(display-p3 0.068 0.104 0.105)\",\n    mint3: \"color(display-p3 0.077 0.17 0.168)\",\n    mint4: \"color(display-p3 0.068 0.224 0.22)\",\n    mint5: \"color(display-p3 0.104 0.275 0.264)\",\n    mint6: \"color(display-p3 0.154 0.332 0.313)\",\n    mint7: \"color(display-p3 0.207 0.403 0.373)\",\n    mint8: \"color(display-p3 0.258 0.49 0.441)\",\n    mint9: \"color(display-p3 0.62 0.908 0.834)\",\n    mint10: \"color(display-p3 0.725 0.954 0.898)\",\n    mint11: \"color(display-p3 0.482 0.825 0.733)\",\n    mint12: \"color(display-p3 0.807 0.955 0.887)\",\n};\nconst mintDarkP3A = {\n    mintA1: \"color(display-p3 0 0.992 0.992 / 0.017)\",\n    mintA2: \"color(display-p3 0.071 0.98 0.98 / 0.043)\",\n    mintA3: \"color(display-p3 0.176 0.996 0.996 / 0.11)\",\n    mintA4: \"color(display-p3 0.071 0.996 0.973 / 0.169)\",\n    mintA5: \"color(display-p3 0.243 1 0.949 / 0.223)\",\n    mintA6: \"color(display-p3 0.369 1 0.933 / 0.286)\",\n    mintA7: \"color(display-p3 0.459 1 0.914 / 0.362)\",\n    mintA8: \"color(display-p3 0.49 1 0.89 / 0.454)\",\n    mintA9: \"color(display-p3 0.678 0.996 0.914 / 0.904)\",\n    mintA10: \"color(display-p3 0.761 1 0.941 / 0.95)\",\n    mintA11: \"color(display-p3 0.482 0.825 0.733)\",\n    mintA12: \"color(display-p3 0.807 0.955 0.887)\",\n};\nconst limeDark = {\n    lime1: \"#11130c\",\n    lime2: \"#151a10\",\n    lime3: \"#1f2917\",\n    lime4: \"#29371d\",\n    lime5: \"#334423\",\n    lime6: \"#3d522a\",\n    lime7: \"#496231\",\n    lime8: \"#577538\",\n    lime9: \"#bdee63\",\n    lime10: \"#d4ff70\",\n    lime11: \"#bde56c\",\n    lime12: \"#e3f7ba\",\n};\nconst limeDarkA = {\n    limeA1: \"#11bb0003\",\n    limeA2: \"#78f7000a\",\n    limeA3: \"#9bfd4c1a\",\n    limeA4: \"#a7fe5c29\",\n    limeA5: \"#affe6537\",\n    limeA6: \"#b2fe6d46\",\n    limeA7: \"#b6ff6f57\",\n    limeA8: \"#b6fd6d6c\",\n    limeA9: \"#caff69ed\",\n    limeA10: \"#d4ff70\",\n    limeA11: \"#d1fe77e4\",\n    limeA12: \"#e9febff7\",\n};\nconst limeDarkP3 = {\n    lime1: \"color(display-p3 0.067 0.073 0.048)\",\n    lime2: \"color(display-p3 0.086 0.1 0.067)\",\n    lime3: \"color(display-p3 0.13 0.16 0.099)\",\n    lime4: \"color(display-p3 0.172 0.214 0.126)\",\n    lime5: \"color(display-p3 0.213 0.266 0.153)\",\n    lime6: \"color(display-p3 0.257 0.321 0.182)\",\n    lime7: \"color(display-p3 0.307 0.383 0.215)\",\n    lime8: \"color(display-p3 0.365 0.456 0.25)\",\n    lime9: \"color(display-p3 0.78 0.928 0.466)\",\n    lime10: \"color(display-p3 0.865 0.995 0.519)\",\n    lime11: \"color(display-p3 0.771 0.893 0.485)\",\n    lime12: \"color(display-p3 0.905 0.966 0.753)\",\n};\nconst limeDarkP3A = {\n    limeA1: \"color(display-p3 0.067 0.941 0 / 0.009)\",\n    limeA2: \"color(display-p3 0.584 0.996 0.071 / 0.038)\",\n    limeA3: \"color(display-p3 0.69 1 0.38 / 0.101)\",\n    limeA4: \"color(display-p3 0.729 1 0.435 / 0.16)\",\n    limeA5: \"color(display-p3 0.745 1 0.471 / 0.215)\",\n    limeA6: \"color(display-p3 0.769 1 0.482 / 0.274)\",\n    limeA7: \"color(display-p3 0.769 1 0.506 / 0.341)\",\n    limeA8: \"color(display-p3 0.784 1 0.51 / 0.416)\",\n    limeA9: \"color(display-p3 0.839 1 0.502 / 0.925)\",\n    limeA10: \"color(display-p3 0.871 1 0.522 / 0.996)\",\n    limeA11: \"color(display-p3 0.771 0.893 0.485)\",\n    limeA12: \"color(display-p3 0.905 0.966 0.753)\",\n};\nconst yellowDark = {\n    yellow1: \"#14120b\",\n    yellow2: \"#1b180f\",\n    yellow3: \"#2d2305\",\n    yellow4: \"#362b00\",\n    yellow5: \"#433500\",\n    yellow6: \"#524202\",\n    yellow7: \"#665417\",\n    yellow8: \"#836a21\",\n    yellow9: \"#ffe629\",\n    yellow10: \"#ffff57\",\n    yellow11: \"#f5e147\",\n    yellow12: \"#f6eeb4\",\n};\nconst yellowDarkA = {\n    yellowA1: \"#d1510004\",\n    yellowA2: \"#f9b4000b\",\n    yellowA3: \"#ffaa001e\",\n    yellowA4: \"#fdb70028\",\n    yellowA5: \"#febb0036\",\n    yellowA6: \"#fec40046\",\n    yellowA7: \"#fdcb225c\",\n    yellowA8: \"#fdca327b\",\n    yellowA9: \"#ffe629\",\n    yellowA10: \"#ffff57\",\n    yellowA11: \"#fee949f5\",\n    yellowA12: \"#fef6baf6\",\n};\nconst yellowDarkP3 = {\n    yellow1: \"color(display-p3 0.078 0.069 0.047)\",\n    yellow2: \"color(display-p3 0.103 0.094 0.063)\",\n    yellow3: \"color(display-p3 0.168 0.137 0.039)\",\n    yellow4: \"color(display-p3 0.209 0.169 0)\",\n    yellow5: \"color(display-p3 0.255 0.209 0)\",\n    yellow6: \"color(display-p3 0.31 0.261 0.07)\",\n    yellow7: \"color(display-p3 0.389 0.331 0.135)\",\n    yellow8: \"color(display-p3 0.497 0.42 0.182)\",\n    yellow9: \"color(display-p3 1 0.92 0.22)\",\n    yellow10: \"color(display-p3 1 1 0.456)\",\n    yellow11: \"color(display-p3 0.948 0.885 0.392)\",\n    yellow12: \"color(display-p3 0.959 0.934 0.731)\",\n};\nconst yellowDarkP3A = {\n    yellowA1: \"color(display-p3 0.973 0.369 0 / 0.013)\",\n    yellowA2: \"color(display-p3 0.996 0.792 0 / 0.038)\",\n    yellowA3: \"color(display-p3 0.996 0.71 0 / 0.11)\",\n    yellowA4: \"color(display-p3 0.996 0.741 0 / 0.152)\",\n    yellowA5: \"color(display-p3 0.996 0.765 0 / 0.202)\",\n    yellowA6: \"color(display-p3 0.996 0.816 0.082 / 0.261)\",\n    yellowA7: \"color(display-p3 1 0.831 0.263 / 0.345)\",\n    yellowA8: \"color(display-p3 1 0.831 0.314 / 0.463)\",\n    yellowA9: \"color(display-p3 1 0.922 0.22)\",\n    yellowA10: \"color(display-p3 1 1 0.455)\",\n    yellowA11: \"color(display-p3 0.948 0.885 0.392)\",\n    yellowA12: \"color(display-p3 0.959 0.934 0.731)\",\n};\nconst amberDark = {\n    amber1: \"#16120c\",\n    amber2: \"#1d180f\",\n    amber3: \"#302008\",\n    amber4: \"#3f2700\",\n    amber5: \"#4d3000\",\n    amber6: \"#5c3d05\",\n    amber7: \"#714f19\",\n    amber8: \"#8f6424\",\n    amber9: \"#ffc53d\",\n    amber10: \"#ffd60a\",\n    amber11: \"#ffca16\",\n    amber12: \"#ffe7b3\",\n};\nconst amberDarkA = {\n    amberA1: \"#e63c0006\",\n    amberA2: \"#fd9b000d\",\n    amberA3: \"#fa820022\",\n    amberA4: \"#fc820032\",\n    amberA5: \"#fd8b0041\",\n    amberA6: \"#fd9b0051\",\n    amberA7: \"#ffab2567\",\n    amberA8: \"#ffae3587\",\n    amberA9: \"#ffc53d\",\n    amberA10: \"#ffd60a\",\n    amberA11: \"#ffca16\",\n    amberA12: \"#ffe7b3\",\n};\nconst amberDarkP3 = {\n    amber1: \"color(display-p3 0.082 0.07 0.05)\",\n    amber2: \"color(display-p3 0.111 0.094 0.064)\",\n    amber3: \"color(display-p3 0.178 0.128 0.049)\",\n    amber4: \"color(display-p3 0.239 0.156 0)\",\n    amber5: \"color(display-p3 0.29 0.193 0)\",\n    amber6: \"color(display-p3 0.344 0.245 0.076)\",\n    amber7: \"color(display-p3 0.422 0.314 0.141)\",\n    amber8: \"color(display-p3 0.535 0.399 0.189)\",\n    amber9: \"color(display-p3 1 0.77 0.26)\",\n    amber10: \"color(display-p3 1 0.87 0.15)\",\n    amber11: \"color(display-p3 1 0.8 0.29)\",\n    amber12: \"color(display-p3 0.984 0.909 0.726)\",\n};\nconst amberDarkP3A = {\n    amberA1: \"color(display-p3 0.992 0.298 0 / 0.017)\",\n    amberA2: \"color(display-p3 0.988 0.651 0 / 0.047)\",\n    amberA3: \"color(display-p3 1 0.6 0 / 0.118)\",\n    amberA4: \"color(display-p3 1 0.557 0 / 0.185)\",\n    amberA5: \"color(display-p3 1 0.592 0 / 0.24)\",\n    amberA6: \"color(display-p3 1 0.659 0.094 / 0.299)\",\n    amberA7: \"color(display-p3 1 0.714 0.263 / 0.383)\",\n    amberA8: \"color(display-p3 0.996 0.729 0.306 / 0.5)\",\n    amberA9: \"color(display-p3 1 0.769 0.259)\",\n    amberA10: \"color(display-p3 1 0.871 0.149)\",\n    amberA11: \"color(display-p3 1 0.8 0.29)\",\n    amberA12: \"color(display-p3 0.984 0.909 0.726)\",\n};\nconst orangeDark = {\n    orange1: \"#17120e\",\n    orange2: \"#1e160f\",\n    orange3: \"#331e0b\",\n    orange4: \"#462100\",\n    orange5: \"#562800\",\n    orange6: \"#66350c\",\n    orange7: \"#7e451d\",\n    orange8: \"#a35829\",\n    orange9: \"#f76b15\",\n    orange10: \"#ff801f\",\n    orange11: \"#ffa057\",\n    orange12: \"#ffe0c2\",\n};\nconst orangeDarkA = {\n    orangeA1: \"#ec360007\",\n    orangeA2: \"#fe6d000e\",\n    orangeA3: \"#fb6a0025\",\n    orangeA4: \"#ff590039\",\n    orangeA5: \"#ff61004a\",\n    orangeA6: \"#fd75045c\",\n    orangeA7: \"#ff832c75\",\n    orangeA8: \"#fe84389d\",\n    orangeA9: \"#fe6d15f7\",\n    orangeA10: \"#ff801f\",\n    orangeA11: \"#ffa057\",\n    orangeA12: \"#ffe0c2\",\n};\nconst orangeDarkP3 = {\n    orange1: \"color(display-p3 0.088 0.07 0.057)\",\n    orange2: \"color(display-p3 0.113 0.089 0.061)\",\n    orange3: \"color(display-p3 0.189 0.12 0.056)\",\n    orange4: \"color(display-p3 0.262 0.132 0)\",\n    orange5: \"color(display-p3 0.315 0.168 0.016)\",\n    orange6: \"color(display-p3 0.376 0.219 0.088)\",\n    orange7: \"color(display-p3 0.465 0.283 0.147)\",\n    orange8: \"color(display-p3 0.601 0.359 0.201)\",\n    orange9: \"color(display-p3 0.9 0.45 0.2)\",\n    orange10: \"color(display-p3 0.98 0.51 0.23)\",\n    orange11: \"color(display-p3 1 0.63 0.38)\",\n    orange12: \"color(display-p3 0.98 0.883 0.775)\",\n};\nconst orangeDarkP3A = {\n    orangeA1: \"color(display-p3 0.961 0.247 0 / 0.022)\",\n    orangeA2: \"color(display-p3 0.992 0.529 0 / 0.051)\",\n    orangeA3: \"color(display-p3 0.996 0.486 0 / 0.131)\",\n    orangeA4: \"color(display-p3 0.996 0.384 0 / 0.211)\",\n    orangeA5: \"color(display-p3 1 0.455 0 / 0.265)\",\n    orangeA6: \"color(display-p3 1 0.529 0.129 / 0.332)\",\n    orangeA7: \"color(display-p3 1 0.569 0.251 / 0.429)\",\n    orangeA8: \"color(display-p3 1 0.584 0.302 / 0.572)\",\n    orangeA9: \"color(display-p3 1 0.494 0.216 / 0.895)\",\n    orangeA10: \"color(display-p3 1 0.522 0.235 / 0.979)\",\n    orangeA11: \"color(display-p3 1 0.63 0.38)\",\n    orangeA12: \"color(display-p3 0.98 0.883 0.775)\",\n};\n\nconst gray = {\n    gray1: \"#fcfcfc\",\n    gray2: \"#f9f9f9\",\n    gray3: \"#f0f0f0\",\n    gray4: \"#e8e8e8\",\n    gray5: \"#e0e0e0\",\n    gray6: \"#d9d9d9\",\n    gray7: \"#cecece\",\n    gray8: \"#bbbbbb\",\n    gray9: \"#8d8d8d\",\n    gray10: \"#838383\",\n    gray11: \"#646464\",\n    gray12: \"#202020\",\n};\nconst grayA = {\n    grayA1: \"#00000003\",\n    grayA2: \"#00000006\",\n    grayA3: \"#0000000f\",\n    grayA4: \"#00000017\",\n    grayA5: \"#0000001f\",\n    grayA6: \"#00000026\",\n    grayA7: \"#00000031\",\n    grayA8: \"#00000044\",\n    grayA9: \"#00000072\",\n    grayA10: \"#0000007c\",\n    grayA11: \"#0000009b\",\n    grayA12: \"#000000df\",\n};\nconst grayP3 = {\n    gray1: \"color(display-p3 0.988 0.988 0.988)\",\n    gray2: \"color(display-p3 0.975 0.975 0.975)\",\n    gray3: \"color(display-p3 0.939 0.939 0.939)\",\n    gray4: \"color(display-p3 0.908 0.908 0.908)\",\n    gray5: \"color(display-p3 0.88 0.88 0.88)\",\n    gray6: \"color(display-p3 0.849 0.849 0.849)\",\n    gray7: \"color(display-p3 0.807 0.807 0.807)\",\n    gray8: \"color(display-p3 0.732 0.732 0.732)\",\n    gray9: \"color(display-p3 0.553 0.553 0.553)\",\n    gray10: \"color(display-p3 0.512 0.512 0.512)\",\n    gray11: \"color(display-p3 0.392 0.392 0.392)\",\n    gray12: \"color(display-p3 0.125 0.125 0.125)\",\n};\nconst grayP3A = {\n    grayA1: \"color(display-p3 0 0 0 / 0.012)\",\n    grayA2: \"color(display-p3 0 0 0 / 0.024)\",\n    grayA3: \"color(display-p3 0 0 0 / 0.063)\",\n    grayA4: \"color(display-p3 0 0 0 / 0.09)\",\n    grayA5: \"color(display-p3 0 0 0 / 0.122)\",\n    grayA6: \"color(display-p3 0 0 0 / 0.153)\",\n    grayA7: \"color(display-p3 0 0 0 / 0.192)\",\n    grayA8: \"color(display-p3 0 0 0 / 0.267)\",\n    grayA9: \"color(display-p3 0 0 0 / 0.447)\",\n    grayA10: \"color(display-p3 0 0 0 / 0.486)\",\n    grayA11: \"color(display-p3 0 0 0 / 0.608)\",\n    grayA12: \"color(display-p3 0 0 0 / 0.875)\",\n};\nconst mauve = {\n    mauve1: \"#fdfcfd\",\n    mauve2: \"#faf9fb\",\n    mauve3: \"#f2eff3\",\n    mauve4: \"#eae7ec\",\n    mauve5: \"#e3dfe6\",\n    mauve6: \"#dbd8e0\",\n    mauve7: \"#d0cdd7\",\n    mauve8: \"#bcbac7\",\n    mauve9: \"#8e8c99\",\n    mauve10: \"#84828e\",\n    mauve11: \"#65636d\",\n    mauve12: \"#211f26\",\n};\nconst mauveA = {\n    mauveA1: \"#55005503\",\n    mauveA2: \"#2b005506\",\n    mauveA3: \"#30004010\",\n    mauveA4: \"#20003618\",\n    mauveA5: \"#20003820\",\n    mauveA6: \"#14003527\",\n    mauveA7: \"#10003332\",\n    mauveA8: \"#08003145\",\n    mauveA9: \"#05001d73\",\n    mauveA10: \"#0500197d\",\n    mauveA11: \"#0400119c\",\n    mauveA12: \"#020008e0\",\n};\nconst mauveP3 = {\n    mauve1: \"color(display-p3 0.991 0.988 0.992)\",\n    mauve2: \"color(display-p3 0.98 0.976 0.984)\",\n    mauve3: \"color(display-p3 0.946 0.938 0.952)\",\n    mauve4: \"color(display-p3 0.915 0.906 0.925)\",\n    mauve5: \"color(display-p3 0.886 0.876 0.901)\",\n    mauve6: \"color(display-p3 0.856 0.846 0.875)\",\n    mauve7: \"color(display-p3 0.814 0.804 0.84)\",\n    mauve8: \"color(display-p3 0.735 0.728 0.777)\",\n    mauve9: \"color(display-p3 0.555 0.549 0.596)\",\n    mauve10: \"color(display-p3 0.514 0.508 0.552)\",\n    mauve11: \"color(display-p3 0.395 0.388 0.424)\",\n    mauve12: \"color(display-p3 0.128 0.122 0.147)\",\n};\nconst mauveP3A = {\n    mauveA1: \"color(display-p3 0.349 0.024 0.349 / 0.012)\",\n    mauveA2: \"color(display-p3 0.184 0.024 0.349 / 0.024)\",\n    mauveA3: \"color(display-p3 0.129 0.008 0.255 / 0.063)\",\n    mauveA4: \"color(display-p3 0.094 0.012 0.216 / 0.095)\",\n    mauveA5: \"color(display-p3 0.098 0.008 0.224 / 0.126)\",\n    mauveA6: \"color(display-p3 0.055 0.004 0.18 / 0.153)\",\n    mauveA7: \"color(display-p3 0.067 0.008 0.184 / 0.197)\",\n    mauveA8: \"color(display-p3 0.02 0.004 0.176 / 0.271)\",\n    mauveA9: \"color(display-p3 0.02 0.004 0.106 / 0.451)\",\n    mauveA10: \"color(display-p3 0.012 0.004 0.09 / 0.491)\",\n    mauveA11: \"color(display-p3 0.016 0 0.059 / 0.612)\",\n    mauveA12: \"color(display-p3 0.008 0 0.027 / 0.879)\",\n};\nconst slate = {\n    slate1: \"#fcfcfd\",\n    slate2: \"#f9f9fb\",\n    slate3: \"#f0f0f3\",\n    slate4: \"#e8e8ec\",\n    slate5: \"#e0e1e6\",\n    slate6: \"#d9d9e0\",\n    slate7: \"#cdced6\",\n    slate8: \"#b9bbc6\",\n    slate9: \"#8b8d98\",\n    slate10: \"#80838d\",\n    slate11: \"#60646c\",\n    slate12: \"#1c2024\",\n};\nconst slateA = {\n    slateA1: \"#00005503\",\n    slateA2: \"#00005506\",\n    slateA3: \"#0000330f\",\n    slateA4: \"#00002d17\",\n    slateA5: \"#0009321f\",\n    slateA6: \"#00002f26\",\n    slateA7: \"#00062e32\",\n    slateA8: \"#00083046\",\n    slateA9: \"#00051d74\",\n    slateA10: \"#00071b7f\",\n    slateA11: \"#0007149f\",\n    slateA12: \"#000509e3\",\n};\nconst slateP3 = {\n    slate1: \"color(display-p3 0.988 0.988 0.992)\",\n    slate2: \"color(display-p3 0.976 0.976 0.984)\",\n    slate3: \"color(display-p3 0.94 0.941 0.953)\",\n    slate4: \"color(display-p3 0.908 0.909 0.925)\",\n    slate5: \"color(display-p3 0.88 0.881 0.901)\",\n    slate6: \"color(display-p3 0.85 0.852 0.876)\",\n    slate7: \"color(display-p3 0.805 0.808 0.838)\",\n    slate8: \"color(display-p3 0.727 0.733 0.773)\",\n    slate9: \"color(display-p3 0.547 0.553 0.592)\",\n    slate10: \"color(display-p3 0.503 0.512 0.549)\",\n    slate11: \"color(display-p3 0.379 0.392 0.421)\",\n    slate12: \"color(display-p3 0.113 0.125 0.14)\",\n};\nconst slateP3A = {\n    slateA1: \"color(display-p3 0.024 0.024 0.349 / 0.012)\",\n    slateA2: \"color(display-p3 0.024 0.024 0.349 / 0.024)\",\n    slateA3: \"color(display-p3 0.004 0.004 0.204 / 0.059)\",\n    slateA4: \"color(display-p3 0.012 0.012 0.184 / 0.091)\",\n    slateA5: \"color(display-p3 0.004 0.039 0.2 / 0.122)\",\n    slateA6: \"color(display-p3 0.008 0.008 0.165 / 0.15)\",\n    slateA7: \"color(display-p3 0.008 0.027 0.184 / 0.197)\",\n    slateA8: \"color(display-p3 0.004 0.031 0.176 / 0.275)\",\n    slateA9: \"color(display-p3 0.004 0.02 0.106 / 0.455)\",\n    slateA10: \"color(display-p3 0.004 0.027 0.098 / 0.499)\",\n    slateA11: \"color(display-p3 0 0.02 0.063 / 0.62)\",\n    slateA12: \"color(display-p3 0 0.012 0.031 / 0.887)\",\n};\nconst sage = {\n    sage1: \"#fbfdfc\",\n    sage2: \"#f7f9f8\",\n    sage3: \"#eef1f0\",\n    sage4: \"#e6e9e8\",\n    sage5: \"#dfe2e0\",\n    sage6: \"#d7dad9\",\n    sage7: \"#cbcfcd\",\n    sage8: \"#b8bcba\",\n    sage9: \"#868e8b\",\n    sage10: \"#7c8481\",\n    sage11: \"#5f6563\",\n    sage12: \"#1a211e\",\n};\nconst sageA = {\n    sageA1: \"#00804004\",\n    sageA2: \"#00402008\",\n    sageA3: \"#002d1e11\",\n    sageA4: \"#001f1519\",\n    sageA5: \"#00180820\",\n    sageA6: \"#00140d28\",\n    sageA7: \"#00140a34\",\n    sageA8: \"#000f0847\",\n    sageA9: \"#00110b79\",\n    sageA10: \"#00100a83\",\n    sageA11: \"#000a07a0\",\n    sageA12: \"#000805e5\",\n};\nconst sageP3 = {\n    sage1: \"color(display-p3 0.986 0.992 0.988)\",\n    sage2: \"color(display-p3 0.97 0.977 0.974)\",\n    sage3: \"color(display-p3 0.935 0.944 0.94)\",\n    sage4: \"color(display-p3 0.904 0.913 0.909)\",\n    sage5: \"color(display-p3 0.875 0.885 0.88)\",\n    sage6: \"color(display-p3 0.844 0.854 0.849)\",\n    sage7: \"color(display-p3 0.8 0.811 0.806)\",\n    sage8: \"color(display-p3 0.725 0.738 0.732)\",\n    sage9: \"color(display-p3 0.531 0.556 0.546)\",\n    sage10: \"color(display-p3 0.492 0.515 0.506)\",\n    sage11: \"color(display-p3 0.377 0.395 0.389)\",\n    sage12: \"color(display-p3 0.107 0.129 0.118)\",\n};\nconst sageP3A = {\n    sageA1: \"color(display-p3 0.024 0.514 0.267 / 0.016)\",\n    sageA2: \"color(display-p3 0.02 0.267 0.145 / 0.032)\",\n    sageA3: \"color(display-p3 0.008 0.184 0.125 / 0.067)\",\n    sageA4: \"color(display-p3 0.012 0.094 0.051 / 0.095)\",\n    sageA5: \"color(display-p3 0.008 0.098 0.035 / 0.126)\",\n    sageA6: \"color(display-p3 0.004 0.078 0.027 / 0.157)\",\n    sageA7: \"color(display-p3 0 0.059 0.039 / 0.2)\",\n    sageA8: \"color(display-p3 0.004 0.047 0.031 / 0.275)\",\n    sageA9: \"color(display-p3 0.004 0.059 0.035 / 0.471)\",\n    sageA10: \"color(display-p3 0 0.047 0.031 / 0.51)\",\n    sageA11: \"color(display-p3 0 0.031 0.02 / 0.624)\",\n    sageA12: \"color(display-p3 0 0.027 0.012 / 0.895)\",\n};\nconst olive = {\n    olive1: \"#fcfdfc\",\n    olive2: \"#f8faf8\",\n    olive3: \"#eff1ef\",\n    olive4: \"#e7e9e7\",\n    olive5: \"#dfe2df\",\n    olive6: \"#d7dad7\",\n    olive7: \"#cccfcc\",\n    olive8: \"#b9bcb8\",\n    olive9: \"#898e87\",\n    olive10: \"#7f847d\",\n    olive11: \"#60655f\",\n    olive12: \"#1d211c\",\n};\nconst oliveA = {\n    oliveA1: \"#00550003\",\n    oliveA2: \"#00490007\",\n    oliveA3: \"#00200010\",\n    oliveA4: \"#00160018\",\n    oliveA5: \"#00180020\",\n    oliveA6: \"#00140028\",\n    oliveA7: \"#000f0033\",\n    oliveA8: \"#040f0047\",\n    oliveA9: \"#050f0078\",\n    oliveA10: \"#040e0082\",\n    oliveA11: \"#020a00a0\",\n    oliveA12: \"#010600e3\",\n};\nconst oliveP3 = {\n    olive1: \"color(display-p3 0.989 0.992 0.989)\",\n    olive2: \"color(display-p3 0.974 0.98 0.973)\",\n    olive3: \"color(display-p3 0.939 0.945 0.937)\",\n    olive4: \"color(display-p3 0.907 0.914 0.905)\",\n    olive5: \"color(display-p3 0.878 0.885 0.875)\",\n    olive6: \"color(display-p3 0.846 0.855 0.843)\",\n    olive7: \"color(display-p3 0.803 0.812 0.8)\",\n    olive8: \"color(display-p3 0.727 0.738 0.723)\",\n    olive9: \"color(display-p3 0.541 0.556 0.532)\",\n    olive10: \"color(display-p3 0.5 0.515 0.491)\",\n    olive11: \"color(display-p3 0.38 0.395 0.374)\",\n    olive12: \"color(display-p3 0.117 0.129 0.111)\",\n};\nconst oliveP3A = {\n    oliveA1: \"color(display-p3 0.024 0.349 0.024 / 0.012)\",\n    oliveA2: \"color(display-p3 0.024 0.302 0.024 / 0.028)\",\n    oliveA3: \"color(display-p3 0.008 0.129 0.008 / 0.063)\",\n    oliveA4: \"color(display-p3 0.012 0.094 0.012 / 0.095)\",\n    oliveA5: \"color(display-p3 0.035 0.098 0.008 / 0.126)\",\n    oliveA6: \"color(display-p3 0.027 0.078 0.004 / 0.157)\",\n    oliveA7: \"color(display-p3 0.02 0.059 0 / 0.2)\",\n    oliveA8: \"color(display-p3 0.02 0.059 0.004 / 0.279)\",\n    oliveA9: \"color(display-p3 0.02 0.051 0.004 / 0.467)\",\n    oliveA10: \"color(display-p3 0.024 0.047 0 / 0.51)\",\n    oliveA11: \"color(display-p3 0.012 0.039 0 / 0.628)\",\n    oliveA12: \"color(display-p3 0.008 0.024 0 / 0.891)\",\n};\nconst sand = {\n    sand1: \"#fdfdfc\",\n    sand2: \"#f9f9f8\",\n    sand3: \"#f1f0ef\",\n    sand4: \"#e9e8e6\",\n    sand5: \"#e2e1de\",\n    sand6: \"#dad9d6\",\n    sand7: \"#cfceca\",\n    sand8: \"#bcbbb5\",\n    sand9: \"#8d8d86\",\n    sand10: \"#82827c\",\n    sand11: \"#63635e\",\n    sand12: \"#21201c\",\n};\nconst sandA = {\n    sandA1: \"#55550003\",\n    sandA2: \"#25250007\",\n    sandA3: \"#20100010\",\n    sandA4: \"#1f150019\",\n    sandA5: \"#1f180021\",\n    sandA6: \"#19130029\",\n    sandA7: \"#19140035\",\n    sandA8: \"#1915014a\",\n    sandA9: \"#0f0f0079\",\n    sandA10: \"#0c0c0083\",\n    sandA11: \"#080800a1\",\n    sandA12: \"#060500e3\",\n};\nconst sandP3 = {\n    sand1: \"color(display-p3 0.992 0.992 0.989)\",\n    sand2: \"color(display-p3 0.977 0.977 0.973)\",\n    sand3: \"color(display-p3 0.943 0.942 0.936)\",\n    sand4: \"color(display-p3 0.913 0.912 0.903)\",\n    sand5: \"color(display-p3 0.885 0.883 0.873)\",\n    sand6: \"color(display-p3 0.854 0.852 0.839)\",\n    sand7: \"color(display-p3 0.813 0.81 0.794)\",\n    sand8: \"color(display-p3 0.738 0.734 0.713)\",\n    sand9: \"color(display-p3 0.553 0.553 0.528)\",\n    sand10: \"color(display-p3 0.511 0.511 0.488)\",\n    sand11: \"color(display-p3 0.388 0.388 0.37)\",\n    sand12: \"color(display-p3 0.129 0.126 0.111)\",\n};\nconst sandP3A = {\n    sandA1: \"color(display-p3 0.349 0.349 0.024 / 0.012)\",\n    sandA2: \"color(display-p3 0.161 0.161 0.024 / 0.028)\",\n    sandA3: \"color(display-p3 0.067 0.067 0.008 / 0.063)\",\n    sandA4: \"color(display-p3 0.129 0.129 0.012 / 0.099)\",\n    sandA5: \"color(display-p3 0.098 0.067 0.008 / 0.126)\",\n    sandA6: \"color(display-p3 0.102 0.075 0.004 / 0.161)\",\n    sandA7: \"color(display-p3 0.098 0.098 0.004 / 0.208)\",\n    sandA8: \"color(display-p3 0.086 0.075 0.004 / 0.287)\",\n    sandA9: \"color(display-p3 0.051 0.051 0.004 / 0.471)\",\n    sandA10: \"color(display-p3 0.047 0.047 0 / 0.514)\",\n    sandA11: \"color(display-p3 0.031 0.031 0 / 0.632)\",\n    sandA12: \"color(display-p3 0.024 0.02 0 / 0.891)\",\n};\nconst tomato = {\n    tomato1: \"#fffcfc\",\n    tomato2: \"#fff8f7\",\n    tomato3: \"#feebe7\",\n    tomato4: \"#ffdcd3\",\n    tomato5: \"#ffcdc2\",\n    tomato6: \"#fdbdaf\",\n    tomato7: \"#f5a898\",\n    tomato8: \"#ec8e7b\",\n    tomato9: \"#e54d2e\",\n    tomato10: \"#dd4425\",\n    tomato11: \"#d13415\",\n    tomato12: \"#5c271f\",\n};\nconst tomatoA = {\n    tomatoA1: \"#ff000003\",\n    tomatoA2: \"#ff200008\",\n    tomatoA3: \"#f52b0018\",\n    tomatoA4: \"#ff35002c\",\n    tomatoA5: \"#ff2e003d\",\n    tomatoA6: \"#f92d0050\",\n    tomatoA7: \"#e7280067\",\n    tomatoA8: \"#db250084\",\n    tomatoA9: \"#df2600d1\",\n    tomatoA10: \"#d72400da\",\n    tomatoA11: \"#cd2200ea\",\n    tomatoA12: \"#460900e0\",\n};\nconst tomatoP3 = {\n    tomato1: \"color(display-p3 0.998 0.989 0.988)\",\n    tomato2: \"color(display-p3 0.994 0.974 0.969)\",\n    tomato3: \"color(display-p3 0.985 0.924 0.909)\",\n    tomato4: \"color(display-p3 0.996 0.868 0.835)\",\n    tomato5: \"color(display-p3 0.98 0.812 0.77)\",\n    tomato6: \"color(display-p3 0.953 0.75 0.698)\",\n    tomato7: \"color(display-p3 0.917 0.673 0.611)\",\n    tomato8: \"color(display-p3 0.875 0.575 0.502)\",\n    tomato9: \"color(display-p3 0.831 0.345 0.231)\",\n    tomato10: \"color(display-p3 0.802 0.313 0.2)\",\n    tomato11: \"color(display-p3 0.755 0.259 0.152)\",\n    tomato12: \"color(display-p3 0.335 0.165 0.132)\",\n};\nconst tomatoP3A = {\n    tomatoA1: \"color(display-p3 0.675 0.024 0.024 / 0.012)\",\n    tomatoA2: \"color(display-p3 0.757 0.145 0.02 / 0.032)\",\n    tomatoA3: \"color(display-p3 0.831 0.184 0.012 / 0.091)\",\n    tomatoA4: \"color(display-p3 0.976 0.192 0.004 / 0.165)\",\n    tomatoA5: \"color(display-p3 0.918 0.192 0.004 / 0.232)\",\n    tomatoA6: \"color(display-p3 0.847 0.173 0.004 / 0.302)\",\n    tomatoA7: \"color(display-p3 0.788 0.165 0.004 / 0.389)\",\n    tomatoA8: \"color(display-p3 0.749 0.153 0.004 / 0.499)\",\n    tomatoA9: \"color(display-p3 0.78 0.149 0 / 0.769)\",\n    tomatoA10: \"color(display-p3 0.757 0.141 0 / 0.8)\",\n    tomatoA11: \"color(display-p3 0.755 0.259 0.152)\",\n    tomatoA12: \"color(display-p3 0.335 0.165 0.132)\",\n};\nconst red = {\n    red1: \"#fffcfc\",\n    red2: \"#fff7f7\",\n    red3: \"#feebec\",\n    red4: \"#ffdbdc\",\n    red5: \"#ffcdce\",\n    red6: \"#fdbdbe\",\n    red7: \"#f4a9aa\",\n    red8: \"#eb8e90\",\n    red9: \"#e5484d\",\n    red10: \"#dc3e42\",\n    red11: \"#ce2c31\",\n    red12: \"#641723\",\n};\nconst redA = {\n    redA1: \"#ff000003\",\n    redA2: \"#ff000008\",\n    redA3: \"#f3000d14\",\n    redA4: \"#ff000824\",\n    redA5: \"#ff000632\",\n    redA6: \"#f8000442\",\n    redA7: \"#df000356\",\n    redA8: \"#d2000571\",\n    redA9: \"#db0007b7\",\n    redA10: \"#d10005c1\",\n    redA11: \"#c40006d3\",\n    redA12: \"#55000de8\",\n};\nconst redP3 = {\n    red1: \"color(display-p3 0.998 0.989 0.988)\",\n    red2: \"color(display-p3 0.995 0.971 0.971)\",\n    red3: \"color(display-p3 0.985 0.925 0.925)\",\n    red4: \"color(display-p3 0.999 0.866 0.866)\",\n    red5: \"color(display-p3 0.984 0.812 0.811)\",\n    red6: \"color(display-p3 0.955 0.751 0.749)\",\n    red7: \"color(display-p3 0.915 0.675 0.672)\",\n    red8: \"color(display-p3 0.872 0.575 0.572)\",\n    red9: \"color(display-p3 0.83 0.329 0.324)\",\n    red10: \"color(display-p3 0.798 0.294 0.285)\",\n    red11: \"color(display-p3 0.744 0.234 0.222)\",\n    red12: \"color(display-p3 0.36 0.115 0.143)\",\n};\nconst redP3A = {\n    redA1: \"color(display-p3 0.675 0.024 0.024 / 0.012)\",\n    redA2: \"color(display-p3 0.863 0.024 0.024 / 0.028)\",\n    redA3: \"color(display-p3 0.792 0.008 0.008 / 0.075)\",\n    redA4: \"color(display-p3 1 0.008 0.008 / 0.134)\",\n    redA5: \"color(display-p3 0.918 0.008 0.008 / 0.189)\",\n    redA6: \"color(display-p3 0.831 0.02 0.004 / 0.251)\",\n    redA7: \"color(display-p3 0.741 0.016 0.004 / 0.33)\",\n    redA8: \"color(display-p3 0.698 0.012 0.004 / 0.428)\",\n    redA9: \"color(display-p3 0.749 0.008 0 / 0.675)\",\n    redA10: \"color(display-p3 0.714 0.012 0 / 0.714)\",\n    redA11: \"color(display-p3 0.744 0.234 0.222)\",\n    redA12: \"color(display-p3 0.36 0.115 0.143)\",\n};\nconst ruby = {\n    ruby1: \"#fffcfd\",\n    ruby2: \"#fff7f8\",\n    ruby3: \"#feeaed\",\n    ruby4: \"#ffdce1\",\n    ruby5: \"#ffced6\",\n    ruby6: \"#f8bfc8\",\n    ruby7: \"#efacb8\",\n    ruby8: \"#e592a3\",\n    ruby9: \"#e54666\",\n    ruby10: \"#dc3b5d\",\n    ruby11: \"#ca244d\",\n    ruby12: \"#64172b\",\n};\nconst rubyA = {\n    rubyA1: \"#ff005503\",\n    rubyA2: \"#ff002008\",\n    rubyA3: \"#f3002515\",\n    rubyA4: \"#ff002523\",\n    rubyA5: \"#ff002a31\",\n    rubyA6: \"#e4002440\",\n    rubyA7: \"#ce002553\",\n    rubyA8: \"#c300286d\",\n    rubyA9: \"#db002cb9\",\n    rubyA10: \"#d2002cc4\",\n    rubyA11: \"#c10030db\",\n    rubyA12: \"#550016e8\",\n};\nconst rubyP3 = {\n    ruby1: \"color(display-p3 0.998 0.989 0.992)\",\n    ruby2: \"color(display-p3 0.995 0.971 0.974)\",\n    ruby3: \"color(display-p3 0.983 0.92 0.928)\",\n    ruby4: \"color(display-p3 0.987 0.869 0.885)\",\n    ruby5: \"color(display-p3 0.968 0.817 0.839)\",\n    ruby6: \"color(display-p3 0.937 0.758 0.786)\",\n    ruby7: \"color(display-p3 0.897 0.685 0.721)\",\n    ruby8: \"color(display-p3 0.851 0.588 0.639)\",\n    ruby9: \"color(display-p3 0.83 0.323 0.408)\",\n    ruby10: \"color(display-p3 0.795 0.286 0.375)\",\n    ruby11: \"color(display-p3 0.728 0.211 0.311)\",\n    ruby12: \"color(display-p3 0.36 0.115 0.171)\",\n};\nconst rubyP3A = {\n    rubyA1: \"color(display-p3 0.675 0.024 0.349 / 0.012)\",\n    rubyA2: \"color(display-p3 0.863 0.024 0.024 / 0.028)\",\n    rubyA3: \"color(display-p3 0.804 0.008 0.11 / 0.079)\",\n    rubyA4: \"color(display-p3 0.91 0.008 0.125 / 0.13)\",\n    rubyA5: \"color(display-p3 0.831 0.004 0.133 / 0.185)\",\n    rubyA6: \"color(display-p3 0.745 0.004 0.118 / 0.244)\",\n    rubyA7: \"color(display-p3 0.678 0.004 0.114 / 0.314)\",\n    rubyA8: \"color(display-p3 0.639 0.004 0.125 / 0.412)\",\n    rubyA9: \"color(display-p3 0.753 0 0.129 / 0.679)\",\n    rubyA10: \"color(display-p3 0.714 0 0.125 / 0.714)\",\n    rubyA11: \"color(display-p3 0.728 0.211 0.311)\",\n    rubyA12: \"color(display-p3 0.36 0.115 0.171)\",\n};\nconst crimson = {\n    crimson1: \"#fffcfd\",\n    crimson2: \"#fef7f9\",\n    crimson3: \"#ffe9f0\",\n    crimson4: \"#fedce7\",\n    crimson5: \"#facedd\",\n    crimson6: \"#f3bed1\",\n    crimson7: \"#eaacc3\",\n    crimson8: \"#e093b2\",\n    crimson9: \"#e93d82\",\n    crimson10: \"#df3478\",\n    crimson11: \"#cb1d63\",\n    crimson12: \"#621639\",\n};\nconst crimsonA = {\n    crimsonA1: \"#ff005503\",\n    crimsonA2: \"#e0004008\",\n    crimsonA3: \"#ff005216\",\n    crimsonA4: \"#f8005123\",\n    crimsonA5: \"#e5004f31\",\n    crimsonA6: \"#d0004b41\",\n    crimsonA7: \"#bf004753\",\n    crimsonA8: \"#b6004a6c\",\n    crimsonA9: \"#e2005bc2\",\n    crimsonA10: \"#d70056cb\",\n    crimsonA11: \"#c4004fe2\",\n    crimsonA12: \"#530026e9\",\n};\nconst crimsonP3 = {\n    crimson1: \"color(display-p3 0.998 0.989 0.992)\",\n    crimson2: \"color(display-p3 0.991 0.969 0.976)\",\n    crimson3: \"color(display-p3 0.987 0.917 0.941)\",\n    crimson4: \"color(display-p3 0.975 0.866 0.904)\",\n    crimson5: \"color(display-p3 0.953 0.813 0.864)\",\n    crimson6: \"color(display-p3 0.921 0.755 0.817)\",\n    crimson7: \"color(display-p3 0.88 0.683 0.761)\",\n    crimson8: \"color(display-p3 0.834 0.592 0.694)\",\n    crimson9: \"color(display-p3 0.843 0.298 0.507)\",\n    crimson10: \"color(display-p3 0.807 0.266 0.468)\",\n    crimson11: \"color(display-p3 0.731 0.195 0.388)\",\n    crimson12: \"color(display-p3 0.352 0.111 0.221)\",\n};\nconst crimsonP3A = {\n    crimsonA1: \"color(display-p3 0.675 0.024 0.349 / 0.012)\",\n    crimsonA2: \"color(display-p3 0.757 0.02 0.267 / 0.032)\",\n    crimsonA3: \"color(display-p3 0.859 0.008 0.294 / 0.083)\",\n    crimsonA4: \"color(display-p3 0.827 0.008 0.298 / 0.134)\",\n    crimsonA5: \"color(display-p3 0.753 0.008 0.275 / 0.189)\",\n    crimsonA6: \"color(display-p3 0.682 0.004 0.247 / 0.244)\",\n    crimsonA7: \"color(display-p3 0.62 0.004 0.251 / 0.318)\",\n    crimsonA8: \"color(display-p3 0.6 0.004 0.251 / 0.408)\",\n    crimsonA9: \"color(display-p3 0.776 0 0.298 / 0.702)\",\n    crimsonA10: \"color(display-p3 0.737 0 0.275 / 0.734)\",\n    crimsonA11: \"color(display-p3 0.731 0.195 0.388)\",\n    crimsonA12: \"color(display-p3 0.352 0.111 0.221)\",\n};\nconst pink = {\n    pink1: \"#fffcfe\",\n    pink2: \"#fef7fb\",\n    pink3: \"#fee9f5\",\n    pink4: \"#fbdcef\",\n    pink5: \"#f6cee7\",\n    pink6: \"#efbfdd\",\n    pink7: \"#e7acd0\",\n    pink8: \"#dd93c2\",\n    pink9: \"#d6409f\",\n    pink10: \"#cf3897\",\n    pink11: \"#c2298a\",\n    pink12: \"#651249\",\n};\nconst pinkA = {\n    pinkA1: \"#ff00aa03\",\n    pinkA2: \"#e0008008\",\n    pinkA3: \"#f4008c16\",\n    pinkA4: \"#e2008b23\",\n    pinkA5: \"#d1008331\",\n    pinkA6: \"#c0007840\",\n    pinkA7: \"#b6006f53\",\n    pinkA8: \"#af006f6c\",\n    pinkA9: \"#c8007fbf\",\n    pinkA10: \"#c2007ac7\",\n    pinkA11: \"#b60074d6\",\n    pinkA12: \"#59003bed\",\n};\nconst pinkP3 = {\n    pink1: \"color(display-p3 0.998 0.989 0.996)\",\n    pink2: \"color(display-p3 0.992 0.97 0.985)\",\n    pink3: \"color(display-p3 0.981 0.917 0.96)\",\n    pink4: \"color(display-p3 0.963 0.867 0.932)\",\n    pink5: \"color(display-p3 0.939 0.815 0.899)\",\n    pink6: \"color(display-p3 0.907 0.756 0.859)\",\n    pink7: \"color(display-p3 0.869 0.683 0.81)\",\n    pink8: \"color(display-p3 0.825 0.59 0.751)\",\n    pink9: \"color(display-p3 0.775 0.297 0.61)\",\n    pink10: \"color(display-p3 0.748 0.27 0.581)\",\n    pink11: \"color(display-p3 0.698 0.219 0.528)\",\n    pink12: \"color(display-p3 0.363 0.101 0.279)\",\n};\nconst pinkP3A = {\n    pinkA1: \"color(display-p3 0.675 0.024 0.675 / 0.012)\",\n    pinkA2: \"color(display-p3 0.757 0.02 0.51 / 0.032)\",\n    pinkA3: \"color(display-p3 0.765 0.008 0.529 / 0.083)\",\n    pinkA4: \"color(display-p3 0.737 0.008 0.506 / 0.134)\",\n    pinkA5: \"color(display-p3 0.663 0.004 0.451 / 0.185)\",\n    pinkA6: \"color(display-p3 0.616 0.004 0.424 / 0.244)\",\n    pinkA7: \"color(display-p3 0.596 0.004 0.412 / 0.318)\",\n    pinkA8: \"color(display-p3 0.573 0.004 0.404 / 0.412)\",\n    pinkA9: \"color(display-p3 0.682 0 0.447 / 0.702)\",\n    pinkA10: \"color(display-p3 0.655 0 0.424 / 0.73)\",\n    pinkA11: \"color(display-p3 0.698 0.219 0.528)\",\n    pinkA12: \"color(display-p3 0.363 0.101 0.279)\",\n};\nconst plum = {\n    plum1: \"#fefcff\",\n    plum2: \"#fdf7fd\",\n    plum3: \"#fbebfb\",\n    plum4: \"#f7def8\",\n    plum5: \"#f2d1f3\",\n    plum6: \"#e9c2ec\",\n    plum7: \"#deade3\",\n    plum8: \"#cf91d8\",\n    plum9: \"#ab4aba\",\n    plum10: \"#a144af\",\n    plum11: \"#953ea3\",\n    plum12: \"#53195d\",\n};\nconst plumA = {\n    plumA1: \"#aa00ff03\",\n    plumA2: \"#c000c008\",\n    plumA3: \"#cc00cc14\",\n    plumA4: \"#c200c921\",\n    plumA5: \"#b700bd2e\",\n    plumA6: \"#a400b03d\",\n    plumA7: \"#9900a852\",\n    plumA8: \"#9000a56e\",\n    plumA9: \"#89009eb5\",\n    plumA10: \"#7f0092bb\",\n    plumA11: \"#730086c1\",\n    plumA12: \"#40004be6\",\n};\nconst plumP3 = {\n    plum1: \"color(display-p3 0.995 0.988 0.999)\",\n    plum2: \"color(display-p3 0.988 0.971 0.99)\",\n    plum3: \"color(display-p3 0.973 0.923 0.98)\",\n    plum4: \"color(display-p3 0.953 0.875 0.966)\",\n    plum5: \"color(display-p3 0.926 0.825 0.945)\",\n    plum6: \"color(display-p3 0.89 0.765 0.916)\",\n    plum7: \"color(display-p3 0.84 0.686 0.877)\",\n    plum8: \"color(display-p3 0.775 0.58 0.832)\",\n    plum9: \"color(display-p3 0.624 0.313 0.708)\",\n    plum10: \"color(display-p3 0.587 0.29 0.667)\",\n    plum11: \"color(display-p3 0.543 0.263 0.619)\",\n    plum12: \"color(display-p3 0.299 0.114 0.352)\",\n};\nconst plumP3A = {\n    plumA1: \"color(display-p3 0.675 0.024 1 / 0.012)\",\n    plumA2: \"color(display-p3 0.58 0.024 0.58 / 0.028)\",\n    plumA3: \"color(display-p3 0.655 0.008 0.753 / 0.079)\",\n    plumA4: \"color(display-p3 0.627 0.008 0.722 / 0.126)\",\n    plumA5: \"color(display-p3 0.58 0.004 0.69 / 0.177)\",\n    plumA6: \"color(display-p3 0.537 0.004 0.655 / 0.236)\",\n    plumA7: \"color(display-p3 0.49 0.004 0.616 / 0.314)\",\n    plumA8: \"color(display-p3 0.471 0.004 0.6 / 0.42)\",\n    plumA9: \"color(display-p3 0.451 0 0.576 / 0.687)\",\n    plumA10: \"color(display-p3 0.42 0 0.529 / 0.71)\",\n    plumA11: \"color(display-p3 0.543 0.263 0.619)\",\n    plumA12: \"color(display-p3 0.299 0.114 0.352)\",\n};\nconst purple = {\n    purple1: \"#fefcfe\",\n    purple2: \"#fbf7fe\",\n    purple3: \"#f7edfe\",\n    purple4: \"#f2e2fc\",\n    purple5: \"#ead5f9\",\n    purple6: \"#e0c4f4\",\n    purple7: \"#d1afec\",\n    purple8: \"#be93e4\",\n    purple9: \"#8e4ec6\",\n    purple10: \"#8347b9\",\n    purple11: \"#8145b5\",\n    purple12: \"#402060\",\n};\nconst purpleA = {\n    purpleA1: \"#aa00aa03\",\n    purpleA2: \"#8000e008\",\n    purpleA3: \"#8e00f112\",\n    purpleA4: \"#8d00e51d\",\n    purpleA5: \"#8000db2a\",\n    purpleA6: \"#7a01d03b\",\n    purpleA7: \"#6d00c350\",\n    purpleA8: \"#6600c06c\",\n    purpleA9: \"#5c00adb1\",\n    purpleA10: \"#53009eb8\",\n    purpleA11: \"#52009aba\",\n    purpleA12: \"#250049df\",\n};\nconst purpleP3 = {\n    purple1: \"color(display-p3 0.995 0.988 0.996)\",\n    purple2: \"color(display-p3 0.983 0.971 0.993)\",\n    purple3: \"color(display-p3 0.963 0.931 0.989)\",\n    purple4: \"color(display-p3 0.937 0.888 0.981)\",\n    purple5: \"color(display-p3 0.904 0.837 0.966)\",\n    purple6: \"color(display-p3 0.86 0.774 0.942)\",\n    purple7: \"color(display-p3 0.799 0.69 0.91)\",\n    purple8: \"color(display-p3 0.719 0.583 0.874)\",\n    purple9: \"color(display-p3 0.523 0.318 0.751)\",\n    purple10: \"color(display-p3 0.483 0.289 0.7)\",\n    purple11: \"color(display-p3 0.473 0.281 0.687)\",\n    purple12: \"color(display-p3 0.234 0.132 0.363)\",\n};\nconst purpleP3A = {\n    purpleA1: \"color(display-p3 0.675 0.024 0.675 / 0.012)\",\n    purpleA2: \"color(display-p3 0.443 0.024 0.722 / 0.028)\",\n    purpleA3: \"color(display-p3 0.506 0.008 0.835 / 0.071)\",\n    purpleA4: \"color(display-p3 0.451 0.004 0.831 / 0.114)\",\n    purpleA5: \"color(display-p3 0.431 0.004 0.788 / 0.165)\",\n    purpleA6: \"color(display-p3 0.384 0.004 0.745 / 0.228)\",\n    purpleA7: \"color(display-p3 0.357 0.004 0.71 / 0.31)\",\n    purpleA8: \"color(display-p3 0.322 0.004 0.702 / 0.416)\",\n    purpleA9: \"color(display-p3 0.298 0 0.639 / 0.683)\",\n    purpleA10: \"color(display-p3 0.271 0 0.58 / 0.71)\",\n    purpleA11: \"color(display-p3 0.473 0.281 0.687)\",\n    purpleA12: \"color(display-p3 0.234 0.132 0.363)\",\n};\nconst violet = {\n    violet1: \"#fdfcfe\",\n    violet2: \"#faf8ff\",\n    violet3: \"#f4f0fe\",\n    violet4: \"#ebe4ff\",\n    violet5: \"#e1d9ff\",\n    violet6: \"#d4cafe\",\n    violet7: \"#c2b5f5\",\n    violet8: \"#aa99ec\",\n    violet9: \"#6e56cf\",\n    violet10: \"#654dc4\",\n    violet11: \"#6550b9\",\n    violet12: \"#2f265f\",\n};\nconst violetA = {\n    violetA1: \"#5500aa03\",\n    violetA2: \"#4900ff07\",\n    violetA3: \"#4400ee0f\",\n    violetA4: \"#4300ff1b\",\n    violetA5: \"#3600ff26\",\n    violetA6: \"#3100fb35\",\n    violetA7: \"#2d01dd4a\",\n    violetA8: \"#2b00d066\",\n    violetA9: \"#2400b7a9\",\n    violetA10: \"#2300abb2\",\n    violetA11: \"#1f0099af\",\n    violetA12: \"#0b0043d9\",\n};\nconst violetP3 = {\n    violet1: \"color(display-p3 0.991 0.988 0.995)\",\n    violet2: \"color(display-p3 0.978 0.974 0.998)\",\n    violet3: \"color(display-p3 0.953 0.943 0.993)\",\n    violet4: \"color(display-p3 0.916 0.897 1)\",\n    violet5: \"color(display-p3 0.876 0.851 1)\",\n    violet6: \"color(display-p3 0.825 0.793 0.981)\",\n    violet7: \"color(display-p3 0.752 0.712 0.943)\",\n    violet8: \"color(display-p3 0.654 0.602 0.902)\",\n    violet9: \"color(display-p3 0.417 0.341 0.784)\",\n    violet10: \"color(display-p3 0.381 0.306 0.741)\",\n    violet11: \"color(display-p3 0.383 0.317 0.702)\",\n    violet12: \"color(display-p3 0.179 0.15 0.359)\",\n};\nconst violetP3A = {\n    violetA1: \"color(display-p3 0.349 0.024 0.675 / 0.012)\",\n    violetA2: \"color(display-p3 0.161 0.024 0.863 / 0.028)\",\n    violetA3: \"color(display-p3 0.204 0.004 0.871 / 0.059)\",\n    violetA4: \"color(display-p3 0.196 0.004 1 / 0.102)\",\n    violetA5: \"color(display-p3 0.165 0.008 1 / 0.15)\",\n    violetA6: \"color(display-p3 0.153 0.004 0.906 / 0.208)\",\n    violetA7: \"color(display-p3 0.141 0.004 0.796 / 0.287)\",\n    violetA8: \"color(display-p3 0.133 0.004 0.753 / 0.397)\",\n    violetA9: \"color(display-p3 0.114 0 0.675 / 0.659)\",\n    violetA10: \"color(display-p3 0.11 0 0.627 / 0.695)\",\n    violetA11: \"color(display-p3 0.383 0.317 0.702)\",\n    violetA12: \"color(display-p3 0.179 0.15 0.359)\",\n};\nconst iris = {\n    iris1: \"#fdfdff\",\n    iris2: \"#f8f8ff\",\n    iris3: \"#f0f1fe\",\n    iris4: \"#e6e7ff\",\n    iris5: \"#dadcff\",\n    iris6: \"#cbcdff\",\n    iris7: \"#b8baf8\",\n    iris8: \"#9b9ef0\",\n    iris9: \"#5b5bd6\",\n    iris10: \"#5151cd\",\n    iris11: \"#5753c6\",\n    iris12: \"#272962\",\n};\nconst irisA = {\n    irisA1: \"#0000ff02\",\n    irisA2: \"#0000ff07\",\n    irisA3: \"#0011ee0f\",\n    irisA4: \"#000bff19\",\n    irisA5: \"#000eff25\",\n    irisA6: \"#000aff34\",\n    irisA7: \"#0008e647\",\n    irisA8: \"#0008d964\",\n    irisA9: \"#0000c0a4\",\n    irisA10: \"#0000b6ae\",\n    irisA11: \"#0600abac\",\n    irisA12: \"#000246d8\",\n};\nconst irisP3 = {\n    iris1: \"color(display-p3 0.992 0.992 0.999)\",\n    iris2: \"color(display-p3 0.972 0.973 0.998)\",\n    iris3: \"color(display-p3 0.943 0.945 0.992)\",\n    iris4: \"color(display-p3 0.902 0.906 1)\",\n    iris5: \"color(display-p3 0.857 0.861 1)\",\n    iris6: \"color(display-p3 0.799 0.805 0.987)\",\n    iris7: \"color(display-p3 0.721 0.727 0.955)\",\n    iris8: \"color(display-p3 0.61 0.619 0.918)\",\n    iris9: \"color(display-p3 0.357 0.357 0.81)\",\n    iris10: \"color(display-p3 0.318 0.318 0.774)\",\n    iris11: \"color(display-p3 0.337 0.326 0.748)\",\n    iris12: \"color(display-p3 0.154 0.161 0.371)\",\n};\nconst irisP3A = {\n    irisA1: \"color(display-p3 0.02 0.02 1 / 0.008)\",\n    irisA2: \"color(display-p3 0.024 0.024 0.863 / 0.028)\",\n    irisA3: \"color(display-p3 0.004 0.071 0.871 / 0.059)\",\n    irisA4: \"color(display-p3 0.012 0.051 1 / 0.099)\",\n    irisA5: \"color(display-p3 0.008 0.035 1 / 0.142)\",\n    irisA6: \"color(display-p3 0 0.02 0.941 / 0.2)\",\n    irisA7: \"color(display-p3 0.004 0.02 0.847 / 0.279)\",\n    irisA8: \"color(display-p3 0.004 0.024 0.788 / 0.389)\",\n    irisA9: \"color(display-p3 0 0 0.706 / 0.644)\",\n    irisA10: \"color(display-p3 0 0 0.667 / 0.683)\",\n    irisA11: \"color(display-p3 0.337 0.326 0.748)\",\n    irisA12: \"color(display-p3 0.154 0.161 0.371)\",\n};\nconst indigo = {\n    indigo1: \"#fdfdfe\",\n    indigo2: \"#f7f9ff\",\n    indigo3: \"#edf2fe\",\n    indigo4: \"#e1e9ff\",\n    indigo5: \"#d2deff\",\n    indigo6: \"#c1d0ff\",\n    indigo7: \"#abbdf9\",\n    indigo8: \"#8da4ef\",\n    indigo9: \"#3e63dd\",\n    indigo10: \"#3358d4\",\n    indigo11: \"#3a5bc7\",\n    indigo12: \"#1f2d5c\",\n};\nconst indigoA = {\n    indigoA1: \"#00008002\",\n    indigoA2: \"#0040ff08\",\n    indigoA3: \"#0047f112\",\n    indigoA4: \"#0044ff1e\",\n    indigoA5: \"#0044ff2d\",\n    indigoA6: \"#003eff3e\",\n    indigoA7: \"#0037ed54\",\n    indigoA8: \"#0034dc72\",\n    indigoA9: \"#0031d2c1\",\n    indigoA10: \"#002ec9cc\",\n    indigoA11: \"#002bb7c5\",\n    indigoA12: \"#001046e0\",\n};\nconst indigoP3 = {\n    indigo1: \"color(display-p3 0.992 0.992 0.996)\",\n    indigo2: \"color(display-p3 0.971 0.977 0.998)\",\n    indigo3: \"color(display-p3 0.933 0.948 0.992)\",\n    indigo4: \"color(display-p3 0.885 0.914 1)\",\n    indigo5: \"color(display-p3 0.831 0.87 1)\",\n    indigo6: \"color(display-p3 0.767 0.814 0.995)\",\n    indigo7: \"color(display-p3 0.685 0.74 0.957)\",\n    indigo8: \"color(display-p3 0.569 0.639 0.916)\",\n    indigo9: \"color(display-p3 0.276 0.384 0.837)\",\n    indigo10: \"color(display-p3 0.234 0.343 0.801)\",\n    indigo11: \"color(display-p3 0.256 0.354 0.755)\",\n    indigo12: \"color(display-p3 0.133 0.175 0.348)\",\n};\nconst indigoP3A = {\n    indigoA1: \"color(display-p3 0.02 0.02 0.51 / 0.008)\",\n    indigoA2: \"color(display-p3 0.024 0.161 0.863 / 0.028)\",\n    indigoA3: \"color(display-p3 0.008 0.239 0.886 / 0.067)\",\n    indigoA4: \"color(display-p3 0.004 0.247 1 / 0.114)\",\n    indigoA5: \"color(display-p3 0.004 0.235 1 / 0.169)\",\n    indigoA6: \"color(display-p3 0.004 0.208 0.984 / 0.232)\",\n    indigoA7: \"color(display-p3 0.004 0.176 0.863 / 0.314)\",\n    indigoA8: \"color(display-p3 0.004 0.165 0.812 / 0.432)\",\n    indigoA9: \"color(display-p3 0 0.153 0.773 / 0.726)\",\n    indigoA10: \"color(display-p3 0 0.137 0.737 / 0.765)\",\n    indigoA11: \"color(display-p3 0.256 0.354 0.755)\",\n    indigoA12: \"color(display-p3 0.133 0.175 0.348)\",\n};\nconst blue = {\n    blue1: \"#fbfdff\",\n    blue2: \"#f4faff\",\n    blue3: \"#e6f4fe\",\n    blue4: \"#d5efff\",\n    blue5: \"#c2e5ff\",\n    blue6: \"#acd8fc\",\n    blue7: \"#8ec8f6\",\n    blue8: \"#5eb1ef\",\n    blue9: \"#0090ff\",\n    blue10: \"#0588f0\",\n    blue11: \"#0d74ce\",\n    blue12: \"#113264\",\n};\nconst blueA = {\n    blueA1: \"#0080ff04\",\n    blueA2: \"#008cff0b\",\n    blueA3: \"#008ff519\",\n    blueA4: \"#009eff2a\",\n    blueA5: \"#0093ff3d\",\n    blueA6: \"#0088f653\",\n    blueA7: \"#0083eb71\",\n    blueA8: \"#0084e6a1\",\n    blueA9: \"#0090ff\",\n    blueA10: \"#0086f0fa\",\n    blueA11: \"#006dcbf2\",\n    blueA12: \"#002359ee\",\n};\nconst blueP3 = {\n    blue1: \"color(display-p3 0.986 0.992 0.999)\",\n    blue2: \"color(display-p3 0.96 0.979 0.998)\",\n    blue3: \"color(display-p3 0.912 0.956 0.991)\",\n    blue4: \"color(display-p3 0.853 0.932 1)\",\n    blue5: \"color(display-p3 0.788 0.894 0.998)\",\n    blue6: \"color(display-p3 0.709 0.843 0.976)\",\n    blue7: \"color(display-p3 0.606 0.777 0.947)\",\n    blue8: \"color(display-p3 0.451 0.688 0.917)\",\n    blue9: \"color(display-p3 0.247 0.556 0.969)\",\n    blue10: \"color(display-p3 0.234 0.523 0.912)\",\n    blue11: \"color(display-p3 0.15 0.44 0.84)\",\n    blue12: \"color(display-p3 0.102 0.193 0.379)\",\n};\nconst blueP3A = {\n    blueA1: \"color(display-p3 0.024 0.514 1 / 0.016)\",\n    blueA2: \"color(display-p3 0.024 0.514 0.906 / 0.04)\",\n    blueA3: \"color(display-p3 0.012 0.506 0.914 / 0.087)\",\n    blueA4: \"color(display-p3 0.008 0.545 1 / 0.146)\",\n    blueA5: \"color(display-p3 0.004 0.502 0.984 / 0.212)\",\n    blueA6: \"color(display-p3 0.004 0.463 0.922 / 0.291)\",\n    blueA7: \"color(display-p3 0.004 0.431 0.863 / 0.393)\",\n    blueA8: \"color(display-p3 0 0.427 0.851 / 0.55)\",\n    blueA9: \"color(display-p3 0 0.412 0.961 / 0.753)\",\n    blueA10: \"color(display-p3 0 0.376 0.886 / 0.765)\",\n    blueA11: \"color(display-p3 0.15 0.44 0.84)\",\n    blueA12: \"color(display-p3 0.102 0.193 0.379)\",\n};\nconst cyan = {\n    cyan1: \"#fafdfe\",\n    cyan2: \"#f2fafb\",\n    cyan3: \"#def7f9\",\n    cyan4: \"#caf1f6\",\n    cyan5: \"#b5e9f0\",\n    cyan6: \"#9ddde7\",\n    cyan7: \"#7dcedc\",\n    cyan8: \"#3db9cf\",\n    cyan9: \"#00a2c7\",\n    cyan10: \"#0797b9\",\n    cyan11: \"#107d98\",\n    cyan12: \"#0d3c48\",\n};\nconst cyanA = {\n    cyanA1: \"#0099cc05\",\n    cyanA2: \"#009db10d\",\n    cyanA3: \"#00c2d121\",\n    cyanA4: \"#00bcd435\",\n    cyanA5: \"#01b4cc4a\",\n    cyanA6: \"#00a7c162\",\n    cyanA7: \"#009fbb82\",\n    cyanA8: \"#00a3c0c2\",\n    cyanA9: \"#00a2c7\",\n    cyanA10: \"#0094b7f8\",\n    cyanA11: \"#007491ef\",\n    cyanA12: \"#00323ef2\",\n};\nconst cyanP3 = {\n    cyan1: \"color(display-p3 0.982 0.992 0.996)\",\n    cyan2: \"color(display-p3 0.955 0.981 0.984)\",\n    cyan3: \"color(display-p3 0.888 0.965 0.975)\",\n    cyan4: \"color(display-p3 0.821 0.941 0.959)\",\n    cyan5: \"color(display-p3 0.751 0.907 0.935)\",\n    cyan6: \"color(display-p3 0.671 0.862 0.9)\",\n    cyan7: \"color(display-p3 0.564 0.8 0.854)\",\n    cyan8: \"color(display-p3 0.388 0.715 0.798)\",\n    cyan9: \"color(display-p3 0.282 0.627 0.765)\",\n    cyan10: \"color(display-p3 0.264 0.583 0.71)\",\n    cyan11: \"color(display-p3 0.08 0.48 0.63)\",\n    cyan12: \"color(display-p3 0.108 0.232 0.277)\",\n};\nconst cyanP3A = {\n    cyanA1: \"color(display-p3 0.02 0.608 0.804 / 0.02)\",\n    cyanA2: \"color(display-p3 0.02 0.557 0.647 / 0.044)\",\n    cyanA3: \"color(display-p3 0.004 0.694 0.796 / 0.114)\",\n    cyanA4: \"color(display-p3 0.004 0.678 0.784 / 0.181)\",\n    cyanA5: \"color(display-p3 0.004 0.624 0.733 / 0.248)\",\n    cyanA6: \"color(display-p3 0.004 0.584 0.706 / 0.33)\",\n    cyanA7: \"color(display-p3 0.004 0.541 0.667 / 0.436)\",\n    cyanA8: \"color(display-p3 0 0.533 0.667 / 0.612)\",\n    cyanA9: \"color(display-p3 0 0.482 0.675 / 0.718)\",\n    cyanA10: \"color(display-p3 0 0.435 0.608 / 0.738)\",\n    cyanA11: \"color(display-p3 0.08 0.48 0.63)\",\n    cyanA12: \"color(display-p3 0.108 0.232 0.277)\",\n};\nconst teal = {\n    teal1: \"#fafefd\",\n    teal2: \"#f3fbf9\",\n    teal3: \"#e0f8f3\",\n    teal4: \"#ccf3ea\",\n    teal5: \"#b8eae0\",\n    teal6: \"#a1ded2\",\n    teal7: \"#83cdc1\",\n    teal8: \"#53b9ab\",\n    teal9: \"#12a594\",\n    teal10: \"#0d9b8a\",\n    teal11: \"#008573\",\n    teal12: \"#0d3d38\",\n};\nconst tealA = {\n    tealA1: \"#00cc9905\",\n    tealA2: \"#00aa800c\",\n    tealA3: \"#00c69d1f\",\n    tealA4: \"#00c39633\",\n    tealA5: \"#00b49047\",\n    tealA6: \"#00a6855e\",\n    tealA7: \"#0099807c\",\n    tealA8: \"#009783ac\",\n    tealA9: \"#009e8ced\",\n    tealA10: \"#009684f2\",\n    tealA11: \"#008573\",\n    tealA12: \"#00332df2\",\n};\nconst tealP3 = {\n    teal1: \"color(display-p3 0.983 0.996 0.992)\",\n    teal2: \"color(display-p3 0.958 0.983 0.976)\",\n    teal3: \"color(display-p3 0.895 0.971 0.952)\",\n    teal4: \"color(display-p3 0.831 0.949 0.92)\",\n    teal5: \"color(display-p3 0.761 0.914 0.878)\",\n    teal6: \"color(display-p3 0.682 0.864 0.825)\",\n    teal7: \"color(display-p3 0.581 0.798 0.756)\",\n    teal8: \"color(display-p3 0.433 0.716 0.671)\",\n    teal9: \"color(display-p3 0.297 0.637 0.581)\",\n    teal10: \"color(display-p3 0.275 0.599 0.542)\",\n    teal11: \"color(display-p3 0.08 0.5 0.43)\",\n    teal12: \"color(display-p3 0.11 0.235 0.219)\",\n};\nconst tealP3A = {\n    tealA1: \"color(display-p3 0.024 0.757 0.514 / 0.016)\",\n    tealA2: \"color(display-p3 0.02 0.647 0.467 / 0.044)\",\n    tealA3: \"color(display-p3 0.004 0.741 0.557 / 0.106)\",\n    tealA4: \"color(display-p3 0.004 0.702 0.537 / 0.169)\",\n    tealA5: \"color(display-p3 0.004 0.643 0.494 / 0.24)\",\n    tealA6: \"color(display-p3 0.004 0.569 0.447 / 0.318)\",\n    tealA7: \"color(display-p3 0.004 0.518 0.424 / 0.42)\",\n    tealA8: \"color(display-p3 0 0.506 0.424 / 0.569)\",\n    tealA9: \"color(display-p3 0 0.482 0.404 / 0.702)\",\n    tealA10: \"color(display-p3 0 0.451 0.369 / 0.726)\",\n    tealA11: \"color(display-p3 0.08 0.5 0.43)\",\n    tealA12: \"color(display-p3 0.11 0.235 0.219)\",\n};\nconst jade = {\n    jade1: \"#fbfefd\",\n    jade2: \"#f4fbf7\",\n    jade3: \"#e6f7ed\",\n    jade4: \"#d6f1e3\",\n    jade5: \"#c3e9d7\",\n    jade6: \"#acdec8\",\n    jade7: \"#8bceb6\",\n    jade8: \"#56ba9f\",\n    jade9: \"#29a383\",\n    jade10: \"#26997b\",\n    jade11: \"#208368\",\n    jade12: \"#1d3b31\",\n};\nconst jadeA = {\n    jadeA1: \"#00c08004\",\n    jadeA2: \"#00a3460b\",\n    jadeA3: \"#00ae4819\",\n    jadeA4: \"#00a85129\",\n    jadeA5: \"#00a2553c\",\n    jadeA6: \"#009a5753\",\n    jadeA7: \"#00945f74\",\n    jadeA8: \"#00976ea9\",\n    jadeA9: \"#00916bd6\",\n    jadeA10: \"#008764d9\",\n    jadeA11: \"#007152df\",\n    jadeA12: \"#002217e2\",\n};\nconst jadeP3 = {\n    jade1: \"color(display-p3 0.986 0.996 0.992)\",\n    jade2: \"color(display-p3 0.962 0.983 0.969)\",\n    jade3: \"color(display-p3 0.912 0.965 0.932)\",\n    jade4: \"color(display-p3 0.858 0.941 0.893)\",\n    jade5: \"color(display-p3 0.795 0.909 0.847)\",\n    jade6: \"color(display-p3 0.715 0.864 0.791)\",\n    jade7: \"color(display-p3 0.603 0.802 0.718)\",\n    jade8: \"color(display-p3 0.44 0.72 0.629)\",\n    jade9: \"color(display-p3 0.319 0.63 0.521)\",\n    jade10: \"color(display-p3 0.299 0.592 0.488)\",\n    jade11: \"color(display-p3 0.15 0.5 0.37)\",\n    jade12: \"color(display-p3 0.142 0.229 0.194)\",\n};\nconst jadeP3A = {\n    jadeA1: \"color(display-p3 0.024 0.757 0.514 / 0.016)\",\n    jadeA2: \"color(display-p3 0.024 0.612 0.22 / 0.04)\",\n    jadeA3: \"color(display-p3 0.012 0.596 0.235 / 0.087)\",\n    jadeA4: \"color(display-p3 0.008 0.588 0.255 / 0.142)\",\n    jadeA5: \"color(display-p3 0.004 0.561 0.251 / 0.204)\",\n    jadeA6: \"color(display-p3 0.004 0.525 0.278 / 0.287)\",\n    jadeA7: \"color(display-p3 0.004 0.506 0.29 / 0.397)\",\n    jadeA8: \"color(display-p3 0 0.506 0.337 / 0.561)\",\n    jadeA9: \"color(display-p3 0 0.459 0.298 / 0.683)\",\n    jadeA10: \"color(display-p3 0 0.42 0.271 / 0.702)\",\n    jadeA11: \"color(display-p3 0.15 0.5 0.37)\",\n    jadeA12: \"color(display-p3 0.142 0.229 0.194)\",\n};\nconst green = {\n    green1: \"#fbfefc\",\n    green2: \"#f4fbf6\",\n    green3: \"#e6f6eb\",\n    green4: \"#d6f1df\",\n    green5: \"#c4e8d1\",\n    green6: \"#adddc0\",\n    green7: \"#8eceaa\",\n    green8: \"#5bb98b\",\n    green9: \"#30a46c\",\n    green10: \"#2b9a66\",\n    green11: \"#218358\",\n    green12: \"#193b2d\",\n};\nconst greenA = {\n    greenA1: \"#00c04004\",\n    greenA2: \"#00a32f0b\",\n    greenA3: \"#00a43319\",\n    greenA4: \"#00a83829\",\n    greenA5: \"#019c393b\",\n    greenA6: \"#00963c52\",\n    greenA7: \"#00914071\",\n    greenA8: \"#00924ba4\",\n    greenA9: \"#008f4acf\",\n    greenA10: \"#008647d4\",\n    greenA11: \"#00713fde\",\n    greenA12: \"#002616e6\",\n};\nconst greenP3 = {\n    green1: \"color(display-p3 0.986 0.996 0.989)\",\n    green2: \"color(display-p3 0.963 0.983 0.967)\",\n    green3: \"color(display-p3 0.913 0.964 0.925)\",\n    green4: \"color(display-p3 0.859 0.94 0.879)\",\n    green5: \"color(display-p3 0.796 0.907 0.826)\",\n    green6: \"color(display-p3 0.718 0.863 0.761)\",\n    green7: \"color(display-p3 0.61 0.801 0.675)\",\n    green8: \"color(display-p3 0.451 0.715 0.559)\",\n    green9: \"color(display-p3 0.332 0.634 0.442)\",\n    green10: \"color(display-p3 0.308 0.595 0.417)\",\n    green11: \"color(display-p3 0.19 0.5 0.32)\",\n    green12: \"color(display-p3 0.132 0.228 0.18)\",\n};\nconst greenP3A = {\n    greenA1: \"color(display-p3 0.024 0.757 0.267 / 0.016)\",\n    greenA2: \"color(display-p3 0.024 0.565 0.129 / 0.036)\",\n    greenA3: \"color(display-p3 0.012 0.596 0.145 / 0.087)\",\n    greenA4: \"color(display-p3 0.008 0.588 0.145 / 0.142)\",\n    greenA5: \"color(display-p3 0.004 0.541 0.157 / 0.204)\",\n    greenA6: \"color(display-p3 0.004 0.518 0.157 / 0.283)\",\n    greenA7: \"color(display-p3 0.004 0.486 0.165 / 0.389)\",\n    greenA8: \"color(display-p3 0 0.478 0.2 / 0.55)\",\n    greenA9: \"color(display-p3 0 0.455 0.165 / 0.667)\",\n    greenA10: \"color(display-p3 0 0.416 0.153 / 0.691)\",\n    greenA11: \"color(display-p3 0.19 0.5 0.32)\",\n    greenA12: \"color(display-p3 0.132 0.228 0.18)\",\n};\nconst grass = {\n    grass1: \"#fbfefb\",\n    grass2: \"#f5fbf5\",\n    grass3: \"#e9f6e9\",\n    grass4: \"#daf1db\",\n    grass5: \"#c9e8ca\",\n    grass6: \"#b2ddb5\",\n    grass7: \"#94ce9a\",\n    grass8: \"#65ba74\",\n    grass9: \"#46a758\",\n    grass10: \"#3e9b4f\",\n    grass11: \"#2a7e3b\",\n    grass12: \"#203c25\",\n};\nconst grassA = {\n    grassA1: \"#00c00004\",\n    grassA2: \"#0099000a\",\n    grassA3: \"#00970016\",\n    grassA4: \"#009f0725\",\n    grassA5: \"#00930536\",\n    grassA6: \"#008f0a4d\",\n    grassA7: \"#018b0f6b\",\n    grassA8: \"#008d199a\",\n    grassA9: \"#008619b9\",\n    grassA10: \"#007b17c1\",\n    grassA11: \"#006514d5\",\n    grassA12: \"#002006df\",\n};\nconst grassP3 = {\n    grass1: \"color(display-p3 0.986 0.996 0.985)\",\n    grass2: \"color(display-p3 0.966 0.983 0.964)\",\n    grass3: \"color(display-p3 0.923 0.965 0.917)\",\n    grass4: \"color(display-p3 0.872 0.94 0.865)\",\n    grass5: \"color(display-p3 0.811 0.908 0.802)\",\n    grass6: \"color(display-p3 0.733 0.864 0.724)\",\n    grass7: \"color(display-p3 0.628 0.803 0.622)\",\n    grass8: \"color(display-p3 0.477 0.72 0.482)\",\n    grass9: \"color(display-p3 0.38 0.647 0.378)\",\n    grass10: \"color(display-p3 0.344 0.598 0.342)\",\n    grass11: \"color(display-p3 0.263 0.488 0.261)\",\n    grass12: \"color(display-p3 0.151 0.233 0.153)\",\n};\nconst grassP3A = {\n    grassA1: \"color(display-p3 0.024 0.757 0.024 / 0.016)\",\n    grassA2: \"color(display-p3 0.024 0.565 0.024 / 0.036)\",\n    grassA3: \"color(display-p3 0.059 0.576 0.008 / 0.083)\",\n    grassA4: \"color(display-p3 0.035 0.565 0.008 / 0.134)\",\n    grassA5: \"color(display-p3 0.047 0.545 0.008 / 0.197)\",\n    grassA6: \"color(display-p3 0.031 0.502 0.004 / 0.275)\",\n    grassA7: \"color(display-p3 0.012 0.482 0.004 / 0.377)\",\n    grassA8: \"color(display-p3 0 0.467 0.008 / 0.522)\",\n    grassA9: \"color(display-p3 0.008 0.435 0 / 0.624)\",\n    grassA10: \"color(display-p3 0.008 0.388 0 / 0.659)\",\n    grassA11: \"color(display-p3 0.263 0.488 0.261)\",\n    grassA12: \"color(display-p3 0.151 0.233 0.153)\",\n};\nconst brown = {\n    brown1: \"#fefdfc\",\n    brown2: \"#fcf9f6\",\n    brown3: \"#f6eee7\",\n    brown4: \"#f0e4d9\",\n    brown5: \"#ebdaca\",\n    brown6: \"#e4cdb7\",\n    brown7: \"#dcbc9f\",\n    brown8: \"#cea37e\",\n    brown9: \"#ad7f58\",\n    brown10: \"#a07553\",\n    brown11: \"#815e46\",\n    brown12: \"#3e332e\",\n};\nconst brownA = {\n    brownA1: \"#aa550003\",\n    brownA2: \"#aa550009\",\n    brownA3: \"#a04b0018\",\n    brownA4: \"#9b4a0026\",\n    brownA5: \"#9f4d0035\",\n    brownA6: \"#a04e0048\",\n    brownA7: \"#a34e0060\",\n    brownA8: \"#9f4a0081\",\n    brownA9: \"#823c00a7\",\n    brownA10: \"#723300ac\",\n    brownA11: \"#522100b9\",\n    brownA12: \"#140600d1\",\n};\nconst brownP3 = {\n    brown1: \"color(display-p3 0.995 0.992 0.989)\",\n    brown2: \"color(display-p3 0.987 0.976 0.964)\",\n    brown3: \"color(display-p3 0.959 0.936 0.909)\",\n    brown4: \"color(display-p3 0.934 0.897 0.855)\",\n    brown5: \"color(display-p3 0.909 0.856 0.798)\",\n    brown6: \"color(display-p3 0.88 0.808 0.73)\",\n    brown7: \"color(display-p3 0.841 0.742 0.639)\",\n    brown8: \"color(display-p3 0.782 0.647 0.514)\",\n    brown9: \"color(display-p3 0.651 0.505 0.368)\",\n    brown10: \"color(display-p3 0.601 0.465 0.344)\",\n    brown11: \"color(display-p3 0.485 0.374 0.288)\",\n    brown12: \"color(display-p3 0.236 0.202 0.183)\",\n};\nconst brownP3A = {\n    brownA1: \"color(display-p3 0.675 0.349 0.024 / 0.012)\",\n    brownA2: \"color(display-p3 0.675 0.349 0.024 / 0.036)\",\n    brownA3: \"color(display-p3 0.573 0.314 0.012 / 0.091)\",\n    brownA4: \"color(display-p3 0.545 0.302 0.008 / 0.146)\",\n    brownA5: \"color(display-p3 0.561 0.29 0.004 / 0.204)\",\n    brownA6: \"color(display-p3 0.553 0.294 0.004 / 0.271)\",\n    brownA7: \"color(display-p3 0.557 0.286 0.004 / 0.361)\",\n    brownA8: \"color(display-p3 0.549 0.275 0.004 / 0.487)\",\n    brownA9: \"color(display-p3 0.447 0.22 0 / 0.632)\",\n    brownA10: \"color(display-p3 0.388 0.188 0 / 0.655)\",\n    brownA11: \"color(display-p3 0.485 0.374 0.288)\",\n    brownA12: \"color(display-p3 0.236 0.202 0.183)\",\n};\nconst bronze = {\n    bronze1: \"#fdfcfc\",\n    bronze2: \"#fdf7f5\",\n    bronze3: \"#f6edea\",\n    bronze4: \"#efe4df\",\n    bronze5: \"#e7d9d3\",\n    bronze6: \"#dfcdc5\",\n    bronze7: \"#d3bcb3\",\n    bronze8: \"#c2a499\",\n    bronze9: \"#a18072\",\n    bronze10: \"#957468\",\n    bronze11: \"#7d5e54\",\n    bronze12: \"#43302b\",\n};\nconst bronzeA = {\n    bronzeA1: \"#55000003\",\n    bronzeA2: \"#cc33000a\",\n    bronzeA3: \"#92250015\",\n    bronzeA4: \"#80280020\",\n    bronzeA5: \"#7423002c\",\n    bronzeA6: \"#7324003a\",\n    bronzeA7: \"#6c1f004c\",\n    bronzeA8: \"#671c0066\",\n    bronzeA9: \"#551a008d\",\n    bronzeA10: \"#4c150097\",\n    bronzeA11: \"#3d0f00ab\",\n    bronzeA12: \"#1d0600d4\",\n};\nconst bronzeP3 = {\n    bronze1: \"color(display-p3 0.991 0.988 0.988)\",\n    bronze2: \"color(display-p3 0.989 0.97 0.961)\",\n    bronze3: \"color(display-p3 0.958 0.932 0.919)\",\n    bronze4: \"color(display-p3 0.929 0.894 0.877)\",\n    bronze5: \"color(display-p3 0.898 0.853 0.832)\",\n    bronze6: \"color(display-p3 0.861 0.805 0.778)\",\n    bronze7: \"color(display-p3 0.812 0.739 0.706)\",\n    bronze8: \"color(display-p3 0.741 0.647 0.606)\",\n    bronze9: \"color(display-p3 0.611 0.507 0.455)\",\n    bronze10: \"color(display-p3 0.563 0.461 0.414)\",\n    bronze11: \"color(display-p3 0.471 0.373 0.336)\",\n    bronze12: \"color(display-p3 0.251 0.191 0.172)\",\n};\nconst bronzeP3A = {\n    bronzeA1: \"color(display-p3 0.349 0.024 0.024 / 0.012)\",\n    bronzeA2: \"color(display-p3 0.71 0.22 0.024 / 0.04)\",\n    bronzeA3: \"color(display-p3 0.482 0.2 0.008 / 0.083)\",\n    bronzeA4: \"color(display-p3 0.424 0.133 0.004 / 0.122)\",\n    bronzeA5: \"color(display-p3 0.4 0.145 0.004 / 0.169)\",\n    bronzeA6: \"color(display-p3 0.388 0.125 0.004 / 0.224)\",\n    bronzeA7: \"color(display-p3 0.365 0.11 0.004 / 0.295)\",\n    bronzeA8: \"color(display-p3 0.341 0.102 0.004 / 0.393)\",\n    bronzeA9: \"color(display-p3 0.29 0.094 0 / 0.546)\",\n    bronzeA10: \"color(display-p3 0.255 0.082 0 / 0.585)\",\n    bronzeA11: \"color(display-p3 0.471 0.373 0.336)\",\n    bronzeA12: \"color(display-p3 0.251 0.191 0.172)\",\n};\nconst gold = {\n    gold1: \"#fdfdfc\",\n    gold2: \"#faf9f2\",\n    gold3: \"#f2f0e7\",\n    gold4: \"#eae6db\",\n    gold5: \"#e1dccf\",\n    gold6: \"#d8d0bf\",\n    gold7: \"#cbc0aa\",\n    gold8: \"#b9a88d\",\n    gold9: \"#978365\",\n    gold10: \"#8c7a5e\",\n    gold11: \"#71624b\",\n    gold12: \"#3b352b\",\n};\nconst goldA = {\n    goldA1: \"#55550003\",\n    goldA2: \"#9d8a000d\",\n    goldA3: \"#75600018\",\n    goldA4: \"#6b4e0024\",\n    goldA5: \"#60460030\",\n    goldA6: \"#64440040\",\n    goldA7: \"#63420055\",\n    goldA8: \"#633d0072\",\n    goldA9: \"#5332009a\",\n    goldA10: \"#492d00a1\",\n    goldA11: \"#362100b4\",\n    goldA12: \"#130c00d4\",\n};\nconst goldP3 = {\n    gold1: \"color(display-p3 0.992 0.992 0.989)\",\n    gold2: \"color(display-p3 0.98 0.976 0.953)\",\n    gold3: \"color(display-p3 0.947 0.94 0.909)\",\n    gold4: \"color(display-p3 0.914 0.904 0.865)\",\n    gold5: \"color(display-p3 0.88 0.865 0.816)\",\n    gold6: \"color(display-p3 0.84 0.818 0.756)\",\n    gold7: \"color(display-p3 0.788 0.753 0.677)\",\n    gold8: \"color(display-p3 0.715 0.66 0.565)\",\n    gold9: \"color(display-p3 0.579 0.517 0.41)\",\n    gold10: \"color(display-p3 0.538 0.479 0.38)\",\n    gold11: \"color(display-p3 0.433 0.386 0.305)\",\n    gold12: \"color(display-p3 0.227 0.209 0.173)\",\n};\nconst goldP3A = {\n    goldA1: \"color(display-p3 0.349 0.349 0.024 / 0.012)\",\n    goldA2: \"color(display-p3 0.592 0.514 0.024 / 0.048)\",\n    goldA3: \"color(display-p3 0.4 0.357 0.012 / 0.091)\",\n    goldA4: \"color(display-p3 0.357 0.298 0.008 / 0.134)\",\n    goldA5: \"color(display-p3 0.345 0.282 0.004 / 0.185)\",\n    goldA6: \"color(display-p3 0.341 0.263 0.004 / 0.244)\",\n    goldA7: \"color(display-p3 0.345 0.235 0.004 / 0.322)\",\n    goldA8: \"color(display-p3 0.345 0.22 0.004 / 0.436)\",\n    goldA9: \"color(display-p3 0.286 0.18 0 / 0.589)\",\n    goldA10: \"color(display-p3 0.255 0.161 0 / 0.62)\",\n    goldA11: \"color(display-p3 0.433 0.386 0.305)\",\n    goldA12: \"color(display-p3 0.227 0.209 0.173)\",\n};\nconst sky = {\n    sky1: \"#f9feff\",\n    sky2: \"#f1fafd\",\n    sky3: \"#e1f6fd\",\n    sky4: \"#d1f0fa\",\n    sky5: \"#bee7f5\",\n    sky6: \"#a9daed\",\n    sky7: \"#8dcae3\",\n    sky8: \"#60b3d7\",\n    sky9: \"#7ce2fe\",\n    sky10: \"#74daf8\",\n    sky11: \"#00749e\",\n    sky12: \"#1d3e56\",\n};\nconst skyA = {\n    skyA1: \"#00d5ff06\",\n    skyA2: \"#00a4db0e\",\n    skyA3: \"#00b3ee1e\",\n    skyA4: \"#00ace42e\",\n    skyA5: \"#00a1d841\",\n    skyA6: \"#0092ca56\",\n    skyA7: \"#0089c172\",\n    skyA8: \"#0085bf9f\",\n    skyA9: \"#00c7fe83\",\n    skyA10: \"#00bcf38b\",\n    skyA11: \"#00749e\",\n    skyA12: \"#002540e2\",\n};\nconst skyP3 = {\n    sky1: \"color(display-p3 0.98 0.995 0.999)\",\n    sky2: \"color(display-p3 0.953 0.98 0.99)\",\n    sky3: \"color(display-p3 0.899 0.963 0.989)\",\n    sky4: \"color(display-p3 0.842 0.937 0.977)\",\n    sky5: \"color(display-p3 0.777 0.9 0.954)\",\n    sky6: \"color(display-p3 0.701 0.851 0.921)\",\n    sky7: \"color(display-p3 0.604 0.785 0.879)\",\n    sky8: \"color(display-p3 0.457 0.696 0.829)\",\n    sky9: \"color(display-p3 0.585 0.877 0.983)\",\n    sky10: \"color(display-p3 0.555 0.845 0.959)\",\n    sky11: \"color(display-p3 0.193 0.448 0.605)\",\n    sky12: \"color(display-p3 0.145 0.241 0.329)\",\n};\nconst skyP3A = {\n    skyA1: \"color(display-p3 0.02 0.804 1 / 0.02)\",\n    skyA2: \"color(display-p3 0.024 0.592 0.757 / 0.048)\",\n    skyA3: \"color(display-p3 0.004 0.655 0.886 / 0.102)\",\n    skyA4: \"color(display-p3 0.004 0.604 0.851 / 0.157)\",\n    skyA5: \"color(display-p3 0.004 0.565 0.792 / 0.224)\",\n    skyA6: \"color(display-p3 0.004 0.502 0.737 / 0.299)\",\n    skyA7: \"color(display-p3 0.004 0.459 0.694 / 0.397)\",\n    skyA8: \"color(display-p3 0 0.435 0.682 / 0.542)\",\n    skyA9: \"color(display-p3 0.004 0.71 0.965 / 0.416)\",\n    skyA10: \"color(display-p3 0.004 0.647 0.914 / 0.444)\",\n    skyA11: \"color(display-p3 0.193 0.448 0.605)\",\n    skyA12: \"color(display-p3 0.145 0.241 0.329)\",\n};\nconst mint = {\n    mint1: \"#f9fefd\",\n    mint2: \"#f2fbf9\",\n    mint3: \"#ddf9f2\",\n    mint4: \"#c8f4e9\",\n    mint5: \"#b3ecde\",\n    mint6: \"#9ce0d0\",\n    mint7: \"#7ecfbd\",\n    mint8: \"#4cbba5\",\n    mint9: \"#86ead4\",\n    mint10: \"#7de0cb\",\n    mint11: \"#027864\",\n    mint12: \"#16433c\",\n};\nconst mintA = {\n    mintA1: \"#00d5aa06\",\n    mintA2: \"#00b18a0d\",\n    mintA3: \"#00d29e22\",\n    mintA4: \"#00cc9937\",\n    mintA5: \"#00c0914c\",\n    mintA6: \"#00b08663\",\n    mintA7: \"#00a17d81\",\n    mintA8: \"#009e7fb3\",\n    mintA9: \"#00d3a579\",\n    mintA10: \"#00c39982\",\n    mintA11: \"#007763fd\",\n    mintA12: \"#00312ae9\",\n};\nconst mintP3 = {\n    mint1: \"color(display-p3 0.98 0.995 0.992)\",\n    mint2: \"color(display-p3 0.957 0.985 0.977)\",\n    mint3: \"color(display-p3 0.888 0.972 0.95)\",\n    mint4: \"color(display-p3 0.819 0.951 0.916)\",\n    mint5: \"color(display-p3 0.747 0.918 0.873)\",\n    mint6: \"color(display-p3 0.668 0.87 0.818)\",\n    mint7: \"color(display-p3 0.567 0.805 0.744)\",\n    mint8: \"color(display-p3 0.42 0.724 0.649)\",\n    mint9: \"color(display-p3 0.62 0.908 0.834)\",\n    mint10: \"color(display-p3 0.585 0.871 0.797)\",\n    mint11: \"color(display-p3 0.203 0.463 0.397)\",\n    mint12: \"color(display-p3 0.136 0.259 0.236)\",\n};\nconst mintP3A = {\n    mintA1: \"color(display-p3 0.02 0.804 0.608 / 0.02)\",\n    mintA2: \"color(display-p3 0.02 0.647 0.467 / 0.044)\",\n    mintA3: \"color(display-p3 0.004 0.761 0.553 / 0.114)\",\n    mintA4: \"color(display-p3 0.004 0.741 0.545 / 0.181)\",\n    mintA5: \"color(display-p3 0.004 0.678 0.51 / 0.255)\",\n    mintA6: \"color(display-p3 0.004 0.616 0.463 / 0.334)\",\n    mintA7: \"color(display-p3 0.004 0.549 0.412 / 0.432)\",\n    mintA8: \"color(display-p3 0 0.529 0.392 / 0.581)\",\n    mintA9: \"color(display-p3 0.004 0.765 0.569 / 0.381)\",\n    mintA10: \"color(display-p3 0.004 0.69 0.51 / 0.416)\",\n    mintA11: \"color(display-p3 0.203 0.463 0.397)\",\n    mintA12: \"color(display-p3 0.136 0.259 0.236)\",\n};\nconst lime = {\n    lime1: \"#fcfdfa\",\n    lime2: \"#f8faf3\",\n    lime3: \"#eef6d6\",\n    lime4: \"#e2f0bd\",\n    lime5: \"#d3e7a6\",\n    lime6: \"#c2da91\",\n    lime7: \"#abc978\",\n    lime8: \"#8db654\",\n    lime9: \"#bdee63\",\n    lime10: \"#b0e64c\",\n    lime11: \"#5c7c2f\",\n    lime12: \"#37401c\",\n};\nconst limeA = {\n    limeA1: \"#66990005\",\n    limeA2: \"#6b95000c\",\n    limeA3: \"#96c80029\",\n    limeA4: \"#8fc60042\",\n    limeA5: \"#81bb0059\",\n    limeA6: \"#72aa006e\",\n    limeA7: \"#61990087\",\n    limeA8: \"#559200ab\",\n    limeA9: \"#93e4009c\",\n    limeA10: \"#8fdc00b3\",\n    limeA11: \"#375f00d0\",\n    limeA12: \"#1e2900e3\",\n};\nconst limeP3 = {\n    lime1: \"color(display-p3 0.989 0.992 0.981)\",\n    lime2: \"color(display-p3 0.975 0.98 0.954)\",\n    lime3: \"color(display-p3 0.939 0.965 0.851)\",\n    lime4: \"color(display-p3 0.896 0.94 0.76)\",\n    lime5: \"color(display-p3 0.843 0.903 0.678)\",\n    lime6: \"color(display-p3 0.778 0.852 0.599)\",\n    lime7: \"color(display-p3 0.694 0.784 0.508)\",\n    lime8: \"color(display-p3 0.585 0.707 0.378)\",\n    lime9: \"color(display-p3 0.78 0.928 0.466)\",\n    lime10: \"color(display-p3 0.734 0.896 0.397)\",\n    lime11: \"color(display-p3 0.386 0.482 0.227)\",\n    lime12: \"color(display-p3 0.222 0.25 0.128)\",\n};\nconst limeP3A = {\n    limeA1: \"color(display-p3 0.412 0.608 0.02 / 0.02)\",\n    limeA2: \"color(display-p3 0.514 0.592 0.024 / 0.048)\",\n    limeA3: \"color(display-p3 0.584 0.765 0.008 / 0.15)\",\n    limeA4: \"color(display-p3 0.561 0.757 0.004 / 0.24)\",\n    limeA5: \"color(display-p3 0.514 0.698 0.004 / 0.322)\",\n    limeA6: \"color(display-p3 0.443 0.627 0 / 0.4)\",\n    limeA7: \"color(display-p3 0.376 0.561 0.004 / 0.491)\",\n    limeA8: \"color(display-p3 0.333 0.529 0 / 0.624)\",\n    limeA9: \"color(display-p3 0.588 0.867 0 / 0.534)\",\n    limeA10: \"color(display-p3 0.561 0.827 0 / 0.604)\",\n    limeA11: \"color(display-p3 0.386 0.482 0.227)\",\n    limeA12: \"color(display-p3 0.222 0.25 0.128)\",\n};\nconst yellow = {\n    yellow1: \"#fdfdf9\",\n    yellow2: \"#fefce9\",\n    yellow3: \"#fffab8\",\n    yellow4: \"#fff394\",\n    yellow5: \"#ffe770\",\n    yellow6: \"#f3d768\",\n    yellow7: \"#e4c767\",\n    yellow8: \"#d5ae39\",\n    yellow9: \"#ffe629\",\n    yellow10: \"#ffdc00\",\n    yellow11: \"#9e6c00\",\n    yellow12: \"#473b1f\",\n};\nconst yellowA = {\n    yellowA1: \"#aaaa0006\",\n    yellowA2: \"#f4dd0016\",\n    yellowA3: \"#ffee0047\",\n    yellowA4: \"#ffe3016b\",\n    yellowA5: \"#ffd5008f\",\n    yellowA6: \"#ebbc0097\",\n    yellowA7: \"#d2a10098\",\n    yellowA8: \"#c99700c6\",\n    yellowA9: \"#ffe100d6\",\n    yellowA10: \"#ffdc00\",\n    yellowA11: \"#9e6c00\",\n    yellowA12: \"#2e2000e0\",\n};\nconst yellowP3 = {\n    yellow1: \"color(display-p3 0.992 0.992 0.978)\",\n    yellow2: \"color(display-p3 0.995 0.99 0.922)\",\n    yellow3: \"color(display-p3 0.997 0.982 0.749)\",\n    yellow4: \"color(display-p3 0.992 0.953 0.627)\",\n    yellow5: \"color(display-p3 0.984 0.91 0.51)\",\n    yellow6: \"color(display-p3 0.934 0.847 0.474)\",\n    yellow7: \"color(display-p3 0.876 0.785 0.46)\",\n    yellow8: \"color(display-p3 0.811 0.689 0.313)\",\n    yellow9: \"color(display-p3 1 0.92 0.22)\",\n    yellow10: \"color(display-p3 0.977 0.868 0.291)\",\n    yellow11: \"color(display-p3 0.6 0.44 0)\",\n    yellow12: \"color(display-p3 0.271 0.233 0.137)\",\n};\nconst yellowP3A = {\n    yellowA1: \"color(display-p3 0.675 0.675 0.024 / 0.024)\",\n    yellowA2: \"color(display-p3 0.953 0.855 0.008 / 0.079)\",\n    yellowA3: \"color(display-p3 0.988 0.925 0.004 / 0.251)\",\n    yellowA4: \"color(display-p3 0.98 0.875 0.004 / 0.373)\",\n    yellowA5: \"color(display-p3 0.969 0.816 0.004 / 0.491)\",\n    yellowA6: \"color(display-p3 0.875 0.71 0 / 0.526)\",\n    yellowA7: \"color(display-p3 0.769 0.604 0 / 0.542)\",\n    yellowA8: \"color(display-p3 0.725 0.549 0 / 0.687)\",\n    yellowA9: \"color(display-p3 1 0.898 0 / 0.781)\",\n    yellowA10: \"color(display-p3 0.969 0.812 0 / 0.71)\",\n    yellowA11: \"color(display-p3 0.6 0.44 0)\",\n    yellowA12: \"color(display-p3 0.271 0.233 0.137)\",\n};\nconst amber = {\n    amber1: \"#fefdfb\",\n    amber2: \"#fefbe9\",\n    amber3: \"#fff7c2\",\n    amber4: \"#ffee9c\",\n    amber5: \"#fbe577\",\n    amber6: \"#f3d673\",\n    amber7: \"#e9c162\",\n    amber8: \"#e2a336\",\n    amber9: \"#ffc53d\",\n    amber10: \"#ffba18\",\n    amber11: \"#ab6400\",\n    amber12: \"#4f3422\",\n};\nconst amberA = {\n    amberA1: \"#c0800004\",\n    amberA2: \"#f4d10016\",\n    amberA3: \"#ffde003d\",\n    amberA4: \"#ffd40063\",\n    amberA5: \"#f8cf0088\",\n    amberA6: \"#eab5008c\",\n    amberA7: \"#dc9b009d\",\n    amberA8: \"#da8a00c9\",\n    amberA9: \"#ffb300c2\",\n    amberA10: \"#ffb300e7\",\n    amberA11: \"#ab6400\",\n    amberA12: \"#341500dd\",\n};\nconst amberP3 = {\n    amber1: \"color(display-p3 0.995 0.992 0.985)\",\n    amber2: \"color(display-p3 0.994 0.986 0.921)\",\n    amber3: \"color(display-p3 0.994 0.969 0.782)\",\n    amber4: \"color(display-p3 0.989 0.937 0.65)\",\n    amber5: \"color(display-p3 0.97 0.902 0.527)\",\n    amber6: \"color(display-p3 0.936 0.844 0.506)\",\n    amber7: \"color(display-p3 0.89 0.762 0.443)\",\n    amber8: \"color(display-p3 0.85 0.65 0.3)\",\n    amber9: \"color(display-p3 1 0.77 0.26)\",\n    amber10: \"color(display-p3 0.959 0.741 0.274)\",\n    amber11: \"color(display-p3 0.64 0.4 0)\",\n    amber12: \"color(display-p3 0.294 0.208 0.145)\",\n};\nconst amberP3A = {\n    amberA1: \"color(display-p3 0.757 0.514 0.024 / 0.016)\",\n    amberA2: \"color(display-p3 0.902 0.804 0.008 / 0.079)\",\n    amberA3: \"color(display-p3 0.965 0.859 0.004 / 0.22)\",\n    amberA4: \"color(display-p3 0.969 0.82 0.004 / 0.35)\",\n    amberA5: \"color(display-p3 0.933 0.796 0.004 / 0.475)\",\n    amberA6: \"color(display-p3 0.875 0.682 0.004 / 0.495)\",\n    amberA7: \"color(display-p3 0.804 0.573 0 / 0.557)\",\n    amberA8: \"color(display-p3 0.788 0.502 0 / 0.699)\",\n    amberA9: \"color(display-p3 1 0.686 0 / 0.742)\",\n    amberA10: \"color(display-p3 0.945 0.643 0 / 0.726)\",\n    amberA11: \"color(display-p3 0.64 0.4 0)\",\n    amberA12: \"color(display-p3 0.294 0.208 0.145)\",\n};\nconst orange = {\n    orange1: \"#fefcfb\",\n    orange2: \"#fff7ed\",\n    orange3: \"#ffefd6\",\n    orange4: \"#ffdfb5\",\n    orange5: \"#ffd19a\",\n    orange6: \"#ffc182\",\n    orange7: \"#f5ae73\",\n    orange8: \"#ec9455\",\n    orange9: \"#f76b15\",\n    orange10: \"#ef5f00\",\n    orange11: \"#cc4e00\",\n    orange12: \"#582d1d\",\n};\nconst orangeA = {\n    orangeA1: \"#c0400004\",\n    orangeA2: \"#ff8e0012\",\n    orangeA3: \"#ff9c0029\",\n    orangeA4: \"#ff91014a\",\n    orangeA5: \"#ff8b0065\",\n    orangeA6: \"#ff81007d\",\n    orangeA7: \"#ed6c008c\",\n    orangeA8: \"#e35f00aa\",\n    orangeA9: \"#f65e00ea\",\n    orangeA10: \"#ef5f00\",\n    orangeA11: \"#cc4e00\",\n    orangeA12: \"#431200e2\",\n};\nconst orangeP3 = {\n    orange1: \"color(display-p3 0.995 0.988 0.985)\",\n    orange2: \"color(display-p3 0.994 0.968 0.934)\",\n    orange3: \"color(display-p3 0.989 0.938 0.85)\",\n    orange4: \"color(display-p3 1 0.874 0.687)\",\n    orange5: \"color(display-p3 1 0.821 0.583)\",\n    orange6: \"color(display-p3 0.975 0.767 0.545)\",\n    orange7: \"color(display-p3 0.919 0.693 0.486)\",\n    orange8: \"color(display-p3 0.877 0.597 0.379)\",\n    orange9: \"color(display-p3 0.9 0.45 0.2)\",\n    orange10: \"color(display-p3 0.87 0.409 0.164)\",\n    orange11: \"color(display-p3 0.76 0.34 0)\",\n    orange12: \"color(display-p3 0.323 0.185 0.127)\",\n};\nconst orangeP3A = {\n    orangeA1: \"color(display-p3 0.757 0.267 0.024 / 0.016)\",\n    orangeA2: \"color(display-p3 0.886 0.533 0.008 / 0.067)\",\n    orangeA3: \"color(display-p3 0.922 0.584 0.008 / 0.15)\",\n    orangeA4: \"color(display-p3 1 0.604 0.004 / 0.314)\",\n    orangeA5: \"color(display-p3 1 0.569 0.004 / 0.416)\",\n    orangeA6: \"color(display-p3 0.949 0.494 0.004 / 0.455)\",\n    orangeA7: \"color(display-p3 0.839 0.408 0 / 0.514)\",\n    orangeA8: \"color(display-p3 0.804 0.349 0 / 0.62)\",\n    orangeA9: \"color(display-p3 0.878 0.314 0 / 0.8)\",\n    orangeA10: \"color(display-p3 0.843 0.29 0 / 0.836)\",\n    orangeA11: \"color(display-p3 0.76 0.34 0)\",\n    orangeA12: \"color(display-p3 0.323 0.185 0.127)\",\n};\n\nconst blackA = {\n    blackA1: \"rgba(0, 0, 0, 0.05)\",\n    blackA2: \"rgba(0, 0, 0, 0.1)\",\n    blackA3: \"rgba(0, 0, 0, 0.15)\",\n    blackA4: \"rgba(0, 0, 0, 0.2)\",\n    blackA5: \"rgba(0, 0, 0, 0.3)\",\n    blackA6: \"rgba(0, 0, 0, 0.4)\",\n    blackA7: \"rgba(0, 0, 0, 0.5)\",\n    blackA8: \"rgba(0, 0, 0, 0.6)\",\n    blackA9: \"rgba(0, 0, 0, 0.7)\",\n    blackA10: \"rgba(0, 0, 0, 0.8)\",\n    blackA11: \"rgba(0, 0, 0, 0.9)\",\n    blackA12: \"rgba(0, 0, 0, 0.95)\",\n};\nconst blackP3A = {\n    blackA1: \"color(display-p3 0 0 0 / 0.05)\",\n    blackA2: \"color(display-p3 0 0 0 / 0.1)\",\n    blackA3: \"color(display-p3 0 0 0 / 0.15)\",\n    blackA4: \"color(display-p3 0 0 0 / 0.2)\",\n    blackA5: \"color(display-p3 0 0 0 / 0.3)\",\n    blackA6: \"color(display-p3 0 0 0 / 0.4)\",\n    blackA7: \"color(display-p3 0 0 0 / 0.5)\",\n    blackA8: \"color(display-p3 0 0 0 / 0.6)\",\n    blackA9: \"color(display-p3 0 0 0 / 0.7)\",\n    blackA10: \"color(display-p3 0 0 0 / 0.8)\",\n    blackA11: \"color(display-p3 0 0 0 / 0.9)\",\n    blackA12: \"color(display-p3 0 0 0 / 0.95)\",\n};\n\nconst whiteA = {\n    whiteA1: \"rgba(255, 255, 255, 0.05)\",\n    whiteA2: \"rgba(255, 255, 255, 0.1)\",\n    whiteA3: \"rgba(255, 255, 255, 0.15)\",\n    whiteA4: \"rgba(255, 255, 255, 0.2)\",\n    whiteA5: \"rgba(255, 255, 255, 0.3)\",\n    whiteA6: \"rgba(255, 255, 255, 0.4)\",\n    whiteA7: \"rgba(255, 255, 255, 0.5)\",\n    whiteA8: \"rgba(255, 255, 255, 0.6)\",\n    whiteA9: \"rgba(255, 255, 255, 0.7)\",\n    whiteA10: \"rgba(255, 255, 255, 0.8)\",\n    whiteA11: \"rgba(255, 255, 255, 0.9)\",\n    whiteA12: \"rgba(255, 255, 255, 0.95)\",\n};\nconst whiteP3A = {\n    whiteA1: \"color(display-p3 1 1 1 / 0.05)\",\n    whiteA2: \"color(display-p3 1 1 1 / 0.1)\",\n    whiteA3: \"color(display-p3 1 1 1 / 0.15)\",\n    whiteA4: \"color(display-p3 1 1 1 / 0.2)\",\n    whiteA5: \"color(display-p3 1 1 1 / 0.3)\",\n    whiteA6: \"color(display-p3 1 1 1 / 0.4)\",\n    whiteA7: \"color(display-p3 1 1 1 / 0.5)\",\n    whiteA8: \"color(display-p3 1 1 1 / 0.6)\",\n    whiteA9: \"color(display-p3 1 1 1 / 0.7)\",\n    whiteA10: \"color(display-p3 1 1 1 / 0.8)\",\n    whiteA11: \"color(display-p3 1 1 1 / 0.9)\",\n    whiteA12: \"color(display-p3 1 1 1 / 0.95)\",\n};\n\nexport { amber, amberA, amberDark, amberDarkA, amberDarkP3, amberDarkP3A, amberP3, amberP3A, blackA, blackP3A, blue, blueA, blueDark, blueDarkA, blueDarkP3, blueDarkP3A, blueP3, blueP3A, bronze, bronzeA, bronzeDark, bronzeDarkA, bronzeDarkP3, bronzeDarkP3A, bronzeP3, bronzeP3A, brown, brownA, brownDark, brownDarkA, brownDarkP3, brownDarkP3A, brownP3, brownP3A, crimson, crimsonA, crimsonDark, crimsonDarkA, crimsonDarkP3, crimsonDarkP3A, crimsonP3, crimsonP3A, cyan, cyanA, cyanDark, cyanDarkA, cyanDarkP3, cyanDarkP3A, cyanP3, cyanP3A, gold, goldA, goldDark, goldDarkA, goldDarkP3, goldDarkP3A, goldP3, goldP3A, grass, grassA, grassDark, grassDarkA, grassDarkP3, grassDarkP3A, grassP3, grassP3A, gray, grayA, grayDark, grayDarkA, grayDarkP3, grayDarkP3A, grayP3, grayP3A, green, greenA, greenDark, greenDarkA, greenDarkP3, greenDarkP3A, greenP3, greenP3A, indigo, indigoA, indigoDark, indigoDarkA, indigoDarkP3, indigoDarkP3A, indigoP3, indigoP3A, iris, irisA, irisDark, irisDarkA, irisDarkP3, irisDarkP3A, irisP3, irisP3A, jade, jadeA, jadeDark, jadeDarkA, jadeDarkP3, jadeDarkP3A, jadeP3, jadeP3A, lime, limeA, limeDark, limeDarkA, limeDarkP3, limeDarkP3A, limeP3, limeP3A, mauve, mauveA, mauveDark, mauveDarkA, mauveDarkP3, mauveDarkP3A, mauveP3, mauveP3A, mint, mintA, mintDark, mintDarkA, mintDarkP3, mintDarkP3A, mintP3, mintP3A, olive, oliveA, oliveDark, oliveDarkA, oliveDarkP3, oliveDarkP3A, oliveP3, oliveP3A, orange, orangeA, orangeDark, orangeDarkA, orangeDarkP3, orangeDarkP3A, orangeP3, orangeP3A, pink, pinkA, pinkDark, pinkDarkA, pinkDarkP3, pinkDarkP3A, pinkP3, pinkP3A, plum, plumA, plumDark, plumDarkA, plumDarkP3, plumDarkP3A, plumP3, plumP3A, purple, purpleA, purpleDark, purpleDarkA, purpleDarkP3, purpleDarkP3A, purpleP3, purpleP3A, red, redA, redDark, redDarkA, redDarkP3, redDarkP3A, redP3, redP3A, ruby, rubyA, rubyDark, rubyDarkA, rubyDarkP3, rubyDarkP3A, rubyP3, rubyP3A, sage, sageA, sageDark, sageDarkA, sageDarkP3, sageDarkP3A, sageP3, sageP3A, sand, sandA, sandDark, sandDarkA, sandDarkP3, sandDarkP3A, sandP3, sandP3A, sky, skyA, skyDark, skyDarkA, skyDarkP3, skyDarkP3A, skyP3, skyP3A, slate, slateA, slateDark, slateDarkA, slateDarkP3, slateDarkP3A, slateP3, slateP3A, teal, tealA, tealDark, tealDarkA, tealDarkP3, tealDarkP3A, tealP3, tealP3A, tomato, tomatoA, tomatoDark, tomatoDarkA, tomatoDarkP3, tomatoDarkP3A, tomatoP3, tomatoP3A, violet, violetA, violetDark, violetDarkA, violetDarkP3, violetDarkP3A, violetP3, violetP3A, whiteA, whiteP3A, yellow, yellowA, yellowDark, yellowDarkA, yellowDarkP3, yellowDarkP3A, yellowP3, yellowP3A };\n"], "mappings": ";;;AAAA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,UAAU;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,UAAU;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,aAAa;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,eAAe;AAAA,EACjB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AAEA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,MAAM;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,aAAa;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAChB;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,MAAM;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,OAAO;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,UAAU;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AAEA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AAEA,IAAM,SAAS;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;AACA,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACd;", "names": []}