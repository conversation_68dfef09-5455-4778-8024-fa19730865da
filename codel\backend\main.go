package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	"github.com/semanser/ai-coder/config"
	"github.com/semanser/ai-coder/providers"
	"github.com/semanser/ai-coder/router"
	"github.com/semanser/ai-coder/storage"
)

func main() {
	log.Println("Starting AI Coder...")

	// Initialize configuration
	config.Init()

	// Initialize memory storage (no database required)
	memStorage := storage.NewMemoryStorage()

	// Initialize the active provider
	err := providers.InitActiveProvider(nil)
	if err != nil {
		log.Printf("Failed to initialize active provider, using default: %v", err)
	}

	port := strconv.Itoa(config.Config.Port)

	// Create router with memory storage
	r := router.NewWithMemoryStorage(memStorage)

	// Setup graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON><PERSON>, os.Interrupt, syscall.SIGTERM)

	// Run the server in a separate goroutine
	go func() {
		log.Printf("Server starting on http://localhost:%s", port)
		log.Printf("GraphQL playground available at http://localhost:%s/playground", port)
		if err := http.ListenAndServe(":"+port, r); err != nil {
			log.Fatalf("HTTP server error: %v", err)
		}
	}()

	// Wait for termination signal
	<-sigChan
	log.Println("Shutting down...")
	log.Println("Shutdown complete")
}
