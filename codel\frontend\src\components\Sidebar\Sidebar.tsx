import { Model } from "@/generated/graphql";
import { MenuItem, MenuItemProps } from "./MenuItem/MenuItem";
import { NewTask } from "./NewTask/NewTask";
import { wrapperStyles } from "./Sidebar.css";

type SidebarProps = {
  items: MenuItemProps[];
  availableModels: Model[];
  activeProvider: string;
  onProviderSwitch: (id: string) => void;
};

export const Sidebar = ({
  items = [],
  availableModels = [],
  activeProvider,
  onProviderSwitch
}: SidebarProps) => {
  return (
    <div className={wrapperStyles}>
      <NewTask
        availableModels={availableModels}
        activeProvider={activeProvider}
        onProviderSwitch={onProviderSwitch}
      />
      {items.map((item) => (
        <MenuItem key={item.id} {...item} />
      ))}
    </div>
  );
};
