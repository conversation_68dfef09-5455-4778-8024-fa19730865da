package providers

import (
	"database/sql"
	"encoding/json"
	"net/http"

	"github.com/semanser/ai-coder/database"
)

type SwitchRequest struct {
	Provider ProviderType `json:"provider"`
}

// HasRunningTasksFunc is a function type that can be set by the executor package
var HasRunningTasksFunc func() bool

func SwitchProviderHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req SwitchRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		// Check for running tasks if the function is available
		if HasRunningTasksFunc != nil && HasRunningTasksFunc() {
			w.WriteHeader(http.StatusConflict)
			return
		}

		// Switch provider
		if err := SetActiveProvider(database.New(db), req.Provider); err != nil {
			http.Error(w, "Failed to switch provider", http.StatusInternalServerError)
			return
		}

		w.<PERSON><PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
	}
}
