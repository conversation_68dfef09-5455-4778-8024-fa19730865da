{"version": 3, "sources": ["../../@uidotdev/usehooks/index.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction isShallowEqual(object1, object2) {\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (let key of keys1) {\n    if (object1[key] !== object2[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isTouchEvent({ nativeEvent }) {\n  return window.TouchEvent\n    ? nativeEvent instanceof TouchEvent\n    : \"touches\" in nativeEvent;\n}\n\nfunction isMouseEvent(event) {\n  return event.nativeEvent instanceof MouseEvent;\n}\n\nfunction throttle(cb, ms) {\n  let lastTime = 0;\n  return () => {\n    const now = Date.now();\n    if (now - lastTime >= ms) {\n      cb();\n      lastTime = now;\n    }\n  };\n}\n\nfunction isPlainObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n\nfunction dispatchStorageEvent(key, newValue) {\n  window.dispatchEvent(new StorageEvent(\"storage\", { key, newValue }));\n}\n\nexport function useBattery() {\n  const [state, setState] = React.useState({\n    supported: true,\n    loading: true,\n    level: null,\n    charging: null,\n    chargingTime: null,\n    dischargingTime: null,\n  });\n\n  React.useEffect(() => {\n    if (!navigator.getBattery) {\n      setState((s) => ({\n        ...s,\n        supported: false,\n        loading: false,\n      }));\n      return;\n    }\n\n    let battery = null;\n\n    const handleChange = () => {\n      setState({\n        supported: true,\n        loading: false,\n        level: battery.level,\n        charging: battery.charging,\n        chargingTime: battery.chargingTime,\n        dischargingTime: battery.dischargingTime,\n      });\n    };\n\n    navigator.getBattery().then((b) => {\n      battery = b;\n      handleChange();\n\n      b.addEventListener(\"levelchange\", handleChange);\n      b.addEventListener(\"chargingchange\", handleChange);\n      b.addEventListener(\"chargingtimechange\", handleChange);\n      b.addEventListener(\"dischargingtimechange\", handleChange);\n    });\n\n    return () => {\n      if (battery) {\n        battery.removeEventListener(\"levelchange\", handleChange);\n        battery.removeEventListener(\"chargingchange\", handleChange);\n        battery.removeEventListener(\"chargingtimechange\", handleChange);\n        battery.removeEventListener(\"dischargingtimechange\", handleChange);\n      }\n    };\n  }, []);\n\n  return state;\n}\n\nexport function useClickAway(cb) {\n  const ref = React.useRef(null);\n  const refCb = React.useRef(cb);\n\n  React.useLayoutEffect(() => {\n    refCb.current = cb;\n  });\n\n  React.useEffect(() => {\n    const handler = (e) => {\n      const element = ref.current;\n      if (element && !element.contains(e.target)) {\n        refCb.current(e);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handler);\n    document.addEventListener(\"touchstart\", handler);\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handler);\n      document.removeEventListener(\"touchstart\", handler);\n    };\n  }, []);\n\n  return ref;\n}\n\nfunction oldSchoolCopy(text) {\n  const tempTextArea = document.createElement(\"textarea\");\n  tempTextArea.value = text;\n  document.body.appendChild(tempTextArea);\n  tempTextArea.select();\n  document.execCommand(\"copy\");\n  document.body.removeChild(tempTextArea);\n}\n\nexport function useCopyToClipboard() {\n  const [state, setState] = React.useState(null);\n\n  const copyToClipboard = React.useCallback((value) => {\n    const handleCopy = async () => {\n      try {\n        if (navigator?.clipboard?.writeText) {\n          await navigator.clipboard.writeText(value);\n          setState(value);\n        } else {\n          throw new Error(\"writeText not supported\");\n        }\n      } catch (e) {\n        oldSchoolCopy(value);\n        setState(value);\n      }\n    };\n\n    handleCopy();\n  }, []);\n\n  return [state, copyToClipboard];\n}\n\nexport function useCounter(startingValue = 0, options = {}) {\n  const { min, max } = options;\n\n  if (typeof min === \"number\" && startingValue < min) {\n    throw new Error(\n      `Your starting value of ${startingValue} is less than your min of ${min}.`\n    );\n  }\n\n  if (typeof max === \"number\" && startingValue > max) {\n    throw new Error(\n      `Your starting value of ${startingValue} is greater than your max of ${max}.`\n    );\n  }\n\n  const [count, setCount] = React.useState(startingValue);\n\n  const increment = React.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c + 1;\n\n      if (typeof max === \"number\" && nextCount > max) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [max]);\n\n  const decrement = React.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c - 1;\n\n      if (typeof min === \"number\" && nextCount < min) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [min]);\n\n  const set = React.useCallback(\n    (nextCount) => {\n      setCount((c) => {\n        if (typeof max === \"number\" && nextCount > max) {\n          return c;\n        }\n\n        if (typeof min === \"number\" && nextCount < min) {\n          return c;\n        }\n\n        return nextCount;\n      });\n    },\n    [max, min]\n  );\n\n  const reset = React.useCallback(() => {\n    setCount(startingValue);\n  }, [startingValue]);\n\n  return [\n    count,\n    {\n      increment,\n      decrement,\n      set,\n      reset,\n    },\n  ];\n}\n\nexport function useDebounce(value, delay) {\n  const [debouncedValue, setDebouncedValue] = React.useState(value);\n\n  React.useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\nexport function useDefault(initialValue, defaultValue) {\n  const [state, setState] = React.useState(initialValue);\n\n  if (typeof state === \"undefined\" || state === null) {\n    return [defaultValue, setState];\n  }\n\n  return [state, setState];\n}\n\nexport function useDocumentTitle(title) {\n  React.useEffect(() => {\n    document.title = title;\n  }, [title]);\n}\n\nexport function useFavicon(url) {\n  React.useEffect(() => {\n    let link = document.querySelector(`link[rel~=\"icon\"]`);\n\n    if (!link) {\n      link = document.createElement(\"link\");\n      link.type = \"image/x-icon\";\n      link.rel = \"icon\";\n      link.href = url;\n      document.head.appendChild(link);\n    } else {\n      link.href = url;\n    }\n  }, [url]);\n}\n\nexport function useGeolocation(options = {}) {\n  const [state, setState] = React.useState({\n    loading: true,\n    accuracy: null,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    latitude: null,\n    longitude: null,\n    speed: null,\n    timestamp: null,\n    error: null,\n  });\n\n  const optionsRef = React.useRef(options);\n\n  React.useEffect(() => {\n    const onEvent = ({ coords, timestamp }) => {\n      setState({\n        loading: false,\n        timestamp,\n        latitude: coords.latitude,\n        longitude: coords.longitude,\n        altitude: coords.altitude,\n        accuracy: coords.accuracy,\n        altitudeAccuracy: coords.altitudeAccuracy,\n        heading: coords.heading,\n        speed: coords.speed,\n      });\n    };\n\n    const onEventError = (error) => {\n      setState((s) => ({\n        ...s,\n        loading: false,\n        error,\n      }));\n    };\n\n    navigator.geolocation.getCurrentPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    const watchId = navigator.geolocation.watchPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    return () => {\n      navigator.geolocation.clearWatch(watchId);\n    };\n  }, []);\n\n  return state;\n}\n\nconst initialUseHistoryStateState = {\n  past: [],\n  present: null,\n  future: [],\n};\n\nconst useHistoryStateReducer = (state, action) => {\n  const { past, present, future } = state;\n\n  if (action.type === \"UNDO\") {\n    return {\n      past: past.slice(0, past.length - 1),\n      present: past[past.length - 1],\n      future: [present, ...future],\n    };\n  } else if (action.type === \"REDO\") {\n    return {\n      past: [...past, present],\n      present: future[0],\n      future: future.slice(1),\n    };\n  } else if (action.type === \"SET\") {\n    const { newPresent } = action;\n\n    if (action.newPresent === present) {\n      return state;\n    }\n\n    return {\n      past: [...past, present],\n      present: newPresent,\n      future: [],\n    };\n  } else if (action.type === \"CLEAR\") {\n    return {\n      ...initialUseHistoryStateState,\n      present: action.initialPresent,\n    };\n  } else {\n    throw new Error(\"Unsupported action type\");\n  }\n};\n\nexport function useHistoryState(initialPresent = {}) {\n  const initialPresentRef = React.useRef(initialPresent);\n\n  const [state, dispatch] = React.useReducer(useHistoryStateReducer, {\n    ...initialUseHistoryStateState,\n    present: initialPresentRef.current,\n  });\n\n  const canUndo = state.past.length !== 0;\n  const canRedo = state.future.length !== 0;\n\n  const undo = React.useCallback(() => {\n    if (canUndo) {\n      dispatch({ type: \"UNDO\" });\n    }\n  }, [canUndo]);\n\n  const redo = React.useCallback(() => {\n    if (canRedo) {\n      dispatch({ type: \"REDO\" });\n    }\n  }, [canRedo]);\n\n  const set = React.useCallback(\n    (newPresent) => dispatch({ type: \"SET\", newPresent }),\n    []\n  );\n\n  const clear = React.useCallback(\n    () =>\n      dispatch({ type: \"CLEAR\", initialPresent: initialPresentRef.current }),\n    []\n  );\n\n  return { state: state.present, set, undo, redo, clear, canUndo, canRedo };\n}\n\nexport function useHover() {\n  const [hovering, setHovering] = React.useState(false);\n  const previousNode = React.useRef(null);\n\n  const handleMouseEnter = React.useCallback(() => {\n    setHovering(true);\n  }, []);\n\n  const handleMouseLeave = React.useCallback(() => {\n    setHovering(false);\n  }, []);\n\n  const customRef = React.useCallback(\n    (node) => {\n      if (previousNode.current?.nodeType === Node.ELEMENT_NODE) {\n        previousNode.current.removeEventListener(\n          \"mouseenter\",\n          handleMouseEnter\n        );\n        previousNode.current.removeEventListener(\n          \"mouseleave\",\n          handleMouseLeave\n        );\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        node.addEventListener(\"mouseenter\", handleMouseEnter);\n        node.addEventListener(\"mouseleave\", handleMouseLeave);\n      }\n\n      previousNode.current = node;\n    },\n    [handleMouseEnter, handleMouseLeave]\n  );\n\n  return [customRef, hovering];\n}\n\nexport function useIdle(ms = 1000 * 60) {\n  const [idle, setIdle] = React.useState(false);\n\n  React.useEffect(() => {\n    let timeoutId;\n\n    const handleTimeout = () => {\n      setIdle(true);\n    };\n\n    const handleEvent = throttle((e) => {\n      setIdle(false);\n\n      window.clearTimeout(timeoutId);\n      timeoutId = window.setTimeout(handleTimeout, ms);\n    }, 500);\n\n    const handleVisibilityChange = () => {\n      if (!document.hidden) {\n        handleEvent();\n      }\n    };\n\n    timeoutId = window.setTimeout(handleTimeout, ms);\n\n    window.addEventListener(\"mousemove\", handleEvent);\n    window.addEventListener(\"mousedown\", handleEvent);\n    window.addEventListener(\"resize\", handleEvent);\n    window.addEventListener(\"keydown\", handleEvent);\n    window.addEventListener(\"touchstart\", handleEvent);\n    window.addEventListener(\"wheel\", handleEvent);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n\n    return () => {\n      window.removeEventListener(\"mousemove\", handleEvent);\n      window.removeEventListener(\"mousedown\", handleEvent);\n      window.removeEventListener(\"resize\", handleEvent);\n      window.removeEventListener(\"keydown\", handleEvent);\n      window.removeEventListener(\"touchstart\", handleEvent);\n      window.removeEventListener(\"wheel\", handleEvent);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      window.clearTimeout(timeoutId);\n    };\n  }, [ms]);\n\n  return idle;\n}\n\nexport function useIntersectionObserver(options = {}) {\n  const { threshold = 1, root = null, rootMargin = \"0px\" } = options;\n  const [entry, setEntry] = React.useState(null);\n\n  const previousObserver = React.useRef(null);\n\n  const customRef = React.useCallback(\n    (node) => {\n      if (previousObserver.current) {\n        previousObserver.current.disconnect();\n        previousObserver.current = null;\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        const observer = new IntersectionObserver(\n          ([entry]) => {\n            setEntry(entry);\n          },\n          { threshold, root, rootMargin }\n        );\n\n        observer.observe(node);\n        previousObserver.current = observer;\n      }\n    },\n    [threshold, root, rootMargin]\n  );\n\n  return [customRef, entry];\n}\n\nexport function useIsClient() {\n  const [isClient, setIsClient] = React.useState(false);\n\n  React.useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\nexport function useIsFirstRender() {\n  const renderRef = React.useRef(true);\n\n  if (renderRef.current === true) {\n    renderRef.current = false;\n    return true;\n  }\n\n  return renderRef.current;\n}\n\nexport function useList(defaultList = []) {\n  const [list, setList] = React.useState(defaultList);\n\n  const set = React.useCallback((l) => {\n    setList(l);\n  }, []);\n\n  const push = React.useCallback((element) => {\n    setList((l) => [...l, element]);\n  }, []);\n\n  const removeAt = React.useCallback((index) => {\n    setList((l) => [...l.slice(0, index), ...l.slice(index + 1)]);\n  }, []);\n\n  const insertAt = React.useCallback((index, element) => {\n    setList((l) => [...l.slice(0, index), element, ...l.slice(index)]);\n  }, []);\n\n  const updateAt = React.useCallback((index, element) => {\n    setList((l) => l.map((e, i) => (i === index ? element : e)));\n  }, []);\n\n  const clear = React.useCallback(() => setList([]), []);\n\n  return [list, { set, push, removeAt, insertAt, updateAt, clear }];\n}\n\nconst setLocalStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.localStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeLocalStorageItem = (key) => {\n  window.localStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getLocalStorageItem = (key) => {\n  return window.localStorage.getItem(key);\n};\n\nconst useLocalStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getLocalStorageServerSnapshot = () => {\n  throw Error(\"useLocalStorage is a client-only hook\");\n};\n\nexport function useLocalStorage(key, initialValue) {\n  const getSnapshot = () => getLocalStorageItem(key);\n\n  const store = React.useSyncExternalStore(\n    useLocalStorageSubscribe,\n    getSnapshot,\n    getLocalStorageServerSnapshot\n  );\n\n  const setState = React.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeLocalStorageItem(key);\n        } else {\n          setLocalStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  React.useEffect(() => {\n    if (\n      getLocalStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setLocalStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nexport function useLockBodyScroll() {\n  React.useLayoutEffect(() => {\n    const originalStyle = window.getComputedStyle(document.body).overflow;\n    document.body.style.overflow = \"hidden\";\n    return () => {\n      document.body.style.overflow = originalStyle;\n    };\n  }, []);\n}\n\nexport function useLongPress(callback, options = {}) {\n  const { threshold = 400, onStart, onFinish, onCancel } = options;\n  const isLongPressActive = React.useRef(false);\n  const isPressed = React.useRef(false);\n  const timerId = React.useRef();\n\n  return React.useMemo(() => {\n    if (typeof callback !== \"function\") {\n      return {};\n    }\n\n    const start = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (onStart) {\n        onStart(event);\n      }\n\n      isPressed.current = true;\n      timerId.current = setTimeout(() => {\n        callback(event);\n        isLongPressActive.current = true;\n      }, threshold);\n    };\n\n    const cancel = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (isLongPressActive.current) {\n        if (onFinish) {\n          onFinish(event);\n        }\n      } else if (isPressed.current) {\n        if (onCancel) {\n          onCancel(event);\n        }\n      }\n\n      isLongPressActive.current = false;\n      isPressed.current = false;\n\n      if (timerId.current) {\n        window.clearTimeout(timerId.current);\n      }\n    };\n\n    const mouseHandlers = {\n      onMouseDown: start,\n      onMouseUp: cancel,\n      onMouseLeave: cancel,\n    };\n\n    const touchHandlers = {\n      onTouchStart: start,\n      onTouchEnd: cancel,\n    };\n\n    return {\n      ...mouseHandlers,\n      ...touchHandlers,\n    };\n  }, [callback, threshold, onCancel, onFinish, onStart]);\n}\n\nexport function useMap(initialState) {\n  const mapRef = React.useRef(new Map(initialState));\n  const [, reRender] = React.useReducer((x) => x + 1, 0);\n\n  mapRef.current.set = (...args) => {\n    Map.prototype.set.apply(mapRef.current, args);\n    reRender();\n    return mapRef.current;\n  };\n\n  mapRef.current.clear = (...args) => {\n    Map.prototype.clear.apply(mapRef.current, args);\n    reRender();\n  };\n\n  mapRef.current.delete = (...args) => {\n    const res = Map.prototype.delete.apply(mapRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return mapRef.current;\n}\n\nexport function useMeasure() {\n  const [dimensions, setDimensions] = React.useState({\n    width: null,\n    height: null,\n  });\n\n  const previousObserver = React.useRef(null);\n\n  const customRef = React.useCallback((node) => {\n    if (previousObserver.current) {\n      previousObserver.current.disconnect();\n      previousObserver.current = null;\n    }\n\n    if (node?.nodeType === Node.ELEMENT_NODE) {\n      const observer = new ResizeObserver(([entry]) => {\n        if (entry && entry.borderBoxSize) {\n          const { inlineSize: width, blockSize: height } =\n            entry.borderBoxSize[0];\n\n          setDimensions({ width, height });\n        }\n      });\n\n      observer.observe(node);\n      previousObserver.current = observer;\n    }\n  }, []);\n\n  return [customRef, dimensions];\n}\n\nexport function useMediaQuery(query) {\n  const subscribe = React.useCallback(\n    (callback) => {\n      const matchMedia = window.matchMedia(query);\n\n      matchMedia.addEventListener(\"change\", callback);\n      return () => {\n        matchMedia.removeEventListener(\"change\", callback);\n      };\n    },\n    [query]\n  );\n\n  const getSnapshot = () => {\n    return window.matchMedia(query).matches;\n  };\n\n  const getServerSnapshot = () => {\n    throw Error(\"useMediaQuery is a client-only hook\");\n  };\n\n  return React.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\nexport function useMouse() {\n  const [state, setState] = React.useState({\n    x: 0,\n    y: 0,\n    elementX: 0,\n    elementY: 0,\n    elementPositionX: 0,\n    elementPositionY: 0,\n  });\n\n  const ref = React.useRef(null);\n\n  React.useLayoutEffect(() => {\n    const handleMouseMove = (event) => {\n      let newState = {\n        x: event.pageX,\n        y: event.pageY,\n      };\n\n      if (ref.current?.nodeType === Node.ELEMENT_NODE) {\n        const { left, top } = ref.current.getBoundingClientRect();\n        const elementPositionX = left + window.scrollX;\n        const elementPositionY = top + window.scrollY;\n        const elementX = event.pageX - elementPositionX;\n        const elementY = event.pageY - elementPositionY;\n\n        newState.elementX = elementX;\n        newState.elementY = elementY;\n        newState.elementPositionX = elementPositionX;\n        newState.elementPositionY = elementPositionY;\n      }\n\n      setState((s) => {\n        return {\n          ...s,\n          ...newState,\n        };\n      });\n    };\n\n    document.addEventListener(\"mousemove\", handleMouseMove);\n\n    return () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n    };\n  }, []);\n\n  return [state, ref];\n}\n\nconst getConnection = () => {\n  return (\n    navigator?.connection ||\n    navigator?.mozConnection ||\n    navigator?.webkitConnection\n  );\n};\n\nconst useNetworkStateSubscribe = (callback) => {\n  window.addEventListener(\"online\", callback, { passive: true });\n  window.addEventListener(\"offline\", callback, { passive: true });\n\n  const connection = getConnection();\n\n  if (connection) {\n    connection.addEventListener(\"change\", callback, { passive: true });\n  }\n\n  return () => {\n    window.removeEventListener(\"online\", callback);\n    window.removeEventListener(\"offline\", callback);\n\n    if (connection) {\n      connection.removeEventListener(\"change\", callback);\n    }\n  };\n};\n\nconst getNetworkStateServerSnapshot = () => {\n  throw Error(\"useNetworkState is a client-only hook\");\n};\n\nexport function useNetworkState() {\n  const cache = React.useRef({});\n\n  const getSnapshot = () => {\n    const online = navigator.onLine;\n    const connection = getConnection();\n\n    const nextState = {\n      online,\n      downlink: connection?.downlink,\n      downlinkMax: connection?.downlinkMax,\n      effectiveType: connection?.effectiveType,\n      rtt: connection?.rtt,\n      saveData: connection?.saveData,\n      type: connection?.type,\n    };\n\n    if (isShallowEqual(cache.current, nextState)) {\n      return cache.current;\n    } else {\n      cache.current = nextState;\n      return nextState;\n    }\n  };\n\n  return React.useSyncExternalStore(\n    useNetworkStateSubscribe,\n    getSnapshot,\n    getNetworkStateServerSnapshot\n  );\n}\n\nexport function useObjectState(initialValue) {\n  const [state, setState] = React.useState(initialValue);\n\n  const handleUpdate = React.useCallback((arg) => {\n    if (typeof arg === \"function\") {\n      setState((s) => {\n        const newState = arg(s);\n\n        if (isPlainObject(newState)) {\n          return {\n            ...s,\n            ...newState,\n          };\n        }\n      });\n    }\n\n    if (isPlainObject(arg)) {\n      setState((s) => ({\n        ...s,\n        ...arg,\n      }));\n    }\n  }, []);\n\n  return [state, handleUpdate];\n}\n\nexport function useOrientation() {\n  const [orientation, setOrientation] = React.useState({\n    angle: 0,\n    type: \"landscape-primary\",\n  });\n\n  React.useLayoutEffect(() => {\n    const handleChange = () => {\n      const { angle, type } = window.screen.orientation;\n      setOrientation({\n        angle,\n        type,\n      });\n    };\n\n    const handle_orientationchange = () => {\n      setOrientation({\n        type: \"UNKNOWN\",\n        angle: window.orientation,\n      });\n    };\n\n    if (window.screen?.orientation) {\n      handleChange();\n      window.screen.orientation.addEventListener(\"change\", handleChange);\n    } else {\n      handle_orientationchange();\n      window.addEventListener(\"orientationchange\", handle_orientationchange);\n    }\n\n    return () => {\n      if (window.screen?.orientation) {\n        window.screen.orientation.removeEventListener(\"change\", handleChange);\n      } else {\n        window.removeEventListener(\n          \"orientationchange\",\n          handle_orientationchange\n        );\n      }\n    };\n  }, []);\n\n  return orientation;\n}\n\nconst usePreferredLanguageSubscribe = (cb) => {\n  window.addEventListener(\"languagechange\", cb);\n  return () => window.removeEventListener(\"languagechange\", cb);\n};\n\nconst getPreferredLanguageSnapshot = () => {\n  return navigator.language;\n};\n\nconst getPreferredLanguageServerSnapshot = () => {\n  throw Error(\"usePreferredLanguage is a client-only hook\");\n};\n\nexport function usePreferredLanguage() {\n  return React.useSyncExternalStore(\n    usePreferredLanguageSubscribe,\n    getPreferredLanguageSnapshot,\n    getPreferredLanguageServerSnapshot\n  );\n}\n\nexport function usePrevious(value) {\n  const [current, setCurrent] = React.useState(value);\n  const [previous, setPrevious] = React.useState(null);\n\n  if (value !== current) {\n    setPrevious(current);\n    setCurrent(value);\n  }\n\n  return previous;\n}\n\nexport function useQueue(initialValue = []) {\n  const [queue, setQueue] = React.useState(initialValue);\n\n  const add = React.useCallback((element) => {\n    setQueue((q) => [...q, element]);\n  }, []);\n\n  const remove = React.useCallback(() => {\n    let removedElement;\n\n    setQueue(([first, ...q]) => {\n      removedElement = first;\n      return q;\n    });\n\n    return removedElement;\n  }, []);\n\n  const clear = React.useCallback(() => {\n    setQueue([]);\n  }, []);\n\n  return {\n    add,\n    remove,\n    clear,\n    first: queue[0],\n    last: queue[queue.length - 1],\n    size: queue.length,\n    queue,\n  };\n}\n\nexport function useRenderCount() {\n  const count = React.useRef(0);\n\n  count.current++;\n\n  return count.current;\n}\n\nexport function useRenderInfo(name = \"Unknown\") {\n  const count = React.useRef(0);\n  const lastRender = React.useRef();\n  const now = Date.now();\n\n  count.current++;\n\n  React.useEffect(() => {\n    lastRender.current = Date.now();\n  });\n\n  const sinceLastRender = lastRender.current ? now - lastRender.current : 0;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    const info = {\n      name,\n      renders: count.current,\n      sinceLastRender,\n      timestamp: now,\n    };\n\n    console.log(info);\n\n    return info;\n  }\n}\n\nexport function useScript(src, options = {}) {\n  const [status, setStatus] = React.useState(\"loading\");\n  const optionsRef = React.useRef(options);\n\n  React.useEffect(() => {\n    let script = document.querySelector(`script[src=\"${src}\"]`);\n\n    const domStatus = script?.getAttribute(\"data-status\");\n    if (domStatus) {\n      setStatus(domStatus);\n      return;\n    }\n\n    if (script === null) {\n      script = document.createElement(\"script\");\n      script.src = src;\n      script.async = true;\n      script.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(script);\n\n      const handleScriptLoad = () => {\n        script.setAttribute(\"data-status\", \"ready\");\n        setStatus(\"ready\");\n        removeEventListeners();\n      };\n\n      const handleScriptError = () => {\n        script.setAttribute(\"data-status\", \"error\");\n        setStatus(\"error\");\n        removeEventListeners();\n      };\n\n      const removeEventListeners = () => {\n        script.removeEventListener(\"load\", handleScriptLoad);\n        script.removeEventListener(\"error\", handleScriptError);\n      };\n\n      script.addEventListener(\"load\", handleScriptLoad);\n      script.addEventListener(\"error\", handleScriptError);\n\n      const removeOnUnmount = optionsRef.current.removeOnUnmount;\n\n      return () => {\n        if (removeOnUnmount === true) {\n          script.remove();\n          removeEventListeners();\n        }\n      };\n    } else {\n      setStatus(\"unknown\");\n    }\n  }, [src]);\n\n  return status;\n}\n\nconst setSessionStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.sessionStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeSessionStorageItem = (key) => {\n  window.sessionStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getSessionStorageItem = (key) => {\n  return window.sessionStorage.getItem(key);\n};\n\nconst useSessionStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getSessionStorageServerSnapshot = () => {\n  throw Error(\"useSessionStorage is a client-only hook\");\n};\n\nexport function useSessionStorage(key, initialValue) {\n  const getSnapshot = () => getSessionStorageItem(key);\n\n  const store = React.useSyncExternalStore(\n    useSessionStorageSubscribe,\n    getSnapshot,\n    getSessionStorageServerSnapshot\n  );\n\n  const setState = React.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeSessionStorageItem(key);\n        } else {\n          setSessionStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  React.useEffect(() => {\n    if (\n      getSessionStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setSessionStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nexport function useSet(values) {\n  const setRef = React.useRef(new Set(values));\n  const [, reRender] = React.useReducer((x) => x + 1, 0);\n\n  setRef.current.add = (...args) => {\n    const res = Set.prototype.add.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  setRef.current.clear = (...args) => {\n    Set.prototype.clear.apply(setRef.current, args);\n    reRender();\n  };\n\n  setRef.current.delete = (...args) => {\n    const res = Set.prototype.delete.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return setRef.current;\n}\n\nexport function useThrottle(value, interval = 500) {\n  const [throttledValue, setThrottledValue] = React.useState(value);\n  const lastUpdated = React.useRef(null);\n\n  React.useEffect(() => {\n    const now = Date.now();\n\n    if (lastUpdated.current && now >= lastUpdated.current + interval) {\n      lastUpdated.current = now;\n      setThrottledValue(value);\n    } else {\n      const id = window.setTimeout(() => {\n        lastUpdated.current = now;\n        setThrottledValue(value);\n      }, interval);\n\n      return () => window.clearTimeout(id);\n    }\n  }, [value, interval]);\n\n  return throttledValue;\n}\n\nexport function useToggle(initialValue) {\n  const [on, setOn] = React.useState(() => {\n    if (typeof initialValue === \"boolean\") {\n      return initialValue;\n    }\n\n    return Boolean(initialValue);\n  });\n\n  const handleToggle = React.useCallback((value) => {\n    if (typeof value === \"boolean\") {\n      return setOn(value);\n    }\n\n    return setOn((v) => !v);\n  }, []);\n\n  return [on, handleToggle];\n}\n\nconst useVisibilityChangeSubscribe = (callback) => {\n  document.addEventListener(\"visibilitychange\", callback);\n\n  return () => {\n    document.removeEventListener(\"visibilitychange\", callback);\n  };\n};\n\nconst getVisibilityChangeSnapshot = () => {\n  return document.visibilityState;\n};\n\nconst getVisibilityChangeServerSnapshot = () => {\n  throw Error(\"useVisibilityChange is a client-only hook\");\n};\n\nexport function useVisibilityChange() {\n  const visibilityState = React.useSyncExternalStore(\n    useVisibilityChangeSubscribe,\n    getVisibilityChangeSnapshot,\n    getVisibilityChangeServerSnapshot\n  );\n\n  return visibilityState === \"visible\";\n}\n\nexport function useWindowScroll() {\n  const [state, setState] = React.useState({\n    x: null,\n    y: null,\n  });\n\n  const scrollTo = React.useCallback((...args) => {\n    if (typeof args[0] === \"object\") {\n      window.scrollTo(args[0]);\n    } else if (typeof args[0] === \"number\" && typeof args[1] === \"number\") {\n      window.scrollTo(args[0], args[1]);\n    } else {\n      throw new Error(\n        `Invalid arguments passed to scrollTo. See here for more info. https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollTo`\n      );\n    }\n  }, []);\n\n  React.useLayoutEffect(() => {\n    const handleScroll = () => {\n      setState({ x: window.scrollX, y: window.scrollY });\n    };\n\n    handleScroll();\n    window.addEventListener(\"scroll\", handleScroll);\n\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  return [state, scrollTo];\n}\n\nexport function useWindowSize() {\n  const [size, setSize] = React.useState({\n    width: null,\n    height: null,\n  });\n\n  React.useLayoutEffect(() => {\n    const handleResize = () => {\n      setSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    handleResize();\n    window.addEventListener(\"resize\", handleResize);\n\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n\n  return size;\n}\n"], "mappings": ";;;;;;;;AAAA,YAAuB;AAEvB,SAAS,eAAe,SAAS,SAAS;AACxC,QAAM,QAAQ,OAAO,KAAK,OAAO;AACjC,QAAM,QAAQ,OAAO,KAAK,OAAO;AAEjC,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AAEA,WAAS,OAAO,OAAO;AACrB,QAAI,QAAQ,GAAG,MAAM,QAAQ,GAAG,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,aAAa,EAAE,YAAY,GAAG;AACrC,SAAO,OAAO,aACV,uBAAuB,aACvB,aAAa;AACnB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,uBAAuB;AACtC;AAEA,SAAS,SAAS,IAAI,IAAI;AACxB,MAAI,WAAW;AACf,SAAO,MAAM;AACX,UAAM,MAAM,KAAK,IAAI;AACrB,QAAI,MAAM,YAAY,IAAI;AACxB,SAAG;AACH,iBAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACnD;AAEA,SAAS,qBAAqB,KAAK,UAAU;AAC3C,SAAO,cAAc,IAAI,aAAa,WAAW,EAAE,KAAK,SAAS,CAAC,CAAC;AACrE;AAEO,SAAS,aAAa;AAC3B,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS;AAAA,IACvC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB,CAAC;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,CAAC,UAAU,YAAY;AACzB,eAAS,CAAC,OAAO;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,QACX,SAAS;AAAA,MACX,EAAE;AACF;AAAA,IACF;AAEA,QAAI,UAAU;AAEd,UAAM,eAAe,MAAM;AACzB,eAAS;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO,QAAQ;AAAA,QACf,UAAU,QAAQ;AAAA,QAClB,cAAc,QAAQ;AAAA,QACtB,iBAAiB,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACH;AAEA,cAAU,WAAW,EAAE,KAAK,CAAC,MAAM;AACjC,gBAAU;AACV,mBAAa;AAEb,QAAE,iBAAiB,eAAe,YAAY;AAC9C,QAAE,iBAAiB,kBAAkB,YAAY;AACjD,QAAE,iBAAiB,sBAAsB,YAAY;AACrD,QAAE,iBAAiB,yBAAyB,YAAY;AAAA,IAC1D,CAAC;AAED,WAAO,MAAM;AACX,UAAI,SAAS;AACX,gBAAQ,oBAAoB,eAAe,YAAY;AACvD,gBAAQ,oBAAoB,kBAAkB,YAAY;AAC1D,gBAAQ,oBAAoB,sBAAsB,YAAY;AAC9D,gBAAQ,oBAAoB,yBAAyB,YAAY;AAAA,MACnE;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEO,SAAS,aAAa,IAAI;AAC/B,QAAM,MAAY,aAAO,IAAI;AAC7B,QAAM,QAAc,aAAO,EAAE;AAE7B,EAAM,sBAAgB,MAAM;AAC1B,UAAM,UAAU;AAAA,EAClB,CAAC;AAED,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU,CAAC,MAAM;AACrB,YAAM,UAAU,IAAI;AACpB,UAAI,WAAW,CAAC,QAAQ,SAAS,EAAE,MAAM,GAAG;AAC1C,cAAM,QAAQ,CAAC;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,iBAAiB,aAAa,OAAO;AAC9C,aAAS,iBAAiB,cAAc,OAAO;AAE/C,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,OAAO;AACjD,eAAS,oBAAoB,cAAc,OAAO;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEA,SAAS,cAAc,MAAM;AAC3B,QAAM,eAAe,SAAS,cAAc,UAAU;AACtD,eAAa,QAAQ;AACrB,WAAS,KAAK,YAAY,YAAY;AACtC,eAAa,OAAO;AACpB,WAAS,YAAY,MAAM;AAC3B,WAAS,KAAK,YAAY,YAAY;AACxC;AAEO,SAAS,qBAAqB;AACnC,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,IAAI;AAE7C,QAAM,kBAAwB,kBAAY,CAAC,UAAU;AACnD,UAAM,aAAa,YAAY;AAjJnC;AAkJM,UAAI;AACF,aAAI,4CAAW,cAAX,mBAAsB,WAAW;AACnC,gBAAM,UAAU,UAAU,UAAU,KAAK;AACzC,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC3C;AAAA,MACF,SAAS,GAAG;AACV,sBAAc,KAAK;AACnB,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAEA,eAAW;AAAA,EACb,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,OAAO,eAAe;AAChC;AAEO,SAAS,WAAW,gBAAgB,GAAG,UAAU,CAAC,GAAG;AAC1D,QAAM,EAAE,KAAK,IAAI,IAAI;AAErB,MAAI,OAAO,QAAQ,YAAY,gBAAgB,KAAK;AAClD,UAAM,IAAI;AAAA,MACR,0BAA0B,aAAa,6BAA6B,GAAG;AAAA,IACzE;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,YAAY,gBAAgB,KAAK;AAClD,UAAM,IAAI;AAAA,MACR,0BAA0B,aAAa,gCAAgC,GAAG;AAAA,IAC5E;AAAA,EACF;AAEA,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,aAAa;AAEtD,QAAM,YAAkB,kBAAY,MAAM;AACxC,aAAS,CAAC,MAAM;AACd,YAAM,YAAY,IAAI;AAEtB,UAAI,OAAO,QAAQ,YAAY,YAAY,KAAK;AAC9C,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,GAAG,CAAC;AAER,QAAM,YAAkB,kBAAY,MAAM;AACxC,aAAS,CAAC,MAAM;AACd,YAAM,YAAY,IAAI;AAEtB,UAAI,OAAO,QAAQ,YAAY,YAAY,KAAK;AAC9C,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,GAAG,CAAC;AAER,QAAM,MAAY;AAAA,IAChB,CAAC,cAAc;AACb,eAAS,CAAC,MAAM;AACd,YAAI,OAAO,QAAQ,YAAY,YAAY,KAAK;AAC9C,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,QAAQ,YAAY,YAAY,KAAK;AAC9C,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,CAAC,KAAK,GAAG;AAAA,EACX;AAEA,QAAM,QAAc,kBAAY,MAAM;AACpC,aAAS,aAAa;AAAA,EACxB,GAAG,CAAC,aAAa,CAAC;AAElB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,YAAY,OAAO,OAAO;AACxC,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAS,KAAK;AAEhE,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU,WAAW,MAAM;AAC/B,wBAAkB,KAAK;AAAA,IACzB,GAAG,KAAK;AAER,WAAO,MAAM;AACX,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,OAAO,KAAK,CAAC;AAEjB,SAAO;AACT;AAEO,SAAS,WAAW,cAAc,cAAc;AACrD,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,YAAY;AAErD,MAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,WAAO,CAAC,cAAc,QAAQ;AAAA,EAChC;AAEA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAEO,SAAS,iBAAiB,OAAO;AACtC,EAAM,gBAAU,MAAM;AACpB,aAAS,QAAQ;AAAA,EACnB,GAAG,CAAC,KAAK,CAAC;AACZ;AAEO,SAAS,WAAW,KAAK;AAC9B,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO,SAAS,cAAc,mBAAmB;AAErD,QAAI,CAAC,MAAM;AACT,aAAO,SAAS,cAAc,MAAM;AACpC,WAAK,OAAO;AACZ,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,eAAS,KAAK,YAAY,IAAI;AAAA,IAChC,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACV;AAEO,SAAS,eAAe,UAAU,CAAC,GAAG;AAC3C,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS;AAAA,IACvC,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,OAAO;AAAA,EACT,CAAC;AAED,QAAM,aAAmB,aAAO,OAAO;AAEvC,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU,CAAC,EAAE,QAAQ,UAAU,MAAM;AACzC,eAAS;AAAA,QACP,SAAS;AAAA,QACT;AAAA,QACA,UAAU,OAAO;AAAA,QACjB,WAAW,OAAO;AAAA,QAClB,UAAU,OAAO;AAAA,QACjB,UAAU,OAAO;AAAA,QACjB,kBAAkB,OAAO;AAAA,QACzB,SAAS,OAAO;AAAA,QAChB,OAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,UAAU;AAC9B,eAAS,CAAC,OAAO;AAAA,QACf,GAAG;AAAA,QACH,SAAS;AAAA,QACT;AAAA,MACF,EAAE;AAAA,IACJ;AAEA,cAAU,YAAY;AAAA,MACpB;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,UAAM,UAAU,UAAU,YAAY;AAAA,MACpC;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,WAAO,MAAM;AACX,gBAAU,YAAY,WAAW,OAAO;AAAA,IAC1C;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC,MAAM,CAAC;AAAA,EACP,SAAS;AAAA,EACT,QAAQ,CAAC;AACX;AAEA,IAAM,yBAAyB,CAAC,OAAO,WAAW;AAChD,QAAM,EAAE,MAAM,SAAS,OAAO,IAAI;AAElC,MAAI,OAAO,SAAS,QAAQ;AAC1B,WAAO;AAAA,MACL,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AAAA,MACnC,SAAS,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,QAAQ,CAAC,SAAS,GAAG,MAAM;AAAA,IAC7B;AAAA,EACF,WAAW,OAAO,SAAS,QAAQ;AACjC,WAAO;AAAA,MACL,MAAM,CAAC,GAAG,MAAM,OAAO;AAAA,MACvB,SAAS,OAAO,CAAC;AAAA,MACjB,QAAQ,OAAO,MAAM,CAAC;AAAA,IACxB;AAAA,EACF,WAAW,OAAO,SAAS,OAAO;AAChC,UAAM,EAAE,WAAW,IAAI;AAEvB,QAAI,OAAO,eAAe,SAAS;AACjC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,MAAM,CAAC,GAAG,MAAM,OAAO;AAAA,MACvB,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,IACX;AAAA,EACF,WAAW,OAAO,SAAS,SAAS;AAClC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,SAAS,OAAO;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AACF;AAEO,SAAS,gBAAgB,iBAAiB,CAAC,GAAG;AACnD,QAAM,oBAA0B,aAAO,cAAc;AAErD,QAAM,CAAC,OAAO,QAAQ,IAAU,iBAAW,wBAAwB;AAAA,IACjE,GAAG;AAAA,IACH,SAAS,kBAAkB;AAAA,EAC7B,CAAC;AAED,QAAM,UAAU,MAAM,KAAK,WAAW;AACtC,QAAM,UAAU,MAAM,OAAO,WAAW;AAExC,QAAM,OAAa,kBAAY,MAAM;AACnC,QAAI,SAAS;AACX,eAAS,EAAE,MAAM,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,OAAa,kBAAY,MAAM;AACnC,QAAI,SAAS;AACX,eAAS,EAAE,MAAM,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,MAAY;AAAA,IAChB,CAAC,eAAe,SAAS,EAAE,MAAM,OAAO,WAAW,CAAC;AAAA,IACpD,CAAC;AAAA,EACH;AAEA,QAAM,QAAc;AAAA,IAClB,MACE,SAAS,EAAE,MAAM,SAAS,gBAAgB,kBAAkB,QAAQ,CAAC;AAAA,IACvE,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,OAAO,MAAM,SAAS,KAAK,MAAM,MAAM,OAAO,SAAS,QAAQ;AAC1E;AAEO,SAAS,WAAW;AACzB,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,KAAK;AACpD,QAAM,eAAqB,aAAO,IAAI;AAEtC,QAAM,mBAAyB,kBAAY,MAAM;AAC/C,gBAAY,IAAI;AAAA,EAClB,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAyB,kBAAY,MAAM;AAC/C,gBAAY,KAAK;AAAA,EACnB,GAAG,CAAC,CAAC;AAEL,QAAM,YAAkB;AAAA,IACtB,CAAC,SAAS;AAtbd;AAubM,YAAI,kBAAa,YAAb,mBAAsB,cAAa,KAAK,cAAc;AACxD,qBAAa,QAAQ;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AACA,qBAAa,QAAQ;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,WAAI,6BAAM,cAAa,KAAK,cAAc;AACxC,aAAK,iBAAiB,cAAc,gBAAgB;AACpD,aAAK,iBAAiB,cAAc,gBAAgB;AAAA,MACtD;AAEA,mBAAa,UAAU;AAAA,IACzB;AAAA,IACA,CAAC,kBAAkB,gBAAgB;AAAA,EACrC;AAEA,SAAO,CAAC,WAAW,QAAQ;AAC7B;AAEO,SAAS,QAAQ,KAAK,MAAO,IAAI;AACtC,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS,KAAK;AAE5C,EAAM,gBAAU,MAAM;AACpB,QAAI;AAEJ,UAAM,gBAAgB,MAAM;AAC1B,cAAQ,IAAI;AAAA,IACd;AAEA,UAAM,cAAc,SAAS,CAAC,MAAM;AAClC,cAAQ,KAAK;AAEb,aAAO,aAAa,SAAS;AAC7B,kBAAY,OAAO,WAAW,eAAe,EAAE;AAAA,IACjD,GAAG,GAAG;AAEN,UAAM,yBAAyB,MAAM;AACnC,UAAI,CAAC,SAAS,QAAQ;AACpB,oBAAY;AAAA,MACd;AAAA,IACF;AAEA,gBAAY,OAAO,WAAW,eAAe,EAAE;AAE/C,WAAO,iBAAiB,aAAa,WAAW;AAChD,WAAO,iBAAiB,aAAa,WAAW;AAChD,WAAO,iBAAiB,UAAU,WAAW;AAC7C,WAAO,iBAAiB,WAAW,WAAW;AAC9C,WAAO,iBAAiB,cAAc,WAAW;AACjD,WAAO,iBAAiB,SAAS,WAAW;AAC5C,aAAS,iBAAiB,oBAAoB,sBAAsB;AAEpE,WAAO,MAAM;AACX,aAAO,oBAAoB,aAAa,WAAW;AACnD,aAAO,oBAAoB,aAAa,WAAW;AACnD,aAAO,oBAAoB,UAAU,WAAW;AAChD,aAAO,oBAAoB,WAAW,WAAW;AACjD,aAAO,oBAAoB,cAAc,WAAW;AACpD,aAAO,oBAAoB,SAAS,WAAW;AAC/C,eAAS,oBAAoB,oBAAoB,sBAAsB;AACvE,aAAO,aAAa,SAAS;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,EAAE,CAAC;AAEP,SAAO;AACT;AAEO,SAAS,wBAAwB,UAAU,CAAC,GAAG;AACpD,QAAM,EAAE,YAAY,GAAG,OAAO,MAAM,aAAa,MAAM,IAAI;AAC3D,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,IAAI;AAE7C,QAAM,mBAAyB,aAAO,IAAI;AAE1C,QAAM,YAAkB;AAAA,IACtB,CAAC,SAAS;AACR,UAAI,iBAAiB,SAAS;AAC5B,yBAAiB,QAAQ,WAAW;AACpC,yBAAiB,UAAU;AAAA,MAC7B;AAEA,WAAI,6BAAM,cAAa,KAAK,cAAc;AACxC,cAAM,WAAW,IAAI;AAAA,UACnB,CAAC,CAACA,MAAK,MAAM;AACX,qBAASA,MAAK;AAAA,UAChB;AAAA,UACA,EAAE,WAAW,MAAM,WAAW;AAAA,QAChC;AAEA,iBAAS,QAAQ,IAAI;AACrB,yBAAiB,UAAU;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,CAAC,WAAW,MAAM,UAAU;AAAA,EAC9B;AAEA,SAAO,CAAC,WAAW,KAAK;AAC1B;AAEO,SAAS,cAAc;AAC5B,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,KAAK;AAEpD,EAAM,gBAAU,MAAM;AACpB,gBAAY,IAAI;AAAA,EAClB,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEO,SAAS,mBAAmB;AACjC,QAAM,YAAkB,aAAO,IAAI;AAEnC,MAAI,UAAU,YAAY,MAAM;AAC9B,cAAU,UAAU;AACpB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU;AACnB;AAEO,SAAS,QAAQ,cAAc,CAAC,GAAG;AACxC,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS,WAAW;AAElD,QAAM,MAAY,kBAAY,CAAC,MAAM;AACnC,YAAQ,CAAC;AAAA,EACX,GAAG,CAAC,CAAC;AAEL,QAAM,OAAa,kBAAY,CAAC,YAAY;AAC1C,YAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;AAAA,EAChC,GAAG,CAAC,CAAC;AAEL,QAAM,WAAiB,kBAAY,CAAC,UAAU;AAC5C,YAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC9D,GAAG,CAAC,CAAC;AAEL,QAAM,WAAiB,kBAAY,CAAC,OAAO,YAAY;AACrD,YAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,SAAS,GAAG,EAAE,MAAM,KAAK,CAAC,CAAC;AAAA,EACnE,GAAG,CAAC,CAAC;AAEL,QAAM,WAAiB,kBAAY,CAAC,OAAO,YAAY;AACrD,YAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,UAAU,CAAE,CAAC;AAAA,EAC7D,GAAG,CAAC,CAAC;AAEL,QAAM,QAAc,kBAAY,MAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AAErD,SAAO,CAAC,MAAM,EAAE,KAAK,MAAM,UAAU,UAAU,UAAU,MAAM,CAAC;AAClE;AAEA,IAAM,sBAAsB,CAAC,KAAK,UAAU;AAC1C,QAAM,mBAAmB,KAAK,UAAU,KAAK;AAC7C,SAAO,aAAa,QAAQ,KAAK,gBAAgB;AACjD,uBAAqB,KAAK,gBAAgB;AAC5C;AAEA,IAAM,yBAAyB,CAAC,QAAQ;AACtC,SAAO,aAAa,WAAW,GAAG;AAClC,uBAAqB,KAAK,IAAI;AAChC;AAEA,IAAM,sBAAsB,CAAC,QAAQ;AACnC,SAAO,OAAO,aAAa,QAAQ,GAAG;AACxC;AAEA,IAAM,2BAA2B,CAAC,aAAa;AAC7C,SAAO,iBAAiB,WAAW,QAAQ;AAC3C,SAAO,MAAM,OAAO,oBAAoB,WAAW,QAAQ;AAC7D;AAEA,IAAM,gCAAgC,MAAM;AAC1C,QAAM,MAAM,uCAAuC;AACrD;AAEO,SAAS,gBAAgB,KAAK,cAAc;AACjD,QAAM,cAAc,MAAM,oBAAoB,GAAG;AAEjD,QAAM,QAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAiB;AAAA,IACrB,CAAC,MAAM;AACL,UAAI;AACF,cAAM,YAAY,OAAO,MAAM,aAAa,EAAE,KAAK,MAAM,KAAK,CAAC,IAAI;AAEnE,YAAI,cAAc,UAAa,cAAc,MAAM;AACjD,iCAAuB,GAAG;AAAA,QAC5B,OAAO;AACL,8BAAoB,KAAK,SAAS;AAAA,QACpC;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,IACA,CAAC,KAAK,KAAK;AAAA,EACb;AAEA,EAAM,gBAAU,MAAM;AACpB,QACE,oBAAoB,GAAG,MAAM,QAC7B,OAAO,iBAAiB,aACxB;AACA,0BAAoB,KAAK,YAAY;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,KAAK,YAAY,CAAC;AAEtB,SAAO,CAAC,QAAQ,KAAK,MAAM,KAAK,IAAI,cAAc,QAAQ;AAC5D;AAEO,SAAS,oBAAoB;AAClC,EAAM,sBAAgB,MAAM;AAC1B,UAAM,gBAAgB,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC7D,aAAS,KAAK,MAAM,WAAW;AAC/B,WAAO,MAAM;AACX,eAAS,KAAK,MAAM,WAAW;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,aAAa,UAAU,UAAU,CAAC,GAAG;AACnD,QAAM,EAAE,YAAY,KAAK,SAAS,UAAU,SAAS,IAAI;AACzD,QAAM,oBAA0B,aAAO,KAAK;AAC5C,QAAM,YAAkB,aAAO,KAAK;AACpC,QAAM,UAAgB,aAAO;AAE7B,SAAa,cAAQ,MAAM;AACzB,QAAI,OAAO,aAAa,YAAY;AAClC,aAAO,CAAC;AAAA,IACV;AAEA,UAAM,QAAQ,CAAC,UAAU;AACvB,UAAI,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,KAAK;AAAG;AAElD,UAAI,SAAS;AACX,gBAAQ,KAAK;AAAA,MACf;AAEA,gBAAU,UAAU;AACpB,cAAQ,UAAU,WAAW,MAAM;AACjC,iBAAS,KAAK;AACd,0BAAkB,UAAU;AAAA,MAC9B,GAAG,SAAS;AAAA,IACd;AAEA,UAAM,SAAS,CAAC,UAAU;AACxB,UAAI,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,KAAK;AAAG;AAElD,UAAI,kBAAkB,SAAS;AAC7B,YAAI,UAAU;AACZ,mBAAS,KAAK;AAAA,QAChB;AAAA,MACF,WAAW,UAAU,SAAS;AAC5B,YAAI,UAAU;AACZ,mBAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAEA,wBAAkB,UAAU;AAC5B,gBAAU,UAAU;AAEpB,UAAI,QAAQ,SAAS;AACnB,eAAO,aAAa,QAAQ,OAAO;AAAA,MACrC;AAAA,IACF;AAEA,UAAM,gBAAgB;AAAA,MACpB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,IAChB;AAEA,UAAM,gBAAgB;AAAA,MACpB,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,UAAU,UAAU,OAAO,CAAC;AACvD;AAEO,SAAS,OAAO,cAAc;AACnC,QAAM,SAAe,aAAO,IAAI,IAAI,YAAY,CAAC;AACjD,QAAM,CAAC,EAAE,QAAQ,IAAU,iBAAW,CAAC,MAAM,IAAI,GAAG,CAAC;AAErD,SAAO,QAAQ,MAAM,IAAI,SAAS;AAChC,QAAI,UAAU,IAAI,MAAM,OAAO,SAAS,IAAI;AAC5C,aAAS;AACT,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO,QAAQ,QAAQ,IAAI,SAAS;AAClC,QAAI,UAAU,MAAM,MAAM,OAAO,SAAS,IAAI;AAC9C,aAAS;AAAA,EACX;AAEA,SAAO,QAAQ,SAAS,IAAI,SAAS;AACnC,UAAM,MAAM,IAAI,UAAU,OAAO,MAAM,OAAO,SAAS,IAAI;AAC3D,aAAS;AAET,WAAO;AAAA,EACT;AAEA,SAAO,OAAO;AAChB;AAEO,SAAS,aAAa;AAC3B,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS;AAAA,IACjD,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,mBAAyB,aAAO,IAAI;AAE1C,QAAM,YAAkB,kBAAY,CAAC,SAAS;AAC5C,QAAI,iBAAiB,SAAS;AAC5B,uBAAiB,QAAQ,WAAW;AACpC,uBAAiB,UAAU;AAAA,IAC7B;AAEA,SAAI,6BAAM,cAAa,KAAK,cAAc;AACxC,YAAM,WAAW,IAAI,eAAe,CAAC,CAAC,KAAK,MAAM;AAC/C,YAAI,SAAS,MAAM,eAAe;AAChC,gBAAM,EAAE,YAAY,OAAO,WAAW,OAAO,IAC3C,MAAM,cAAc,CAAC;AAEvB,wBAAc,EAAE,OAAO,OAAO,CAAC;AAAA,QACjC;AAAA,MACF,CAAC;AAED,eAAS,QAAQ,IAAI;AACrB,uBAAiB,UAAU;AAAA,IAC7B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,WAAW,UAAU;AAC/B;AAEO,SAAS,cAAc,OAAO;AACnC,QAAM,YAAkB;AAAA,IACtB,CAAC,aAAa;AACZ,YAAM,aAAa,OAAO,WAAW,KAAK;AAE1C,iBAAW,iBAAiB,UAAU,QAAQ;AAC9C,aAAO,MAAM;AACX,mBAAW,oBAAoB,UAAU,QAAQ;AAAA,MACnD;AAAA,IACF;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAEA,QAAM,cAAc,MAAM;AACxB,WAAO,OAAO,WAAW,KAAK,EAAE;AAAA,EAClC;AAEA,QAAM,oBAAoB,MAAM;AAC9B,UAAM,MAAM,qCAAqC;AAAA,EACnD;AAEA,SAAa,2BAAqB,WAAW,aAAa,iBAAiB;AAC7E;AAEO,SAAS,WAAW;AACzB,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS;AAAA,IACvC,GAAG;AAAA,IACH,GAAG;AAAA,IACH,UAAU;AAAA,IACV,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,EACpB,CAAC;AAED,QAAM,MAAY,aAAO,IAAI;AAE7B,EAAM,sBAAgB,MAAM;AAC1B,UAAM,kBAAkB,CAAC,UAAU;AArzBvC;AAszBM,UAAI,WAAW;AAAA,QACb,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX;AAEA,YAAI,SAAI,YAAJ,mBAAa,cAAa,KAAK,cAAc;AAC/C,cAAM,EAAE,MAAM,IAAI,IAAI,IAAI,QAAQ,sBAAsB;AACxD,cAAM,mBAAmB,OAAO,OAAO;AACvC,cAAM,mBAAmB,MAAM,OAAO;AACtC,cAAM,WAAW,MAAM,QAAQ;AAC/B,cAAM,WAAW,MAAM,QAAQ;AAE/B,iBAAS,WAAW;AACpB,iBAAS,WAAW;AACpB,iBAAS,mBAAmB;AAC5B,iBAAS,mBAAmB;AAAA,MAC9B;AAEA,eAAS,CAAC,MAAM;AACd,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,aAAa,eAAe;AAEtD,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,eAAe;AAAA,IAC3D;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,OAAO,GAAG;AACpB;AAEA,IAAM,gBAAgB,MAAM;AAC1B,UACE,uCAAW,gBACX,uCAAW,mBACX,uCAAW;AAEf;AAEA,IAAM,2BAA2B,CAAC,aAAa;AAC7C,SAAO,iBAAiB,UAAU,UAAU,EAAE,SAAS,KAAK,CAAC;AAC7D,SAAO,iBAAiB,WAAW,UAAU,EAAE,SAAS,KAAK,CAAC;AAE9D,QAAM,aAAa,cAAc;AAEjC,MAAI,YAAY;AACd,eAAW,iBAAiB,UAAU,UAAU,EAAE,SAAS,KAAK,CAAC;AAAA,EACnE;AAEA,SAAO,MAAM;AACX,WAAO,oBAAoB,UAAU,QAAQ;AAC7C,WAAO,oBAAoB,WAAW,QAAQ;AAE9C,QAAI,YAAY;AACd,iBAAW,oBAAoB,UAAU,QAAQ;AAAA,IACnD;AAAA,EACF;AACF;AAEA,IAAM,gCAAgC,MAAM;AAC1C,QAAM,MAAM,uCAAuC;AACrD;AAEO,SAAS,kBAAkB;AAChC,QAAM,QAAc,aAAO,CAAC,CAAC;AAE7B,QAAM,cAAc,MAAM;AACxB,UAAM,SAAS,UAAU;AACzB,UAAM,aAAa,cAAc;AAEjC,UAAM,YAAY;AAAA,MAChB;AAAA,MACA,UAAU,yCAAY;AAAA,MACtB,aAAa,yCAAY;AAAA,MACzB,eAAe,yCAAY;AAAA,MAC3B,KAAK,yCAAY;AAAA,MACjB,UAAU,yCAAY;AAAA,MACtB,MAAM,yCAAY;AAAA,IACpB;AAEA,QAAI,eAAe,MAAM,SAAS,SAAS,GAAG;AAC5C,aAAO,MAAM;AAAA,IACf,OAAO;AACL,YAAM,UAAU;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,eAAe,cAAc;AAC3C,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,YAAY;AAErD,QAAM,eAAqB,kBAAY,CAAC,QAAQ;AAC9C,QAAI,OAAO,QAAQ,YAAY;AAC7B,eAAS,CAAC,MAAM;AACd,cAAM,WAAW,IAAI,CAAC;AAEtB,YAAI,cAAc,QAAQ,GAAG;AAC3B,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,cAAc,GAAG,GAAG;AACtB,eAAS,CAAC,OAAO;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,OAAO,YAAY;AAC7B;AAEO,SAAS,iBAAiB;AAC/B,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS;AAAA,IACnD,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AAED,EAAM,sBAAgB,MAAM;AA57B9B;AA67BI,UAAM,eAAe,MAAM;AACzB,YAAM,EAAE,OAAO,KAAK,IAAI,OAAO,OAAO;AACtC,qBAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,2BAA2B,MAAM;AACrC,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH;AAEA,SAAI,YAAO,WAAP,mBAAe,aAAa;AAC9B,mBAAa;AACb,aAAO,OAAO,YAAY,iBAAiB,UAAU,YAAY;AAAA,IACnE,OAAO;AACL,+BAAyB;AACzB,aAAO,iBAAiB,qBAAqB,wBAAwB;AAAA,IACvE;AAEA,WAAO,MAAM;AAp9BjB,UAAAC;AAq9BM,WAAIA,MAAA,OAAO,WAAP,gBAAAA,IAAe,aAAa;AAC9B,eAAO,OAAO,YAAY,oBAAoB,UAAU,YAAY;AAAA,MACtE,OAAO;AACL,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEA,IAAM,gCAAgC,CAAC,OAAO;AAC5C,SAAO,iBAAiB,kBAAkB,EAAE;AAC5C,SAAO,MAAM,OAAO,oBAAoB,kBAAkB,EAAE;AAC9D;AAEA,IAAM,+BAA+B,MAAM;AACzC,SAAO,UAAU;AACnB;AAEA,IAAM,qCAAqC,MAAM;AAC/C,QAAM,MAAM,4CAA4C;AAC1D;AAEO,SAAS,uBAAuB;AACrC,SAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,YAAY,OAAO;AACjC,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,IAAI;AAEnD,MAAI,UAAU,SAAS;AACrB,gBAAY,OAAO;AACnB,eAAW,KAAK;AAAA,EAClB;AAEA,SAAO;AACT;AAEO,SAAS,SAAS,eAAe,CAAC,GAAG;AAC1C,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,YAAY;AAErD,QAAM,MAAY,kBAAY,CAAC,YAAY;AACzC,aAAS,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;AAAA,EACjC,GAAG,CAAC,CAAC;AAEL,QAAM,SAAe,kBAAY,MAAM;AACrC,QAAI;AAEJ,aAAS,CAAC,CAAC,OAAU,IAAC,MAAM;AAC1B,uBAAiB;AACjB,aAAO;AAAA,IACT,CAAC;AAED,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,QAAc,kBAAY,MAAM;AACpC,aAAS,CAAC,CAAC;AAAA,EACb,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,MAAM,CAAC;AAAA,IACd,MAAM,MAAM,MAAM,SAAS,CAAC;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ;AAAA,EACF;AACF;AAEO,SAAS,iBAAiB;AAC/B,QAAM,QAAc,aAAO,CAAC;AAE5B,QAAM;AAEN,SAAO,MAAM;AACf;AAEO,SAAS,cAAc,OAAO,WAAW;AAC9C,QAAM,QAAc,aAAO,CAAC;AAC5B,QAAM,aAAmB,aAAO;AAChC,QAAM,MAAM,KAAK,IAAI;AAErB,QAAM;AAEN,EAAM,gBAAU,MAAM;AACpB,eAAW,UAAU,KAAK,IAAI;AAAA,EAChC,CAAC;AAED,QAAM,kBAAkB,WAAW,UAAU,MAAM,WAAW,UAAU;AAExE,MAAI,MAAuC;AACzC,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,MAAM;AAAA,MACf;AAAA,MACA,WAAW;AAAA,IACb;AAEA,YAAQ,IAAI,IAAI;AAEhB,WAAO;AAAA,EACT;AACF;AAEO,SAAS,UAAU,KAAK,UAAU,CAAC,GAAG;AAC3C,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,SAAS;AACpD,QAAM,aAAmB,aAAO,OAAO;AAEvC,EAAM,gBAAU,MAAM;AACpB,QAAI,SAAS,SAAS,cAAc,eAAe,GAAG,IAAI;AAE1D,UAAM,YAAY,iCAAQ,aAAa;AACvC,QAAI,WAAW;AACb,gBAAU,SAAS;AACnB;AAAA,IACF;AAEA,QAAI,WAAW,MAAM;AACnB,eAAS,SAAS,cAAc,QAAQ;AACxC,aAAO,MAAM;AACb,aAAO,QAAQ;AACf,aAAO,aAAa,eAAe,SAAS;AAC5C,eAAS,KAAK,YAAY,MAAM;AAEhC,YAAM,mBAAmB,MAAM;AAC7B,eAAO,aAAa,eAAe,OAAO;AAC1C,kBAAU,OAAO;AACjB,6BAAqB;AAAA,MACvB;AAEA,YAAM,oBAAoB,MAAM;AAC9B,eAAO,aAAa,eAAe,OAAO;AAC1C,kBAAU,OAAO;AACjB,6BAAqB;AAAA,MACvB;AAEA,YAAM,uBAAuB,MAAM;AACjC,eAAO,oBAAoB,QAAQ,gBAAgB;AACnD,eAAO,oBAAoB,SAAS,iBAAiB;AAAA,MACvD;AAEA,aAAO,iBAAiB,QAAQ,gBAAgB;AAChD,aAAO,iBAAiB,SAAS,iBAAiB;AAElD,YAAM,kBAAkB,WAAW,QAAQ;AAE3C,aAAO,MAAM;AACX,YAAI,oBAAoB,MAAM;AAC5B,iBAAO,OAAO;AACd,+BAAqB;AAAA,QACvB;AAAA,MACF;AAAA,IACF,OAAO;AACL,gBAAU,SAAS;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AAER,SAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,KAAK,UAAU;AAC5C,QAAM,mBAAmB,KAAK,UAAU,KAAK;AAC7C,SAAO,eAAe,QAAQ,KAAK,gBAAgB;AACnD,uBAAqB,KAAK,gBAAgB;AAC5C;AAEA,IAAM,2BAA2B,CAAC,QAAQ;AACxC,SAAO,eAAe,WAAW,GAAG;AACpC,uBAAqB,KAAK,IAAI;AAChC;AAEA,IAAM,wBAAwB,CAAC,QAAQ;AACrC,SAAO,OAAO,eAAe,QAAQ,GAAG;AAC1C;AAEA,IAAM,6BAA6B,CAAC,aAAa;AAC/C,SAAO,iBAAiB,WAAW,QAAQ;AAC3C,SAAO,MAAM,OAAO,oBAAoB,WAAW,QAAQ;AAC7D;AAEA,IAAM,kCAAkC,MAAM;AAC5C,QAAM,MAAM,yCAAyC;AACvD;AAEO,SAAS,kBAAkB,KAAK,cAAc;AACnD,QAAM,cAAc,MAAM,sBAAsB,GAAG;AAEnD,QAAM,QAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAiB;AAAA,IACrB,CAAC,MAAM;AACL,UAAI;AACF,cAAM,YAAY,OAAO,MAAM,aAAa,EAAE,KAAK,MAAM,KAAK,CAAC,IAAI;AAEnE,YAAI,cAAc,UAAa,cAAc,MAAM;AACjD,mCAAyB,GAAG;AAAA,QAC9B,OAAO;AACL,gCAAsB,KAAK,SAAS;AAAA,QACtC;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,IACA,CAAC,KAAK,KAAK;AAAA,EACb;AAEA,EAAM,gBAAU,MAAM;AACpB,QACE,sBAAsB,GAAG,MAAM,QAC/B,OAAO,iBAAiB,aACxB;AACA,4BAAsB,KAAK,YAAY;AAAA,IACzC;AAAA,EACF,GAAG,CAAC,KAAK,YAAY,CAAC;AAEtB,SAAO,CAAC,QAAQ,KAAK,MAAM,KAAK,IAAI,cAAc,QAAQ;AAC5D;AAEO,SAAS,OAAO,QAAQ;AAC7B,QAAM,SAAe,aAAO,IAAI,IAAI,MAAM,CAAC;AAC3C,QAAM,CAAC,EAAE,QAAQ,IAAU,iBAAW,CAAC,MAAM,IAAI,GAAG,CAAC;AAErD,SAAO,QAAQ,MAAM,IAAI,SAAS;AAChC,UAAM,MAAM,IAAI,UAAU,IAAI,MAAM,OAAO,SAAS,IAAI;AACxD,aAAS;AAET,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,QAAQ,IAAI,SAAS;AAClC,QAAI,UAAU,MAAM,MAAM,OAAO,SAAS,IAAI;AAC9C,aAAS;AAAA,EACX;AAEA,SAAO,QAAQ,SAAS,IAAI,SAAS;AACnC,UAAM,MAAM,IAAI,UAAU,OAAO,MAAM,OAAO,SAAS,IAAI;AAC3D,aAAS;AAET,WAAO;AAAA,EACT;AAEA,SAAO,OAAO;AAChB;AAEO,SAAS,YAAY,OAAO,WAAW,KAAK;AACjD,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAS,KAAK;AAChE,QAAM,cAAoB,aAAO,IAAI;AAErC,EAAM,gBAAU,MAAM;AACpB,UAAM,MAAM,KAAK,IAAI;AAErB,QAAI,YAAY,WAAW,OAAO,YAAY,UAAU,UAAU;AAChE,kBAAY,UAAU;AACtB,wBAAkB,KAAK;AAAA,IACzB,OAAO;AACL,YAAM,KAAK,OAAO,WAAW,MAAM;AACjC,oBAAY,UAAU;AACtB,0BAAkB,KAAK;AAAA,MACzB,GAAG,QAAQ;AAEX,aAAO,MAAM,OAAO,aAAa,EAAE;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,OAAO,QAAQ,CAAC;AAEpB,SAAO;AACT;AAEO,SAAS,UAAU,cAAc;AACtC,QAAM,CAAC,IAAI,KAAK,IAAU,eAAS,MAAM;AACvC,QAAI,OAAO,iBAAiB,WAAW;AACrC,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,YAAY;AAAA,EAC7B,CAAC;AAED,QAAM,eAAqB,kBAAY,CAAC,UAAU;AAChD,QAAI,OAAO,UAAU,WAAW;AAC9B,aAAO,MAAM,KAAK;AAAA,IACpB;AAEA,WAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AAAA,EACxB,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,IAAI,YAAY;AAC1B;AAEA,IAAM,+BAA+B,CAAC,aAAa;AACjD,WAAS,iBAAiB,oBAAoB,QAAQ;AAEtD,SAAO,MAAM;AACX,aAAS,oBAAoB,oBAAoB,QAAQ;AAAA,EAC3D;AACF;AAEA,IAAM,8BAA8B,MAAM;AACxC,SAAO,SAAS;AAClB;AAEA,IAAM,oCAAoC,MAAM;AAC9C,QAAM,MAAM,2CAA2C;AACzD;AAEO,SAAS,sBAAsB;AACpC,QAAM,kBAAwB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO,oBAAoB;AAC7B;AAEO,SAAS,kBAAkB;AAChC,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS;AAAA,IACvC,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AAED,QAAM,WAAiB,kBAAY,IAAI,SAAS;AAC9C,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,aAAO,SAAS,KAAK,CAAC,CAAC;AAAA,IACzB,WAAW,OAAO,KAAK,CAAC,MAAM,YAAY,OAAO,KAAK,CAAC,MAAM,UAAU;AACrE,aAAO,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,IAClC,OAAO;AACL,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,EAAM,sBAAgB,MAAM;AAC1B,UAAM,eAAe,MAAM;AACzB,eAAS,EAAE,GAAG,OAAO,SAAS,GAAG,OAAO,QAAQ,CAAC;AAAA,IACnD;AAEA,iBAAa;AACb,WAAO,iBAAiB,UAAU,YAAY;AAE9C,WAAO,MAAM;AACX,aAAO,oBAAoB,UAAU,YAAY;AAAA,IACnD;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,OAAO,QAAQ;AACzB;AAEO,SAAS,gBAAgB;AAC9B,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS;AAAA,IACrC,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AAED,EAAM,sBAAgB,MAAM;AAC1B,UAAM,eAAe,MAAM;AACzB,cAAQ;AAAA,QACN,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,iBAAa;AACb,WAAO,iBAAiB,UAAU,YAAY;AAE9C,WAAO,MAAM;AACX,aAAO,oBAAoB,UAAU,YAAY;AAAA,IACnD;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;", "names": ["entry", "_a"]}