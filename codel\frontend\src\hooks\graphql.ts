import { useQuery, useMutation, useSubscription } from 'urql';
import {
  FlowDocument,
  FlowsDocument,
  AvailableModelsDocument,
  CreateFlowDocument,
  CreateTaskDocument,
  FinishFlowDocument,
  TaskAddedDocument,
  TaskUpdatedDocument,
  FlowUpdatedDocument,
  BrowserUpdatedDocument,
  TerminalLogsAddedDocument,
  FlowQuery,
  FlowsQuery,
  AvailableModelsQuery,
  CreateFlowMutation,
  CreateTaskMutation,
  FinishFlowMutation,
  TaskAddedSubscription,
  TaskUpdatedSubscription,
  FlowUpdatedSubscription,
  BrowserUpdatedSubscription,
  TerminalLogsAddedSubscription,
  FlowQueryVariables,
  CreateFlowMutationVariables,
  CreateTaskMutationVariables,
  FinishFlowMutationVariables,
  TaskAddedSubscriptionVariables,
  FlowUpdatedSubscriptionVariables,
  BrowserUpdatedSubscriptionVariables,
  TerminalLogsAddedSubscriptionVariables,
} from '@/generated/graphql';

export const useFlowQuery = (variables: { variables: FlowQueryVariables; pause?: boolean }) => {
  return useQuery<FlowQuery>({
    query: FlowDocument,
    variables: variables.variables,
    pause: variables.pause,
  });
};

export const useFlowsQuery = () => {
  return useQuery<FlowsQuery>({
    query: FlowsDocument,
  });
};

export const useAvailableModelsQuery = () => {
  return useQuery<AvailableModelsQuery>({
    query: AvailableModelsDocument,
  });
};

export const useCreateFlowMutation = () => {
  return useMutation<CreateFlowMutation, CreateFlowMutationVariables>(CreateFlowDocument);
};

export const useCreateTaskMutation = () => {
  return useMutation<CreateTaskMutation, CreateTaskMutationVariables>(CreateTaskDocument);
};

export const useFinishFlowMutation = () => {
  return useMutation<FinishFlowMutation, FinishFlowMutationVariables>(FinishFlowDocument);
};

export const useTaskAddedSubscription = (
  variables: { variables: TaskAddedSubscriptionVariables; pause?: boolean },
  handler?: (data: TaskAddedSubscription) => void
) => {
  return useSubscription<TaskAddedSubscription>(
    {
      query: TaskAddedDocument,
      variables: variables.variables,
      pause: variables.pause,
    },
    handler
  );
};

export const useTaskUpdatedSubscription = (
  handler?: (data: TaskUpdatedSubscription) => void
) => {
  return useSubscription<TaskUpdatedSubscription>(
    {
      query: TaskUpdatedDocument,
    },
    handler
  );
};

export const useFlowUpdatedSubscription = (
  variables: { variables: FlowUpdatedSubscriptionVariables; pause?: boolean },
  handler?: (data: FlowUpdatedSubscription) => void
) => {
  return useSubscription<FlowUpdatedSubscription>(
    {
      query: FlowUpdatedDocument,
      variables: variables.variables,
      pause: variables.pause,
    },
    handler
  );
};

export const useBrowserUpdatedSubscription = (
  variables: { variables: BrowserUpdatedSubscriptionVariables; pause?: boolean },
  handler?: (data: BrowserUpdatedSubscription) => void
) => {
  return useSubscription<BrowserUpdatedSubscription>(
    {
      query: BrowserUpdatedDocument,
      variables: variables.variables,
      pause: variables.pause,
    },
    handler
  );
};

export const useTerminalLogsAddedSubscription = (
  variables: { variables: TerminalLogsAddedSubscriptionVariables; pause?: boolean },
  handler?: (data: TerminalLogsAddedSubscription) => void
) => {
  return useSubscription<TerminalLogsAddedSubscription>(
    {
      query: TerminalLogsAddedDocument,
      variables: variables.variables,
      pause: variables.pause,
    },
    handler
  );
};
