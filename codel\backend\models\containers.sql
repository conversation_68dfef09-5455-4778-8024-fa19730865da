-- name: GetAllRunningContainers :many
SELECT * FROM containers WHERE status = 'running';

-- name: <PERSON>reate<PERSON>ontaine<PERSON> :one
INSERT INTO containers (
  name, image, status
)
VALUES (
  ?, ?, ?
)
RETURNING *;

-- name: UpdateContainerStatus :one
UPDATE containers
SET status = ?
WHERE id = ?
RETURNING *;

-- name: UpdateContainerLocalId :one
UPDATE containers
SET local_id = ?
WHERE id = ?
RETURNING *;
