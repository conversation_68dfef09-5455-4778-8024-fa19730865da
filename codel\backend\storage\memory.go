package storage

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/semanser/ai-coder/database"
)

type MemoryStorage struct {
	flows    map[int64]*database.Flow
	tasks    map[int64]*database.Task
	settings map[string]string
	mu       sync.RWMutex
	nextID   int64
}

func NewMemoryStorage() *MemoryStorage {
	return &MemoryStorage{
		flows:    make(map[int64]*database.Flow),
		tasks:    make(map[int64]*database.Task),
		settings: make(map[string]string),
		nextID:   1,
	}
}

func (m *MemoryStorage) getNextID() int64 {
	m.mu.Lock()
	defer m.mu.Unlock()
	id := m.nextID
	m.nextID++
	return id
}

// Flow operations
func (m *MemoryStorage) CreateFlow(ctx context.Context, arg database.CreateFlowParams) (database.Flow, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	id := m.getNextID()
	flow := database.Flow{
		ID:            id,
		Name:          arg.Name,
		ModelProvider: arg.ModelProvider,
		Model:         arg.Model,
		Status:        sql.NullString{String: "inProgress", Valid: true},
		CreatedAt:     sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedAt:     sql.NullTime{Time: time.Now(), Valid: true},
		ContainerID:   arg.ContainerID,
	}
	m.flows[id] = &flow
	return flow, nil
}

func (m *MemoryStorage) GetFlow(ctx context.Context, id int64) (database.Flow, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	flow, exists := m.flows[id]
	if !exists {
		return database.Flow{}, fmt.Errorf("flow not found")
	}
	return *flow, nil
}

func (m *MemoryStorage) GetFlows(ctx context.Context) ([]database.Flow, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	flows := make([]database.Flow, 0, len(m.flows))
	for _, flow := range m.flows {
		flows = append(flows, *flow)
	}
	return flows, nil
}

func (m *MemoryStorage) UpdateFlowStatus(ctx context.Context, arg database.UpdateFlowStatusParams) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	flow, exists := m.flows[arg.ID]
	if !exists {
		return fmt.Errorf("flow not found")
	}
	flow.Status = arg.Status
	flow.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	return nil
}

// Task operations
func (m *MemoryStorage) CreateTask(ctx context.Context, arg database.CreateTaskParams) (database.Task, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	id := m.getNextID()
	task := database.Task{
		ID:         id,
		FlowID:     arg.FlowID,
		Type:       arg.Type,
		Status:     arg.Status,
		Message:    arg.Message,
		Args:       arg.Args,
		Results:    arg.Results,
		ToolCallID: arg.ToolCallID,
		CreatedAt:  sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedAt:  sql.NullTime{Time: time.Now(), Valid: true},
	}
	m.tasks[id] = &task
	return task, nil
}

func (m *MemoryStorage) GetTasksByFlowID(ctx context.Context, flowID int64) ([]database.Task, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var tasks []database.Task
	for _, task := range m.tasks {
		if task.FlowID.Valid && task.FlowID.Int64 == flowID {
			tasks = append(tasks, *task)
		}
	}
	return tasks, nil
}

func (m *MemoryStorage) UpdateTaskStatus(ctx context.Context, arg database.UpdateTaskStatusParams) (database.Task, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	task, exists := m.tasks[arg.ID]
	if !exists {
		return database.Task{}, fmt.Errorf("task not found")
	}
	task.Status = arg.Status
	task.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	return *task, nil
}

// Settings operations
func (m *MemoryStorage) GetSetting(ctx context.Context, key string) (string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	value, exists := m.settings[key]
	if !exists {
		return "", nil // Return empty string if not found
	}
	return value, nil
}

func (m *MemoryStorage) SetSetting(ctx context.Context, key, value string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.settings[key] = value
	return nil
}


