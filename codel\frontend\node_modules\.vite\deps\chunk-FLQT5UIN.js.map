{"version": 3, "sources": ["../../@radix-ui/react-direction/dist/packages/react/direction/src/index.ts", "../../@radix-ui/react-direction/dist/packages/react/direction/src/Direction.tsx", "../../@radix-ui/react-collection/dist/packages/react/collection/src/index.ts", "../../@radix-ui/react-collection/dist/packages/react/collection/src/Collection.tsx", "../../@radix-ui/react-roving-focus/dist/packages/react/roving-focus/src/index.ts", "../../@radix-ui/react-roving-focus/dist/packages/react/roving-focus/src/RovingFocusGroup.tsx"], "sourcesContent": ["export {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n} from './Direction';\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n", "export { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\ntype SlotProps = Radix.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "export {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n} from './RovingFocusGroup';\nexport type { RovingFocusGroupProps, RovingFocusItemProps } from './RovingFocusGroup';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId = null, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId,\n    onChange: onCurrentTabStopIdChange,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends PrimitiveSpanProps {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;ACGA,IAAMA,6CAAmBC,aAAAA,eAA2CC,MAA3C;AAiBzB,SAASC,0CAAaC,UAAsB;AAC1C,QAAMC,gBAAYC,aAAAA,YAAiBC,sCAAjB;AAClB,SAAOH,YAAYC,aAAa;;;;;;;;AEJlC,SAASG,0CAAiEC,MAAc;AAKtF,QAAMC,gBAAgBD,OAAO;AAC7B,QAAM,CAACE,yBAAyBC,qBAA1B,IAAmDC,yCAAmBH,aAAD;AAO3E,QAAM,CAACI,wBAAwBC,oBAAzB,IAAiDJ,wBACrDD,eACA;IAAEM,eAAe;MAAEC,SAAS;;IAAQC,SAAS,oBAAIC,IAAJ;GAF+B;AAK9E,QAAMC,qBAA4EC,CAAAA,UAAU;AAC1F,UAAM,EArCV,OAAA,SAqCmBC,IAAaD;AAC5B,UAAME,MAAMC,cAAAA,QAAMC,OAA0B,IAAhC;AACZ,UAAMP,UAAUM,cAAAA,QAAMC,OAAgC,oBAAIN,IAAJ,CAAtC,EAAiDF;AACjE,WACE,cAAAS,QAAA,cAAC,wBADH;MAC0B;MAAc;MAAkB,eAAeH;OACpED,QADH;;AAMJ,SAAA,OAAA,oBAAA;IAAA,aAAA;GAAA;AAMA,QAAMK,uBAAuBlB,OAAO;AAEpC,QAAMmB,iBAAiBJ,cAAAA,QAAMK,WAC3B,CAACR,OAAOS,iBAAiB;AACvB,UAAM,EAzDZ,OAAA,SAyDqBR,IAAaD;AAC5B,UAAMU,UAAUhB,qBAAqBY,sBAAsBK,KAAvB;AACpC,UAAMC,eAAeC,0CAAgBJ,cAAcC,QAAQf,aAAvB;AACpC,WAAO,cAAAU,QAAA,cAAC,2CAAR;MAAa,KAAKO;OAAeX,QAA1B;GALY;AASvB,SAAA,OAAA,gBAAA;IAAA,aAAA;GAAA;AAMA,QAAMa,iBAAiB1B,OAAO;AAC9B,QAAM2B,iBAAiB;AAOvB,QAAMC,qBAAqBb,cAAAA,QAAMK,WAC/B,CAACR,OAAOS,iBAAiB;AACvB,UAAM,EAhFZ,OAAA,UAgF+B,GAAGQ,SAAH,IAAgBjB;AACzC,UAAME,MAAMC,cAAAA,QAAMC,OAAoB,IAA1B;AACZ,UAAMQ,eAAeC,0CAAgBJ,cAAcP,GAAf;AACpC,UAAMQ,UAAUhB,qBAAqBoB,gBAAgBH,KAAjB;AAEpCR,kBAAAA,QAAMe,UAAU,MAAM;AACpBR,cAAQb,QAAQsB,IAAIjB,KAAK;QAtFjC;QAsFwC,GAAIe;OAApC;AACA,aAAO,MAAM,KAAKP,QAAQb,QAAQuB,OAAOlB,GAAvB;KAFpB;AAKA,WACE,cAAAG,QAAA,cAAC,2CADH;MACc,CAACU,cAAD,GAAkB;MAAM,KAAKH;OACtCX,QADH;GAbqB;AAoB3B,SAAA,OAAA,oBAAA;IAAA,aAAA;GAAA;AAMA,WAASoB,cAAcV,OAAY;AACjC,UAAMD,UAAUhB,qBAAqBN,OAAO,sBAAsBuB,KAA9B;AAEpC,UAAMW,WAAWnB,cAAAA,QAAMoB,YAAY,MAAM;AACvC,YAAMC,iBAAiBd,QAAQf,cAAcC;AAC7C,UAAI,CAAC4B;AAAgB,eAAO,CAAA;AAC5B,YAAMC,eAAeC,MAAMC,KAAKH,eAAeI,iBAAkB,IAAGb,cAAe,GAAnD,CAAX;AACrB,YAAMc,QAAQH,MAAMC,KAAKjB,QAAQb,QAAQiC,OAAhB,CAAX;AACd,YAAMC,eAAeF,MAAMG;QACzB,CAACC,GAAGC,MAAMT,aAAaU,QAAQF,EAAE/B,IAAIN,OAA3B,IAAuC6B,aAAaU,QAAQD,EAAEhC,IAAIN,OAA3B;MAD9B;AAGrB,aAAOmC;OACN;MAACrB,QAAQf;MAAee,QAAQb;KATlB;AAWjB,WAAOyB;;AAGT,SAAO;IACL;MAAEc,UAAUrC;MAAoBsC,MAAM9B;MAAgB+B,UAAUtB;;IAChEK;IACA9B;;;;;AE9GJ,IAAMgD,oCAAc;AACpB,IAAMC,sCAAgB;EAAEC,SAAS;EAAOC,YAAY;;AAMpD,IAAMC,mCAAa;AAGnB,IAAM,CAACC,kCAAYC,qCAAeC,2CAA5B,IAAqDC,0CAGzDJ,gCAHyE;AAM3E,IAAM,CAACK,qDAA+BC,wCAAhC,IAA+DC,yCACnEP,kCACA;EAACG;CAFoF;AAiCvF,IAAM,CAACK,2CAAqBC,2CAAtB,IACJJ,oDAAkDL,gCAArB;AAK/B,IAAMU,gDAAmBC,cAAAA,YACvB,CAACC,OAA2CC,iBAAiB;AAC3D,aACE,cAAAC,eAAC,iCAAW,UADd;IACuB,OAAOF,MAAMG;SAChC,cAAAD,eAAC,iCAAW,MADd;IACmB,OAAOF,MAAMG;SAC5B,cAAAD,eAAC,4CAAD,SAAA,CAAA,GAA0BF,OAD5B;IACmC,KAAKC;GAAtC,CAAA,CADF,CADF;CAHmB;AAYzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAeA,IAAMG,iDAAuBL,cAAAA,YAG3B,CAACC,OAA+CC,iBAAiB;AACjE,QAAM,EAAA,yBAAA,aAAA,OAGG,OAHH,KAKJI,kBAAkBC,sBALd,yBAAA,0BAAA,cASJ,GAAGC,WAAH,IACEP;AACJ,QAAMQ,UAAMT,cAAAA,QAA0C,IAA1C;AACZ,QAAMU,eAAeC,0CAAgBT,cAAcO,GAAf;AACpC,QAAMG,YAAYC,0CAAaC,GAAD;AAC9B,QAAM,CAACR,mBAAmB,MAAMS,mBAA1B,IAAiDC,yCAAqB;IAC1EC,MAAMV;IACNW,aAAaC;IACbC,UAAUC;GAH+D;AAK3E,QAAM,CAACC,kBAAkBC,mBAAnB,QAA0CvB,cAAAA,UAAe,KAAf;AAChD,QAAMwB,mBAAmBC,0CAAeC,YAAD;AACvC,QAAMC,WAAWpC,oCAAca,uBAAD;AAC9B,QAAMwB,sBAAkB5B,cAAAA,QAAa,KAAb;AACxB,QAAM,CAAC6B,qBAAqBC,sBAAtB,QAAgD9B,cAAAA,UAAe,CAAf;AAEtDA,oBAAAA,WAAgB,MAAM;AACpB,UAAM+B,OAAOtB,IAAIuB;AACjB,QAAID,MAAM;AACRA,WAAKE,iBAAiBhD,mCAAauC,gBAAnC;AACA,aAAO,MAAMO,KAAKG,oBAAoBjD,mCAAauC,gBAAtC;;KAEd;IAACA;GANJ;AAQA,aACE,cAAArB,eAAC,2CADH;IAEI,OAAOC;IACP;IACA,KAAKQ;IACL;IACA;IACA,iBAAaZ,cAAAA;MACVmC,CAAAA,cAAcpB,oBAAoBoB,SAAD;MAClC;QAACpB;;IAFU;IAIb,oBAAgBf,cAAAA;MAAkB,MAAMuB,oBAAoB,IAAD;MAAQ,CAAA;IAAnD;IAChB,wBAAoBvB,cAAAA;MAClB,MAAM8B;QAAwBM,CAAAA,cAAcA,YAAY;MAA5B;MAC5B,CAAA;IAFkB;IAIpB,2BAAuBpC,cAAAA;MACrB,MAAM8B;QAAwBM,CAAAA,cAAcA,YAAY;MAA5B;MAC5B,CAAA;IAFqB;SAKvB,cAAAjC,eAAC,0CAAU,KApBb,SAAA;IAqBI,UAAUmB,oBAAoBO,wBAAwB,IAAI,KAAK;IAC/D,oBAAkBQ;KACd7B,YAHN;IAIE,KAAKE;IACL,OAAO;MAAE4B,SAAS;MAAQ,GAAGrC,MAAMsC;;IACnC,aAAaC,0CAAqBvC,MAAMwC,aAAa,MAAM;AACzDb,sBAAgBI,UAAU;KADK;IAGjC,SAASQ,0CAAqBvC,MAAMyC,SAAUC,CAAAA,UAAU;AAKtD,YAAMC,kBAAkB,CAAChB,gBAAgBI;AAEzC,UAAIW,MAAME,WAAWF,MAAMG,iBAAiBF,mBAAmB,CAACtB,kBAAkB;AAChF,cAAMyB,kBAAkB,IAAIC,YAAY/D,mCAAaC,mCAA7B;AACxByD,cAAMG,cAAcG,cAAcF,eAAlC;AAEA,YAAI,CAACA,gBAAgBG,kBAAkB;AACrC,gBAAMC,QAAQxB,SAAQ,EAAGyB;YAAQC,CAAAA,SAASA,KAAKC;UAAjC;AACd,gBAAMC,aAAaJ,MAAMK;YAAMH,CAAAA,SAASA,KAAKI;UAA1B;AACnB,gBAAMC,cAAcP,MAAMK;YAAMH,CAAAA,SAASA,KAAKM,OAAOrD;UAAjC;AACpB,gBAAMsD,iBAAiB;YAACL;YAAYG;eAAgBP;YAAOC,OACzDS,OADqB;AAGvB,gBAAMC,iBAAiBF,eAAeG;YAAKV,CAAAA,SAASA,KAAK5C,IAAIuB;UAAtC;AACvBgC,2CAAWF,cAAD;;;AAIdlC,sBAAgBI,UAAU;KAvBC;IAyB7B,QAAQQ;MAAqBvC,MAAMgE;MAAQ,MAAM1C,oBAAoB,KAAD;IAAxC;GAlC9B,CAAA,CApBF;CAtCyB;AAsG7B,IAAM2C,kCAAY;AAUlB,IAAMC,+CAAuBnE,cAAAA,YAC3B,CAACC,OAA0CC,iBAAiB;AAC1D,QAAM,EAAA,yBAAA,YAEQ,MAFR,SAGK,OAHL,WAKJ,GAAGkE,UAAH,IACEnE;AACJ,QAAMoE,SAASC,0CAAK;AACpB,QAAMX,KAAKxB,aAAakC;AACxB,QAAME,UAAUzE,4CAAsBoE,iCAAW9D,uBAAZ;AACrC,QAAMoE,mBAAmBD,QAAQjE,qBAAqBqD;AACtD,QAAMhC,WAAWpC,oCAAca,uBAAD;AAE9B,QAAM,EAAA,oBAAA,sBAAsBqE,IAA0BF;AAEtDvE,oBAAAA,WAAgB,MAAM;AACpB,QAAIsD,WAAW;AACboB,yBAAkB;AAClB,aAAO,MAAMD,sBAAqB;;KAEnC;IAACnB;IAAWoB;IAAoBD;GALnC;AAOA,aACE,cAAAtE,eAAC,iCAAW,UADd;IAEI,OAAOC;IACP;IACA;IACA;SAEA,cAAAD,eAAC,0CAAU,MANb,SAAA;IAOI,UAAUqE,mBAAmB,IAAI;IACjC,oBAAkBD,QAAQlC;KACtB+B,WAHN;IAIE,KAAKlE;IACL,aAAasC,0CAAqBvC,MAAMwC,aAAcE,CAAAA,UAAU;AAG9D,UAAI,CAACW;AAAWX,cAAMgC,eAAN;;AAEXJ,gBAAQK,YAAYjB,EAApB;KAL0B;IAOjC,SAASnB;MAAqBvC,MAAMyC;MAAS,MAAM6B,QAAQK,YAAYjB,EAApB;IAAtB;IAC7B,WAAWnB,0CAAqBvC,MAAM4E,WAAYlC,CAAAA,UAAU;AAC1D,UAAIA,MAAMmC,QAAQ,SAASnC,MAAMoC,UAAU;AACzCR,gBAAQS,eAAR;AACA;;AAGF,UAAIrC,MAAME,WAAWF,MAAMG;AAAe;AAE1C,YAAMmC,cAAcC,qCAAevC,OAAO4B,QAAQlC,aAAakC,QAAQzD,GAArC;AAElC,UAAImE,gBAAgBE,QAAW;AAC7BxC,cAAMgC,eAAN;AACA,cAAMxB,QAAQxB,SAAQ,EAAGyB;UAAQC,CAAAA,SAASA,KAAKC;QAAjC;AACd,YAAIQ,iBAAiBX,MAAMY;UAAKV,CAAAA,SAASA,KAAK5C,IAAIuB;QAA7B;AAErB,YAAIiD,gBAAgB;AAAQnB,yBAAesB,QAAf;iBACnBH,gBAAgB,UAAUA,gBAAgB,QAAQ;AACzD,cAAIA,gBAAgB;AAAQnB,2BAAesB,QAAf;AAC5B,gBAAMC,eAAevB,eAAewB,QAAQ3C,MAAMG,aAA7B;AACrBgB,2BAAiBS,QAAQgB,OACrBC,gCAAU1B,gBAAgBuB,eAAe,CAAhC,IACTvB,eAAe2B,MAAMJ,eAAe,CAApC;;AAONK;UAAW,MAAM1B,iCAAWF,cAAD;QAAjB;;KA5BiB;GAbjC,CAAA,CANF;CAzBuB;AAiF7B,OAAA,OAAA,0CAAA;EAAA,aAAA;CAAA;AAKA,IAAM6B,gDAAuD;EAC3DC,WAAW;EAAQC,SAAS;EAC5BC,YAAY;EAAQC,WAAW;EAC/BC,QAAQ;EAASC,MAAM;EACvBC,UAAU;EAAQC,KAAK;;AAGzB,SAASC,2CAAqBtB,KAAahE,KAAiB;AAC1D,MAAIA,QAAQ;AAAO,WAAOgE;AAC1B,SAAOA,QAAQ,cAAc,eAAeA,QAAQ,eAAe,cAAcA;;AAKnF,SAASI,qCAAevC,OAA4BN,aAA2BvB,KAAiB;AAC9F,QAAMgE,MAAMsB,2CAAqBzD,MAAMmC,KAAKhE,GAAZ;AAChC,MAAIuB,gBAAgB,cAAc;IAAC;IAAa;IAAcgE,SAASvB,GAArC;AAA2C,WAAOK;AACpF,MAAI9C,gBAAgB,gBAAgB;IAAC;IAAW;IAAagE,SAASvB,GAAlC;AAAwC,WAAOK;AACnF,SAAOQ,8CAAwBb,GAAD;;AAGhC,SAASd,iCAAWsC,YAA2B;AAC7C,QAAMC,6BAA6BC,SAASC;AAC5C,aAAWC,aAAaJ,YAAY;AAElC,QAAII,cAAcH;AAA4B;AAC9CG,cAAUC,MAAV;AACA,QAAIH,SAASC,kBAAkBF;AAA4B;;;AAQ/D,SAASf,gCAAaoB,OAAYC,YAAoB;AACpD,SAAOD,MAAM7C;IAAI,CAAC+C,GAAGC,UAAUH,OAAOC,aAAaE,SAASH,MAAMI,MAA9B;EAA7B;;AAGT,IAAMC,4CAAOlH;AACb,IAAMmH,4CAAO/C;", "names": ["DirectionContext", "React", "undefined", "useDirection", "localDir", "globalDir", "React", "DirectionContext", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "createContextScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "Map", "CollectionProvider", "props", "children", "ref", "React", "useRef", "$6vYhU$react", "COLLECTION_SLOT_NAME", "CollectionSlot", "forwardRef", "forwardedRef", "context", "scope", "composedRefs", "useComposedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "useCollection", "getItems", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "orderedItems", "sort", "a", "b", "indexOf", "Provider", "Slot", "ItemSlot", "ENTRY_FOCUS", "EVENT_OPTIONS", "bubbles", "cancelable", "GROUP_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createRovingFocusGroupContext", "createRovingFocusGroupScope", "createContextScope", "RovingFocus<PERSON><PERSON>ider", "useRovingFocusContext", "RovingFocusGroup", "React", "props", "forwardedRef", "$98Iye$createElement", "__scopeRovingFocusGroup", "RovingFocusGroupImpl", "currentTabStopId", "currentTabStopIdProp", "groupProps", "ref", "composedRefs", "useComposedRefs", "direction", "useDirection", "dir", "setCurrentTabStopId", "useControllableState", "prop", "defaultProp", "defaultCurrentTabStopId", "onChange", "onCurrentTabStopIdChange", "isTabbingBackOut", "setIsTabbingBackOut", "handleEntryFocus", "useCallbackRef", "onEntryFocus", "getItems", "isClickFocusRef", "focusableItemsCount", "setFocusableItemsCount", "node", "current", "addEventListener", "removeEventListener", "tabStopId", "prevCount", "orientation", "outline", "style", "composeEventHandlers", "onMouseDown", "onFocus", "event", "isKeyboardFocus", "target", "currentTarget", "entryFocusEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "activeItem", "find", "active", "currentItem", "id", "candidate<PERSON><PERSON>s", "Boolean", "candidateNodes", "map", "focusFirst", "onBlur", "ITEM_NAME", "RovingFocusGroupItem", "itemProps", "autoId", "useId", "context", "isCurrentTabStop", "onFocusableItemRemove", "onFocusableItemAdd", "preventDefault", "onItemFocus", "onKeyDown", "key", "shift<PERSON>ey", "onItemShiftTab", "focusIntent", "getFocusIntent", "undefined", "reverse", "currentIndex", "indexOf", "loop", "wrapArray", "slice", "setTimeout", "MAP_KEY_TO_FOCUS_INTENT", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "PageUp", "Home", "PageDown", "End", "getDirectionAwareKey", "includes", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "document", "activeElement", "candidate", "focus", "array", "startIndex", "_", "index", "length", "Root", "<PERSON><PERSON>"]}