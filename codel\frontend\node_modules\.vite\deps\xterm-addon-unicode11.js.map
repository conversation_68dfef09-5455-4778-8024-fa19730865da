{"version": 3, "sources": ["../../xterm-addon-unicode11/lib/webpack:/Unicode11Addon/webpack/universalModuleDefinition", "../../xterm-addon-unicode11/lib/webpack:/Unicode11Addon/src/UnicodeV11.ts", "../../xterm-addon-unicode11/lib/webpack:/Unicode11Addon/webpack/bootstrap", "../../xterm-addon-unicode11/lib/webpack:/Unicode11Addon/src/Unicode11Addon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Unicode11Addon\"] = factory();\n\telse\n\t\troot[\"Unicode11Addon\"] = factory();\n})(this, () => {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IUnicodeVersionProvider } from 'xterm';\n\ntype CharWidth = 0 | 1 | 2;\n\nconst BMP_COMBINING = [\n  [0x0300, 0x036F], [0x0483, 0x0489], [0x0591, 0x05BD],\n  [0x05BF, 0x05BF], [0x05C1, 0x05C2], [0x05C4, 0x05C5],\n  [0x05C7, 0x05C7], [0x0600, 0x0605], [0x0610, 0x061A],\n  [0x061C, 0x061C], [0x064B, 0x065F], [0x0670, 0x0670],\n  [0x06D6, 0x06DD], [0x06DF, 0x06E4], [0x06E7, 0x06E8],\n  [0x06EA, 0x06ED], [0x070F, 0x070F], [0x0711, 0x0711],\n  [0x0730, 0x074A], [0x07A6, 0x07B0], [0x07EB, 0x07F3],\n  [0x07FD, 0x07FD], [0x0816, 0x0819], [0x081B, 0x0823],\n  [0x0825, 0x0827], [0x0829, 0x082D], [0x0859, 0x085B],\n  [0x08D3, 0x0902], [0x093A, 0x093A], [0x093C, 0x093C],\n  [0x0941, 0x0948], [0x094D, 0x094D], [0x0951, 0x0957],\n  [0x0962, 0x0963], [0x0981, 0x0981], [0x09BC, 0x09BC],\n  [0x09C1, 0x09C4], [0x09CD, 0x09CD], [0x09E2, 0x09E3],\n  [0x09FE, 0x09FE], [0x0A01, 0x0A02], [0x0A3C, 0x0A3C],\n  [0x0A41, 0x0A42], [0x0A47, 0x0A48], [0x0A4B, 0x0A4D],\n  [0x0A51, 0x0A51], [0x0A70, 0x0A71], [0x0A75, 0x0A75],\n  [0x0A81, 0x0A82], [0x0ABC, 0x0ABC], [0x0AC1, 0x0AC5],\n  [0x0AC7, 0x0AC8], [0x0ACD, 0x0ACD], [0x0AE2, 0x0AE3],\n  [0x0AFA, 0x0AFF], [0x0B01, 0x0B01], [0x0B3C, 0x0B3C],\n  [0x0B3F, 0x0B3F], [0x0B41, 0x0B44], [0x0B4D, 0x0B4D],\n  [0x0B56, 0x0B56], [0x0B62, 0x0B63], [0x0B82, 0x0B82],\n  [0x0BC0, 0x0BC0], [0x0BCD, 0x0BCD], [0x0C00, 0x0C00],\n  [0x0C04, 0x0C04], [0x0C3E, 0x0C40], [0x0C46, 0x0C48],\n  [0x0C4A, 0x0C4D], [0x0C55, 0x0C56], [0x0C62, 0x0C63],\n  [0x0C81, 0x0C81], [0x0CBC, 0x0CBC], [0x0CBF, 0x0CBF],\n  [0x0CC6, 0x0CC6], [0x0CCC, 0x0CCD], [0x0CE2, 0x0CE3],\n  [0x0D00, 0x0D01], [0x0D3B, 0x0D3C], [0x0D41, 0x0D44],\n  [0x0D4D, 0x0D4D], [0x0D62, 0x0D63], [0x0DCA, 0x0DCA],\n  [0x0DD2, 0x0DD4], [0x0DD6, 0x0DD6], [0x0E31, 0x0E31],\n  [0x0E34, 0x0E3A], [0x0E47, 0x0E4E], [0x0EB1, 0x0EB1],\n  [0x0EB4, 0x0EBC], [0x0EC8, 0x0ECD], [0x0F18, 0x0F19],\n  [0x0F35, 0x0F35], [0x0F37, 0x0F37], [0x0F39, 0x0F39],\n  [0x0F71, 0x0F7E], [0x0F80, 0x0F84], [0x0F86, 0x0F87],\n  [0x0F8D, 0x0F97], [0x0F99, 0x0FBC], [0x0FC6, 0x0FC6],\n  [0x102D, 0x1030], [0x1032, 0x1037], [0x1039, 0x103A],\n  [0x103D, 0x103E], [0x1058, 0x1059], [0x105E, 0x1060],\n  [0x1071, 0x1074], [0x1082, 0x1082], [0x1085, 0x1086],\n  [0x108D, 0x108D], [0x109D, 0x109D], [0x1160, 0x11FF],\n  [0x135D, 0x135F], [0x1712, 0x1714], [0x1732, 0x1734],\n  [0x1752, 0x1753], [0x1772, 0x1773], [0x17B4, 0x17B5],\n  [0x17B7, 0x17BD], [0x17C6, 0x17C6], [0x17C9, 0x17D3],\n  [0x17DD, 0x17DD], [0x180B, 0x180E], [0x1885, 0x1886],\n  [0x18A9, 0x18A9], [0x1920, 0x1922], [0x1927, 0x1928],\n  [0x1932, 0x1932], [0x1939, 0x193B], [0x1A17, 0x1A18],\n  [0x1A1B, 0x1A1B], [0x1A56, 0x1A56], [0x1A58, 0x1A5E],\n  [0x1A60, 0x1A60], [0x1A62, 0x1A62], [0x1A65, 0x1A6C],\n  [0x1A73, 0x1A7C], [0x1A7F, 0x1A7F], [0x1AB0, 0x1ABE],\n  [0x1B00, 0x1B03], [0x1B34, 0x1B34], [0x1B36, 0x1B3A],\n  [0x1B3C, 0x1B3C], [0x1B42, 0x1B42], [0x1B6B, 0x1B73],\n  [0x1B80, 0x1B81], [0x1BA2, 0x1BA5], [0x1BA8, 0x1BA9],\n  [0x1BAB, 0x1BAD], [0x1BE6, 0x1BE6], [0x1BE8, 0x1BE9],\n  [0x1BED, 0x1BED], [0x1BEF, 0x1BF1], [0x1C2C, 0x1C33],\n  [0x1C36, 0x1C37], [0x1CD0, 0x1CD2], [0x1CD4, 0x1CE0],\n  [0x1CE2, 0x1CE8], [0x1CED, 0x1CED], [0x1CF4, 0x1CF4],\n  [0x1CF8, 0x1CF9], [0x1DC0, 0x1DF9], [0x1DFB, 0x1DFF],\n  [0x200B, 0x200F], [0x202A, 0x202E], [0x2060, 0x2064],\n  [0x2066, 0x206F], [0x20D0, 0x20F0], [0x2CEF, 0x2CF1],\n  [0x2D7F, 0x2D7F], [0x2DE0, 0x2DFF], [0x302A, 0x302D],\n  [0x3099, 0x309A], [0xA66F, 0xA672], [0xA674, 0xA67D],\n  [0xA69E, 0xA69F], [0xA6F0, 0xA6F1], [0xA802, 0xA802],\n  [0xA806, 0xA806], [0xA80B, 0xA80B], [0xA825, 0xA826],\n  [0xA8C4, 0xA8C5], [0xA8E0, 0xA8F1], [0xA8FF, 0xA8FF],\n  [0xA926, 0xA92D], [0xA947, 0xA951], [0xA980, 0xA982],\n  [0xA9B3, 0xA9B3], [0xA9B6, 0xA9B9], [0xA9BC, 0xA9BD],\n  [0xA9E5, 0xA9E5], [0xAA29, 0xAA2E], [0xAA31, 0xAA32],\n  [0xAA35, 0xAA36], [0xAA43, 0xAA43], [0xAA4C, 0xAA4C],\n  [0xAA7C, 0xAA7C], [0xAAB0, 0xAAB0], [0xAAB2, 0xAAB4],\n  [0xAAB7, 0xAAB8], [0xAABE, 0xAABF], [0xAAC1, 0xAAC1],\n  [0xAAEC, 0xAAED], [0xAAF6, 0xAAF6], [0xABE5, 0xABE5],\n  [0xABE8, 0xABE8], [0xABED, 0xABED], [0xFB1E, 0xFB1E],\n  [0xFE00, 0xFE0F], [0xFE20, 0xFE2F], [0xFEFF, 0xFEFF],\n  [0xFFF9, 0xFFFB]\n];\nconst HIGH_COMBINING = [\n  [0x101FD, 0x101FD], [0x102E0, 0x102E0],\n  [0x10376, 0x1037A], [0x10A01, 0x10A03], [0x10A05, 0x10A06],\n  [0x10A0C, 0x10A0F], [0x10A38, 0x10A3A], [0x10A3F, 0x10A3F],\n  [0x10AE5, 0x10AE6], [0x10D24, 0x10D27], [0x10F46, 0x10F50],\n  [0x11001, 0x11001], [0x11038, 0x11046], [0x1107F, 0x11081],\n  [0x110B3, 0x110B6], [0x110B9, 0x110BA], [0x110BD, 0x110BD],\n  [0x110CD, 0x110CD], [0x11100, 0x11102], [0x11127, 0x1112B],\n  [0x1112D, 0x11134], [0x11173, 0x11173], [0x11180, 0x11181],\n  [0x111B6, 0x111BE], [0x111C9, 0x111CC], [0x1122F, 0x11231],\n  [0x11234, 0x11234], [0x11236, 0x11237], [0x1123E, 0x1123E],\n  [0x112DF, 0x112DF], [0x112E3, 0x112EA], [0x11300, 0x11301],\n  [0x1133B, 0x1133C], [0x11340, 0x11340], [0x11366, 0x1136C],\n  [0x11370, 0x11374], [0x11438, 0x1143F], [0x11442, 0x11444],\n  [0x11446, 0x11446], [0x1145E, 0x1145E], [0x114B3, 0x114B8],\n  [0x114BA, 0x114BA], [0x114BF, 0x114C0], [0x114C2, 0x114C3],\n  [0x115B2, 0x115B5], [0x115BC, 0x115BD], [0x115BF, 0x115C0],\n  [0x115DC, 0x115DD], [0x11633, 0x1163A], [0x1163D, 0x1163D],\n  [0x1163F, 0x11640], [0x116AB, 0x116AB], [0x116AD, 0x116AD],\n  [0x116B0, 0x116B5], [0x116B7, 0x116B7], [0x1171D, 0x1171F],\n  [0x11722, 0x11725], [0x11727, 0x1172B], [0x1182F, 0x11837],\n  [0x11839, 0x1183A], [0x119D4, 0x119D7], [0x119DA, 0x119DB],\n  [0x119E0, 0x119E0], [0x11A01, 0x11A0A], [0x11A33, 0x11A38],\n  [0x11A3B, 0x11A3E], [0x11A47, 0x11A47], [0x11A51, 0x11A56],\n  [0x11A59, 0x11A5B], [0x11A8A, 0x11A96], [0x11A98, 0x11A99],\n  [0x11C30, 0x11C36], [0x11C38, 0x11C3D], [0x11C3F, 0x11C3F],\n  [0x11C92, 0x11CA7], [0x11CAA, 0x11CB0], [0x11CB2, 0x11CB3],\n  [0x11CB5, 0x11CB6], [0x11D31, 0x11D36], [0x11D3A, 0x11D3A],\n  [0x11D3C, 0x11D3D], [0x11D3F, 0x11D45], [0x11D47, 0x11D47],\n  [0x11D90, 0x11D91], [0x11D95, 0x11D95], [0x11D97, 0x11D97],\n  [0x11EF3, 0x11EF4], [0x13430, 0x13438], [0x16AF0, 0x16AF4],\n  [0x16B30, 0x16B36], [0x16F4F, 0x16F4F], [0x16F8F, 0x16F92],\n  [0x1BC9D, 0x1BC9E], [0x1BCA0, 0x1BCA3], [0x1D167, 0x1D169],\n  [0x1D173, 0x1D182], [0x1D185, 0x1D18B], [0x1D1AA, 0x1D1AD],\n  [0x1D242, 0x1D244], [0x1DA00, 0x1DA36], [0x1DA3B, 0x1DA6C],\n  [0x1DA75, 0x1DA75], [0x1DA84, 0x1DA84], [0x1DA9B, 0x1DA9F],\n  [0x1DAA1, 0x1DAAF], [0x1E000, 0x1E006], [0x1E008, 0x1E018],\n  [0x1E01B, 0x1E021], [0x1E023, 0x1E024], [0x1E026, 0x1E02A],\n  [0x1E130, 0x1E136], [0x1E2EC, 0x1E2EF], [0x1E8D0, 0x1E8D6],\n  [0x1E944, 0x1E94A], [0xE0001, 0xE0001], [0xE0020, 0xE007F],\n  [0xE0100, 0xE01EF]\n];\nconst BMP_WIDE = [\n  [0x1100, 0x115F], [0x231A, 0x231B], [0x2329, 0x232A],\n  [0x23E9, 0x23EC], [0x23F0, 0x23F0], [0x23F3, 0x23F3],\n  [0x25FD, 0x25FE], [0x2614, 0x2615], [0x2648, 0x2653],\n  [0x267F, 0x267F], [0x2693, 0x2693], [0x26A1, 0x26A1],\n  [0x26AA, 0x26AB], [0x26BD, 0x26BE], [0x26C4, 0x26C5],\n  [0x26CE, 0x26CE], [0x26D4, 0x26D4], [0x26EA, 0x26EA],\n  [0x26F2, 0x26F3], [0x26F5, 0x26F5], [0x26FA, 0x26FA],\n  [0x26FD, 0x26FD], [0x2705, 0x2705], [0x270A, 0x270B],\n  [0x2728, 0x2728], [0x274C, 0x274C], [0x274E, 0x274E],\n  [0x2753, 0x2755], [0x2757, 0x2757], [0x2795, 0x2797],\n  [0x27B0, 0x27B0], [0x27BF, 0x27BF], [0x2B1B, 0x2B1C],\n  [0x2B50, 0x2B50], [0x2B55, 0x2B55], [0x2E80, 0x2E99],\n  [0x2E9B, 0x2EF3], [0x2F00, 0x2FD5], [0x2FF0, 0x2FFB],\n  [0x3000, 0x3029], [0x302E, 0x303E], [0x3041, 0x3096],\n  [0x309B, 0x30FF], [0x3105, 0x312F], [0x3131, 0x318E],\n  [0x3190, 0x31BA], [0x31C0, 0x31E3], [0x31F0, 0x321E],\n  [0x3220, 0x3247], [0x3250, 0x4DBF], [0x4E00, 0xA48C],\n  [0xA490, 0xA4C6], [0xA960, 0xA97C], [0xAC00, 0xD7A3],\n  [0xF900, 0xFAFF], [0xFE10, 0xFE19], [0xFE30, 0xFE52],\n  [0xFE54, 0xFE66], [0xFE68, 0xFE6B], [0xFF01, 0xFF60],\n  [0xFFE0, 0xFFE6]\n];\nconst HIGH_WIDE = [\n  [0x16FE0, 0x16FE3], [0x17000, 0x187F7],\n  [0x18800, 0x18AF2], [0x1B000, 0x1B11E], [0x1B150, 0x1B152],\n  [0x1B164, 0x1B167], [0x1B170, 0x1B2FB], [0x1F004, 0x1F004],\n  [0x1F0CF, 0x1F0CF], [0x1F18E, 0x1F18E], [0x1F191, 0x1F19A],\n  [0x1F200, 0x1F202], [0x1F210, 0x1F23B], [0x1F240, 0x1F248],\n  [0x1F250, 0x1F251], [0x1F260, 0x1F265], [0x1F300, 0x1F320],\n  [0x1F32D, 0x1F335], [0x1F337, 0x1F37C], [0x1F37E, 0x1F393],\n  [0x1F3A0, 0x1F3CA], [0x1F3CF, 0x1F3D3], [0x1F3E0, 0x1F3F0],\n  [0x1F3F4, 0x1F3F4], [0x1F3F8, 0x1F43E], [0x1F440, 0x1F440],\n  [0x1F442, 0x1F4FC], [0x1F4FF, 0x1F53D], [0x1F54B, 0x1F54E],\n  [0x1F550, 0x1F567], [0x1F57A, 0x1F57A], [0x1F595, 0x1F596],\n  [0x1F5A4, 0x1F5A4], [0x1F5FB, 0x1F64F], [0x1F680, 0x1F6C5],\n  [0x1F6CC, 0x1F6CC], [0x1F6D0, 0x1F6D2], [0x1F6D5, 0x1F6D5],\n  [0x1F6EB, 0x1F6EC], [0x1F6F4, 0x1F6FA], [0x1F7E0, 0x1F7EB],\n  [0x1F90D, 0x1F971], [0x1F973, 0x1F976], [0x1F97A, 0x1F9A2],\n  [0x1F9A5, 0x1F9AA], [0x1F9AE, 0x1F9CA], [0x1F9CD, 0x1F9FF],\n  [0x1FA70, 0x1FA73], [0x1FA78, 0x1FA7A], [0x1FA80, 0x1FA82],\n  [0x1FA90, 0x1FA95], [0x20000, 0x2FFFD], [0x30000, 0x3FFFD]\n];\n\n// BMP lookup table, lazy initialized during first addon loading\nlet table: Uint8Array;\n\nfunction bisearch(ucs: number, data: number[][]): boolean {\n  let min = 0;\n  let max = data.length - 1;\n  let mid;\n  if (ucs < data[0][0] || ucs > data[max][1]) {\n    return false;\n  }\n  while (max >= min) {\n    mid = (min + max) >> 1;\n    if (ucs > data[mid][1]) {\n      min = mid + 1;\n    } else if (ucs < data[mid][0]) {\n      max = mid - 1;\n    } else {\n      return true;\n    }\n  }\n  return false;\n}\n\n\nexport class UnicodeV11 implements IUnicodeVersionProvider {\n  public readonly version = '11';\n\n  constructor() {\n    if (!table) {\n      table = new Uint8Array(65536);\n      table.fill(1);\n      table[0] = 0;\n      table.fill(0, 1, 32);\n      table.fill(0, 0x7f, 0xa0);\n      for (let r = 0; r < BMP_COMBINING.length; ++r) {\n        table.fill(0, BMP_COMBINING[r][0], BMP_COMBINING[r][1] + 1);\n      }\n      for (let r = 0; r < BMP_WIDE.length; ++r) {\n        table.fill(2, BMP_WIDE[r][0], BMP_WIDE[r][1] + 1);\n      }\n    }\n  }\n\n  public wcwidth(num: number): CharWidth {\n    if (num < 32) return 0;\n    if (num < 127) return 1;\n    if (num < 65536) return table[num] as CharWidth;\n    if (bisearch(num, HIGH_COMBINING)) return 0;\n    if (bisearch(num, HIGH_WIDE)) return 2;\n    return 1;\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n *\n * UnicodeVersionProvider for V11.\n */\n\nimport { Terminal, ITerminalAddon } from 'xterm';\nimport { UnicodeV11 } from './UnicodeV11';\n\n\nexport class Unicode11Addon implements ITerminalAddon {\n  public activate(terminal: Terminal): void {\n    terminal.unicode.register(new UnicodeV11());\n  }\n  public dispose(): void { }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAwB,iBAAID,EAAAA,IAE5BD,EAAqB,iBAAIC,EAAAA;IAC1B,EAAEK,SAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,EAAA,KAAA,CAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAA;ACAT,cAAMC,KAAgB,CACpB,CAAC,KAAQ,GAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,CAAA,GAELC,KAAiB,CACrB,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAC9B,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,KAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,CAAA,GAENC,IAAW,CACf,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAAS,CAAC,MAAQ,IAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAAS,CAAC,OAAQ,KAAA,GAC7C,CAAC,OAAQ,KAAA,CAAA,GAELC,IAAY,CAChB,CAAC,OAAS,KAAA,GAAU,CAAC,OAAS,MAAA,GAC9B,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAClD,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,GAAU,CAAC,QAAS,MAAA,CAAA;AAIpD,YAAIC;AAEJ,iBAASC,EAASC,IAAaC,IAAAA;AAC7B,cAEIC,IAFAC,KAAM,GACNC,KAAMH,GAAKI,SAAS;AAExB,cAAIL,KAAMC,GAAK,CAAA,EAAG,CAAA,KAAMD,KAAMC,GAAKG,EAAAA,EAAK,CAAA;AACtC,mBAAA;AAEF,iBAAOA,MAAOD;AAEZ,gBADAD,KAAOC,KAAMC,MAAQ,GACjBJ,KAAMC,GAAKC,EAAAA,EAAK,CAAA;AAClBC,cAAAA,KAAMD,KAAM;iBACP;AAAA,kBAAA,EAAIF,KAAMC,GAAKC,EAAAA,EAAK,CAAA;AAGzB,uBAAA;AAFAE,cAAAA,KAAMF,KAAM;YAAA;AAKhB,iBAAA;QACF;AAGA,QAAAT,GAAA,aAAA,MAAA;UAGE,cAAAa;AACE,gBAHc,KAAAC,UAAU,MAAA,CAGnBT,GAAO;AACVA,kBAAQ,IAAIU,WAAW,KAAA,GACvBV,EAAMW,KAAK,CAAA,GACXX,EAAM,CAAA,IAAK,GACXA,EAAMW,KAAK,GAAG,GAAG,EAAA,GACjBX,EAAMW,KAAK,GAAG,KAAM,GAAA;AACpB,uBAASC,KAAI,GAAGA,KAAIhB,GAAcW,QAAAA,EAAUK;AAC1CZ,kBAAMW,KAAK,GAAGf,GAAcgB,EAAAA,EAAG,CAAA,GAAIhB,GAAcgB,EAAAA,EAAG,CAAA,IAAK,CAAA;AAE3D,uBAASA,KAAI,GAAGA,KAAId,EAASS,QAAAA,EAAUK;AACrCZ,kBAAMW,KAAK,GAAGb,EAASc,EAAAA,EAAG,CAAA,GAAId,EAASc,EAAAA,EAAG,CAAA,IAAK,CAAA;YAAA;UAGrD;UAEO,QAAQC,IAAAA;AACb,mBAAIA,KAAM,KAAW,IACjBA,KAAM,MAAY,IAClBA,KAAM,QAAcb,EAAMa,EAAAA,IAC1BZ,EAASY,IAAKhB,EAAAA,IAAwB,IACtCI,EAASY,IAAKd,CAAAA,IAAmB,IAC9B;UACT;QAAA;MAAA,EAAA,GC1NEe,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC;AACH,iBAAOA,EAAa5B;AAGrB,YAAIC,IAASwB,EAAyBE,EAAAA,IAAY,EAGjD3B,SAAS,CAAC,EAAA;AAOX,eAHA6B,EAAoBF,EAAAA,EAAU1B,GAAQA,EAAOD,SAAS0B,CAAAA,GAG/CzB,EAAOD;MACf;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAAK,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,iBAAA;ACdA,cAAAC,KAAA,EAAA,GAAA;AAGA,QAAAD,GAAA,iBAAA,MAAA;UACS,SAASyB,IAAAA;AACdA,YAAAA,GAASC,QAAQC,SAAS,IAAI1B,GAAA2B,YAAAA;UAChC;UACO,UAAAC;UAAkB;QAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "this", "e", "t", "BMP_COMBINING", "HIGH_COMBINING", "BMP_WIDE", "HIGH_WIDE", "table", "bisearch", "ucs", "data", "mid", "min", "max", "length", "constructor", "version", "Uint8Array", "fill", "r", "num", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "terminal", "unicode", "register", "UnicodeV11", "dispose"]}