package database

import (
	"context"
	"database/sql"
)

type Setting struct {
	Key   string
	Value string
}

func (q *Queries) GetSetting(ctx context.Context, key string) (string, error) {
	var value string
	err := q.db.QueryRowContext(ctx, "SELECT value FROM settings WHERE key = $1", key).Scan(&value)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil
		}
		return "", err
	}
	return value, nil
}

func (q *Queries) SetSetting(ctx context.Context, key, value string) error {
	_, err := q.db.ExecContext(ctx, `
		INSERT INTO settings (key, value)
		VALUES ($1, $2)
		ON CONFLICT (key) DO UPDATE SET value = EXCLUDED.value
	`, key, value)
	return err
}

func (q *Queries) GetAllSettings(ctx context.Context) (map[string]string, error) {
	rows, err := q.db.QueryContext(ctx, "SELECT key, value FROM settings")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	settings := make(map[string]string)
	for rows.Next() {
		var s Setting
		if err := rows.Scan(&s.Key, &s.Value); err != nil {
			return nil, err
		}
		settings[s.Key] = s.Value
	}
	return settings, nil
}
