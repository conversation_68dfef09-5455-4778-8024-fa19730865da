query Flow($id: Uint!) {
  flow(id: $id) {
    id
    name
    status
    model {
      id
      provider
    }
    tasks {
      id
      type
      status
      message
      args
      results
      createdAt
    }
    terminal {
      containerName
      connected
      logs {
        id
        text
      }
    }
    browser {
      url
      screenshotUrl
    }
  }
}

query Flows {
  flows {
    id
    name
    status
    model {
      id
      provider
    }
  }
}

query AvailableModels {
  availableModels {
    id
    provider
  }
}

mutation CreateFlow($modelProvider: String!, $modelId: String!) {
  createFlow(modelProvider: $modelProvider, modelId: $modelId) {
    id
    name
    status
    model {
      id
      provider
    }
  }
}

mutation CreateTask($flowId: Uint!, $query: String!) {
  createTask(flowId: $flowId, query: $query) {
    id
    type
    status
    message
    args
    results
    createdAt
  }
}

mutation FinishFlow($flowId: Uint!) {
  finishFlow(flowId: $flowId) {
    id
    status
  }
}

subscription TaskAdded($flowId: Uint!) {
  taskAdded(flowId: $flowId) {
    id
    type
    status
    message
    args
    results
    createdAt
  }
}

subscription TaskUpdated {
  taskUpdated {
    id
    type
    status
    message
    args
    results
    createdAt
  }
}

subscription FlowUpdated($flowId: Uint!) {
  flowUpdated(flowId: $flowId) {
    id
    name
    status
    model {
      id
      provider
    }
    tasks {
      id
      type
      status
      message
      args
      results
      createdAt
    }
  }
}

subscription BrowserUpdated($flowId: Uint!) {
  browserUpdated(flowId: $flowId) {
    url
    screenshotUrl
  }
}

subscription TerminalLogsAdded($flowId: Uint!) {
  terminalLogsAdded(flowId: $flowId) {
    id
    text
  }
}
