{"version": 3, "sources": ["../../@radix-ui/react-visually-hidden/dist/packages/react/visually-hidden/src/index.ts", "../../@radix-ui/react-visually-hidden/dist/packages/react/visually-hidden/src/VisuallyHidden.tsx", "../../@radix-ui/react-tooltip/dist/packages/react/tooltip/src/index.ts", "../../@radix-ui/react-tooltip/dist/packages/react/tooltip/src/Tooltip.tsx"], "sourcesContent": ["export {\n  VisuallyHidden,\n  //\n  Root,\n} from './VisuallyHidden';\nexport type { VisuallyHiddenProps } from './VisuallyHidden';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n", "export {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n} from './Tooltip';\nexport type {\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n} from './Tooltip';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slottable } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P = {}> = P & { __scopeTooltip?: Scope };\nconst [createTooltipContext, createTooltipScope] = createContextScope('Tooltip', [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'TooltipProvider';\nconst DEFAULT_DELAY_DURATION = 700;\nconst TOOLTIP_OPEN = 'tooltip.open';\n\ntype TooltipProviderContextValue = {\n  isOpenDelayed: boolean;\n  delayDuration: number;\n  onOpen(): void;\n  onClose(): void;\n  onPointerInTransitChange(inTransit: boolean): void;\n  isPointerInTransitRef: React.MutableRefObject<boolean>;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipProviderContextProvider, useTooltipProviderContext] =\n  createTooltipContext<TooltipProviderContextValue>(PROVIDER_NAME);\n\ninterface TooltipProviderProps {\n  children: React.ReactNode;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst TooltipProvider: React.FC<TooltipProviderProps> = (\n  props: ScopedProps<TooltipProviderProps>\n) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children,\n  } = props;\n  const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n\n  return (\n    <TooltipProviderContextProvider\n      scope={__scopeTooltip}\n      isOpenDelayed={isOpenDelayed}\n      delayDuration={delayDuration}\n      onOpen={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        setIsOpenDelayed(false);\n      }, [])}\n      onClose={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => setIsOpenDelayed(true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration])}\n      isPointerInTransitRef={isPointerInTransitRef}\n      onPointerInTransitChange={React.useCallback((inTransit: boolean) => {\n        isPointerInTransitRef.current = inTransit;\n      }, [])}\n      disableHoverableContent={disableHoverableContent}\n    >\n      {children}\n    </TooltipProviderContextProvider>\n  );\n};\n\nTooltipProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLTIP_NAME = 'Tooltip';\n\ntype TooltipContextValue = {\n  contentId: string;\n  open: boolean;\n  stateAttribute: 'closed' | 'delayed-open' | 'instant-open';\n  trigger: TooltipTriggerElement | null;\n  onTriggerChange(trigger: TooltipTriggerElement | null): void;\n  onTriggerEnter(): void;\n  onTriggerLeave(): void;\n  onOpen(): void;\n  onClose(): void;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipContextProvider, useTooltipContext] =\n  createTooltipContext<TooltipContextValue>(TOOLTIP_NAME);\n\ninterface TooltipProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened. This will\n   * override the prop with the same name passed to Provider.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst Tooltip: React.FC<TooltipProps> = (props: ScopedProps<TooltipProps>) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen = false,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp,\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState<HTMLButtonElement | null>(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent =\n    disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: (open) => {\n      if (open) {\n        providerContext.onOpen();\n\n        // as `onChange` is called within a lifecycle method we\n        // avoid dispatching via `dispatchDiscreteCustomEvent`.\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open);\n    },\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? (wasOpenDelayedRef.current ? 'delayed-open' : 'instant-open') : 'closed';\n  }, [open]);\n\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    setOpen(false);\n  }, [setOpen]);\n\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(openTimerRef.current);\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <TooltipContextProvider\n        scope={__scopeTooltip}\n        contentId={contentId}\n        open={open}\n        stateAttribute={stateAttribute}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        onTriggerEnter={React.useCallback(() => {\n          if (providerContext.isOpenDelayed) handleDelayedOpen();\n          else handleOpen();\n        }, [providerContext.isOpenDelayed, handleDelayedOpen, handleOpen])}\n        onTriggerLeave={React.useCallback(() => {\n          if (disableHoverableContent) {\n            handleClose();\n          } else {\n            // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n            window.clearTimeout(openTimerRef.current);\n          }\n        }, [handleClose, disableHoverableContent])}\n        onOpen={handleOpen}\n        onClose={handleClose}\n        disableHoverableContent={disableHoverableContent}\n      >\n        {children}\n      </TooltipContextProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nTooltip.displayName = TOOLTIP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TooltipTrigger';\n\ntype TooltipTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TooltipTriggerProps extends PrimitiveButtonProps {}\n\nconst TooltipTrigger = React.forwardRef<TooltipTriggerElement, TooltipTriggerProps>(\n  (props: ScopedProps<TooltipTriggerProps>, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef<TooltipTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => (isPointerDownRef.current = false), []);\n\n    React.useEffect(() => {\n      return () => document.removeEventListener('pointerup', handlePointerUp);\n    }, [handlePointerUp]);\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          // We purposefully avoid adding `type=button` here because tooltip triggers are also\n          // commonly anchors and the anchor `type` attribute signifies MIME type.\n          aria-describedby={context.open ? context.contentId : undefined}\n          data-state={context.stateAttribute}\n          {...triggerProps}\n          ref={composedRefs}\n          onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n            if (event.pointerType === 'touch') return;\n            if (\n              !hasPointerMoveOpenedRef.current &&\n              !providerContext.isPointerInTransitRef.current\n            ) {\n              context.onTriggerEnter();\n              hasPointerMoveOpenedRef.current = true;\n            }\n          })}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, () => {\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })}\n          onPointerDown={composeEventHandlers(props.onPointerDown, () => {\n            isPointerDownRef.current = true;\n            document.addEventListener('pointerup', handlePointerUp, { once: true });\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            if (!isPointerDownRef.current) context.onOpen();\n          })}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          onClick={composeEventHandlers(props.onClick, context.onClose)}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nTooltipTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'TooltipPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createTooltipContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface TooltipPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipPortal: React.FC<TooltipPortalProps> = (props: ScopedProps<TooltipPortalProps>) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return (\n    <PortalProvider scope={__scopeTooltip} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nTooltipPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TooltipContent';\n\ntype TooltipContentElement = TooltipContentImplElement;\ninterface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipContent = React.forwardRef<TooltipContentElement, TooltipContentProps>(\n  (props: ScopedProps<TooltipContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = 'top', ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.disableHoverableContent ? (\n          <TooltipContentImpl side={side} {...contentProps} ref={forwardedRef} />\n        ) : (\n          <TooltipContentHoverable side={side} {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\n\ntype TooltipContentHoverableElement = TooltipContentImplElement;\ninterface TooltipContentHoverableProps extends TooltipContentImplProps {}\n\nconst TooltipContentHoverable = React.forwardRef<\n  TooltipContentHoverableElement,\n  TooltipContentHoverableProps\n>((props: ScopedProps<TooltipContentHoverableProps>, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef<TooltipContentHoverableElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState<Polygon | null>(null);\n\n  const { trigger, onClose } = context;\n  const content = ref.current;\n\n  const { onPointerInTransitChange } = providerContext;\n\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n\n  const handleCreateGraceArea = React.useCallback(\n    (event: PointerEvent, hoverTarget: HTMLElement) => {\n      const currentTarget = event.currentTarget as HTMLElement;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, trigger);\n\n      trigger.addEventListener('pointerleave', handleTriggerLeave);\n      content.addEventListener('pointerleave', handleContentLeave);\n      return () => {\n        trigger.removeEventListener('pointerleave', handleTriggerLeave);\n        content.removeEventListener('pointerleave', handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        const target = event.target as HTMLElement;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener('pointermove', handleTrackPointerGrace);\n      return () => document.removeEventListener('pointermove', handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n\n  return <TooltipContentImpl {...props} ref={composedRefs} />;\n});\n\nconst [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] =\n  createTooltipContext(TOOLTIP_NAME, { isInside: false });\n\ntype TooltipContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface TooltipContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * A more descriptive label for accessibility purpose\n   */\n  'aria-label'?: string;\n\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `Tooltip`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n}\n\nconst TooltipContentImpl = React.forwardRef<TooltipContentImplElement, TooltipContentImplProps>(\n  (props: ScopedProps<TooltipContentImplProps>, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      'aria-label': ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n\n    // Close this tooltip if another one opens\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n\n    // Close the tooltip if the trigger is scrolled\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event: Event) => {\n          const target = event.target as HTMLElement;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener('scroll', handleScroll, { capture: true });\n        return () => window.removeEventListener('scroll', handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n\n    return (\n      <DismissableLayer\n        asChild\n        disableOutsidePointerEvents={false}\n        onEscapeKeyDown={onEscapeKeyDown}\n        onPointerDownOutside={onPointerDownOutside}\n        onFocusOutside={(event) => event.preventDefault()}\n        onDismiss={onClose}\n      >\n        <PopperPrimitive.Content\n          data-state={context.stateAttribute}\n          {...popperScope}\n          {...contentProps}\n          ref={forwardedRef}\n          style={{\n            ...contentProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n              '--radix-tooltip-content-transform-origin': 'var(--radix-popper-transform-origin)',\n              '--radix-tooltip-content-available-width': 'var(--radix-popper-available-width)',\n              '--radix-tooltip-content-available-height': 'var(--radix-popper-available-height)',\n              '--radix-tooltip-trigger-width': 'var(--radix-popper-anchor-width)',\n              '--radix-tooltip-trigger-height': 'var(--radix-popper-anchor-height)',\n            },\n          }}\n        >\n          <Slottable>{children}</Slottable>\n          <VisuallyHiddenContentContextProvider scope={__scopeTooltip} isInside={true}>\n            <VisuallyHiddenPrimitive.Root id={context.contentId} role=\"tooltip\">\n              {ariaLabel || children}\n            </VisuallyHiddenPrimitive.Root>\n          </VisuallyHiddenContentContextProvider>\n        </PopperPrimitive.Content>\n      </DismissableLayer>\n    );\n  }\n);\n\nTooltipContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'TooltipArrow';\n\ntype TooltipArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface TooltipArrowProps extends PopperArrowProps {}\n\nconst TooltipArrow = React.forwardRef<TooltipArrowElement, TooltipArrowProps>(\n  (props: ScopedProps<TooltipArrowProps>, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    // if the arrow is inside the `VisuallyHidden`, we don't want to render it all to\n    // prevent issues in positioning the arrow due to the duplicate\n    return visuallyHiddenContentContext.isInside ? null : (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    );\n  }\n);\n\nTooltipArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype Side = NonNullable<TooltipContentProps['side']>;\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect): Side {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left';\n    case right:\n      return 'right';\n    case top:\n      return 'top';\n    case bottom:\n      return 'bottom';\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nfunction getPaddedExitPoints(exitPoint: Point, exitSide: Side, padding = 5) {\n  const paddedExitPoints: Point[] = [];\n  switch (exitSide) {\n    case 'top':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'bottom':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case 'left':\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'right':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ];\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice();\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return +1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return +1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1) return points.slice();\n\n  const upperHull: Array<P> = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n\n  const lowerHull: Array<P> = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n\n  if (\n    upperHull.length === 1 &&\n    lowerHull.length === 1 &&\n    upperHull[0].x === lowerHull[0].x &&\n    upperHull[0].y === lowerHull[0].y\n  ) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\n\nconst Provider = TooltipProvider;\nconst Root = Tooltip;\nconst Trigger = TooltipTrigger;\nconst Portal = TooltipPortal;\nconst Content = TooltipContent;\nconst Arrow = TooltipArrow;\n\nexport {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSA,IAAMA,6BAAO;AAMb,IAAMC,gDAAiBC,aAAAA,YACrB,CAACC,OAAOC,iBAAiB;AACvB,aACE,aAAAC,eAAC,0CAAU,MAAX,SAAA,CAAA,GACMF,OAFR;IAGI,KAAKC;IACL,OAAO;;MAELE,UAAU;MACVC,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,SAAS;MACTC,QAAQ;MACRC,UAAU;MACVC,MAAM;MACNC,YAAY;MACZC,UAAU;MACV,GAAGZ,MAAMa;;GAfb,CAAA;CAHiB;AAyBvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,IAAMC,4CAAOhB;;;AEzBb,IAAM,CAACiB,4CAAsBC,yCAAvB,IAA6CC,yCAAmB,WAAW;EAC/EC;CADmE;AAGrE,IAAMC,uCAAiBD,wCAAiB;AAMxC,IAAME,sCAAgB;AACtB,IAAMC,+CAAyB;AAC/B,IAAMC,qCAAe;AAYrB,IAAM,CAACC,sDAAgCC,+CAAjC,IACJT,2CAAkDK,mCAA9B;AAqBtB,IAAMK,4CACJC,CAAAA,UACG;AACH,QAAM,EAAA,gBAAA,gBAEYL,8CAFZ,oBAGgB,KAHhB,0BAIsB,OAJtB,SAKJM,IACED;AACJ,QAAM,CAACE,eAAeC,gBAAhB,QAAoCC,cAAAA,UAAe,IAAf;AAC1C,QAAMC,4BAAwBD,cAAAA,QAAa,KAAb;AAC9B,QAAME,wBAAoBF,cAAAA,QAAa,CAAb;AAE1BA,oBAAAA,WAAgB,MAAM;AACpB,UAAMG,iBAAiBD,kBAAkBE;AACzC,WAAO,MAAMC,OAAOC,aAAaH,cAApB;KACZ,CAAA,CAHH;AAKA,aACE,cAAAI,eAAC,sDADH;IAEI,OAAOC;IACP;IACA;IACA,YAAQR,cAAAA,aAAkB,MAAM;AAC9BK,aAAOC,aAAaJ,kBAAkBE,OAAtC;AACAL,uBAAiB,KAAD;OACf,CAAA,CAHK;IAIR,aAASC,cAAAA,aAAkB,MAAM;AAC/BK,aAAOC,aAAaJ,kBAAkBE,OAAtC;AACAF,wBAAkBE,UAAUC,OAAOI;QACjC,MAAMV,iBAAiB,IAAD;QACtBW;MAF0B;OAI3B;MAACA;KANK;IAOT;IACA,8BAA0BV,cAAAA,aAAmBW,CAAAA,cAAuB;AAClEV,4BAAsBG,UAAUO;OAC/B,CAAA,CAFuB;IAG1B;KAECd,QArBH;;AA0BJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMe,qCAAe;AAerB,IAAM,CAACC,8CAAwBC,uCAAzB,IACJ7B,2CAA0C2B,kCAAtB;AAoBtB,IAAMG,4CAAmCnB,CAAAA,UAAqC;AAC5E,QAAM,EAAA,gBAAA,UAGJoB,MAAMC,UAHF,cAIU,OAJV,cAMJC,yBAAyBC,6BACzBC,eAAeC,kBAAfD,IACExB;AACJ,QAAM0B,kBAAkB5B,gDAA0BkB,oCAAchB,MAAMY,cAArB;AACjD,QAAMe,cAAclC,qCAAemB,cAAD;AAClC,QAAM,CAACgB,SAASC,UAAV,QAAwBzB,cAAAA,UAAyC,IAAzC;AAC9B,QAAM0B,YAAYC,0CAAK;AACvB,QAAMC,mBAAe5B,cAAAA,QAAa,CAAb;AACrB,QAAMkB,0BACJC,gCAD2B,QAC3BA,gCAD2B,SAC3BA,8BAA+BG,gBAAgBJ;AACjD,QAAME,gBAAgBC,sBAAH,QAAGA,sBAAH,SAAGA,oBAAqBC,gBAAgBF;AAC3D,QAAMS,wBAAoB7B,cAAAA,QAAa,KAAb;AAC1B,QAAM,CAACgB,QAAO,OAAOc,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMf;IACNgB,aAAaC;IACbC,UAAWnB,CAAAA,SAAS;AAClB,UAAIA,MAAM;AACRM,wBAAgBc,OAAhB;AAIAC,iBAASC,cAAc,IAAIC,YAAY/C,kCAAhB,CAAvB;;AAEA8B,wBAAgBkB,QAAhB;AAEFC,uBAAY,QAAZA,iBAAY,UAAZA,aAAezB,IAAH;;GAboC;AAgBpD,QAAM0B,qBAAiB1C,cAAAA,SAAc,MAAM;AACzC,WAAOgB,QAAQa,kBAAkBzB,UAAU,iBAAiB,iBAAkB;KAC7E;IAACY;GAFmB;AAIvB,QAAM2B,iBAAa3C,cAAAA,aAAkB,MAAM;AACzCK,WAAOC,aAAasB,aAAaxB,OAAjC;AACAyB,sBAAkBzB,UAAU;AAC5B0B,YAAQ,IAAD;KACN;IAACA;GAJe;AAMnB,QAAMc,kBAAc5C,cAAAA,aAAkB,MAAM;AAC1CK,WAAOC,aAAasB,aAAaxB,OAAjC;AACA0B,YAAQ,KAAD;KACN;IAACA;GAHgB;AAKpB,QAAMe,wBAAoB7C,cAAAA,aAAkB,MAAM;AAChDK,WAAOC,aAAasB,aAAaxB,OAAjC;AACAwB,iBAAaxB,UAAUC,OAAOI,WAAW,MAAM;AAC7CoB,wBAAkBzB,UAAU;AAC5B0B,cAAQ,IAAD;OACNV,aAHoB;KAItB;IAACA;IAAeU;GANO;AAQ1B9B,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAMK,OAAOC,aAAasB,aAAaxB,OAAjC;KACZ,CAAA,CAFH;AAIA,aACE,cAAAG,eAAC,2CAAyBgB,iBACxB,cAAAhB,eAAC,8CAFL;IAGM,OAAOC;IACP;IACA,MAAMQ;IACN;IACA;IACA,iBAAiBS;IACjB,oBAAgBzB,cAAAA,aAAkB,MAAM;AACtC,UAAIsB,gBAAgBxB;AAAe+C,0BAAiB;;AAC/CF,mBAAU;OACd;MAACrB,gBAAgBxB;MAAe+C;MAAmBF;KAHtC;IAIhB,oBAAgB3C,cAAAA,aAAkB,MAAM;AACtC,UAAIkB;AACF0B,oBAAW;;AAGXvC,eAAOC,aAAasB,aAAaxB,OAAjC;OAED;MAACwC;MAAa1B;KAPD;IAQhB,QAAQyB;IACR,SAASC;IACT;KAEC/C,QAvBH,CADF;;AA8BJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiD,qCAAe;AAMrB,IAAMC,gDAAiB/C,cAAAA,YACrB,CAACJ,OAAyCoD,iBAAiB;AACzD,QAAM,EAAA,gBAAkB,GAAGC,aAAH,IAAoBrD;AAC5C,QAAMsD,UAAUpC,wCAAkBgC,oCAActC,cAAf;AACjC,QAAMc,kBAAkB5B,gDAA0BoD,oCAActC,cAAf;AACjD,QAAMe,cAAclC,qCAAemB,cAAD;AAClC,QAAM2C,UAAMnD,cAAAA,QAAoC,IAApC;AACZ,QAAMoD,eAAeC,0CAAgBL,cAAcG,KAAKD,QAAQI,eAA5B;AACpC,QAAMC,uBAAmBvD,cAAAA,QAAa,KAAb;AACzB,QAAMwD,8BAA0BxD,cAAAA,QAAa,KAAb;AAChC,QAAMyD,sBAAkBzD,cAAAA;IAAkB,MAAOuD,iBAAiBnD,UAAU;IAAQ,CAAA;EAA5D;AAExBJ,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAMqC,SAASqB,oBAAoB,aAAaD,eAA1C;KACZ;IAACA;GAFJ;AAIA,aACE,cAAAlD,eAAC,2CADH,SAAA;IAC0B,SAAA;KAAYgB,WAApC,OACE,cAAAhB,eAAC,0CAAU,QADb,SAAA;;;IAII,oBAAkB2C,QAAQlC,OAAOkC,QAAQxB,YAAYiC;IACrD,cAAYT,QAAQR;KAChBO,cALN;IAME,KAAKG;IACL,eAAeQ,0CAAqBhE,MAAMiE,eAAgBC,CAAAA,UAAU;AAClE,UAAIA,MAAMC,gBAAgB;AAAS;AACnC,UACE,CAACP,wBAAwBpD,WACzB,CAACkB,gBAAgBrB,sBAAsBG,SACvC;AACA8C,gBAAQc,eAAR;AACAR,gCAAwBpD,UAAU;;KAPH;IAUnC,gBAAgBwD,0CAAqBhE,MAAMqE,gBAAgB,MAAM;AAC/Df,cAAQgB,eAAR;AACAV,8BAAwBpD,UAAU;KAFA;IAIpC,eAAewD,0CAAqBhE,MAAMuE,eAAe,MAAM;AAC7DZ,uBAAiBnD,UAAU;AAC3BiC,eAAS+B,iBAAiB,aAAaX,iBAAiB;QAAEY,MAAM;OAAhE;KAFiC;IAInC,SAAST,0CAAqBhE,MAAM0E,SAAS,MAAM;AACjD,UAAI,CAACf,iBAAiBnD;AAAS8C,gBAAQd,OAAR;KADJ;IAG7B,QAAQwB,0CAAqBhE,MAAM2E,QAAQrB,QAAQV,OAAvB;IAC5B,SAASoB,0CAAqBhE,MAAM4E,SAAStB,QAAQV,OAAxB;GA7B/B,CAAA,CADF;CAjBiB;AAsDvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiC,oCAAc;AAGpB,IAAM,CAACC,sCAAgBC,sCAAjB,IAAqC1F,2CAAyCwF,mCAAa;EAC/FG,YAAYjB;CADiD;AAkB/D,IAAMkB,4CAA+CjF,CAAAA,UAA2C;AAC9F,QAAM,EAAA,gBAAA,YAAA,UAAA,UAAwCkF,IAAclF;AAC5D,QAAMsD,UAAUpC,wCAAkB2D,mCAAajE,cAAd;AACjC,aACE,cAAAD,eAAC,sCADH;IACkB,OAAOC;IAAgB;SACrC,cAAAD,eAAC,2CADH;IACY,SAASqE,cAAc1B,QAAQlC;SACvC,cAAAT,eAAC,2CADH;IACmB,SAAO;IAAC;KACtBV,QADH,CADF,CADF;;AAUJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkF,qCAAe;AAWrB,IAAMC,gDAAiBhF,cAAAA,YACrB,CAACJ,OAAyCoD,iBAAiB;AACzD,QAAMiC,gBAAgBN,uCAAiBI,oCAAcnF,MAAMY,cAArB;AACtC,QAAM,EAAA,aAAeyE,cAAcL,YAA7B,OAAgD,OAAO,GAAGM,aAAH,IAAoBtF;AACjF,QAAMsD,UAAUpC,wCAAkBiE,oCAAcnF,MAAMY,cAArB;AAEjC,aACE,cAAAD,eAAC,2CADH;IACY,SAASqE,cAAc1B,QAAQlC;KACtCkC,QAAQhC,8BACP,cAAAX,eAAC,0CAFL,SAAA;IAEwB;KAAgB2E,cAApC;IAAkD,KAAKlC;GAAvD,CAAA,QAEA,cAAAzC,eAAC,+CAFD,SAAA;IAEyB;KAAgB2E,cAAzC;IAAuD,KAAKlC;GAA5D,CAAA,CAJJ;CAPiB;AAwBvB,IAAMmC,oDAA0BnF,cAAAA,YAG9B,CAACJ,OAAkDoD,iBAAiB;AACpE,QAAME,UAAUpC,wCAAkBiE,oCAAcnF,MAAMY,cAArB;AACjC,QAAMc,kBAAkB5B,gDAA0BqF,oCAAcnF,MAAMY,cAArB;AACjD,QAAM2C,UAAMnD,cAAAA,QAA6C,IAA7C;AACZ,QAAMoD,eAAeC,0CAAgBL,cAAcG,GAAf;AACpC,QAAM,CAACiC,kBAAkBC,mBAAnB,QAA0CrF,cAAAA,UAA+B,IAA/B;AAEhD,QAAM,EAAA,SAAA,QAAWwC,IAAYU;AAC7B,QAAMoC,UAAUnC,IAAI/C;AAEpB,QAAM,EAAA,yBAAEmF,IAA6BjE;AAErC,QAAMkE,4BAAwBxF,cAAAA,aAAkB,MAAM;AACpDqF,wBAAoB,IAAD;AACnBE,6BAAyB,KAAD;KACvB;IAACA;GAH0B;AAK9B,QAAME,4BAAwBzF,cAAAA,aAC5B,CAAC8D,OAAqB4B,gBAA6B;AACjD,UAAMC,gBAAgB7B,MAAM6B;AAC5B,UAAMC,YAAY;MAAEC,GAAG/B,MAAMgC;MAASC,GAAGjC,MAAMkC;;AAC/C,UAAMC,WAAWC,0CAAoBN,WAAWD,cAAcQ,sBAAd,CAAZ;AACpC,UAAMC,mBAAmBC,0CAAoBT,WAAWK,QAAZ;AAC5C,UAAMK,oBAAoBC,wCAAkBb,YAAYS,sBAAZ,CAAD;AAC3C,UAAMK,YAAYC,8BAAQ;SAAIL;SAAqBE;KAA1B;AACzBjB,wBAAoBmB,SAAD;AACnBjB,6BAAyB,IAAD;KAE1B;IAACA;GAX2B;AAc9BvF,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAMwF,sBAAqB;KACjC;IAACA;GAFJ;AAIAxF,oBAAAA,WAAgB,MAAM;AACpB,QAAIwB,WAAW8D,SAAS;AACtB,YAAMoB,qBAAsB5C,CAAAA,UAAwB2B,sBAAsB3B,OAAOwB,OAAR;AACzE,YAAMqB,qBAAsB7C,CAAAA,UAAwB2B,sBAAsB3B,OAAOtC,OAAR;AAEzEA,cAAQ4C,iBAAiB,gBAAgBsC,kBAAzC;AACApB,cAAQlB,iBAAiB,gBAAgBuC,kBAAzC;AACA,aAAO,MAAM;AACXnF,gBAAQkC,oBAAoB,gBAAgBgD,kBAA5C;AACApB,gBAAQ5B,oBAAoB,gBAAgBiD,kBAA5C;;;KAGH;IAACnF;IAAS8D;IAASG;IAAuBD;GAZ7C;AAcAxF,oBAAAA,WAAgB,MAAM;AACpB,QAAIoF,kBAAkB;AACpB,YAAMwB,0BAA2B9C,CAAAA,UAAwB;AACvD,cAAM+C,SAAS/C,MAAM+C;AACrB,cAAMC,kBAAkB;UAAEjB,GAAG/B,MAAMgC;UAASC,GAAGjC,MAAMkC;;AACrD,cAAMe,oBAAmBvF,YAAO,QAAPA,YAAO,SAAP,SAAAA,QAASwF,SAASH,MAAlB,OAA6BvB,YAA7B,QAA6BA,YAA7B,SAAA,SAA6BA,QAAS0B,SAASH,MAAlB;AACtD,cAAMI,4BAA4B,CAACC,uCAAiBJ,iBAAiB1B,gBAAlB;AAEnD,YAAI2B;AACFvB,gCAAqB;iBACZyB,2BAA2B;AACpCzB,gCAAqB;AACrBhD,kBAAO;;;AAGXH,eAAS+B,iBAAiB,eAAewC,uBAAzC;AACA,aAAO,MAAMvE,SAASqB,oBAAoB,eAAekD,uBAA5C;;KAEd;IAACpF;IAAS8D;IAASF;IAAkB5C;IAASgD;GAlBjD;AAoBA,aAAO,cAAAjF,eAAC,0CAAD,SAAA,CAAA,GAAwBX,OAA/B;IAAsC,KAAKwD;GAApC,CAAA;CAxEuB;AA2EhC,IAAM,CAAC+D,4DAAsCC,qDAAvC,IACJnI,2CAAqB2B,oCAAc;EAAEyG,UAAU;CAA3B;AAuBtB,IAAMC,+CAAqBtH,cAAAA,YACzB,CAACJ,OAA6CoD,iBAAiB;AAC7D,QAAM,EAAA,gBAAA,UAGJ,cAAcuE,WAHV,iBAAA,sBAMJ,GAAGrC,aAAH,IACEtF;AACJ,QAAMsD,UAAUpC,wCAAkBiE,oCAAcvE,cAAf;AACjC,QAAMe,cAAclC,qCAAemB,cAAD;AAClC,QAAM,EAAA,QAAEgC,IAAYU;AAGpBlD,oBAAAA,WAAgB,MAAM;AACpBqC,aAAS+B,iBAAiB5E,oCAAcgD,OAAxC;AACA,WAAO,MAAMH,SAASqB,oBAAoBlE,oCAAcgD,OAA3C;KACZ;IAACA;GAHJ;AAMAxC,oBAAAA,WAAgB,MAAM;AACpB,QAAIkD,QAAQ1B,SAAS;AACnB,YAAMgG,eAAgB1D,CAAAA,UAAiB;AACrC,cAAM+C,SAAS/C,MAAM+C;AACrB,YAAIA,WAAJ,QAAIA,WAAJ,UAAIA,OAAQG,SAAS9D,QAAQ1B,OAAzB;AAAmCgB,kBAAO;;AAEhDnC,aAAO+D,iBAAiB,UAAUoD,cAAc;QAAEC,SAAS;OAA3D;AACA,aAAO,MAAMpH,OAAOqD,oBAAoB,UAAU8D,cAAc;QAAEC,SAAS;OAA9D;;KAEd;IAACvE,QAAQ1B;IAASgB;GATrB;AAWA,aACE,cAAAjC,eAAC,2CADH;IAEI,SAAO;IACP,6BAA6B;IAC7B;IACA;IACA,gBAAiBuD,CAAAA,UAAUA,MAAM4D,eAAN;IAC3B,WAAWlF;SAEX,cAAAjC,eAAC,2CARH,SAAA;IASI,cAAY2C,QAAQR;KAChBnB,aACA2D,cAHN;IAIE,KAAKlC;IACL,OAAO;MACL,GAAGkC,aAAayC;MAGd,4CAA4C;MAC5C,2CAA2C;MAC3C,4CAA4C;MAC5C,iCAAiC;MACjC,kCAAkC;;GAbxC,OAiBE,cAAApH,eAAC,2CAAD,MAAYV,QAAZ,OACA,cAAAU,eAAC,4DAlBH;IAkBwC,OAAOC;IAAgB,UAAU;SACrE,cAAAD,eAAC,2CADH;IACgC,IAAI2C,QAAQxB;IAAW,MAAK;KACvD6F,aAAa1H,QADhB,CADF,CAlBF,CARF;CAjCqB;AAsE3B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM+H,mCAAa;AAMnB,IAAMC,gDAAe7H,cAAAA,YACnB,CAACJ,OAAuCoD,iBAAiB;AACvD,QAAM,EAAA,gBAAkB,GAAG8E,WAAH,IAAkBlI;AAC1C,QAAM2B,cAAclC,qCAAemB,cAAD;AAClC,QAAMuH,+BAA+BX,sDACnCQ,kCACApH,cAFkE;AAMpE,SAAOuH,6BAA6BV,WAAW,WAC7C,cAAA9G,eAAC,2CAAD,SAAA,CAAA,GAA2BgB,aAAiBuG,YAD9C;IAC0D,KAAK9E;GAA7D,CAAA;CAXe;AAgBrB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,SAASkD,0CAAoB8B,OAAcC,MAAqB;AAC9D,QAAMC,MAAMC,KAAKC,IAAIH,KAAKC,MAAMF,MAAMjC,CAA1B;AACZ,QAAMsC,SAASF,KAAKC,IAAIH,KAAKI,SAASL,MAAMjC,CAA7B;AACf,QAAMuC,QAAQH,KAAKC,IAAIH,KAAKK,QAAQN,MAAMnC,CAA5B;AACd,QAAM0C,OAAOJ,KAAKC,IAAIH,KAAKM,OAAOP,MAAMnC,CAA3B;AAEb,UAAQsC,KAAKK,IAAIN,KAAKG,QAAQC,OAAOC,IAA7B,GAAR;IACE,KAAKA;AACH,aAAO;IACT,KAAKD;AACH,aAAO;IACT,KAAKJ;AACH,aAAO;IACT,KAAKG;AACH,aAAO;IACT;AACE,YAAM,IAAII,MAAM,aAAV;;;AAIZ,SAASpC,0CAAoBT,WAAkBK,UAAgByC,UAAU,GAAG;AAC1E,QAAMtC,mBAA4B,CAAA;AAClC,UAAQH,UAAR;IACE,KAAK;AACHG,uBAAiBuC,KACf;QAAE9C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;SAC7C;QAAE7C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;OAF/C;AAIA;IACF,KAAK;AACHtC,uBAAiBuC,KACf;QAAE9C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;SAC7C;QAAE7C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;OAF/C;AAIA;IACF,KAAK;AACHtC,uBAAiBuC,KACf;QAAE9C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;SAC7C;QAAE7C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;OAF/C;AAIA;IACF,KAAK;AACHtC,uBAAiBuC,KACf;QAAE9C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;SAC7C;QAAE7C,GAAGD,UAAUC,IAAI6C;QAAS3C,GAAGH,UAAUG,IAAI2C;OAF/C;AAIA;;AAEJ,SAAOtC;;AAGT,SAASG,wCAAkB0B,MAAe;AACxC,QAAM,EAAA,KAAA,OAAA,QAAA,KAAsBM,IAASN;AACrC,SAAO;IACL;MAAEpC,GAAG0C;MAAMxC,GAAGmC;;IACd;MAAErC,GAAGyC;MAAOvC,GAAGmC;;IACf;MAAErC,GAAGyC;MAAOvC,GAAGsC;;IACf;MAAExC,GAAG0C;MAAMxC,GAAGsC;;;;AAMlB,SAASnB,uCAAiBc,OAAcY,SAAkB;AACxD,QAAM,EAAA,GAAA,EAAK7C,IAAMiC;AACjB,MAAIa,SAAS;AACb,WAASC,IAAI,GAAGC,IAAIH,QAAQI,SAAS,GAAGF,IAAIF,QAAQI,QAAQD,IAAID,KAAK;AACnE,UAAMG,KAAKL,QAAQE,CAAD,EAAIjD;AACtB,UAAMqD,KAAKN,QAAQE,CAAD,EAAI/C;AACtB,UAAMoD,KAAKP,QAAQG,CAAD,EAAIlD;AACtB,UAAMuD,KAAKR,QAAQG,CAAD,EAAIhD;AAGtB,UAAMsD,YAAcH,KAAKnD,MAAQqD,KAAKrD,KAAQF,KAAKsD,KAAKF,OAAOlD,IAAImD,OAAOE,KAAKF,MAAMD;AACrF,QAAII;AAAWR,eAAS,CAACA;;AAG3B,SAAOA;;AAKT,SAASpC,8BAAyB6C,QAAsC;AACtE,QAAMC,YAAsBD,OAAOE,MAAP;AAC5BD,YAAUE,KAAK,CAACC,GAAUC,MAAa;AACrC,QAAID,EAAE7D,IAAI8D,EAAE9D;AAAG,aAAO;aACb6D,EAAE7D,IAAI8D,EAAE9D;AAAG,aAAO;aAClB6D,EAAE3D,IAAI4D,EAAE5D;AAAG,aAAO;aAClB2D,EAAE3D,IAAI4D,EAAE5D;AAAG,aAAO;;AACtB,aAAO;GALd;AAOA,SAAO6D,uCAAiBL,SAAD;;AAIzB,SAASK,uCAAkCN,QAAsC;AAC/E,MAAIA,OAAON,UAAU;AAAG,WAAOM,OAAOE,MAAP;AAE/B,QAAMK,YAAsB,CAAA;AAC5B,WAASf,IAAI,GAAGA,IAAIQ,OAAON,QAAQF,KAAK;AACtC,UAAMgB,IAAIR,OAAOR,CAAD;AAChB,WAAOe,UAAUb,UAAU,GAAG;AAC5B,YAAMe,IAAIF,UAAUA,UAAUb,SAAS,CAApB;AACnB,YAAMgB,IAAIH,UAAUA,UAAUb,SAAS,CAApB;AACnB,WAAKe,EAAElE,IAAImE,EAAEnE,MAAMiE,EAAE/D,IAAIiE,EAAEjE,OAAOgE,EAAEhE,IAAIiE,EAAEjE,MAAM+D,EAAEjE,IAAImE,EAAEnE;AAAIgE,kBAAUI,IAAV;;AACvD;;AAEPJ,cAAUlB,KAAKmB,CAAf;;AAEFD,YAAUI,IAAV;AAEA,QAAMC,YAAsB,CAAA;AAC5B,WAASpB,KAAIQ,OAAON,SAAS,GAAGF,MAAK,GAAGA,MAAK;AAC3C,UAAMgB,IAAIR,OAAOR,EAAD;AAChB,WAAOoB,UAAUlB,UAAU,GAAG;AAC5B,YAAMe,IAAIG,UAAUA,UAAUlB,SAAS,CAApB;AACnB,YAAMgB,IAAIE,UAAUA,UAAUlB,SAAS,CAApB;AACnB,WAAKe,EAAElE,IAAImE,EAAEnE,MAAMiE,EAAE/D,IAAIiE,EAAEjE,OAAOgE,EAAEhE,IAAIiE,EAAEjE,MAAM+D,EAAEjE,IAAImE,EAAEnE;AAAIqE,kBAAUD,IAAV;;AACvD;;AAEPC,cAAUvB,KAAKmB,CAAf;;AAEFI,YAAUD,IAAV;AAEA,MACEJ,UAAUb,WAAW,KACrBkB,UAAUlB,WAAW,KACrBa,UAAU,CAAD,EAAIhE,MAAMqE,UAAU,CAAD,EAAIrE,KAChCgE,UAAU,CAAD,EAAI9D,MAAMmE,UAAU,CAAD,EAAInE;AAEhC,WAAO8D;;AAEP,WAAOA,UAAUM,OAAOD,SAAjB;;AAIX,IAAME,4CAAWzK;AACjB,IAAM0K,4CAAOtJ;AACb,IAAMuJ,4CAAUvH;AAChB,IAAMwH,4CAAS1F;AACf,IAAM2F,4CAAUxF;AAChB,IAAMyF,4CAAQ5C;", "names": ["NAME", "VisuallyHidden", "React", "props", "forwardedRef", "$kVwnw$createElement", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "style", "Root", "createTooltipContext", "createTooltipScope", "createContextScope", "createPopperScope", "usePopperScope", "PROVIDER_NAME", "DEFAULT_DELAY_DURATION", "TOOLTIP_OPEN", "TooltipProviderContextProvider", "useTooltipProviderContext", "TooltipProvider", "props", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "isPointerInTransitRef", "skip<PERSON>elayTimerRef", "skip<PERSON><PERSON><PERSON>T<PERSON>r", "current", "window", "clearTimeout", "$8wepK$createElement", "__scopeTooltip", "setTimeout", "skipDelayDuration", "inTransit", "TOOLTIP_NAME", "TooltipContextProvider", "useTooltipContext", "<PERSON><PERSON><PERSON>", "open", "openProp", "disableHover<PERSON><PERSON><PERSON>nt", "disableHoverableContentProp", "delayDuration", "delayDurationProp", "providerContext", "popperScope", "trigger", "setTrigger", "contentId", "useId", "openTimerRef", "wasOpenDelayedRef", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "defaultOpen", "onChange", "onOpen", "document", "dispatchEvent", "CustomEvent", "onClose", "onOpenChange", "stateAttribute", "handleOpen", "handleClose", "handleDelayedOpen", "TRIGGER_NAME", "TooltipTrigger", "forwardedRef", "triggerProps", "context", "ref", "composedRefs", "useComposedRefs", "onTriggerChange", "isPointerDownRef", "hasPointerMoveOpenedRef", "handlePointerUp", "removeEventListener", "undefined", "composeEventHandlers", "onPointerMove", "event", "pointerType", "onTriggerEnter", "onPointerLeave", "onTriggerLeave", "onPointerDown", "addEventListener", "once", "onFocus", "onBlur", "onClick", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "TooltipPortal", "container", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "contentProps", "TooltipContentHoverable", "pointerGraceArea", "setPointerGraceArea", "content", "onPointerInTransitChange", "handleRemoveGraceArea", "handleCreateGraceArea", "hoverTarget", "currentTarget", "exitPoint", "x", "clientX", "y", "clientY", "exitSide", "getExitSideFromRect", "getBoundingClientRect", "paddedExitPoints", "getPaddedExitPoints", "hoverTargetPoints", "getPointsFromRect", "grace<PERSON><PERSON>", "getHull", "handleTriggerLeave", "handleContentLeave", "handleTrackPointerGrace", "target", "pointerPosition", "hasEnteredTarget", "contains", "isPointerOutsideGraceArea", "isPointInPolygon", "VisuallyHiddenContentContextProvider", "useVisuallyHiddenContentContext", "isInside", "TooltipContentImpl", "aria<PERSON><PERSON><PERSON>", "handleScroll", "capture", "preventDefault", "style", "ARROW_NAME", "TooltipArrow", "arrowProps", "visuallyHiddenContentContext", "point", "rect", "top", "Math", "abs", "bottom", "right", "left", "min", "Error", "padding", "push", "polygon", "inside", "i", "j", "length", "xi", "yi", "xj", "yj", "intersect", "points", "newPoints", "slice", "sort", "a", "b", "getHullPresorted", "upperHull", "p", "q", "r", "pop", "lowerHull", "concat", "Provider", "Root", "<PERSON><PERSON>", "Portal", "Content", "Arrow"]}