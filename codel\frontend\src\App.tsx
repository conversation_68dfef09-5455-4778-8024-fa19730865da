import { Navigate, Route, Routes } from "react-router-dom";

import { AppLayout } from "./layouts/AppLayout/AppLayout";
import { ChatPage } from "./pages/ChatPage/ChatPage";
import "./styles/font.css.ts";
import "./styles/global.css.ts";
import "./styles/theme.css.ts";

function App() {
  console.log("App component rendered");
  return (
    <Routes>
      <Route element={<AppLayout />}>
        <Route path="/chat/:id?" element={<ChatPage />} />
      </Route>
      <Route path="*" element={<Navigate to="/chat" />} />
    </Routes>
  );
}

export default App;
