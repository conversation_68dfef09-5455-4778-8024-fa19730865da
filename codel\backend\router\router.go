package router

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/lru"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"

	"github.com/semanser/ai-coder/database"
	"github.com/semanser/ai-coder/graph"
	"github.com/semanser/ai-coder/providers"
	"github.com/semanser/ai-coder/storage"
)

func New(db *database.Queries) *gin.Engine {
	return NewWithDB(db, nil)
}

func NewWithDB(db *database.Queries, sqlDB *sql.DB) *gin.Engine {
	// Initialize Gin router
	r := gin.Default()

	// Configure CORS middleware
	config := cors.DefaultConfig()
	// TODO change to only allow specific origins
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(config))

	r.Use(static.Serve("/", static.LocalFile("./fe", true)))

	// GraphQL endpoint
	r.Any("/graphql", graphqlHandler(db))

	// GraphQL playground route
	r.GET("/playground", playgroundHandler())

	// Provider switching endpoint
	if sqlDB != nil {
		r.POST("/api/switch-provider", gin.WrapF(providers.SwitchProviderHandler(sqlDB)))
	}

	// Static file server
	r.Static("/browser", "./tmp/browser")

	r.NoRoute(func(c *gin.Context) {
		c.Redirect(301, "/")
	})

	return r
}

func NewWithMemoryStorage(memStorage *storage.MemoryStorage) *gin.Engine {
	// Initialize Gin router
	r := gin.Default()

	// Configure CORS middleware
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(config))

	r.Use(static.Serve("/", static.LocalFile("./fe", true)))

	// GraphQL endpoint
	r.Any("/graphql", graphqlHandlerMemory(memStorage))

	// GraphQL playground route
	r.GET("/playground", playgroundHandler())

	// Provider switching endpoint
	r.POST("/api/switch-provider", gin.WrapF(providers.SwitchProviderHandler(nil)))

	// Static file server
	r.Static("/browser", "./tmp/browser")

	r.NoRoute(func(c *gin.Context) {
		c.Redirect(301, "/")
	})

	return r
}

func graphqlHandler(db *database.Queries) gin.HandlerFunc {
	// NewExecutableSchema and Config are in the generated.go file
	// Resolver is in the resolver.go file
	h := handler.New(graph.NewExecutableSchema(graph.Config{Resolvers: &graph.Resolver{
		Db: db,
	}}))

	h.AroundResponses(func(ctx context.Context, next graphql.ResponseHandler) *graphql.Response {
		res := next(ctx)
		if res == nil {
			return res
		}

		err := res.Errors.Error()

		if err != "" {
			log.Printf("graphql error: %s", err)
		}

		return res
	})

	// We can't use the default error handler because it doesn't work with websockets
	// https://stackoverflow.com/a/75444816
	// So we add all the transports manually (see handler.NewDefaultServer in gqlgen for reference)
	h.AddTransport(transport.Options{})
	h.AddTransport(transport.GET{})
	h.AddTransport(transport.POST{})
	h.AddTransport(transport.MultipartForm{})

	h.SetQueryCache(lru.New(1000))

	h.Use(extension.Introspection{})
	h.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New(100),
	})



	return gin.WrapH(h)
}

func graphqlHandlerMemory(memStorage *storage.MemoryStorage) gin.HandlerFunc {
	// For now, use the same GraphQL handler but with memory storage
	// We'll need to create a wrapper that converts database.Queries interface to use memory storage
	h := handler.NewDefaultServer(graph.NewExecutableSchema(graph.Config{Resolvers: &graph.Resolver{
		Db: &MemoryQueryWrapper{Storage: memStorage},
	}}))

	h.AddTransport(transport.Options{})
	h.AddTransport(transport.GET{})
	h.AddTransport(transport.POST{})
	h.AddTransport(transport.MultipartForm{})

	h.SetQueryCache(lru.New(1000))

	h.Use(extension.Introspection{})
	h.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New(100),
	})

	return gin.WrapH(h)
}

// MemoryQueryWrapper wraps memory storage to implement database.Queries interface
type MemoryQueryWrapper struct {
	Storage *storage.MemoryStorage
}

// Implement the database.Queries interface methods
func (m *MemoryQueryWrapper) CreateFlow(ctx context.Context, arg database.CreateFlowParams) (database.Flow, error) {
	return m.Storage.CreateFlow(ctx, arg)
}

func (m *MemoryQueryWrapper) ReadFlow(ctx context.Context, id int64) (database.ReadFlowRow, error) {
	flow, err := m.Storage.GetFlow(ctx, id)
	if err != nil {
		return database.ReadFlowRow{}, err
	}
	return database.ReadFlowRow{
		ID:            flow.ID,
		CreatedAt:     flow.CreatedAt,
		UpdatedAt:     flow.UpdatedAt,
		Name:          flow.Name,
		Status:        flow.Status,
		ContainerID:   flow.ContainerID,
		Model:         flow.Model,
		ModelProvider: flow.ModelProvider,
	}, nil
}

func (m *MemoryQueryWrapper) ReadAllFlows(ctx context.Context) ([]database.ReadAllFlowsRow, error) {
	flows, err := m.Storage.GetFlows(ctx)
	if err != nil {
		return nil, err
	}
	var result []database.ReadAllFlowsRow
	for _, flow := range flows {
		result = append(result, database.ReadAllFlowsRow{
			ID:            flow.ID,
			CreatedAt:     flow.CreatedAt,
			UpdatedAt:     flow.UpdatedAt,
			Name:          flow.Name,
			Status:        flow.Status,
			ContainerID:   flow.ContainerID,
			Model:         flow.Model,
			ModelProvider: flow.ModelProvider,
		})
	}
	return result, nil
}

func (m *MemoryQueryWrapper) UpdateFlowStatus(ctx context.Context, arg database.UpdateFlowStatusParams) (database.Flow, error) {
	err := m.Storage.UpdateFlowStatus(ctx, arg)
	if err != nil {
		return database.Flow{}, err
	}
	return m.Storage.GetFlow(ctx, arg.ID)
}

func (m *MemoryQueryWrapper) CreateTask(ctx context.Context, arg database.CreateTaskParams) (database.Task, error) {
	return m.Storage.CreateTask(ctx, arg)
}

func (m *MemoryQueryWrapper) ReadTasksByFlowId(ctx context.Context, flowID sql.NullInt64) ([]database.Task, error) {
	if !flowID.Valid {
		return []database.Task{}, nil
	}
	return m.Storage.GetTasksByFlowID(ctx, flowID.Int64)
}

func (m *MemoryQueryWrapper) UpdateTaskStatus(ctx context.Context, arg database.UpdateTaskStatusParams) (database.Task, error) {
	return m.Storage.UpdateTaskStatus(ctx, arg)
}

func (m *MemoryQueryWrapper) GetLogsByFlowId(ctx context.Context, flowID sql.NullInt64) ([]database.Log, error) {
	// Return empty logs for now
	return []database.Log{}, nil
}

func playgroundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		playground.Handler("GraphQL", "/graphql").ServeHTTP(c.Writer, c.Request)
	}
}

func wsHandler(db *database.Queries) gin.HandlerFunc {
	return func(c *gin.Context) {
		idParam := c.Param("id")

		// convert id to uint
		id, err := strconv.ParseUint(idParam, 10, 64)

		if err != nil {
			c.AbortWithError(400, err)
		}

		flow, err := db.ReadFlow(c, int64(id))

		if err != nil {
			c.AbortWithError(404, err)
			return
		}

		if flow.Status.String != "in_progress" {
			c.AbortWithError(404, fmt.Errorf("flow is not in progress"))
			return
		}

		if flow.ContainerStatus.String != "running" {
			c.AbortWithError(404, fmt.Errorf("container is not running"))
			return
		}

		websocket.HandleWebsocket(c)
	}
}
