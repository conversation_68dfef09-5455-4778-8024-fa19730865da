import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  JSON: { input: any; output: any; }
  Time: { input: any; output: any; }
  Uint: { input: any; output: any; }
};

export type Browser = {
  __typename?: 'Browser';
  screenshotUrl: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type Flow = {
  __typename?: 'Flow';
  browser: Browser;
  id: Scalars['Uint']['output'];
  model: Model;
  name: Scalars['String']['output'];
  status: FlowStatus;
  tasks: Array<Task>;
  terminal: Terminal;
};

export enum FlowStatus {
  Finished = 'finished',
  InProgress = 'inProgress'
}

export type Log = {
  __typename?: 'Log';
  id: Scalars['Uint']['output'];
  text: Scalars['String']['output'];
};

export type Model = {
  __typename?: 'Model';
  id: Scalars['String']['output'];
  provider: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  _exec: Scalars['String']['output'];
  createFlow: Flow;
  createTask: Task;
  finishFlow: Flow;
};


export type Mutation_ExecArgs = {
  command: Scalars['String']['input'];
  containerId: Scalars['String']['input'];
};


export type MutationCreateFlowArgs = {
  modelId: Scalars['String']['input'];
  modelProvider: Scalars['String']['input'];
};


export type MutationCreateTaskArgs = {
  flowId: Scalars['Uint']['input'];
  query: Scalars['String']['input'];
};


export type MutationFinishFlowArgs = {
  flowId: Scalars['Uint']['input'];
};

export type Query = {
  __typename?: 'Query';
  availableModels: Array<Model>;
  flow: Flow;
  flows: Array<Flow>;
};


export type QueryFlowArgs = {
  id: Scalars['Uint']['input'];
};

export type Subscription = {
  __typename?: 'Subscription';
  browserUpdated: Browser;
  flowUpdated: Flow;
  taskAdded: Task;
  taskUpdated: Task;
  terminalLogsAdded: Log;
};


export type SubscriptionBrowserUpdatedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionFlowUpdatedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionTaskAddedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionTerminalLogsAddedArgs = {
  flowId: Scalars['Uint']['input'];
};

export type Task = {
  __typename?: 'Task';
  args: Scalars['JSON']['output'];
  createdAt: Scalars['Time']['output'];
  id: Scalars['Uint']['output'];
  message: Scalars['String']['output'];
  results: Scalars['JSON']['output'];
  status: TaskStatus;
  type: TaskType;
};

export enum TaskStatus {
  Failed = 'failed',
  Finished = 'finished',
  InProgress = 'inProgress',
  Stopped = 'stopped'
}

export enum TaskType {
  Ask = 'ask',
  Browser = 'browser',
  Code = 'code',
  Done = 'done',
  Input = 'input',
  Terminal = 'terminal'
}

export type Terminal = {
  __typename?: 'Terminal';
  connected: Scalars['Boolean']['output'];
  containerName: Scalars['String']['output'];
  logs: Array<Log>;
};



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;



/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  Browser: ResolverTypeWrapper<Browser>;
  Flow: ResolverTypeWrapper<Flow>;
  FlowStatus: FlowStatus;
  JSON: ResolverTypeWrapper<Scalars['JSON']['output']>;
  Log: ResolverTypeWrapper<Log>;
  Model: ResolverTypeWrapper<Model>;
  Mutation: ResolverTypeWrapper<{}>;
  Query: ResolverTypeWrapper<{}>;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  Subscription: ResolverTypeWrapper<{}>;
  Task: ResolverTypeWrapper<Task>;
  TaskStatus: TaskStatus;
  TaskType: TaskType;
  Terminal: ResolverTypeWrapper<Terminal>;
  Time: ResolverTypeWrapper<Scalars['Time']['output']>;
  Uint: ResolverTypeWrapper<Scalars['Uint']['output']>;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  Boolean: Scalars['Boolean']['output'];
  Browser: Browser;
  Flow: Flow;
  JSON: Scalars['JSON']['output'];
  Log: Log;
  Model: Model;
  Mutation: {};
  Query: {};
  String: Scalars['String']['output'];
  Subscription: {};
  Task: Task;
  Terminal: Terminal;
  Time: Scalars['Time']['output'];
  Uint: Scalars['Uint']['output'];
};

export type BrowserResolvers<ContextType = any, ParentType extends ResolversParentTypes['Browser'] = ResolversParentTypes['Browser']> = {
  screenshotUrl?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type FlowResolvers<ContextType = any, ParentType extends ResolversParentTypes['Flow'] = ResolversParentTypes['Flow']> = {
  browser?: Resolver<ResolversTypes['Browser'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Uint'], ParentType, ContextType>;
  model?: Resolver<ResolversTypes['Model'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  status?: Resolver<ResolversTypes['FlowStatus'], ParentType, ContextType>;
  tasks?: Resolver<Array<ResolversTypes['Task']>, ParentType, ContextType>;
  terminal?: Resolver<ResolversTypes['Terminal'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface JsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['JSON'], any> {
  name: 'JSON';
}

export type LogResolvers<ContextType = any, ParentType extends ResolversParentTypes['Log'] = ResolversParentTypes['Log']> = {
  id?: Resolver<ResolversTypes['Uint'], ParentType, ContextType>;
  text?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ModelResolvers<ContextType = any, ParentType extends ResolversParentTypes['Model'] = ResolversParentTypes['Model']> = {
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  provider?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MutationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = {
  _exec?: Resolver<ResolversTypes['String'], ParentType, ContextType, RequireFields<Mutation_ExecArgs, 'command' | 'containerId'>>;
  createFlow?: Resolver<ResolversTypes['Flow'], ParentType, ContextType, RequireFields<MutationCreateFlowArgs, 'modelId' | 'modelProvider'>>;
  createTask?: Resolver<ResolversTypes['Task'], ParentType, ContextType, RequireFields<MutationCreateTaskArgs, 'flowId' | 'query'>>;
  finishFlow?: Resolver<ResolversTypes['Flow'], ParentType, ContextType, RequireFields<MutationFinishFlowArgs, 'flowId'>>;
};

export type QueryResolvers<ContextType = any, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = {
  availableModels?: Resolver<Array<ResolversTypes['Model']>, ParentType, ContextType>;
  flow?: Resolver<ResolversTypes['Flow'], ParentType, ContextType, RequireFields<QueryFlowArgs, 'id'>>;
  flows?: Resolver<Array<ResolversTypes['Flow']>, ParentType, ContextType>;
};

export type SubscriptionResolvers<ContextType = any, ParentType extends ResolversParentTypes['Subscription'] = ResolversParentTypes['Subscription']> = {
  browserUpdated?: SubscriptionResolver<ResolversTypes['Browser'], "browserUpdated", ParentType, ContextType, RequireFields<SubscriptionBrowserUpdatedArgs, 'flowId'>>;
  flowUpdated?: SubscriptionResolver<ResolversTypes['Flow'], "flowUpdated", ParentType, ContextType, RequireFields<SubscriptionFlowUpdatedArgs, 'flowId'>>;
  taskAdded?: SubscriptionResolver<ResolversTypes['Task'], "taskAdded", ParentType, ContextType, RequireFields<SubscriptionTaskAddedArgs, 'flowId'>>;
  taskUpdated?: SubscriptionResolver<ResolversTypes['Task'], "taskUpdated", ParentType, ContextType>;
  terminalLogsAdded?: SubscriptionResolver<ResolversTypes['Log'], "terminalLogsAdded", ParentType, ContextType, RequireFields<SubscriptionTerminalLogsAddedArgs, 'flowId'>>;
};

export type TaskResolvers<ContextType = any, ParentType extends ResolversParentTypes['Task'] = ResolversParentTypes['Task']> = {
  args?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['Time'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Uint'], ParentType, ContextType>;
  message?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  results?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  status?: Resolver<ResolversTypes['TaskStatus'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['TaskType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TerminalResolvers<ContextType = any, ParentType extends ResolversParentTypes['Terminal'] = ResolversParentTypes['Terminal']> = {
  connected?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  containerName?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  logs?: Resolver<Array<ResolversTypes['Log']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface TimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Time'], any> {
  name: 'Time';
}

export interface UintScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Uint'], any> {
  name: 'Uint';
}

export type Resolvers<ContextType = any> = {
  Browser?: BrowserResolvers<ContextType>;
  Flow?: FlowResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  Log?: LogResolvers<ContextType>;
  Model?: ModelResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  Subscription?: SubscriptionResolvers<ContextType>;
  Task?: TaskResolvers<ContextType>;
  Terminal?: TerminalResolvers<ContextType>;
  Time?: GraphQLScalarType;
  Uint?: GraphQLScalarType;
};

