{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format:validate": "prettier --check .", "format:fix": "prettier --write .", "graphql:generate": "graphql-codegen config --config codegen.yml"}, "dependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript-resolvers": "^4.5.1", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@uidotdev/usehooks": "^2.4.1", "@urql/devtools": "^2.0.3", "@urql/exchange-graphcache": "^6.5.0", "@vanilla-extract/css": "^1.14.1", "@vanilla-extract/vite-plugin": "^4.0.6", "date-fns": "^3.6.0", "fontfaceobserver": "^2.3.0", "graphql": "^16.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "urql": "^4.0.6", "xterm": "5.4.0-beta.37", "xterm-addon-attach": "^0.9.0", "xterm-addon-canvas": "^0.5.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-unicode11": "^0.6.0", "xterm-addon-web-links": "^0.9.0", "xterm-addon-webgl": "^0.16.0", "xterm-theme": "^1.1.0"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "graphql-codegen": "", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^5.1.6"}}