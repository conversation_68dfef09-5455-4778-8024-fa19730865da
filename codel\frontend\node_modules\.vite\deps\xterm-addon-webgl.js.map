{"version": 3, "sources": ["../../xterm-addon-webgl/lib/webpack:/WebglAddon/webpack/universalModuleDefinition", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/GlyphRenderer.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/RectangleRenderer.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/RenderModel.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/WebglRenderer.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/WebglUtils.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/renderLayer/BaseRenderLayer.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/renderLayer/LinkRenderLayer.ts", "../../xterm-addon-webgl/lib/src/browser/Lifecycle.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/CellColorResolver.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/CharAtlasCache.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/CharAtlasUtils.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/Constants.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/CursorBlinkStateManager.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/CustomGlyphs.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/DevicePixelObserver.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/RendererUtils.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/SelectionRenderModel.ts", "../../xterm-addon-webgl/lib/src/browser/renderer/shared/TextureAtlas.ts", "../../xterm-addon-webgl/lib/src/common/Color.ts", "../../xterm-addon-webgl/lib/src/common/EventEmitter.ts", "../../xterm-addon-webgl/lib/src/common/Lifecycle.ts", "../../xterm-addon-webgl/lib/src/common/MultiKeyMap.ts", "../../xterm-addon-webgl/lib/src/common/Platform.ts", "../../xterm-addon-webgl/lib/src/common/TaskQueue.ts", "../../xterm-addon-webgl/lib/src/common/buffer/AttributeData.ts", "../../xterm-addon-webgl/lib/src/common/buffer/CellData.ts", "../../xterm-addon-webgl/lib/src/common/buffer/Constants.ts", "../../xterm-addon-webgl/lib/src/common/input/TextDecoder.ts", "../../xterm-addon-webgl/lib/src/common/services/LogService.ts", "../../xterm-addon-webgl/lib/src/common/services/ServiceRegistry.ts", "../../xterm-addon-webgl/lib/src/common/services/Services.ts", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/webpack/bootstrap", "../../xterm-addon-webgl/lib/webpack:/WebglAddon/src/WebglAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"WebglAddon\"] = factory();\n\telse\n\t\troot[\"WebglAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { TextureAtlas } from 'browser/renderer/shared/TextureAtlas';\nimport { IRasterizedGlyph, IRenderDimensions, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { NULL_CELL_CODE } from 'common/buffer/Constants';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { traceCall } from 'common/services/LogService';\nimport { Terminal } from 'xterm';\nimport { IRenderModel, IWebGL2RenderingContext, IWebGLVertexArrayObject } from './Types';\nimport { createProgram, GLTexture, PROJECTION_MATRIX } from './WebglUtils';\n\ninterface IVertices {\n  attributes: Float32Array;\n  /**\n   * These buffers are the ones used to bind to WebGL, the reason there are\n   * multiple is to allow double buffering to work as you cannot modify the\n   * buffer while it's being used by the GPU. Having multiple lets us start\n   * working on the next frame.\n   */\n  attributesBuffers: Float32Array[];\n  count: number;\n}\n\nconst enum VertexAttribLocations {\n  UNIT_QUAD = 0,\n  CELL_POSITION = 1,\n  OFFSET = 2,\n  SIZE = 3,\n  TEXPAGE = 4,\n  TEXCOORD = 5,\n  TEXSIZE = 6\n}\n\nconst vertexShaderSource = `#version 300 es\nlayout (location = ${VertexAttribLocations.UNIT_QUAD}) in vec2 a_unitquad;\nlayout (location = ${VertexAttribLocations.CELL_POSITION}) in vec2 a_cellpos;\nlayout (location = ${VertexAttribLocations.OFFSET}) in vec2 a_offset;\nlayout (location = ${VertexAttribLocations.SIZE}) in vec2 a_size;\nlayout (location = ${VertexAttribLocations.TEXPAGE}) in float a_texpage;\nlayout (location = ${VertexAttribLocations.TEXCOORD}) in vec2 a_texcoord;\nlayout (location = ${VertexAttribLocations.TEXSIZE}) in vec2 a_texsize;\n\nuniform mat4 u_projection;\nuniform vec2 u_resolution;\n\nout vec2 v_texcoord;\nflat out int v_texpage;\n\nvoid main() {\n  vec2 zeroToOne = (a_offset / u_resolution) + a_cellpos + (a_unitquad * a_size);\n  gl_Position = u_projection * vec4(zeroToOne, 0.0, 1.0);\n  v_texpage = int(a_texpage);\n  v_texcoord = a_texcoord + a_unitquad * a_texsize;\n}`;\n\nfunction createFragmentShaderSource(maxFragmentShaderTextureUnits: number): string {\n  let textureConditionals = '';\n  for (let i = 1; i < maxFragmentShaderTextureUnits; i++) {\n    textureConditionals += ` else if (v_texpage == ${i}) { outColor = texture(u_texture[${i}], v_texcoord); }`;\n  }\n  return (`#version 300 es\nprecision lowp float;\n\nin vec2 v_texcoord;\nflat in int v_texpage;\n\nuniform sampler2D u_texture[${maxFragmentShaderTextureUnits}];\n\nout vec4 outColor;\n\nvoid main() {\n  if (v_texpage == 0) {\n    outColor = texture(u_texture[0], v_texcoord);\n  } ${textureConditionals}\n}`);\n}\n\nconst INDICES_PER_CELL = 11;\nconst BYTES_PER_CELL = INDICES_PER_CELL * Float32Array.BYTES_PER_ELEMENT;\nconst CELL_POSITION_INDICES = 2;\n\n// Work variables to avoid garbage collection\nlet $i = 0;\nlet $glyph: IRasterizedGlyph | undefined = undefined;\nlet $leftCellPadding = 0;\nlet $clippedPixels = 0;\n\nexport class GlyphRenderer extends Disposable {\n  private readonly _program: WebGLProgram;\n  private readonly _vertexArrayObject: IWebGLVertexArrayObject;\n  private readonly _projectionLocation: WebGLUniformLocation;\n  private readonly _resolutionLocation: WebGLUniformLocation;\n  private readonly _textureLocation: WebGLUniformLocation;\n  private readonly _atlasTextures: GLTexture[];\n  private readonly _attributesBuffer: WebGLBuffer;\n\n  private _atlas: ITextureAtlas | undefined;\n  private _activeBuffer: number = 0;\n  private readonly _vertices: IVertices = {\n    count: 0,\n    attributes: new Float32Array(0),\n    attributesBuffers: [\n      new Float32Array(0),\n      new Float32Array(0)\n    ]\n  };\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _gl: IWebGL2RenderingContext,\n    private _dimensions: IRenderDimensions\n  ) {\n    super();\n\n    const gl = this._gl;\n\n    if (TextureAtlas.maxAtlasPages === undefined) {\n      // Typically 8 or 16\n      TextureAtlas.maxAtlasPages = Math.min(32, throwIfFalsy(gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS) as number | null));\n      // Almost all clients will support >= 4096\n      TextureAtlas.maxTextureSize = throwIfFalsy(gl.getParameter(gl.MAX_TEXTURE_SIZE) as number | null);\n    }\n\n    this._program = throwIfFalsy(createProgram(gl, vertexShaderSource, createFragmentShaderSource(TextureAtlas.maxAtlasPages)));\n    this.register(toDisposable(() => gl.deleteProgram(this._program)));\n\n    // Uniform locations\n    this._projectionLocation = throwIfFalsy(gl.getUniformLocation(this._program, 'u_projection'));\n    this._resolutionLocation = throwIfFalsy(gl.getUniformLocation(this._program, 'u_resolution'));\n    this._textureLocation = throwIfFalsy(gl.getUniformLocation(this._program, 'u_texture'));\n\n    // Create and set the vertex array object\n    this._vertexArrayObject = gl.createVertexArray();\n    gl.bindVertexArray(this._vertexArrayObject);\n\n    // Setup a_unitquad, this defines the 4 vertices of a rectangle\n    const unitQuadVertices = new Float32Array([0, 0, 1, 0, 0, 1, 1, 1]);\n    const unitQuadVerticesBuffer = gl.createBuffer();\n    this.register(toDisposable(() => gl.deleteBuffer(unitQuadVerticesBuffer)));\n    gl.bindBuffer(gl.ARRAY_BUFFER, unitQuadVerticesBuffer);\n    gl.bufferData(gl.ARRAY_BUFFER, unitQuadVertices, gl.STATIC_DRAW);\n    gl.enableVertexAttribArray(VertexAttribLocations.UNIT_QUAD);\n    gl.vertexAttribPointer(VertexAttribLocations.UNIT_QUAD, 2, this._gl.FLOAT, false, 0, 0);\n\n    // Setup the unit quad element array buffer, this points to indices in\n    // unitQuadVertices to allow is to draw 2 triangles from the vertices via a\n    // triangle strip\n    const unitQuadElementIndices = new Uint8Array([0, 1, 2, 3]);\n    const elementIndicesBuffer = gl.createBuffer();\n    this.register(toDisposable(() => gl.deleteBuffer(elementIndicesBuffer)));\n    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, elementIndicesBuffer);\n    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, unitQuadElementIndices, gl.STATIC_DRAW);\n\n    // Setup attributes\n    this._attributesBuffer = throwIfFalsy(gl.createBuffer());\n    this.register(toDisposable(() => gl.deleteBuffer(this._attributesBuffer)));\n    gl.bindBuffer(gl.ARRAY_BUFFER, this._attributesBuffer);\n    gl.enableVertexAttribArray(VertexAttribLocations.OFFSET);\n    gl.vertexAttribPointer(VertexAttribLocations.OFFSET, 2, gl.FLOAT, false, BYTES_PER_CELL, 0);\n    gl.vertexAttribDivisor(VertexAttribLocations.OFFSET, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.SIZE);\n    gl.vertexAttribPointer(VertexAttribLocations.SIZE, 2, gl.FLOAT, false, BYTES_PER_CELL, 2 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.SIZE, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.TEXPAGE);\n    gl.vertexAttribPointer(VertexAttribLocations.TEXPAGE, 1, gl.FLOAT, false, BYTES_PER_CELL, 4 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.TEXPAGE, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.TEXCOORD);\n    gl.vertexAttribPointer(VertexAttribLocations.TEXCOORD, 2, gl.FLOAT, false, BYTES_PER_CELL, 5 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.TEXCOORD, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.TEXSIZE);\n    gl.vertexAttribPointer(VertexAttribLocations.TEXSIZE, 2, gl.FLOAT, false, BYTES_PER_CELL, 7 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.TEXSIZE, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.CELL_POSITION);\n    gl.vertexAttribPointer(VertexAttribLocations.CELL_POSITION, 2, gl.FLOAT, false, BYTES_PER_CELL, 9 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.CELL_POSITION, 1);\n\n    // Setup static uniforms\n    gl.useProgram(this._program);\n    const textureUnits = new Int32Array(TextureAtlas.maxAtlasPages);\n    for (let i = 0; i < TextureAtlas.maxAtlasPages; i++) {\n      textureUnits[i] = i;\n    }\n    gl.uniform1iv(this._textureLocation, textureUnits);\n    gl.uniformMatrix4fv(this._projectionLocation, false, PROJECTION_MATRIX);\n\n    // Setup 1x1 red pixel textures for all potential atlas pages, if one of these invalid textures\n    // is ever drawn it will show characters as red rectangles.\n    this._atlasTextures = [];\n    for (let i = 0; i < TextureAtlas.maxAtlasPages; i++) {\n      const glTexture = new GLTexture(throwIfFalsy(gl.createTexture()));\n      this.register(toDisposable(() => gl.deleteTexture(glTexture.texture)));\n      gl.activeTexture(gl.TEXTURE0 + i);\n      gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE, new Uint8Array([255, 0, 0, 255]));\n      this._atlasTextures[i] = glTexture;\n    }\n\n    // Allow drawing of transparent texture\n    gl.enable(gl.BLEND);\n    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n\n    // Set viewport\n    this.handleResize();\n  }\n\n  public beginFrame(): boolean {\n    return this._atlas ? this._atlas.beginFrame() : true;\n  }\n\n  @traceCall\n  public updateCell(x: number, y: number, code: number, bg: number, fg: number, ext: number, chars: string, lastBg: number): void {\n    // Since this function is called for every cell (`rows*cols`), it must be very optimized. It\n    // should not instantiate any variables unless a new glyph is drawn to the cache where the\n    // slight slowdown is acceptable for the developer ergonomics provided as it's a once of for\n    // each glyph.\n    this._updateCell(this._vertices.attributes, x, y, code, bg, fg, ext, chars, lastBg);\n  }\n\n  private _updateCell(array: Float32Array, x: number, y: number, code: number | undefined, bg: number, fg: number, ext: number, chars: string, lastBg: number): void {\n    $i = (y * this._terminal.cols + x) * INDICES_PER_CELL;\n\n    // Exit early if this is a null character, allow space character to continue as it may have\n    // underline/strikethrough styles\n    if (code === NULL_CELL_CODE || code === undefined/* This is used for the right side of wide chars */) {\n      array.fill(0, $i, $i + INDICES_PER_CELL - 1 - CELL_POSITION_INDICES);\n      return;\n    }\n\n    if (!this._atlas) {\n      return;\n    }\n\n    // Get the glyph\n    if (chars && chars.length > 1) {\n      $glyph = this._atlas.getRasterizedGlyphCombinedChar(chars, bg, fg, ext, false);\n    } else {\n      $glyph = this._atlas.getRasterizedGlyph(code, bg, fg, ext, false);\n    }\n\n    $leftCellPadding = Math.floor((this._dimensions.device.cell.width - this._dimensions.device.char.width) / 2);\n    if (bg !== lastBg && $glyph.offset.x > $leftCellPadding) {\n      $clippedPixels = $glyph.offset.x - $leftCellPadding;\n      // a_origin\n      array[$i    ] = -($glyph.offset.x - $clippedPixels) + this._dimensions.device.char.left;\n      array[$i + 1] = -$glyph.offset.y + this._dimensions.device.char.top;\n      // a_size\n      array[$i + 2] = ($glyph.size.x - $clippedPixels) / this._dimensions.device.canvas.width;\n      array[$i + 3] = $glyph.size.y / this._dimensions.device.canvas.height;\n      // a_texpage\n      array[$i + 4] = $glyph.texturePage;\n      // a_texcoord\n      array[$i + 5] = $glyph.texturePositionClipSpace.x + $clippedPixels / this._atlas.pages[$glyph.texturePage].canvas.width;\n      array[$i + 6] = $glyph.texturePositionClipSpace.y;\n      // a_texsize\n      array[$i + 7] = $glyph.sizeClipSpace.x - $clippedPixels / this._atlas.pages[$glyph.texturePage].canvas.width;\n      array[$i + 8] = $glyph.sizeClipSpace.y;\n    } else {\n      // a_origin\n      array[$i    ] = -$glyph.offset.x + this._dimensions.device.char.left;\n      array[$i + 1] = -$glyph.offset.y + this._dimensions.device.char.top;\n      // a_size\n      array[$i + 2] = $glyph.size.x / this._dimensions.device.canvas.width;\n      array[$i + 3] = $glyph.size.y / this._dimensions.device.canvas.height;\n      // a_texpage\n      array[$i + 4] = $glyph.texturePage;\n      // a_texcoord\n      array[$i + 5] = $glyph.texturePositionClipSpace.x;\n      array[$i + 6] = $glyph.texturePositionClipSpace.y;\n      // a_texsize\n      array[$i + 7] = $glyph.sizeClipSpace.x;\n      array[$i + 8] = $glyph.sizeClipSpace.y;\n    }\n    // a_cellpos only changes on resize\n  }\n\n  public clear(): void {\n    const terminal = this._terminal;\n    const newCount = terminal.cols * terminal.rows * INDICES_PER_CELL;\n\n    // Clear vertices\n    if (this._vertices.count !== newCount) {\n      this._vertices.attributes = new Float32Array(newCount);\n    } else {\n      this._vertices.attributes.fill(0);\n    }\n    let i = 0;\n    for (; i < this._vertices.attributesBuffers.length; i++) {\n      if (this._vertices.count !== newCount) {\n        this._vertices.attributesBuffers[i] = new Float32Array(newCount);\n      } else {\n        this._vertices.attributesBuffers[i].fill(0);\n      }\n    }\n    this._vertices.count = newCount;\n    i = 0;\n    for (let y = 0; y < terminal.rows; y++) {\n      for (let x = 0; x < terminal.cols; x++) {\n        this._vertices.attributes[i + 9] = x / terminal.cols;\n        this._vertices.attributes[i + 10] = y / terminal.rows;\n        i += INDICES_PER_CELL;\n      }\n    }\n  }\n\n  public handleResize(): void {\n    const gl = this._gl;\n    gl.useProgram(this._program);\n    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);\n    gl.uniform2f(this._resolutionLocation, gl.canvas.width, gl.canvas.height);\n    this.clear();\n  }\n\n  public render(renderModel: IRenderModel): void {\n    if (!this._atlas) {\n      return;\n    }\n\n    const gl = this._gl;\n\n    gl.useProgram(this._program);\n    gl.bindVertexArray(this._vertexArrayObject);\n\n    // Alternate buffers each frame as the active buffer gets locked while it's in use by the GPU\n    this._activeBuffer = (this._activeBuffer + 1) % 2;\n    const activeBuffer = this._vertices.attributesBuffers[this._activeBuffer];\n\n    // Copy data for each cell of each line up to its line length (the last non-whitespace cell)\n    // from the attributes buffer into activeBuffer, which is the one that gets bound to the GPU.\n    // The reasons for this are as follows:\n    // - So the active buffer can be alternated so we don't get blocked on rendering finishing\n    // - To copy either the normal attributes buffer or the selection attributes buffer when there\n    //   is a selection\n    // - So we don't send vertices for all the line-ending whitespace to the GPU\n    let bufferLength = 0;\n    for (let y = 0; y < renderModel.lineLengths.length; y++) {\n      const si = y * this._terminal.cols * INDICES_PER_CELL;\n      const sub = this._vertices.attributes.subarray(si, si + renderModel.lineLengths[y] * INDICES_PER_CELL);\n      activeBuffer.set(sub, bufferLength);\n      bufferLength += sub.length;\n    }\n\n    // Bind the attributes buffer\n    gl.bindBuffer(gl.ARRAY_BUFFER, this._attributesBuffer);\n    gl.bufferData(gl.ARRAY_BUFFER, activeBuffer.subarray(0, bufferLength), gl.STREAM_DRAW);\n\n    // Bind the atlas page texture if they have changed\n    for (let i = 0; i < this._atlas.pages.length; i++) {\n      if (this._atlas.pages[i].version !== this._atlasTextures[i].version) {\n        this._bindAtlasPageTexture(gl, this._atlas, i);\n      }\n    }\n\n    // Draw the viewport\n    gl.drawElementsInstanced(gl.TRIANGLE_STRIP, 4, gl.UNSIGNED_BYTE, 0, bufferLength / INDICES_PER_CELL);\n  }\n\n  public setAtlas(atlas: ITextureAtlas): void {\n    this._atlas = atlas;\n    for (const glTexture of this._atlasTextures) {\n      glTexture.version = -1;\n    }\n  }\n\n  private _bindAtlasPageTexture(gl: IWebGL2RenderingContext, atlas: ITextureAtlas, i: number): void {\n    gl.activeTexture(gl.TEXTURE0 + i);\n    gl.bindTexture(gl.TEXTURE_2D, this._atlasTextures[i].texture);\n    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, atlas.pages[i].canvas);\n    gl.generateMipmap(gl.TEXTURE_2D);\n    this._atlasTextures[i].version = atlas.pages[i].version;\n  }\n\n  public setDimensions(dimensions: IRenderDimensions): void {\n    this._dimensions = dimensions;\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\nimport { IThemeService } from 'browser/services/Services';\nimport { ReadonlyColorSet } from 'browser/Types';\nimport { Attributes, FgFlags } from 'common/buffer/Constants';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { IColor } from 'common/Types';\nimport { Terminal } from 'xterm';\nimport { RENDER_MODEL_BG_OFFSET, RENDER_MODEL_FG_OFFSET, RENDER_MODEL_INDICIES_PER_CELL } from './RenderModel';\nimport { IRenderModel, IWebGL2RenderingContext, IWebGLVertexArrayObject } from './Types';\nimport { createProgram, expandFloat32Array, PROJECTION_MATRIX } from './WebglUtils';\n\nconst enum VertexAttribLocations {\n  POSITION = 0,\n  SIZE = 1,\n  COLOR = 2,\n  UNIT_QUAD = 3\n}\n\nconst vertexShaderSource = `#version 300 es\nlayout (location = ${VertexAttribLocations.POSITION}) in vec2 a_position;\nlayout (location = ${VertexAttribLocations.SIZE}) in vec2 a_size;\nlayout (location = ${VertexAttribLocations.COLOR}) in vec4 a_color;\nlayout (location = ${VertexAttribLocations.UNIT_QUAD}) in vec2 a_unitquad;\n\nuniform mat4 u_projection;\n\nout vec4 v_color;\n\nvoid main() {\n  vec2 zeroToOne = a_position + (a_unitquad * a_size);\n  gl_Position = u_projection * vec4(zeroToOne, 0.0, 1.0);\n  v_color = a_color;\n}`;\n\nconst fragmentShaderSource = `#version 300 es\nprecision lowp float;\n\nin vec4 v_color;\n\nout vec4 outColor;\n\nvoid main() {\n  outColor = v_color;\n}`;\n\nconst INDICES_PER_RECTANGLE = 8;\nconst BYTES_PER_RECTANGLE = INDICES_PER_RECTANGLE * Float32Array.BYTES_PER_ELEMENT;\n\nconst INITIAL_BUFFER_RECTANGLE_CAPACITY = 20 * INDICES_PER_RECTANGLE;\n\nclass Vertices {\n  public attributes: Float32Array;\n  public count: number;\n\n  constructor() {\n    this.attributes = new Float32Array(INITIAL_BUFFER_RECTANGLE_CAPACITY);\n    this.count = 0;\n  }\n}\n\n// Work variables to avoid garbage collection\nlet $rgba = 0;\nlet $x1 = 0;\nlet $y1 = 0;\nlet $r = 0;\nlet $g = 0;\nlet $b = 0;\nlet $a = 0;\n\nexport class RectangleRenderer extends Disposable {\n\n  private _program: WebGLProgram;\n  private _vertexArrayObject: IWebGLVertexArrayObject;\n  private _attributesBuffer: WebGLBuffer;\n  private _projectionLocation: WebGLUniformLocation;\n  private _bgFloat!: Float32Array;\n  private _cursorFloat!: Float32Array;\n\n  private _vertices: Vertices = new Vertices();\n  private _verticesCursor: Vertices = new Vertices();\n\n  constructor(\n    private _terminal: Terminal,\n    private _gl: IWebGL2RenderingContext,\n    private _dimensions: IRenderDimensions,\n    private readonly _themeService: IThemeService\n  ) {\n    super();\n\n    const gl = this._gl;\n\n    this._program = throwIfFalsy(createProgram(gl, vertexShaderSource, fragmentShaderSource));\n    this.register(toDisposable(() => gl.deleteProgram(this._program)));\n\n    // Uniform locations\n    this._projectionLocation = throwIfFalsy(gl.getUniformLocation(this._program, 'u_projection'));\n\n    // Create and set the vertex array object\n    this._vertexArrayObject = gl.createVertexArray();\n    gl.bindVertexArray(this._vertexArrayObject);\n\n    // Setup a_unitquad, this defines the 4 vertices of a rectangle\n    const unitQuadVertices = new Float32Array([0, 0, 1, 0, 0, 1, 1, 1]);\n    const unitQuadVerticesBuffer = gl.createBuffer();\n    this.register(toDisposable(() => gl.deleteBuffer(unitQuadVerticesBuffer)));\n    gl.bindBuffer(gl.ARRAY_BUFFER, unitQuadVerticesBuffer);\n    gl.bufferData(gl.ARRAY_BUFFER, unitQuadVertices, gl.STATIC_DRAW);\n    gl.enableVertexAttribArray(VertexAttribLocations.UNIT_QUAD);\n    gl.vertexAttribPointer(VertexAttribLocations.UNIT_QUAD, 2, this._gl.FLOAT, false, 0, 0);\n\n    // Setup the unit quad element array buffer, this points to indices in\n    // unitQuadVertices to allow is to draw 2 triangles from the vertices via a\n    // triangle strip\n    const unitQuadElementIndices = new Uint8Array([0, 1, 2, 3]);\n    const elementIndicesBuffer = gl.createBuffer();\n    this.register(toDisposable(() => gl.deleteBuffer(elementIndicesBuffer)));\n    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, elementIndicesBuffer);\n    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, unitQuadElementIndices, gl.STATIC_DRAW);\n\n    // Setup attributes\n    this._attributesBuffer = throwIfFalsy(gl.createBuffer());\n    this.register(toDisposable(() => gl.deleteBuffer(this._attributesBuffer)));\n    gl.bindBuffer(gl.ARRAY_BUFFER, this._attributesBuffer);\n    gl.enableVertexAttribArray(VertexAttribLocations.POSITION);\n    gl.vertexAttribPointer(VertexAttribLocations.POSITION, 2, gl.FLOAT, false, BYTES_PER_RECTANGLE, 0);\n    gl.vertexAttribDivisor(VertexAttribLocations.POSITION, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.SIZE);\n    gl.vertexAttribPointer(VertexAttribLocations.SIZE, 2, gl.FLOAT, false, BYTES_PER_RECTANGLE, 2 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.SIZE, 1);\n    gl.enableVertexAttribArray(VertexAttribLocations.COLOR);\n    gl.vertexAttribPointer(VertexAttribLocations.COLOR, 4, gl.FLOAT, false, BYTES_PER_RECTANGLE, 4 * Float32Array.BYTES_PER_ELEMENT);\n    gl.vertexAttribDivisor(VertexAttribLocations.COLOR, 1);\n\n    this._updateCachedColors(_themeService.colors);\n    this.register(this._themeService.onChangeColors(e => {\n      this._updateCachedColors(e);\n      this._updateViewportRectangle();\n    }));\n  }\n\n  public renderBackgrounds(): void {\n    this._renderVertices(this._vertices);\n  }\n\n  public renderCursor(): void {\n    this._renderVertices(this._verticesCursor);\n  }\n\n  private _renderVertices(vertices: Vertices): void {\n    const gl = this._gl;\n\n    gl.useProgram(this._program);\n\n    gl.bindVertexArray(this._vertexArrayObject);\n\n    gl.uniformMatrix4fv(this._projectionLocation, false, PROJECTION_MATRIX);\n\n    // Bind attributes buffer and draw\n    gl.bindBuffer(gl.ARRAY_BUFFER, this._attributesBuffer);\n    gl.bufferData(gl.ARRAY_BUFFER, vertices.attributes, gl.DYNAMIC_DRAW);\n    gl.drawElementsInstanced(this._gl.TRIANGLE_STRIP, 4, gl.UNSIGNED_BYTE, 0, vertices.count);\n  }\n\n  public handleResize(): void {\n    this._updateViewportRectangle();\n  }\n\n  public setDimensions(dimensions: IRenderDimensions): void {\n    this._dimensions = dimensions;\n  }\n\n  private _updateCachedColors(colors: ReadonlyColorSet): void {\n    this._bgFloat = this._colorToFloat32Array(colors.background);\n    this._cursorFloat = this._colorToFloat32Array(colors.cursor);\n  }\n\n  private _updateViewportRectangle(): void {\n    // Set first rectangle that clears the screen\n    this._addRectangleFloat(\n      this._vertices.attributes,\n      0,\n      0,\n      0,\n      this._terminal.cols * this._dimensions.device.cell.width,\n      this._terminal.rows * this._dimensions.device.cell.height,\n      this._bgFloat\n    );\n  }\n\n  public updateBackgrounds(model: IRenderModel): void {\n    const terminal = this._terminal;\n    const vertices = this._vertices;\n\n    // Declare variable ahead of time to avoid garbage collection\n    let rectangleCount = 1;\n    let y: number;\n    let x: number;\n    let currentStartX: number;\n    let currentBg: number;\n    let currentFg: number;\n    let currentInverse: boolean;\n    let modelIndex: number;\n    let bg: number;\n    let fg: number;\n    let inverse: boolean;\n    let offset: number;\n\n    for (y = 0; y < terminal.rows; y++) {\n      currentStartX = -1;\n      currentBg = 0;\n      currentFg = 0;\n      currentInverse = false;\n      for (x = 0; x < terminal.cols; x++) {\n        modelIndex = ((y * terminal.cols) + x) * RENDER_MODEL_INDICIES_PER_CELL;\n        bg = model.cells[modelIndex + RENDER_MODEL_BG_OFFSET];\n        fg = model.cells[modelIndex + RENDER_MODEL_FG_OFFSET];\n        inverse = !!(fg & FgFlags.INVERSE);\n        if (bg !== currentBg || (fg !== currentFg && (currentInverse || inverse))) {\n          // A rectangle needs to be drawn if going from non-default to another color\n          if (currentBg !== 0 || (currentInverse && currentFg !== 0)) {\n            offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n            this._updateRectangle(vertices, offset, currentFg, currentBg, currentStartX, x, y);\n          }\n          currentStartX = x;\n          currentBg = bg;\n          currentFg = fg;\n          currentInverse = inverse;\n        }\n      }\n      // Finish rectangle if it's still going\n      if (currentBg !== 0 || (currentInverse && currentFg !== 0)) {\n        offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n        this._updateRectangle(vertices, offset, currentFg, currentBg, currentStartX, terminal.cols, y);\n      }\n    }\n    vertices.count = rectangleCount;\n  }\n\n  public updateCursor(model: IRenderModel): void {\n    const vertices = this._verticesCursor;\n    const cursor = model.cursor;\n    if (!cursor || cursor.style === 'block') {\n      vertices.count = 0;\n      return;\n    }\n\n    let offset: number;\n    let rectangleCount = 0;\n\n    if (cursor.style === 'bar' || cursor.style === 'outline') {\n      // Left edge\n      offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n      this._addRectangleFloat(\n        vertices.attributes,\n        offset,\n        cursor.x * this._dimensions.device.cell.width,\n        cursor.y * this._dimensions.device.cell.height,\n        cursor.style === 'bar' ? cursor.dpr * cursor.cursorWidth : cursor.dpr,\n        this._dimensions.device.cell.height,\n        this._cursorFloat\n      );\n    }\n    if (cursor.style === 'underline' || cursor.style === 'outline') {\n      // Bottom edge\n      offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n      this._addRectangleFloat(\n        vertices.attributes,\n        offset,\n        cursor.x * this._dimensions.device.cell.width,\n        (cursor.y + 1) * this._dimensions.device.cell.height - cursor.dpr,\n        cursor.width * this._dimensions.device.cell.width,\n        cursor.dpr,\n        this._cursorFloat\n      );\n    }\n    if (cursor.style === 'outline') {\n      // Top edge\n      offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n      this._addRectangleFloat(\n        vertices.attributes,\n        offset,\n        cursor.x * this._dimensions.device.cell.width,\n        cursor.y * this._dimensions.device.cell.height,\n        cursor.width * this._dimensions.device.cell.width,\n        cursor.dpr,\n        this._cursorFloat\n      );\n      // Right edge\n      offset = rectangleCount++ * INDICES_PER_RECTANGLE;\n      this._addRectangleFloat(\n        vertices.attributes,\n        offset,\n        (cursor.x + cursor.width) * this._dimensions.device.cell.width - cursor.dpr,\n        cursor.y * this._dimensions.device.cell.height,\n        cursor.dpr,\n        this._dimensions.device.cell.height,\n        this._cursorFloat\n      );\n    }\n\n    vertices.count = rectangleCount;\n  }\n\n  private _updateRectangle(vertices: Vertices, offset: number, fg: number, bg: number, startX: number, endX: number, y: number): void {\n    if (fg & FgFlags.INVERSE) {\n      switch (fg & Attributes.CM_MASK) {\n        case Attributes.CM_P16:\n        case Attributes.CM_P256:\n          $rgba = this._themeService.colors.ansi[fg & Attributes.PCOLOR_MASK].rgba;\n          break;\n        case Attributes.CM_RGB:\n          $rgba = (fg & Attributes.RGB_MASK) << 8;\n          break;\n        case Attributes.CM_DEFAULT:\n        default:\n          $rgba = this._themeService.colors.foreground.rgba;\n      }\n    } else {\n      switch (bg & Attributes.CM_MASK) {\n        case Attributes.CM_P16:\n        case Attributes.CM_P256:\n          $rgba = this._themeService.colors.ansi[bg & Attributes.PCOLOR_MASK].rgba;\n          break;\n        case Attributes.CM_RGB:\n          $rgba = (bg & Attributes.RGB_MASK) << 8;\n          break;\n        case Attributes.CM_DEFAULT:\n        default:\n          $rgba = this._themeService.colors.background.rgba;\n      }\n    }\n\n    if (vertices.attributes.length < offset + 4) {\n      vertices.attributes = expandFloat32Array(vertices.attributes, this._terminal.rows * this._terminal.cols * INDICES_PER_RECTANGLE);\n    }\n    $x1 = startX * this._dimensions.device.cell.width;\n    $y1 = y * this._dimensions.device.cell.height;\n    $r = (($rgba >> 24) & 0xFF) / 255;\n    $g = (($rgba >> 16) & 0xFF) / 255;\n    $b = (($rgba >> 8 ) & 0xFF) / 255;\n    $a = 1;\n\n    this._addRectangle(vertices.attributes, offset, $x1, $y1, (endX - startX) * this._dimensions.device.cell.width, this._dimensions.device.cell.height, $r, $g, $b, $a);\n  }\n\n  private _addRectangle(array: Float32Array, offset: number, x1: number, y1: number, width: number, height: number, r: number, g: number, b: number, a: number): void {\n    array[offset    ] = x1 / this._dimensions.device.canvas.width;\n    array[offset + 1] = y1 / this._dimensions.device.canvas.height;\n    array[offset + 2] = width / this._dimensions.device.canvas.width;\n    array[offset + 3] = height / this._dimensions.device.canvas.height;\n    array[offset + 4] = r;\n    array[offset + 5] = g;\n    array[offset + 6] = b;\n    array[offset + 7] = a;\n  }\n\n  private _addRectangleFloat(array: Float32Array, offset: number, x1: number, y1: number, width: number, height: number, color: Float32Array): void {\n    array[offset    ] = x1 / this._dimensions.device.canvas.width;\n    array[offset + 1] = y1 / this._dimensions.device.canvas.height;\n    array[offset + 2] = width / this._dimensions.device.canvas.width;\n    array[offset + 3] = height / this._dimensions.device.canvas.height;\n    array[offset + 4] = color[0];\n    array[offset + 5] = color[1];\n    array[offset + 6] = color[2];\n    array[offset + 7] = color[3];\n  }\n\n  private _colorToFloat32Array(color: IColor): Float32Array {\n    return new Float32Array([\n      ((color.rgba >> 24) & 0xFF) / 255,\n      ((color.rgba >> 16) & 0xFF) / 255,\n      ((color.rgba >> 8 ) & 0xFF) / 255,\n      ((color.rgba      ) & 0xFF) / 255\n    ]);\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICursorRenderModel, IRenderModel } from './Types';\nimport { ISelectionRenderModel } from 'browser/renderer/shared/Types';\nimport { createSelectionRenderModel } from 'browser/renderer/shared/SelectionRenderModel';\n\nexport const RENDER_MODEL_INDICIES_PER_CELL = 4;\nexport const RENDER_MODEL_BG_OFFSET = 1;\nexport const RENDER_MODEL_FG_OFFSET = 2;\nexport const RENDER_MODEL_EXT_OFFSET = 3;\n\nexport const COMBINED_CHAR_BIT_MASK = 0x80000000;\n\nexport class RenderModel implements IRenderModel {\n  public cells: Uint32Array;\n  public lineLengths: Uint32Array;\n  public selection: ISelectionRenderModel;\n  public cursor?: ICursorRenderModel;\n\n  constructor() {\n    this.cells = new Uint32Array(0);\n    this.lineLengths = new Uint32Array(0);\n    this.selection = createSelectionRenderModel();\n  }\n\n  public resize(cols: number, rows: number): void {\n    const indexCount = cols * rows * RENDER_MODEL_INDICIES_PER_CELL;\n    if (indexCount !== this.cells.length) {\n      this.cells = new Uint32Array(indexCount);\n      this.lineLengths = new Uint32Array(rows);\n    }\n  }\n\n  public clear(): void {\n    this.cells.fill(0, 0);\n    this.lineLengths.fill(0, 0);\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { addDisposableDomListener } from 'browser/Lifecycle';\nimport { ITerminal } from 'browser/Types';\nimport { CellColorResolver } from 'browser/renderer/shared/CellColorResolver';\nimport { acquireTextureAtlas, removeTerminalFromCache } from 'browser/renderer/shared/CharAtlasCache';\nimport { CursorBlinkStateManager } from 'browser/renderer/shared/CursorBlinkStateManager';\nimport { observeDevicePixelDimensions } from 'browser/renderer/shared/DevicePixelObserver';\nimport { createRenderDimensions } from 'browser/renderer/shared/RendererUtils';\nimport { IRenderDimensions, IRenderer, IRequestRedrawEvent, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { ICharSizeService, ICharacterJoinerService, ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { EventEmitter, forwardEvent } from 'common/EventEmitter';\nimport { Disposable, MutableDisposable, getDisposeArrayDisposable, toDisposable } from 'common/Lifecycle';\nimport { CharData, IBufferLine, ICellData } from 'common/Types';\nimport { AttributeData } from 'common/buffer/AttributeData';\nimport { CellData } from 'common/buffer/CellData';\nimport { Attributes, Content, NULL_CELL_CHAR, NULL_CELL_CODE } from 'common/buffer/Constants';\nimport { traceCall } from 'common/services/LogService';\nimport { ICoreService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { GlyphRenderer } from './GlyphRenderer';\nimport { RectangleRenderer } from './RectangleRenderer';\nimport { COMBINED_CHAR_BIT_MASK, RENDER_MODEL_BG_OFFSET, RENDER_MODEL_EXT_OFFSET, RENDER_MODEL_FG_OFFSET, RENDER_MODEL_INDICIES_PER_CELL, RenderModel } from './RenderModel';\nimport { IWebGL2RenderingContext } from './Types';\nimport { LinkRenderLayer } from './renderLayer/LinkRenderLayer';\nimport { IRenderLayer } from './renderLayer/Types';\n\nexport class WebglRenderer extends Disposable implements IRenderer {\n  private _renderLayers: IRenderLayer[];\n  private _cursorBlinkStateManager: MutableDisposable<CursorBlinkStateManager> = new MutableDisposable();\n  private _charAtlasDisposable = this.register(new MutableDisposable());\n  private _charAtlas: ITextureAtlas | undefined;\n  private _devicePixelRatio: number;\n\n  private _model: RenderModel = new RenderModel();\n  private _workCell: CellData = new CellData();\n  private _cellColorResolver: CellColorResolver;\n\n  private _canvas: HTMLCanvasElement;\n  private _gl: IWebGL2RenderingContext;\n  private _rectangleRenderer: MutableDisposable<RectangleRenderer> = this.register(new MutableDisposable());\n  private _glyphRenderer: MutableDisposable<GlyphRenderer> = this.register(new MutableDisposable());\n\n  public readonly dimensions: IRenderDimensions;\n\n  private _core: ITerminal;\n  private _isAttached: boolean;\n  private _contextRestorationTimeout: number | undefined;\n\n  private readonly _onChangeTextureAtlas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onChangeTextureAtlas = this._onChangeTextureAtlas.event;\n  private readonly _onAddTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n  private readonly _onRemoveTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onRemoveTextureAtlasCanvas = this._onRemoveTextureAtlasCanvas.event;\n  private readonly _onRequestRedraw = this.register(new EventEmitter<IRequestRedrawEvent>());\n  public readonly onRequestRedraw = this._onRequestRedraw.event;\n  private readonly _onContextLoss = this.register(new EventEmitter<void>());\n  public readonly onContextLoss = this._onContextLoss.event;\n\n  constructor(\n    private _terminal: Terminal,\n    private readonly _characterJoinerService: ICharacterJoinerService,\n    private readonly _charSizeService: ICharSizeService,\n    private readonly _coreBrowserService: ICoreBrowserService,\n    private readonly _coreService: ICoreService,\n    private readonly _decorationService: IDecorationService,\n    private readonly _optionsService: IOptionsService,\n    private readonly _themeService: IThemeService,\n    preserveDrawingBuffer?: boolean\n  ) {\n    super();\n\n    this.register(this._themeService.onChangeColors(() => this._handleColorChange()));\n\n    this._cellColorResolver = new CellColorResolver(this._terminal, this._model.selection, this._decorationService, this._coreBrowserService, this._themeService);\n\n    this._core = (this._terminal as any)._core;\n\n    this._renderLayers = [\n      new LinkRenderLayer(this._core.screenElement!, 2, this._terminal, this._core.linkifier2, this._coreBrowserService, _optionsService, this._themeService)\n    ];\n    this.dimensions = createRenderDimensions();\n    this._devicePixelRatio = this._coreBrowserService.dpr;\n    this._updateDimensions();\n    this._updateCursorBlink();\n    this.register(_optionsService.onOptionChange(() => this._handleOptionsChanged()));\n\n    this._canvas = document.createElement('canvas');\n\n    const contextAttributes = {\n      antialias: false,\n      depth: false,\n      preserveDrawingBuffer\n    };\n    this._gl = this._canvas.getContext('webgl2', contextAttributes) as IWebGL2RenderingContext;\n    if (!this._gl) {\n      throw new Error('WebGL2 not supported ' + this._gl);\n    }\n\n    this.register(addDisposableDomListener(this._canvas, 'webglcontextlost', (e) => {\n      console.log('webglcontextlost event received');\n      // Prevent the default behavior in order to enable WebGL context restoration.\n      e.preventDefault();\n      // Wait a few seconds to see if the 'webglcontextrestored' event is fired.\n      // If not, dispatch the onContextLoss notification to observers.\n      this._contextRestorationTimeout = setTimeout(() => {\n        this._contextRestorationTimeout = undefined;\n        console.warn('webgl context not restored; firing onContextLoss');\n        this._onContextLoss.fire(e);\n      }, 3000 /* ms */);\n    }));\n    this.register(addDisposableDomListener(this._canvas, 'webglcontextrestored', (e) => {\n      console.warn('webglcontextrestored event received');\n      clearTimeout(this._contextRestorationTimeout);\n      this._contextRestorationTimeout = undefined;\n      // The texture atlas and glyph renderer must be fully reinitialized\n      // because their contents have been lost.\n      removeTerminalFromCache(this._terminal);\n      this._initializeWebGLState();\n      this._requestRedrawViewport();\n    }));\n\n    this.register(observeDevicePixelDimensions(this._canvas, this._coreBrowserService.window, (w, h) => this._setCanvasDevicePixelDimensions(w, h)));\n\n    this._core.screenElement!.appendChild(this._canvas);\n\n    [this._rectangleRenderer.value, this._glyphRenderer.value] = this._initializeWebGLState();\n\n    this._isAttached = this._coreBrowserService.window.document.body.contains(this._core.screenElement!);\n\n    this.register(toDisposable(() => {\n      for (const l of this._renderLayers) {\n        l.dispose();\n      }\n      this._canvas.parentElement?.removeChild(this._canvas);\n      removeTerminalFromCache(this._terminal);\n    }));\n  }\n\n  public get textureAtlas(): HTMLCanvasElement | undefined {\n    return this._charAtlas?.pages[0].canvas;\n  }\n\n  private _handleColorChange(): void {\n    this._refreshCharAtlas();\n\n    // Force a full refresh\n    this._clearModel(true);\n  }\n\n  public handleDevicePixelRatioChange(): void {\n    // If the device pixel ratio changed, the char atlas needs to be regenerated\n    // and the terminal needs to refreshed\n    if (this._devicePixelRatio !== this._coreBrowserService.dpr) {\n      this._devicePixelRatio = this._coreBrowserService.dpr;\n      this.handleResize(this._terminal.cols, this._terminal.rows);\n    }\n  }\n\n  public handleResize(cols: number, rows: number): void {\n    // Update character and canvas dimensions\n    this._updateDimensions();\n\n    this._model.resize(this._terminal.cols, this._terminal.rows);\n\n    // Resize all render layers\n    for (const l of this._renderLayers) {\n      l.resize(this._terminal, this.dimensions);\n    }\n\n    // Resize the canvas\n    this._canvas.width = this.dimensions.device.canvas.width;\n    this._canvas.height = this.dimensions.device.canvas.height;\n    this._canvas.style.width = `${this.dimensions.css.canvas.width}px`;\n    this._canvas.style.height = `${this.dimensions.css.canvas.height}px`;\n\n    // Resize the screen\n    this._core.screenElement!.style.width = `${this.dimensions.css.canvas.width}px`;\n    this._core.screenElement!.style.height = `${this.dimensions.css.canvas.height}px`;\n\n    this._rectangleRenderer.value?.setDimensions(this.dimensions);\n    this._rectangleRenderer.value?.handleResize();\n    this._glyphRenderer.value?.setDimensions(this.dimensions);\n    this._glyphRenderer.value?.handleResize();\n\n    this._refreshCharAtlas();\n\n    // Force a full refresh. Resizing `_glyphRenderer` should clear it already,\n    // so there is no need to clear it again here.\n    this._clearModel(false);\n  }\n\n  public handleCharSizeChanged(): void {\n    this.handleResize(this._terminal.cols, this._terminal.rows);\n  }\n\n  public handleBlur(): void {\n    for (const l of this._renderLayers) {\n      l.handleBlur(this._terminal);\n    }\n    this._cursorBlinkStateManager.value?.pause();\n    // Request a redraw for active/inactive selection background\n    this._requestRedrawViewport();\n  }\n\n  public handleFocus(): void {\n    for (const l of this._renderLayers) {\n      l.handleFocus(this._terminal);\n    }\n    this._cursorBlinkStateManager.value?.resume();\n    // Request a redraw for active/inactive selection background\n    this._requestRedrawViewport();\n  }\n\n  public handleSelectionChanged(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean): void {\n    for (const l of this._renderLayers) {\n      l.handleSelectionChanged(this._terminal, start, end, columnSelectMode);\n    }\n    this._model.selection.update(this._terminal, start, end, columnSelectMode);\n    this._requestRedrawViewport();\n  }\n\n  public handleCursorMove(): void {\n    for (const l of this._renderLayers) {\n      l.handleCursorMove(this._terminal);\n    }\n    this._cursorBlinkStateManager.value?.restartBlinkAnimation();\n  }\n\n  private _handleOptionsChanged(): void {\n    this._updateDimensions();\n    this._refreshCharAtlas();\n    this._updateCursorBlink();\n  }\n\n  /**\n   * Initializes members dependent on WebGL context state.\n   */\n  private _initializeWebGLState(): [RectangleRenderer, GlyphRenderer] {\n    this._rectangleRenderer.value = new RectangleRenderer(this._terminal, this._gl, this.dimensions, this._themeService);\n    this._glyphRenderer.value = new GlyphRenderer(this._terminal, this._gl, this.dimensions);\n\n    // Update dimensions and acquire char atlas\n    this.handleCharSizeChanged();\n\n    return [this._rectangleRenderer.value, this._glyphRenderer.value];\n  }\n\n  /**\n   * Refreshes the char atlas, aquiring a new one if necessary.\n   */\n  private _refreshCharAtlas(): void {\n    if (this.dimensions.device.char.width <= 0 && this.dimensions.device.char.height <= 0) {\n      // Mark as not attached so char atlas gets refreshed on next render\n      this._isAttached = false;\n      return;\n    }\n\n    const atlas = acquireTextureAtlas(\n      this._terminal,\n      this._optionsService.rawOptions,\n      this._themeService.colors,\n      this.dimensions.device.cell.width,\n      this.dimensions.device.cell.height,\n      this.dimensions.device.char.width,\n      this.dimensions.device.char.height,\n      this._coreBrowserService.dpr\n    );\n    if (this._charAtlas !== atlas) {\n      this._onChangeTextureAtlas.fire(atlas.pages[0].canvas);\n      this._charAtlasDisposable.value = getDisposeArrayDisposable([\n        forwardEvent(atlas.onAddTextureAtlasCanvas, this._onAddTextureAtlasCanvas),\n        forwardEvent(atlas.onRemoveTextureAtlasCanvas, this._onRemoveTextureAtlasCanvas)\n      ]);\n    }\n    this._charAtlas = atlas;\n    this._charAtlas.warmUp();\n    this._glyphRenderer.value?.setAtlas(this._charAtlas);\n  }\n\n  /**\n   * Clear the model.\n   * @param clearGlyphRenderer Whether to also clear the glyph renderer. This\n   * should be true generally to make sure it is in the same state as the model.\n   */\n  private _clearModel(clearGlyphRenderer: boolean): void {\n    this._model.clear();\n    if (clearGlyphRenderer) {\n      this._glyphRenderer.value?.clear();\n    }\n  }\n\n  public clearTextureAtlas(): void {\n    this._charAtlas?.clearTexture();\n    this._clearModel(true);\n    this._requestRedrawViewport();\n  }\n\n  public clear(): void {\n    this._clearModel(true);\n    for (const l of this._renderLayers) {\n      l.reset(this._terminal);\n    }\n\n    this._cursorBlinkStateManager.value?.restartBlinkAnimation();\n    this._updateCursorBlink();\n  }\n\n  public registerCharacterJoiner(handler: (text: string) => [number, number][]): number {\n    return -1;\n  }\n\n  public deregisterCharacterJoiner(joinerId: number): boolean {\n    return false;\n  }\n\n  @traceCall\n  public renderRows(start: number, end: number): void {\n    if (!this._isAttached) {\n      if (this._coreBrowserService.window.document.body.contains(this._core.screenElement!) && this._charSizeService.width && this._charSizeService.height) {\n        this._updateDimensions();\n        this._refreshCharAtlas();\n        this._isAttached = true;\n      } else {\n        return;\n      }\n    }\n\n    // Update render layers\n    for (const l of this._renderLayers) {\n      l.handleGridChanged(this._terminal, start, end);\n    }\n\n    if (!this._glyphRenderer.value || !this._rectangleRenderer.value) {\n      return;\n    }\n\n    // Tell renderer the frame is beginning\n    // upon a model clear also refresh the full viewport model\n    // (also triggered by an atlas page merge, part of #4480)\n    if (this._glyphRenderer.value.beginFrame()) {\n      this._clearModel(true);\n      this._updateModel(0, this._terminal.rows - 1);\n    } else {\n      // just update changed lines to draw\n      this._updateModel(start, end);\n    }\n\n    // Render\n    this._rectangleRenderer.value.renderBackgrounds();\n    this._glyphRenderer.value.render(this._model);\n    if (!this._cursorBlinkStateManager.value || this._cursorBlinkStateManager.value.isCursorVisible) {\n      this._rectangleRenderer.value.renderCursor();\n    }\n  }\n\n  private _updateCursorBlink(): void {\n    if (this._terminal.options.cursorBlink) {\n      this._cursorBlinkStateManager.value = new CursorBlinkStateManager(() => {\n        this._requestRedrawCursor();\n      }, this._coreBrowserService);\n    } else {\n      this._cursorBlinkStateManager.clear();\n    }\n    // Request a refresh from the terminal as management of rendering is being\n    // moved back to the terminal\n    this._requestRedrawCursor();\n  }\n\n  private _updateModel(start: number, end: number): void {\n    const terminal = this._core;\n    let cell: ICellData = this._workCell;\n\n    // Declare variable ahead of time to avoid garbage collection\n    let lastBg: number;\n    let y: number;\n    let row: number;\n    let line: IBufferLine;\n    let joinedRanges: [number, number][];\n    let isJoined: boolean;\n    let lastCharX: number;\n    let range: [number, number];\n    let chars: string;\n    let code: number;\n    let i: number;\n    let x: number;\n    let j: number;\n    start = clamp(start, terminal.rows - 1, 0);\n    end = clamp(end, terminal.rows - 1, 0);\n\n    const cursorY = this._terminal.buffer.active.baseY + this._terminal.buffer.active.cursorY;\n    // in case cursor.x == cols adjust visual cursor to cols - 1\n    const cursorX = Math.min(this._terminal.buffer.active.cursorX, terminal.cols - 1);\n    let lastCursorX = -1;\n    const isCursorVisible =\n      this._coreService.isCursorInitialized &&\n      !this._coreService.isCursorHidden &&\n      (!this._cursorBlinkStateManager.value || this._cursorBlinkStateManager.value.isCursorVisible);\n    this._model.cursor = undefined;\n    let modelUpdated = false;\n\n    for (y = start; y <= end; y++) {\n      row = y + terminal.buffer.ydisp;\n      line = terminal.buffer.lines.get(row)!;\n      this._model.lineLengths[y] = 0;\n      joinedRanges = this._characterJoinerService.getJoinedCharacters(row);\n      for (x = 0; x < terminal.cols; x++) {\n        lastBg = this._cellColorResolver.result.bg;\n        line.loadCell(x, cell);\n\n        if (x === 0) {\n          lastBg = this._cellColorResolver.result.bg;\n        }\n\n        // If true, indicates that the current character(s) to draw were joined.\n        isJoined = false;\n        lastCharX = x;\n\n        // Process any joined character ranges as needed. Because of how the\n        // ranges are produced, we know that they are valid for the characters\n        // and attributes of our input.\n        if (joinedRanges.length > 0 && x === joinedRanges[0][0]) {\n          isJoined = true;\n          range = joinedRanges.shift()!;\n\n          // We already know the exact start and end column of the joined range,\n          // so we get the string and width representing it directly.\n          cell = new JoinedCellData(\n            cell,\n            line!.translateToString(true, range[0], range[1]),\n            range[1] - range[0]\n          );\n\n          // Skip over the cells occupied by this range in the loop\n          lastCharX = range[1] - 1;\n        }\n\n        chars = cell.getChars();\n        code = cell.getCode();\n        i = ((y * terminal.cols) + x) * RENDER_MODEL_INDICIES_PER_CELL;\n\n        // Load colors/resolve overrides into work colors\n        this._cellColorResolver.resolve(cell, x, row);\n\n        // Override colors for cursor cell\n        if (isCursorVisible && row === cursorY) {\n          if (x === cursorX) {\n            this._model.cursor = {\n              x: cursorX,\n              y: this._terminal.buffer.active.cursorY,\n              width: cell.getWidth(),\n              style: this._coreBrowserService.isFocused ?\n                (terminal.options.cursorStyle || 'block') : terminal.options.cursorInactiveStyle,\n              cursorWidth: terminal.options.cursorWidth,\n              dpr: this._devicePixelRatio\n            };\n            lastCursorX = cursorX + cell.getWidth() - 1;\n          }\n          if (x >= cursorX && x <= lastCursorX &&\n              ((this._coreBrowserService.isFocused &&\n              (terminal.options.cursorStyle || 'block') === 'block') ||\n              (this._coreBrowserService.isFocused === false &&\n              terminal.options.cursorInactiveStyle === 'block'))) {\n            this._cellColorResolver.result.fg =\n              Attributes.CM_RGB | (this._themeService.colors.cursorAccent.rgba >> 8 & Attributes.RGB_MASK);\n            this._cellColorResolver.result.bg =\n              Attributes.CM_RGB | (this._themeService.colors.cursor.rgba >> 8 & Attributes.RGB_MASK);\n          }\n        }\n\n        if (code !== NULL_CELL_CODE) {\n          this._model.lineLengths[y] = x + 1;\n        }\n\n        // Nothing has changed, no updates needed\n        if (this._model.cells[i] === code &&\n            this._model.cells[i + RENDER_MODEL_BG_OFFSET] === this._cellColorResolver.result.bg &&\n            this._model.cells[i + RENDER_MODEL_FG_OFFSET] === this._cellColorResolver.result.fg &&\n            this._model.cells[i + RENDER_MODEL_EXT_OFFSET] === this._cellColorResolver.result.ext) {\n          continue;\n        }\n\n        modelUpdated = true;\n\n        // Flag combined chars with a bit mask so they're easily identifiable\n        if (chars.length > 1) {\n          code |= COMBINED_CHAR_BIT_MASK;\n        }\n\n        // Cache the results in the model\n        this._model.cells[i] = code;\n        this._model.cells[i + RENDER_MODEL_BG_OFFSET] = this._cellColorResolver.result.bg;\n        this._model.cells[i + RENDER_MODEL_FG_OFFSET] = this._cellColorResolver.result.fg;\n        this._model.cells[i + RENDER_MODEL_EXT_OFFSET] = this._cellColorResolver.result.ext;\n\n        this._glyphRenderer.value!.updateCell(x, y, code, this._cellColorResolver.result.bg, this._cellColorResolver.result.fg, this._cellColorResolver.result.ext, chars, lastBg);\n\n        if (isJoined) {\n          // Restore work cell\n          cell = this._workCell;\n\n          // Null out non-first cells\n          for (x++; x < lastCharX; x++) {\n            j = ((y * terminal.cols) + x) * RENDER_MODEL_INDICIES_PER_CELL;\n            this._glyphRenderer.value!.updateCell(x, y, NULL_CELL_CODE, 0, 0, 0, NULL_CELL_CHAR, 0);\n            this._model.cells[j] = NULL_CELL_CODE;\n            this._model.cells[j + RENDER_MODEL_BG_OFFSET] = this._cellColorResolver.result.bg;\n            this._model.cells[j + RENDER_MODEL_FG_OFFSET] = this._cellColorResolver.result.fg;\n            this._model.cells[j + RENDER_MODEL_EXT_OFFSET] = this._cellColorResolver.result.ext;\n          }\n        }\n      }\n    }\n    if (modelUpdated) {\n      this._rectangleRenderer.value!.updateBackgrounds(this._model);\n    }\n    this._rectangleRenderer.value!.updateCursor(this._model);\n  }\n\n  /**\n   * Recalculates the character and canvas dimensions.\n   */\n  private _updateDimensions(): void {\n    // Perform a new measure if the CharMeasure dimensions are not yet available\n    if (!this._charSizeService.width || !this._charSizeService.height) {\n      return;\n    }\n\n    // Calculate the device character width. Width is floored as it must be drawn to an integer grid\n    // in order for the char atlas glyphs to not be blurry.\n    this.dimensions.device.char.width = Math.floor(this._charSizeService.width * this._devicePixelRatio);\n\n    // Calculate the device character height. Height is ceiled in case devicePixelRatio is a\n    // floating point number in order to ensure there is enough space to draw the character to the\n    // cell.\n    this.dimensions.device.char.height = Math.ceil(this._charSizeService.height * this._devicePixelRatio);\n\n    // Calculate the device cell height, if lineHeight is _not_ 1, the resulting value will be\n    // floored since lineHeight can never be lower then 1, this guarentees the device cell height\n    // will always be larger than device char height.\n    this.dimensions.device.cell.height = Math.floor(this.dimensions.device.char.height * this._optionsService.rawOptions.lineHeight);\n\n    // Calculate the y offset within a cell that glyph should draw at in order for it to be centered\n    // correctly within the cell.\n    this.dimensions.device.char.top = this._optionsService.rawOptions.lineHeight === 1 ? 0 : Math.round((this.dimensions.device.cell.height - this.dimensions.device.char.height) / 2);\n\n    // Calculate the device cell width, taking the letterSpacing into account.\n    this.dimensions.device.cell.width = this.dimensions.device.char.width + Math.round(this._optionsService.rawOptions.letterSpacing);\n\n    // Calculate the x offset with a cell that text should draw from in order for it to be centered\n    // correctly within the cell.\n    this.dimensions.device.char.left = Math.floor(this._optionsService.rawOptions.letterSpacing / 2);\n\n    // Recalculate the canvas dimensions, the device dimensions define the actual number of pixel in\n    // the canvas\n    this.dimensions.device.canvas.height = this._terminal.rows * this.dimensions.device.cell.height;\n    this.dimensions.device.canvas.width = this._terminal.cols * this.dimensions.device.cell.width;\n\n    // The the size of the canvas on the page. It's important that this rounds to nearest integer\n    // and not ceils as browsers often have floating point precision issues where\n    // `window.devicePixelRatio` ends up being something like `1.***************` for example, when\n    // it's actually 1.1. Ceiling may causes blurriness as the backing canvas image is 1 pixel too\n    // large for the canvas element size.\n    this.dimensions.css.canvas.height = Math.round(this.dimensions.device.canvas.height / this._devicePixelRatio);\n    this.dimensions.css.canvas.width = Math.round(this.dimensions.device.canvas.width / this._devicePixelRatio);\n\n    // Get the CSS dimensions of an individual cell. This needs to be derived from the calculated\n    // device pixel canvas value above. CharMeasure.width/height by itself is insufficient when the\n    // page is not at 100% zoom level as CharMeasure is measured in CSS pixels, but the actual char\n    // size on the canvas can differ.\n    this.dimensions.css.cell.height = this.dimensions.device.cell.height / this._devicePixelRatio;\n    this.dimensions.css.cell.width = this.dimensions.device.cell.width / this._devicePixelRatio;\n  }\n\n  private _setCanvasDevicePixelDimensions(width: number, height: number): void {\n    if (this._canvas.width === width && this._canvas.height === height) {\n      return;\n    }\n    // While the actual canvas size has changed, keep device canvas dimensions as the value before\n    // the change as it's an exact multiple of the cell sizes.\n    this._canvas.width = width;\n    this._canvas.height = height;\n    this._requestRedrawViewport();\n  }\n\n  private _requestRedrawViewport(): void {\n    this._onRequestRedraw.fire({ start: 0, end: this._terminal.rows - 1 });\n  }\n\n  private _requestRedrawCursor(): void {\n    const cursorY = this._terminal.buffer.active.cursorY;\n    this._onRequestRedraw.fire({ start: cursorY, end: cursorY });\n  }\n}\n\n// TODO: Share impl with core\nexport class JoinedCellData extends AttributeData implements ICellData {\n  private _width: number;\n  // .content carries no meaning for joined CellData, simply nullify it\n  // thus we have to overload all other .content accessors\n  public content: number = 0;\n  public fg: number;\n  public bg: number;\n  public combinedData: string = '';\n\n  constructor(firstCell: ICellData, chars: string, width: number) {\n    super();\n    this.fg = firstCell.fg;\n    this.bg = firstCell.bg;\n    this.combinedData = chars;\n    this._width = width;\n  }\n\n  public isCombined(): number {\n    // always mark joined cell data as combined\n    return Content.IS_COMBINED_MASK;\n  }\n\n  public getWidth(): number {\n    return this._width;\n  }\n\n  public getChars(): string {\n    return this.combinedData;\n  }\n\n  public getCode(): number {\n    // code always gets the highest possible fake codepoint (read as -1)\n    // this is needed as code is used by caches as identifier\n    return 0x1FFFFF;\n  }\n\n  public setFromCharData(value: CharData): void {\n    throw new Error('not implemented');\n  }\n\n  public getAsCharData(): CharData {\n    return [this.fg, this.getChars(), this.getWidth(), this.getCode()];\n  }\n}\n\nfunction clamp(value: number, max: number, min: number = 0): number {\n  return Math.max(Math.min(value, max), min);\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\n\n/**\n * A matrix that when multiplies will translate 0-1 coordinates (left to right,\n * top to bottom) to clip space.\n */\nexport const PROJECTION_MATRIX = new Float32Array([\n  2, 0, 0, 0,\n  0, -2, 0, 0,\n  0, 0, 1, 0,\n  -1, 1, 0, 1\n]);\n\nexport function createProgram(gl: WebGLRenderingContext, vertexSource: string, fragmentSource: string): WebGLProgram | undefined {\n  const program = throwIfFalsy(gl.createProgram());\n  gl.attachShader(program, throwIfFalsy(createShader(gl, gl.VERTEX_SHADER, vertexSource)));\n  gl.attachShader(program, throwIfFalsy(createShader(gl, gl.FRAGMENT_SHADER, fragmentSource)));\n  gl.linkProgram(program);\n  const success = gl.getProgramParameter(program, gl.LINK_STATUS);\n  if (success) {\n    return program;\n  }\n\n  console.error(gl.getProgramInfoLog(program));\n  gl.deleteProgram(program);\n}\n\nexport function createShader(gl: WebGLRenderingContext, type: number, source: string): WebGLShader | undefined {\n  const shader = throwIfFalsy(gl.createShader(type));\n  gl.shaderSource(shader, source);\n  gl.compileShader(shader);\n  const success = gl.getShaderParameter(shader, gl.COMPILE_STATUS);\n  if (success) {\n    return shader;\n  }\n\n  console.error(gl.getShaderInfoLog(shader));\n  gl.deleteShader(shader);\n}\n\nexport function expandFloat32Array(source: Float32Array, max: number): Float32Array {\n  const newLength = Math.min(source.length * 2, max);\n  const newArray = new Float32Array(newLength);\n  for (let i = 0; i < source.length; i++) {\n    newArray[i] = source[i];\n  }\n  return newArray;\n}\n\nexport class GLTexture {\n  public texture: WebGLTexture;\n  public version: number;\n\n  constructor(texture: WebGLTexture) {\n    this.texture = texture;\n    this.version = -1;\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ReadonlyColorSet } from 'browser/Types';\nimport { acquireTextureAtlas } from 'browser/renderer/shared/CharAtlasCache';\nimport { TEXT_BASELINE } from 'browser/renderer/shared/Constants';\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { IRenderDimensions, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { CellData } from 'common/buffer/CellData';\nimport { IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { IRenderLayer } from './Types';\n\nexport abstract class BaseRenderLayer extends Disposable implements IRenderLayer {\n  private _canvas: HTMLCanvasElement;\n  protected _ctx!: CanvasRenderingContext2D;\n  private _deviceCharWidth: number = 0;\n  private _deviceCharHeight: number = 0;\n  private _deviceCellWidth: number = 0;\n  private _deviceCellHeight: number = 0;\n  private _deviceCharLeft: number = 0;\n  private _deviceCharTop: number = 0;\n\n  protected _charAtlas: ITextureAtlas | undefined;\n\n  constructor(\n    terminal: Terminal,\n    private _container: HTMLElement,\n    id: string,\n    zIndex: number,\n    private _alpha: boolean,\n    protected readonly _coreBrowserService: ICoreBrowserService,\n    protected readonly _optionsService: IOptionsService,\n    protected readonly _themeService: IThemeService\n  ) {\n    super();\n    this._canvas = document.createElement('canvas');\n    this._canvas.classList.add(`xterm-${id}-layer`);\n    this._canvas.style.zIndex = zIndex.toString();\n    this._initCanvas();\n    this._container.appendChild(this._canvas);\n    this.register(this._themeService.onChangeColors(e => {\n      this._refreshCharAtlas(terminal, e);\n      this.reset(terminal);\n    }));\n    this.register(toDisposable(() => {\n      this._canvas.remove();\n    }));\n  }\n\n  private _initCanvas(): void {\n    this._ctx = throwIfFalsy(this._canvas.getContext('2d', { alpha: this._alpha }));\n    // Draw the background if this is an opaque layer\n    if (!this._alpha) {\n      this._clearAll();\n    }\n  }\n\n  public handleBlur(terminal: Terminal): void {}\n  public handleFocus(terminal: Terminal): void {}\n  public handleCursorMove(terminal: Terminal): void {}\n  public handleGridChanged(terminal: Terminal, startRow: number, endRow: number): void {}\n  public handleSelectionChanged(terminal: Terminal, start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean = false): void {}\n\n  protected _setTransparency(terminal: Terminal, alpha: boolean): void {\n    // Do nothing when alpha doesn't change\n    if (alpha === this._alpha) {\n      return;\n    }\n\n    // Create new canvas and replace old one\n    const oldCanvas = this._canvas;\n    this._alpha = alpha;\n    // Cloning preserves properties\n    this._canvas = this._canvas.cloneNode() as HTMLCanvasElement;\n    this._initCanvas();\n    this._container.replaceChild(this._canvas, oldCanvas);\n\n    // Regenerate char atlas and force a full redraw\n    this._refreshCharAtlas(terminal, this._themeService.colors);\n    this.handleGridChanged(terminal, 0, terminal.rows - 1);\n  }\n\n  /**\n   * Refreshes the char atlas, aquiring a new one if necessary.\n   * @param terminal The terminal.\n   * @param colorSet The color set to use for the char atlas.\n   */\n  private _refreshCharAtlas(terminal: Terminal, colorSet: ReadonlyColorSet): void {\n    if (this._deviceCharWidth <= 0 && this._deviceCharHeight <= 0) {\n      return;\n    }\n    this._charAtlas = acquireTextureAtlas(terminal, this._optionsService.rawOptions, colorSet, this._deviceCellWidth, this._deviceCellHeight, this._deviceCharWidth, this._deviceCharHeight, this._coreBrowserService.dpr);\n    this._charAtlas.warmUp();\n  }\n\n  public resize(terminal: Terminal, dim: IRenderDimensions): void {\n    this._deviceCellWidth = dim.device.cell.width;\n    this._deviceCellHeight = dim.device.cell.height;\n    this._deviceCharWidth = dim.device.char.width;\n    this._deviceCharHeight = dim.device.char.height;\n    this._deviceCharLeft = dim.device.char.left;\n    this._deviceCharTop = dim.device.char.top;\n    this._canvas.width = dim.device.canvas.width;\n    this._canvas.height = dim.device.canvas.height;\n    this._canvas.style.width = `${dim.css.canvas.width}px`;\n    this._canvas.style.height = `${dim.css.canvas.height}px`;\n\n    // Draw the background if this is an opaque layer\n    if (!this._alpha) {\n      this._clearAll();\n    }\n\n    this._refreshCharAtlas(terminal, this._themeService.colors);\n  }\n\n  public abstract reset(terminal: Terminal): void;\n\n  /**\n   * Fills a 1px line (2px on HDPI) at the bottom of the cell. This uses the\n   * existing fillStyle on the context.\n   * @param x The column to fill.\n   * @param y The row to fill.\n   */\n  protected _fillBottomLineAtCells(x: number, y: number, width: number = 1): void {\n    this._ctx.fillRect(\n      x * this._deviceCellWidth,\n      (y + 1) * this._deviceCellHeight - this._coreBrowserService.dpr - 1 /* Ensure it's drawn within the cell */,\n      width * this._deviceCellWidth,\n      this._coreBrowserService.dpr);\n  }\n\n  /**\n   * Clears the entire canvas.\n   */\n  protected _clearAll(): void {\n    if (this._alpha) {\n      this._ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n      this._ctx.fillRect(0, 0, this._canvas.width, this._canvas.height);\n    }\n  }\n\n  /**\n   * Clears 1+ cells completely.\n   * @param x The column to start at.\n   * @param y The row to start at.\n   * @param width The number of columns to clear.\n   * @param height The number of rows to clear.\n   */\n  protected _clearCells(x: number, y: number, width: number, height: number): void {\n    if (this._alpha) {\n      this._ctx.clearRect(\n        x * this._deviceCellWidth,\n        y * this._deviceCellHeight,\n        width * this._deviceCellWidth,\n        height * this._deviceCellHeight);\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n      this._ctx.fillRect(\n        x * this._deviceCellWidth,\n        y * this._deviceCellHeight,\n        width * this._deviceCellWidth,\n        height * this._deviceCellHeight);\n    }\n  }\n\n  /**\n   * Draws a truecolor character at the cell. The character will be clipped to\n   * ensure that it fits with the cell, including the cell to the right if it's\n   * a wide character. This uses the existing fillStyle on the context.\n   * @param terminal The terminal.\n   * @param cell The cell data for the character to draw.\n   * @param x The column to draw at.\n   * @param y The row to draw at.\n   */\n  protected _fillCharTrueColor(terminal: Terminal, cell: CellData, x: number, y: number): void {\n    this._ctx.font = this._getFont(terminal, false, false);\n    this._ctx.textBaseline = TEXT_BASELINE;\n    this._clipCell(x, y, cell.getWidth());\n    this._ctx.fillText(\n      cell.getChars(),\n      x * this._deviceCellWidth + this._deviceCharLeft,\n      y * this._deviceCellHeight + this._deviceCharTop + this._deviceCharHeight);\n  }\n\n  /**\n   * Clips a cell to ensure no pixels will be drawn outside of it.\n   * @param x The column to clip.\n   * @param y The row to clip.\n   * @param width The number of columns to clip.\n   */\n  private _clipCell(x: number, y: number, width: number): void {\n    this._ctx.beginPath();\n    this._ctx.rect(\n      x * this._deviceCellWidth,\n      y * this._deviceCellHeight,\n      width * this._deviceCellWidth,\n      this._deviceCellHeight);\n    this._ctx.clip();\n  }\n\n  /**\n   * Gets the current font.\n   * @param terminal The terminal.\n   * @param isBold If we should use the bold fontWeight.\n   */\n  protected _getFont(terminal: Terminal, isBold: boolean, isItalic: boolean): string {\n    const fontWeight = isBold ? terminal.options.fontWeightBold : terminal.options.fontWeight;\n    const fontStyle = isItalic ? 'italic' : '';\n\n    return `${fontStyle} ${fontWeight} ${terminal.options.fontSize! * this._coreBrowserService.dpr}px ${terminal.options.fontFamily}`;\n  }\n}\n\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { is256Color } from 'browser/renderer/shared/CharAtlasUtils';\nimport { INVERTED_DEFAULT_COLOR } from 'browser/renderer/shared/Constants';\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { ILinkifier2, ILinkifierEvent } from 'browser/Types';\nimport { IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { BaseRenderLayer } from './BaseRenderLayer';\n\nexport class LinkRenderLayer extends BaseRenderLayer {\n  private _state: ILinkifierEvent | undefined;\n\n  constructor(\n    container: HTMLElement,\n    zIndex: number,\n    terminal: Terminal,\n    linkifier2: ILinkifier2,\n    coreBrowserService: ICoreBrowserService,\n    optionsService: IOptionsService,\n    themeService: IThemeService\n  ) {\n    super(terminal, container, 'link', zIndex, true, coreBrowserService, optionsService, themeService);\n\n    this.register(linkifier2.onShowLinkUnderline(e => this._handleShowLinkUnderline(e)));\n    this.register(linkifier2.onHideLinkUnderline(e => this._handleHideLinkUnderline(e)));\n  }\n\n  public resize(terminal: Terminal, dim: IRenderDimensions): void {\n    super.resize(terminal, dim);\n    // Resizing the canvas discards the contents of the canvas so clear state\n    this._state = undefined;\n  }\n\n  public reset(terminal: Terminal): void {\n    this._clearCurrentLink();\n  }\n\n  private _clearCurrentLink(): void {\n    if (this._state) {\n      this._clearCells(this._state.x1, this._state.y1, this._state.cols - this._state.x1, 1);\n      const middleRowCount = this._state.y2 - this._state.y1 - 1;\n      if (middleRowCount > 0) {\n        this._clearCells(0, this._state.y1 + 1, this._state.cols, middleRowCount);\n      }\n      this._clearCells(0, this._state.y2, this._state.x2, 1);\n      this._state = undefined;\n    }\n  }\n\n  private _handleShowLinkUnderline(e: ILinkifierEvent): void {\n    if (e.fg === INVERTED_DEFAULT_COLOR) {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n    } else if (e.fg !== undefined && is256Color(e.fg)) {\n      // 256 color support\n      this._ctx.fillStyle = this._themeService.colors.ansi[e.fg!].css;\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.foreground.css;\n    }\n\n    if (e.y1 === e.y2) {\n      // Single line link\n      this._fillBottomLineAtCells(e.x1, e.y1, e.x2 - e.x1);\n    } else {\n      // Multi-line link\n      this._fillBottomLineAtCells(e.x1, e.y1, e.cols - e.x1);\n      for (let y = e.y1 + 1; y < e.y2; y++) {\n        this._fillBottomLineAtCells(0, y, e.cols);\n      }\n      this._fillBottomLineAtCells(0, e.y2, e.x2);\n    }\n    this._state = e;\n  }\n\n  private _handleHideLinkUnderline(e: ILinkifierEvent): void {\n    this._clearCurrentLink();\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\n/**\n * Adds a disposable listener to a node in the DOM, returning the disposable.\n * @param node The node to add a listener to.\n * @param type The event type.\n * @param handler The handler for the listener.\n * @param options The boolean or options object to pass on to the event\n * listener.\n */\nexport function addDisposableDomListener(\n  node: Element | Window | Document,\n  type: string,\n  handler: (e: any) => void,\n  options?: boolean | AddEventListenerOptions\n): IDisposable {\n  node.addEventListener(type, handler, options);\n  let disposed = false;\n  return {\n    dispose: () => {\n      if (disposed) {\n        return;\n      }\n      disposed = true;\n      node.removeEventListener(type, handler, options);\n    }\n  };\n}\n", "import { ISelectionRenderModel } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { ReadonlyColorSet } from 'browser/Types';\nimport { Attributes, BgFlags, FgFlags } from 'common/buffer/Constants';\nimport { IDecorationService } from 'common/services/Services';\nimport { ICellData } from 'common/Types';\nimport { Terminal } from 'xterm';\n\n// Work variables to avoid garbage collection\nlet $fg = 0;\nlet $bg = 0;\nlet $hasFg = false;\nlet $hasBg = false;\nlet $isSelected = false;\nlet $colors: ReadonlyColorSet | undefined;\n\nexport class CellColorResolver {\n  /**\n   * The shared result of the {@link resolve} call. This is only safe to use immediately after as\n   * any other calls will share object.\n   */\n  public readonly result: { fg: number, bg: number, ext: number } = {\n    fg: 0,\n    bg: 0,\n    ext: 0\n  };\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _selectionRenderModel: ISelectionRenderModel,\n    private readonly _decorationService: IDecorationService,\n    private readonly _coreBrowserService: ICoreBrowserService,\n    private readonly _themeService: IThemeService\n  ) {\n  }\n\n  /**\n   * Resolves colors for the cell, putting the result into the shared {@link result}. This resolves\n   * overrides, inverse and selection for the cell which can then be used to feed into the renderer.\n   */\n  public resolve(cell: ICellData, x: number, y: number): void {\n    this.result.bg = cell.bg;\n    this.result.fg = cell.fg;\n    this.result.ext = cell.bg & BgFlags.HAS_EXTENDED ? cell.extended.ext : 0;\n    // Get any foreground/background overrides, this happens on the model to avoid spreading\n    // override logic throughout the different sub-renderers\n\n    // Reset overrides work variables\n    $bg = 0;\n    $fg = 0;\n    $hasBg = false;\n    $hasFg = false;\n    $isSelected = false;\n    $colors = this._themeService.colors;\n\n    // Apply decorations on the bottom layer\n    this._decorationService.forEachDecorationAtCell(x, y, 'bottom', d => {\n      if (d.backgroundColorRGB) {\n        $bg = d.backgroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasBg = true;\n      }\n      if (d.foregroundColorRGB) {\n        $fg = d.foregroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    });\n\n    // Apply the selection color if needed\n    $isSelected = this._selectionRenderModel.isCellSelected(this._terminal, x, y);\n    if ($isSelected) {\n      $bg = (this._coreBrowserService.isFocused ? $colors.selectionBackgroundOpaque : $colors.selectionInactiveBackgroundOpaque).rgba >> 8 & 0xFFFFFF;\n      $hasBg = true;\n      if ($colors.selectionForeground) {\n        $fg = $colors.selectionForeground.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    }\n\n    // Apply decorations on the top layer\n    this._decorationService.forEachDecorationAtCell(x, y, 'top', d => {\n      if (d.backgroundColorRGB) {\n        $bg = d.backgroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasBg = true;\n      }\n      if (d.foregroundColorRGB) {\n        $fg = d.foregroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    });\n\n    // Convert any overrides from rgba to the fg/bg packed format. This resolves the inverse flag\n    // ahead of time in order to use the correct cache key\n    if ($hasBg) {\n      if ($isSelected) {\n        // Non-RGB attributes from model + force non-dim + override + force RGB color mode\n        $bg = (cell.bg & ~Attributes.RGB_MASK & ~BgFlags.DIM) | $bg | Attributes.CM_RGB;\n      } else {\n        // Non-RGB attributes from model + override + force RGB color mode\n        $bg = (cell.bg & ~Attributes.RGB_MASK) | $bg | Attributes.CM_RGB;\n      }\n    }\n    if ($hasFg) {\n      // Non-RGB attributes from model + force disable inverse + override + force RGB color mode\n      $fg = (cell.fg & ~Attributes.RGB_MASK & ~FgFlags.INVERSE) | $fg | Attributes.CM_RGB;\n    }\n\n    // Handle case where inverse was specified by only one of bg override or fg override was set,\n    // resolving the other inverse color and setting the inverse flag if needed.\n    if (this.result.fg & FgFlags.INVERSE) {\n      if ($hasBg && !$hasFg) {\n        // Resolve bg color type (default color has a different meaning in fg vs bg)\n        if ((this.result.bg & Attributes.CM_MASK) === Attributes.CM_DEFAULT) {\n          $fg = (this.result.fg & ~(Attributes.RGB_MASK | FgFlags.INVERSE | Attributes.CM_MASK)) | (($colors.background.rgba >> 8 & 0xFFFFFF) & Attributes.RGB_MASK) | Attributes.CM_RGB;\n        } else {\n          $fg = (this.result.fg & ~(Attributes.RGB_MASK | FgFlags.INVERSE | Attributes.CM_MASK)) | this.result.bg & (Attributes.RGB_MASK | Attributes.CM_MASK);\n        }\n        $hasFg = true;\n      }\n      if (!$hasBg && $hasFg) {\n        // Resolve bg color type (default color has a different meaning in fg vs bg)\n        if ((this.result.fg & Attributes.CM_MASK) === Attributes.CM_DEFAULT) {\n          $bg = (this.result.bg & ~(Attributes.RGB_MASK | Attributes.CM_MASK)) | (($colors.foreground.rgba >> 8 & 0xFFFFFF) & Attributes.RGB_MASK) | Attributes.CM_RGB;\n        } else {\n          $bg = (this.result.bg & ~(Attributes.RGB_MASK | Attributes.CM_MASK)) | this.result.fg & (Attributes.RGB_MASK | Attributes.CM_MASK);\n        }\n        $hasBg = true;\n      }\n    }\n\n    // Release object\n    $colors = undefined;\n\n    // Use the override if it exists\n    this.result.bg = $hasBg ? $bg : this.result.bg;\n    this.result.fg = $hasFg ? $fg : this.result.fg;\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { TextureAtlas } from 'browser/renderer/shared/TextureAtlas';\nimport { ITerminalOptions, Terminal } from 'xterm';\nimport { ITerminal, ReadonlyColorSet } from 'browser/Types';\nimport { ICharAtlasConfig, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { generateConfig, configEquals } from 'browser/renderer/shared/CharAtlasUtils';\n\ninterface ITextureAtlasCacheEntry {\n  atlas: ITextureAtlas;\n  config: ICharAtlasConfig;\n  // N.B. This implementation potentially holds onto copies of the terminal forever, so\n  // this may cause memory leaks.\n  ownedBy: Terminal[];\n}\n\nconst charAtlasCache: ITextureAtlasCacheEntry[] = [];\n\n/**\n * Acquires a char atlas, either generating a new one or returning an existing\n * one that is in use by another terminal.\n */\nexport function acquireTextureAtlas(\n  terminal: Terminal,\n  options: Required<ITerminalOptions>,\n  colors: ReadonlyColorSet,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  deviceCharWidth: number,\n  deviceCharHeight: number,\n  devicePixelRatio: number\n): ITextureAtlas {\n  const newConfig = generateConfig(deviceCellWidth, deviceCellHeight, deviceCharWidth, deviceCharHeight, options, colors, devicePixelRatio);\n\n  // Check to see if the terminal already owns this config\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const entry = charAtlasCache[i];\n    const ownedByIndex = entry.ownedBy.indexOf(terminal);\n    if (ownedByIndex >= 0) {\n      if (configEquals(entry.config, newConfig)) {\n        return entry.atlas;\n      }\n      // The configs differ, release the terminal from the entry\n      if (entry.ownedBy.length === 1) {\n        entry.atlas.dispose();\n        charAtlasCache.splice(i, 1);\n      } else {\n        entry.ownedBy.splice(ownedByIndex, 1);\n      }\n      break;\n    }\n  }\n\n  // Try match a char atlas from the cache\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const entry = charAtlasCache[i];\n    if (configEquals(entry.config, newConfig)) {\n      // Add the terminal to the cache entry and return\n      entry.ownedBy.push(terminal);\n      return entry.atlas;\n    }\n  }\n\n  const core: ITerminal = (terminal as any)._core;\n  const newEntry: ITextureAtlasCacheEntry = {\n    atlas: new TextureAtlas(document, newConfig, core.unicodeService),\n    config: newConfig,\n    ownedBy: [terminal]\n  };\n  charAtlasCache.push(newEntry);\n  return newEntry.atlas;\n}\n\n/**\n * Removes a terminal reference from the cache, allowing its memory to be freed.\n * @param terminal The terminal to remove.\n */\nexport function removeTerminalFromCache(terminal: Terminal): void {\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const index = charAtlasCache[i].ownedBy.indexOf(terminal);\n    if (index !== -1) {\n      if (charAtlasCache[i].ownedBy.length === 1) {\n        // Remove the cache entry if it's the only terminal\n        charAtlasCache[i].atlas.dispose();\n        charAtlasCache.splice(i, 1);\n      } else {\n        // Remove the reference from the cache entry\n        charAtlasCache[i].ownedBy.splice(index, 1);\n      }\n      break;\n    }\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICharAtlasConfig } from './Types';\nimport { Attributes } from 'common/buffer/Constants';\nimport { ITerminalOptions } from 'xterm';\nimport { IColorSet, ReadonlyColorSet } from 'browser/Types';\nimport { NULL_COLOR } from 'common/Color';\n\nexport function generateConfig(deviceCellWidth: number, deviceCellHeight: number, deviceCharWidth: number, deviceCharHeight: number, options: Required<ITerminalOptions>, colors: ReadonlyColorSet, devicePixelRatio: number): ICharAtlasConfig {\n  // null out some fields that don't matter\n  const clonedColors: IColorSet = {\n    foreground: colors.foreground,\n    background: colors.background,\n    cursor: NULL_COLOR,\n    cursorAccent: NULL_COLOR,\n    selectionForeground: NULL_COLOR,\n    selectionBackgroundTransparent: NULL_COLOR,\n    selectionBackgroundOpaque: NULL_COLOR,\n    selectionInactiveBackgroundTransparent: NULL_COLOR,\n    selectionInactiveBackgroundOpaque: NULL_COLOR,\n    // For the static char atlas, we only use the first 16 colors, but we need all 256 for the\n    // dynamic character atlas.\n    ansi: colors.ansi.slice(),\n    contrastCache: colors.contrastCache,\n    halfContrastCache: colors.halfContrastCache\n  };\n  return {\n    customGlyphs: options.customGlyphs,\n    devicePixelRatio,\n    letterSpacing: options.letterSpacing,\n    lineHeight: options.lineHeight,\n    deviceCellWidth: deviceCellWidth,\n    deviceCellHeight: deviceCellHeight,\n    deviceCharWidth: deviceCharWidth,\n    deviceCharHeight: deviceCharHeight,\n    fontFamily: options.fontFamily,\n    fontSize: options.fontSize,\n    fontWeight: options.fontWeight,\n    fontWeightBold: options.fontWeightBold,\n    allowTransparency: options.allowTransparency,\n    drawBoldTextInBrightColors: options.drawBoldTextInBrightColors,\n    minimumContrastRatio: options.minimumContrastRatio,\n    colors: clonedColors\n  };\n}\n\nexport function configEquals(a: ICharAtlasConfig, b: ICharAtlasConfig): boolean {\n  for (let i = 0; i < a.colors.ansi.length; i++) {\n    if (a.colors.ansi[i].rgba !== b.colors.ansi[i].rgba) {\n      return false;\n    }\n  }\n  return a.devicePixelRatio === b.devicePixelRatio &&\n      a.customGlyphs === b.customGlyphs &&\n      a.lineHeight === b.lineHeight &&\n      a.letterSpacing === b.letterSpacing &&\n      a.fontFamily === b.fontFamily &&\n      a.fontSize === b.fontSize &&\n      a.fontWeight === b.fontWeight &&\n      a.fontWeightBold === b.fontWeightBold &&\n      a.allowTransparency === b.allowTransparency &&\n      a.deviceCharWidth === b.deviceCharWidth &&\n      a.deviceCharHeight === b.deviceCharHeight &&\n      a.drawBoldTextInBrightColors === b.drawBoldTextInBrightColors &&\n      a.minimumContrastRatio === b.minimumContrastRatio &&\n      a.colors.foreground.rgba === b.colors.foreground.rgba &&\n      a.colors.background.rgba === b.colors.background.rgba;\n}\n\nexport function is256Color(colorCode: number): boolean {\n  return (colorCode & Attributes.CM_MASK) === Attributes.CM_P16 || (colorCode & Attributes.CM_MASK) === Attributes.CM_P256;\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isFirefox, isLegacyEdge } from 'common/Platform';\n\nexport const INVERTED_DEFAULT_COLOR = 257;\n\nexport const DIM_OPACITY = 0.5;\n// The text baseline is set conditionally by browser. Using 'ideographic' for Firefox or Legacy Edge\n// would result in truncated text (Issue 3353). Using 'bottom' for Chrome would result in slightly\n// unaligned Powerline fonts (PR 3356#issuecomment-850928179).\nexport const TEXT_BASELINE: CanvasTextBaseline = isFirefox || isLegacyEdge ? 'bottom' : 'ideographic';\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICoreBrowserService } from 'browser/services/Services';\n\n/**\n * The time between cursor blinks.\n */\nconst BLINK_INTERVAL = 600;\n\nexport class CursorBlinkStateManager {\n  public isCursorVisible: boolean;\n\n  private _animationFrame: number | undefined;\n  private _blinkStartTimeout: number | undefined;\n  private _blinkInterval: number | undefined;\n\n  /**\n   * The time at which the animation frame was restarted, this is used on the\n   * next render to restart the timers so they don't need to restart the timers\n   * multiple times over a short period.\n   */\n  private _animationTimeRestarted: number | undefined;\n\n  constructor(\n    private _renderCallback: () => void,\n    private _coreBrowserService: ICoreBrowserService\n  ) {\n    this.isCursorVisible = true;\n    if (this._coreBrowserService.isFocused) {\n      this._restartInterval();\n    }\n  }\n\n  public get isPaused(): boolean { return !(this._blinkStartTimeout || this._blinkInterval); }\n\n  public dispose(): void {\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n    if (this._blinkStartTimeout) {\n      this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout);\n      this._blinkStartTimeout = undefined;\n    }\n    if (this._animationFrame) {\n      this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame);\n      this._animationFrame = undefined;\n    }\n  }\n\n  public restartBlinkAnimation(): void {\n    if (this.isPaused) {\n      return;\n    }\n    // Save a timestamp so that the restart can be done on the next interval\n    this._animationTimeRestarted = Date.now();\n    // Force a cursor render to ensure it's visible and in the correct position\n    this.isCursorVisible = true;\n    if (!this._animationFrame) {\n      this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n        this._renderCallback();\n        this._animationFrame = undefined;\n      });\n    }\n  }\n\n  private _restartInterval(timeToStart: number = BLINK_INTERVAL): void {\n    // Clear any existing interval\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n\n    // Setup the initial timeout which will hide the cursor, this is done before\n    // the regular interval is setup in order to support restarting the blink\n    // animation in a lightweight way (without thrashing clearInterval and\n    // setInterval).\n    this._blinkStartTimeout = this._coreBrowserService.window.setTimeout(() => {\n      // Check if another animation restart was requested while this was being\n      // started\n      if (this._animationTimeRestarted) {\n        const time = BLINK_INTERVAL - (Date.now() - this._animationTimeRestarted);\n        this._animationTimeRestarted = undefined;\n        if (time > 0) {\n          this._restartInterval(time);\n          return;\n        }\n      }\n\n      // Hide the cursor\n      this.isCursorVisible = false;\n      this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n        this._renderCallback();\n        this._animationFrame = undefined;\n      });\n\n      // Setup the blink interval\n      this._blinkInterval = this._coreBrowserService.window.setInterval(() => {\n        // Adjust the animation time if it was restarted\n        if (this._animationTimeRestarted) {\n          // calc time diff\n          // Make restart interval do a setTimeout initially?\n          const time = BLINK_INTERVAL - (Date.now() - this._animationTimeRestarted);\n          this._animationTimeRestarted = undefined;\n          this._restartInterval(time);\n          return;\n        }\n\n        // Invert visibility and render\n        this.isCursorVisible = !this.isCursorVisible;\n        this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n          this._renderCallback();\n          this._animationFrame = undefined;\n        });\n      }, BLINK_INTERVAL);\n    }, timeToStart);\n  }\n\n  public pause(): void {\n    this.isCursorVisible = true;\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n    if (this._blinkStartTimeout) {\n      this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout);\n      this._blinkStartTimeout = undefined;\n    }\n    if (this._animationFrame) {\n      this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame);\n      this._animationFrame = undefined;\n    }\n  }\n\n  public resume(): void {\n    // Clear out any existing timers just in case\n    this.pause();\n\n    this._animationTimeRestarted = undefined;\n    this._restartInterval();\n    this.restartBlinkAnimation();\n  }\n}\n", "/**\n * Copyright (c) 2021 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\n\ninterface IBlockVector {\n  x: number;\n  y: number;\n  w: number;\n  h: number;\n}\n\nexport const blockElementDefinitions: { [index: string]: IBlockVector[] | undefined } = {\n  // Block elements (0x2580-0x2590)\n  '▀': [{ x: 0, y: 0, w: 8, h: 4 }], // UPPER HALF BLOCK\n  '▁': [{ x: 0, y: 7, w: 8, h: 1 }], // LOWER ONE EIGHTH BLOCK\n  '▂': [{ x: 0, y: 6, w: 8, h: 2 }], // LOWER ONE QUARTER BLOCK\n  '▃': [{ x: 0, y: 5, w: 8, h: 3 }], // LOWER THREE EIGHTHS BLOCK\n  '▄': [{ x: 0, y: 4, w: 8, h: 4 }], // LOWER HALF BLOCK\n  '▅': [{ x: 0, y: 3, w: 8, h: 5 }], // LOWER FIVE EIGHTHS BLOCK\n  '▆': [{ x: 0, y: 2, w: 8, h: 6 }], // LOWER THREE QUARTERS BLOCK\n  '▇': [{ x: 0, y: 1, w: 8, h: 7 }], // LOWER SEVEN EIGHTHS BLOCK\n  '█': [{ x: 0, y: 0, w: 8, h: 8 }], // FULL BLOCK\n  '▉': [{ x: 0, y: 0, w: 7, h: 8 }], // LEFT SEVEN EIGHTHS BLOCK\n  '▊': [{ x: 0, y: 0, w: 6, h: 8 }], // LEFT THREE QUARTERS BLOCK\n  '▋': [{ x: 0, y: 0, w: 5, h: 8 }], // LEFT FIVE EIGHTHS BLOCK\n  '▌': [{ x: 0, y: 0, w: 4, h: 8 }], // LEFT HALF BLOCK\n  '▍': [{ x: 0, y: 0, w: 3, h: 8 }], // LEFT THREE EIGHTHS BLOCK\n  '▎': [{ x: 0, y: 0, w: 2, h: 8 }], // LEFT ONE QUARTER BLOCK\n  '▏': [{ x: 0, y: 0, w: 1, h: 8 }], // LEFT ONE EIGHTH BLOCK\n  '▐': [{ x: 4, y: 0, w: 4, h: 8 }], // RIGHT HALF BLOCK\n\n  // Block elements (0x2594-0x2595)\n  '▔': [{ x: 0, y: 0, w: 8, h: 1 }], // UPPER ONE EIGHTH BLOCK\n  '▕': [{ x: 7, y: 0, w: 1, h: 8 }], // RIGHT ONE EIGHTH BLOCK\n\n  // Terminal graphic characters (0x2596-0x259F)\n  '▖': [{ x: 0, y: 4, w: 4, h: 4 }],                             // QUADRANT LOWER LEFT\n  '▗': [{ x: 4, y: 4, w: 4, h: 4 }],                             // QUADRANT LOWER RIGHT\n  '▘': [{ x: 0, y: 0, w: 4, h: 4 }],                             // QUADRANT UPPER LEFT\n  '▙': [{ x: 0, y: 0, w: 4, h: 8 }, { x: 0, y: 4, w: 8, h: 4 }], // QUADRANT UPPER LEFT AND LOWER LEFT AND LOWER RIGHT\n  '▚': [{ x: 0, y: 0, w: 4, h: 4 }, { x: 4, y: 4, w: 4, h: 4 }], // QUADRANT UPPER LEFT AND LOWER RIGHT\n  '▛': [{ x: 0, y: 0, w: 4, h: 8 }, { x: 4, y: 0, w: 4, h: 4 }], // QUADRANT UPPER LEFT AND UPPER RIGHT AND LOWER LEFT\n  '▜': [{ x: 0, y: 0, w: 8, h: 4 }, { x: 4, y: 0, w: 4, h: 8 }], // QUADRANT UPPER LEFT AND UPPER RIGHT AND LOWER RIGHT\n  '▝': [{ x: 4, y: 0, w: 4, h: 4 }],                             // QUADRANT UPPER RIGHT\n  '▞': [{ x: 4, y: 0, w: 4, h: 4 }, { x: 0, y: 4, w: 4, h: 4 }], // QUADRANT UPPER RIGHT AND LOWER LEFT\n  '▟': [{ x: 4, y: 0, w: 4, h: 8 }, { x: 0, y: 4, w: 8, h: 4 }], // QUADRANT UPPER RIGHT AND LOWER LEFT AND LOWER RIGHT\n\n  // VERTICAL ONE EIGHTH BLOCK-2 through VERTICAL ONE EIGHTH BLOCK-7\n  '\\u{1FB70}': [{ x: 1, y: 0, w: 1, h: 8 }],\n  '\\u{1FB71}': [{ x: 2, y: 0, w: 1, h: 8 }],\n  '\\u{1FB72}': [{ x: 3, y: 0, w: 1, h: 8 }],\n  '\\u{1FB73}': [{ x: 4, y: 0, w: 1, h: 8 }],\n  '\\u{1FB74}': [{ x: 5, y: 0, w: 1, h: 8 }],\n  '\\u{1FB75}': [{ x: 6, y: 0, w: 1, h: 8 }],\n\n  // HORIZONTAL ONE EIGHTH BLOCK-2 through HORIZONTAL ONE EIGHTH BLOCK-7\n  '\\u{1FB76}': [{ x: 0, y: 1, w: 8, h: 1 }],\n  '\\u{1FB77}': [{ x: 0, y: 2, w: 8, h: 1 }],\n  '\\u{1FB78}': [{ x: 0, y: 3, w: 8, h: 1 }],\n  '\\u{1FB79}': [{ x: 0, y: 4, w: 8, h: 1 }],\n  '\\u{1FB7A}': [{ x: 0, y: 5, w: 8, h: 1 }],\n  '\\u{1FB7B}': [{ x: 0, y: 6, w: 8, h: 1 }],\n\n  // LEFT AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB7C}': [{ x: 0, y: 0, w: 1, h: 8 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // LEFT AND UPPER ONE EIGHTH BLOCK\n  '\\u{1FB7D}': [{ x: 0, y: 0, w: 1, h: 8 }, { x: 0, y: 0, w: 8, h: 1 }],\n  // RIGHT AND UPPER ONE EIGHTH BLOCK\n  '\\u{1FB7E}': [{ x: 7, y: 0, w: 1, h: 8 }, { x: 0, y: 0, w: 8, h: 1 }],\n  // RIGHT AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB7F}': [{ x: 7, y: 0, w: 1, h: 8 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // UPPER AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB80}': [{ x: 0, y: 0, w: 8, h: 1 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // HORIZONTAL ONE EIGHTH BLOCK-1358\n  '\\u{1FB81}': [{ x: 0, y: 0, w: 8, h: 1 }, { x: 0, y: 2, w: 8, h: 1 }, { x: 0, y: 4, w: 8, h: 1 }, { x: 0, y: 7, w: 8, h: 1 }],\n\n  // UPPER ONE QUARTER BLOCK\n  '\\u{1FB82}': [{ x: 0, y: 0, w: 8, h: 2 }],\n  // UPPER THREE EIGHTHS BLOCK\n  '\\u{1FB83}': [{ x: 0, y: 0, w: 8, h: 3 }],\n  // UPPER FIVE EIGHTHS BLOCK\n  '\\u{1FB84}': [{ x: 0, y: 0, w: 8, h: 5 }],\n  // UPPER THREE QUARTERS BLOCK\n  '\\u{1FB85}': [{ x: 0, y: 0, w: 8, h: 6 }],\n  // UPPER SEVEN EIGHTHS BLOCK\n  '\\u{1FB86}': [{ x: 0, y: 0, w: 8, h: 7 }],\n\n  // RIGHT ONE QUARTER BLOCK\n  '\\u{1FB87}': [{ x: 6, y: 0, w: 2, h: 8 }],\n  // RIGHT THREE EIGHTHS B0OCK\n  '\\u{1FB88}': [{ x: 5, y: 0, w: 3, h: 8 }],\n  // RIGHT FIVE EIGHTHS BL0CK\n  '\\u{1FB89}': [{ x: 3, y: 0, w: 5, h: 8 }],\n  // RIGHT THREE QUARTERS 0LOCK\n  '\\u{1FB8A}': [{ x: 2, y: 0, w: 6, h: 8 }],\n  // RIGHT SEVEN EIGHTHS B0OCK\n  '\\u{1FB8B}': [{ x: 1, y: 0, w: 7, h: 8 }],\n\n  // CHECKER BOARD FILL\n  '\\u{1FB95}': [\n    { x: 0, y: 0, w: 2, h: 2 }, { x: 4, y: 0, w: 2, h: 2 },\n    { x: 2, y: 2, w: 2, h: 2 }, { x: 6, y: 2, w: 2, h: 2 },\n    { x: 0, y: 4, w: 2, h: 2 }, { x: 4, y: 4, w: 2, h: 2 },\n    { x: 2, y: 6, w: 2, h: 2 }, { x: 6, y: 6, w: 2, h: 2 }\n  ],\n  // INVERSE CHECKER BOARD FILL\n  '\\u{1FB96}': [\n    { x: 2, y: 0, w: 2, h: 2 }, { x: 6, y: 0, w: 2, h: 2 },\n    { x: 0, y: 2, w: 2, h: 2 }, { x: 4, y: 2, w: 2, h: 2 },\n    { x: 2, y: 4, w: 2, h: 2 }, { x: 6, y: 4, w: 2, h: 2 },\n    { x: 0, y: 6, w: 2, h: 2 }, { x: 4, y: 6, w: 2, h: 2 }\n  ],\n  // HEAVY HORIZONTAL FILL (upper middle and lower one quarter block)\n  '\\u{1FB97}': [{ x: 0, y: 2, w: 8, h: 2 }, { x: 0, y: 6, w: 8, h: 2 }]\n};\n\ntype PatternDefinition = number[][];\n\n/**\n * Defines the repeating pattern used by special characters, the pattern is made up of a 2d array of\n * pixel values to be filled (1) or not filled (0).\n */\nconst patternCharacterDefinitions: { [key: string]: PatternDefinition | undefined } = {\n  // Shade characters (0x2591-0x2593)\n  '░': [ // LIGHT SHADE (25%)\n    [1, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 1, 0],\n    [0, 0, 0, 0]\n  ],\n  '▒': [ // MEDIUM SHADE (50%)\n    [1, 0],\n    [0, 0],\n    [0, 1],\n    [0, 0]\n  ],\n  '▓': [ // DARK SHADE (75%)\n    [0, 1],\n    [1, 1],\n    [1, 0],\n    [1, 1]\n  ]\n};\n\nconst enum Shapes {\n  /** │ */ TOP_TO_BOTTOM = 'M.5,0 L.5,1',\n  /** ─ */ LEFT_TO_RIGHT = 'M0,.5 L1,.5',\n\n  /** └ */ TOP_TO_RIGHT = 'M.5,0 L.5,.5 L1,.5',\n  /** ┘ */ TOP_TO_LEFT = 'M.5,0 L.5,.5 L0,.5',\n  /** ┐ */ LEFT_TO_BOTTOM = 'M0,.5 L.5,.5 L.5,1',\n  /** ┌ */ RIGHT_TO_BOTTOM = 'M0.5,1 L.5,.5 L1,.5',\n\n  /** ╵ */ MIDDLE_TO_TOP = 'M.5,.5 L.5,0',\n  /** ╴ */ MIDDLE_TO_LEFT = 'M.5,.5 L0,.5',\n  /** ╶ */ MIDDLE_TO_RIGHT = 'M.5,.5 L1,.5',\n  /** ╷ */ MIDDLE_TO_BOTTOM = 'M.5,.5 L.5,1',\n\n  /** ┴ */ T_TOP = 'M0,.5 L1,.5 M.5,.5 L.5,0',\n  /** ┤ */ T_LEFT = 'M.5,0 L.5,1 M.5,.5 L0,.5',\n  /** ├ */ T_RIGHT = 'M.5,0 L.5,1 M.5,.5 L1,.5',\n  /** ┬ */ T_BOTTOM = 'M0,.5 L1,.5 M.5,.5 L.5,1',\n\n  /** ┼ */ CROSS = 'M0,.5 L1,.5 M.5,0 L.5,1',\n\n  /** ╌ */ TWO_DASHES_HORIZONTAL = 'M.1,.5 L.4,.5 M.6,.5 L.9,.5', // .2 empty, .3 filled\n  /** ┄ */ THREE_DASHES_HORIZONTAL = 'M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5', // .1333 empty, .2 filled\n  /** ┉ */ FOUR_DASHES_HORIZONTAL = 'M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5', // .1 empty, .15 filled\n  /** ╎ */ TWO_DASHES_VERTICAL = 'M.5,.1 L.5,.4 M.5,.6 L.5,.9',\n  /** ┆ */ THREE_DASHES_VERTICAL = 'M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333',\n  /** ┊ */ FOUR_DASHES_VERTICAL = 'M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95',\n}\n\nconst enum Style {\n  NORMAL = 1,\n  BOLD = 3\n}\n\n/**\n * @param xp The percentage of 15% of the x axis.\n * @param yp The percentage of 15% of the x axis on the y axis.\n */\ntype DrawFunctionDefinition = (xp: number, yp: number) => string;\n\n/**\n * This contains the definitions of all box drawing characters in the format of SVG paths (ie. the\n * svg d attribute).\n */\nexport const boxDrawingDefinitions: { [character: string]: { [fontWeight: number]: string | DrawFunctionDefinition } | undefined } = {\n  // Uniform normal and bold\n  '─': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT },\n  '━': { [Style.BOLD]:   Shapes.LEFT_TO_RIGHT },\n  '│': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM },\n  '┃': { [Style.BOLD]:   Shapes.TOP_TO_BOTTOM },\n  '┌': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM },\n  '┏': { [Style.BOLD]:   Shapes.RIGHT_TO_BOTTOM },\n  '┐': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM },\n  '┓': { [Style.BOLD]:   Shapes.LEFT_TO_BOTTOM },\n  '└': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT },\n  '┗': { [Style.BOLD]:   Shapes.TOP_TO_RIGHT },\n  '┘': { [Style.NORMAL]: Shapes.TOP_TO_LEFT },\n  '┛': { [Style.BOLD]:   Shapes.TOP_TO_LEFT },\n  '├': { [Style.NORMAL]: Shapes.T_RIGHT },\n  '┣': { [Style.BOLD]:   Shapes.T_RIGHT },\n  '┤': { [Style.NORMAL]: Shapes.T_LEFT },\n  '┫': { [Style.BOLD]:   Shapes.T_LEFT },\n  '┬': { [Style.NORMAL]: Shapes.T_BOTTOM },\n  '┳': { [Style.BOLD]:   Shapes.T_BOTTOM },\n  '┴': { [Style.NORMAL]: Shapes.T_TOP },\n  '┻': { [Style.BOLD]:   Shapes.T_TOP },\n  '┼': { [Style.NORMAL]: Shapes.CROSS },\n  '╋': { [Style.BOLD]:   Shapes.CROSS },\n  '╴': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT },\n  '╸': { [Style.BOLD]:   Shapes.MIDDLE_TO_LEFT },\n  '╵': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP },\n  '╹': { [Style.BOLD]:   Shapes.MIDDLE_TO_TOP },\n  '╶': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT },\n  '╺': { [Style.BOLD]:   Shapes.MIDDLE_TO_RIGHT },\n  '╷': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM },\n  '╻': { [Style.BOLD]:   Shapes.MIDDLE_TO_BOTTOM },\n\n  // Double border\n  '═': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '║': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╒': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 - yp} L1,${.5 - yp} M.5,${.5 + yp} L1,${.5 + yp}` },\n  '╓': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},1 L${.5 - xp},.5 L1,.5 M${.5 + xp},.5 L${.5 + xp},1` },\n  '╔': { [Style.NORMAL]: (xp, yp) => `M1,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1` },\n  '╕': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L.5,${.5 - yp} L.5,1 M0,${.5 + yp} L.5,${.5 + yp}` },\n  '╖': { [Style.NORMAL]: (xp, yp) => `M${.5 + xp},1 L${.5 + xp},.5 L0,.5 M${.5 - xp},.5 L${.5 - xp},1` },\n  '╗': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M0,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},1` },\n  '╘': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 + yp} L1,${.5 + yp} M.5,${.5 - yp} L1,${.5 - yp}` },\n  '╙': { [Style.NORMAL]: (xp, yp) => `M1,.5 L${.5 - xp},.5 L${.5 - xp},0 M${.5 + xp},.5 L${.5 + xp},0` },\n  '╚': { [Style.NORMAL]: (xp, yp) => `M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0 M1,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},0` },\n  '╛': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L.5,${.5 + yp} L.5,0 M0,${.5 - yp} L.5,${.5 - yp}` },\n  '╜': { [Style.NORMAL]: (xp, yp) => `M0,.5 L${.5 + xp},.5 L${.5 + xp},0 M${.5 - xp},.5 L${.5 - xp},0` },\n  '╝': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M0,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},0` },\n  '╞': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M.5,${.5 - yp} L1,${.5 - yp} M.5,${.5 + yp} L1,${.5 + yp}` },\n  '╟': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1 M${.5 + xp},.5 L1,.5` },\n  '╠': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n  '╡': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M0,${.5 - yp} L.5,${.5 - yp} M0,${.5 + yp} L.5,${.5 + yp}` },\n  '╢': { [Style.NORMAL]: (xp, yp) => `M0,.5 L${.5 - xp},.5 M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╣': { [Style.NORMAL]: (xp, yp) => `M${.5 + xp},0 L${.5 + xp},1 M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0` },\n  '╤': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp} M.5,${.5 + yp} L.5,1` },\n  '╥': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},.5 L${.5 - xp},1 M${.5 + xp},.5 L${.5 + xp},1` },\n  '╦': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1` },\n  '╧': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - yp} M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '╨': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},.5 L${.5 - xp},0 M${.5 + xp},.5 L${.5 + xp},0` },\n  '╩': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L1,${.5 + yp} M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n  '╪': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '╫': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╬': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1 M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n\n  // Diagonal\n  '╱': { [Style.NORMAL]: 'M1,0 L0,1' },\n  '╲': { [Style.NORMAL]: 'M0,0 L1,1' },\n  '╳': { [Style.NORMAL]: 'M1,0 L0,1 M0,0 L1,1' },\n\n  // Mixed weight\n  '╼': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '╽': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '╾': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '╿': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┍': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┎': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┑': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┒': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┕': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┖': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┙': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┚': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┝': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┞': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┟': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┠': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '┡': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '┢': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '┥': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┦': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┧': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┨': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '┩': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '┪': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '┭': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┮': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┯': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '┰': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┱': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '┲': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '┵': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┶': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┷': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '┸': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┹': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '┺': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '┽': { [Style.NORMAL]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_RIGHT}`,  [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┾': { [Style.NORMAL]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_LEFT}`,   [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┿': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '╀': { [Style.NORMAL]: `${Shapes.LEFT_TO_RIGHT} ${Shapes.MIDDLE_TO_BOTTOM}`, [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '╁': { [Style.NORMAL]: `${Shapes.MIDDLE_TO_TOP} ${Shapes.LEFT_TO_RIGHT}`,    [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '╂': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '╃': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '╄': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '╅': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '╆': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '╇': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: `${Shapes.MIDDLE_TO_TOP} ${Shapes.LEFT_TO_RIGHT}` },\n  '╈': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: `${Shapes.LEFT_TO_RIGHT} ${Shapes.MIDDLE_TO_BOTTOM}` },\n  '╉': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_LEFT}` },\n  '╊': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_RIGHT}` },\n\n  // Dashed\n  '╌': { [Style.NORMAL]: Shapes.TWO_DASHES_HORIZONTAL },\n  '╍': { [Style.BOLD]:   Shapes.TWO_DASHES_HORIZONTAL },\n  '┄': { [Style.NORMAL]: Shapes.THREE_DASHES_HORIZONTAL },\n  '┅': { [Style.BOLD]:   Shapes.THREE_DASHES_HORIZONTAL },\n  '┈': { [Style.NORMAL]: Shapes.FOUR_DASHES_HORIZONTAL },\n  '┉': { [Style.BOLD]:   Shapes.FOUR_DASHES_HORIZONTAL },\n  '╎': { [Style.NORMAL]: Shapes.TWO_DASHES_VERTICAL },\n  '╏': { [Style.BOLD]:   Shapes.TWO_DASHES_VERTICAL },\n  '┆': { [Style.NORMAL]: Shapes.THREE_DASHES_VERTICAL  },\n  '┇': { [Style.BOLD]:   Shapes.THREE_DASHES_VERTICAL },\n  '┊': { [Style.NORMAL]: Shapes.FOUR_DASHES_VERTICAL },\n  '┋': { [Style.BOLD]:   Shapes.FOUR_DASHES_VERTICAL },\n\n  // Curved\n  '╭': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 + (yp / .15 * .5)} C.5,${.5 + (yp / .15 * .5)},.5,.5,1,.5` },\n  '╮': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 + (yp / .15 * .5)} C.5,${.5 + (yp / .15 * .5)},.5,.5,0,.5` },\n  '╯': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - (yp / .15 * .5)} C.5,${.5 - (yp / .15 * .5)},.5,.5,0,.5` },\n  '╰': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - (yp / .15 * .5)} C.5,${.5 - (yp / .15 * .5)},.5,.5,1,.5` }\n};\n\ninterface IVectorShape {\n  d: string;\n  type: VectorType;\n  leftPadding?: number;\n  rightPadding?: number;\n}\n\nconst enum VectorType {\n  FILL,\n  STROKE\n}\n\n/**\n * This contains the definitions of the primarily used box drawing characters as vector shapes. The\n * reason these characters are defined specially is to avoid common problems if a user's font has\n * not been patched with powerline characters and also to get pixel perfect rendering as rendering\n * issues can occur around AA/SPAA.\n *\n * The line variants draw beyond the cell and get clipped to ensure the end of the line is not\n * visible.\n *\n * Original symbols defined in https://github.com/powerline/fontpatcher\n */\nexport const powerlineDefinitions: { [index: string]: IVectorShape } = {\n  // Right triangle solid\n  '\\u{E0B0}': { d: 'M0,0 L1,.5 L0,1', type: VectorType.FILL, rightPadding: 2 },\n  // Right triangle line\n  '\\u{E0B1}': { d: 'M-1,-.5 L1,.5 L-1,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Left triangle solid\n  '\\u{E0B2}': { d: 'M1,0 L0,.5 L1,1', type: VectorType.FILL, leftPadding: 2 },\n  // Left triangle line\n  '\\u{E0B3}': { d: 'M2,-.5 L0,.5 L2,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Right semi-circle solid\n  '\\u{E0B4}': { d: 'M0,0 L0,1 C0.552,1,1,0.776,1,.5 C1,0.224,0.552,0,0,0', type: VectorType.FILL, rightPadding: 1 },\n  // Right semi-circle line\n  '\\u{E0B5}': { d: 'M.2,1 C.422,1,.8,.826,.78,.5 C.8,.174,0.422,0,.2,0', type: VectorType.STROKE, rightPadding: 1 },\n  // Left semi-circle solid\n  '\\u{E0B6}': { d: 'M1,0 L1,1 C0.448,1,0,0.776,0,.5 C0,0.224,0.448,0,1,0', type: VectorType.FILL, leftPadding: 1 },\n  // Left semi-circle line\n  '\\u{E0B7}': { d: 'M.8,1 C0.578,1,0.2,.826,.22,.5 C0.2,0.174,0.578,0,0.8,0', type: VectorType.STROKE, leftPadding: 1 },\n  // Lower left triangle\n  '\\u{E0B8}': { d: 'M-.5,-.5 L1.5,1.5 L-.5,1.5', type: VectorType.FILL },\n  // Backslash separator\n  '\\u{E0B9}': { d: 'M-.5,-.5 L1.5,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Lower right triangle\n  '\\u{E0BA}': { d: 'M1.5,-.5 L-.5,1.5 L1.5,1.5', type: VectorType.FILL },\n  // Upper left triangle\n  '\\u{E0BC}': { d: 'M1.5,-.5 L-.5,1.5 L-.5,-.5', type: VectorType.FILL },\n  // Forward slash separator\n  '\\u{E0BD}': { d: 'M1.5,-.5 L-.5,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Upper right triangle\n  '\\u{E0BE}': { d: 'M-.5,-.5 L1.5,1.5 L1.5,-.5', type: VectorType.FILL }\n};\n// Forward slash separator redundant\npowerlineDefinitions['\\u{E0BB}'] = powerlineDefinitions['\\u{E0BD}'];\n// Backslash separator redundant\npowerlineDefinitions['\\u{E0BF}'] = powerlineDefinitions['\\u{E0B9}'];\n\n/**\n * Try drawing a custom block element or box drawing character, returning whether it was\n * successfully drawn.\n */\nexport function tryDrawCustomChar(\n  ctx: CanvasRenderingContext2D,\n  c: string,\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  fontSize: number,\n  devicePixelRatio: number\n): boolean {\n  const blockElementDefinition = blockElementDefinitions[c];\n  if (blockElementDefinition) {\n    drawBlockElementChar(ctx, blockElementDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n    return true;\n  }\n\n  const patternDefinition = patternCharacterDefinitions[c];\n  if (patternDefinition) {\n    drawPatternChar(ctx, patternDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n    return true;\n  }\n\n  const boxDrawingDefinition = boxDrawingDefinitions[c];\n  if (boxDrawingDefinition) {\n    drawBoxDrawingChar(ctx, boxDrawingDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight, devicePixelRatio);\n    return true;\n  }\n\n  const powerlineDefinition = powerlineDefinitions[c];\n  if (powerlineDefinition) {\n    drawPowerlineChar(ctx, powerlineDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight, fontSize, devicePixelRatio);\n    return true;\n  }\n\n  return false;\n}\n\nfunction drawBlockElementChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: IBlockVector[],\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number\n): void {\n  for (let i = 0; i < charDefinition.length; i++) {\n    const box = charDefinition[i];\n    const xEighth = deviceCellWidth / 8;\n    const yEighth = deviceCellHeight / 8;\n    ctx.fillRect(\n      xOffset + box.x * xEighth,\n      yOffset + box.y * yEighth,\n      box.w * xEighth,\n      box.h * yEighth\n    );\n  }\n}\n\nconst cachedPatterns: Map<PatternDefinition, Map</* fillStyle */string, CanvasPattern>> = new Map();\n\nfunction drawPatternChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: number[][],\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number\n): void {\n  let patternSet = cachedPatterns.get(charDefinition);\n  if (!patternSet) {\n    patternSet = new Map();\n    cachedPatterns.set(charDefinition, patternSet);\n  }\n  const fillStyle = ctx.fillStyle;\n  if (typeof fillStyle !== 'string') {\n    throw new Error(`Unexpected fillStyle type \"${fillStyle}\"`);\n  }\n  let pattern = patternSet.get(fillStyle);\n  if (!pattern) {\n    const width = charDefinition[0].length;\n    const height = charDefinition.length;\n    const tmpCanvas = document.createElement('canvas');\n    tmpCanvas.width = width;\n    tmpCanvas.height = height;\n    const tmpCtx = throwIfFalsy(tmpCanvas.getContext('2d'));\n    const imageData = new ImageData(width, height);\n\n    // Extract rgba from fillStyle\n    let r: number;\n    let g: number;\n    let b: number;\n    let a: number;\n    if (fillStyle.startsWith('#')) {\n      r = parseInt(fillStyle.slice(1, 3), 16);\n      g = parseInt(fillStyle.slice(3, 5), 16);\n      b = parseInt(fillStyle.slice(5, 7), 16);\n      a = fillStyle.length > 7 && parseInt(fillStyle.slice(7, 9), 16) || 1;\n    } else if (fillStyle.startsWith('rgba')) {\n      ([r, g, b, a] = fillStyle.substring(5, fillStyle.length - 1).split(',').map(e => parseFloat(e)));\n    } else {\n      throw new Error(`Unexpected fillStyle color format \"${fillStyle}\" when drawing pattern glyph`);\n    }\n\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        imageData.data[(y * width + x) * 4    ] = r;\n        imageData.data[(y * width + x) * 4 + 1] = g;\n        imageData.data[(y * width + x) * 4 + 2] = b;\n        imageData.data[(y * width + x) * 4 + 3] = charDefinition[y][x] * (a * 255);\n      }\n    }\n    tmpCtx.putImageData(imageData, 0, 0);\n    pattern = throwIfFalsy(ctx.createPattern(tmpCanvas, null));\n    patternSet.set(fillStyle, pattern);\n  }\n  ctx.fillStyle = pattern;\n  ctx.fillRect(xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n}\n\n/**\n * Draws the following box drawing characters by mapping a subset of SVG d attribute instructions to\n * canvas draw calls.\n *\n * Box styles:       ┎┰┒┍┯┑╓╥╖╒╤╕ ┏┳┓┌┲┓┌┬┐┏┱┐\n * ┌─┬─┐ ┏━┳━┓ ╔═╦═╗ ┠╂┨┝┿┥╟╫╢╞╪╡ ┡╇┩├╊┫┢╈┪┣╉┤\n * │ │ │ ┃ ┃ ┃ ║ ║ ║ ┖┸┚┕┷┙╙╨╜╘╧╛ └┴┘└┺┛┗┻┛┗┹┘\n * ├─┼─┤ ┣━╋━┫ ╠═╬═╣ ┏┱┐┌┲┓┌┬┐┌┬┐ ┏┳┓┌┮┓┌┬┐┏┭┐\n * │ │ │ ┃ ┃ ┃ ║ ║ ║ ┡╃┤├╄┩├╆┪┢╅┤ ┞╀┦├┾┫┟╁┧┣┽┤\n * └─┴─┘ ┗━┻━┛ ╚═╩═╝ └┴┘└┴┘└┺┛┗┹┘ └┴┘└┶┛┗┻┛┗┵┘\n *\n * Other:\n * ╭─╮ ╲ ╱ ╷╻╎╏┆┇┊┋ ╺╾╴ ╌╌╌ ┄┄┄ ┈┈┈\n * │ │  ╳  ╽╿╎╏┆┇┊┋ ╶╼╸ ╍╍╍ ┅┅┅ ┉┉┉\n * ╰─╯ ╱ ╲ ╹╵╎╏┆┇┊┋\n *\n * All box drawing characters:\n * ─ ━ │ ┃ ┄ ┅ ┆ ┇ ┈ ┉ ┊ ┋ ┌ ┍ ┎ ┏\n * ┐ ┑ ┒ ┓ └ ┕ ┖ ┗ ┘ ┙ ┚ ┛ ├ ┝ ┞ ┟\n * ┠ ┡ ┢ ┣ ┤ ┥ ┦ ┧ ┨ ┩ ┪ ┫ ┬ ┭ ┮ ┯\n * ┰ ┱ ┲ ┳ ┴ ┵ ┶ ┷ ┸ ┹ ┺ ┻ ┼ ┽ ┾ ┿\n * ╀ ╁ ╂ ╃ ╄ ╅ ╆ ╇ ╈ ╉ ╊ ╋ ╌ ╍ ╎ ╏\n * ═ ║ ╒ ╓ ╔ ╕ ╖ ╗ ╘ ╙ ╚ ╛ ╜ ╝ ╞ ╟\n * ╠ ╡ ╢ ╣ ╤ ╥ ╦ ╧ ╨ ╩ ╪ ╫ ╬ ╭ ╮ ╯\n * ╰ ╱ ╲ ╳ ╴ ╵ ╶ ╷ ╸ ╹ ╺ ╻ ╼ ╽ ╾ ╿\n *\n * ---\n *\n * Box drawing alignment tests:                                          █\n *                                                                       ▉\n *   ╔══╦══╗  ┌──┬──┐  ╭──┬──╮  ╭──┬──╮  ┏━━┳━━┓  ┎┒┏┑   ╷  ╻ ┏┯┓ ┌┰┐    ▊ ╱╲╱╲╳╳╳\n *   ║┌─╨─┐║  │╔═╧═╗│  │╒═╪═╕│  │╓─╁─╖│  ┃┌─╂─┐┃  ┗╃╄┙  ╶┼╴╺╋╸┠┼┨ ┝╋┥    ▋ ╲╱╲╱╳╳╳\n *   ║│╲ ╱│║  │║   ║│  ││ │ ││  │║ ┃ ║│  ┃│ ╿ │┃  ┍╅╆┓   ╵  ╹ ┗┷┛ └┸┘    ▌ ╱╲╱╲╳╳╳\n *   ╠╡ ╳ ╞╣  ├╢   ╟┤  ├┼─┼─┼┤  ├╫─╂─╫┤  ┣┿╾┼╼┿┫  ┕┛┖┚     ┌┄┄┐ ╎ ┏┅┅┓ ┋ ▍ ╲╱╲╱╳╳╳\n *   ║│╱ ╲│║  │║   ║│  ││ │ ││  │║ ┃ ║│  ┃│ ╽ │┃  ░░▒▒▓▓██ ┊  ┆ ╎ ╏  ┇ ┋ ▎\n *   ║└─╥─┘║  │╚═╤═╝│  │╘═╪═╛│  │╙─╀─╜│  ┃└─╂─┘┃  ░░▒▒▓▓██ ┊  ┆ ╎ ╏  ┇ ┋ ▏\n *   ╚══╩══╝  └──┴──┘  ╰──┴──╯  ╰──┴──╯  ┗━━┻━━┛           └╌╌┘ ╎ ┗╍╍┛ ┋  ▁▂▃▄▅▆▇█\n *\n * Source: https://www.w3.org/2001/06/utf-8-test/UTF-8-demo.html\n */\nfunction drawBoxDrawingChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: { [fontWeight: number]: string | ((xp: number, yp: number) => string) },\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  devicePixelRatio: number\n): void {\n  ctx.strokeStyle = ctx.fillStyle;\n  for (const [fontWeight, instructions] of Object.entries(charDefinition)) {\n    ctx.beginPath();\n    ctx.lineWidth = devicePixelRatio * Number.parseInt(fontWeight);\n    let actualInstructions: string;\n    if (typeof instructions === 'function') {\n      const xp = .15;\n      const yp = .15 / deviceCellHeight * deviceCellWidth;\n      actualInstructions = instructions(xp, yp);\n    } else {\n      actualInstructions = instructions;\n    }\n    for (const instruction of actualInstructions.split(' ')) {\n      const type = instruction[0];\n      const f = svgToCanvasInstructionMap[type];\n      if (!f) {\n        console.error(`Could not find drawing instructions for \"${type}\"`);\n        continue;\n      }\n      const args: string[] = instruction.substring(1).split(',');\n      if (!args[0] || !args[1]) {\n        continue;\n      }\n      f(ctx, translateArgs(args, deviceCellWidth, deviceCellHeight, xOffset, yOffset, true, devicePixelRatio));\n    }\n    ctx.stroke();\n    ctx.closePath();\n  }\n}\n\nfunction drawPowerlineChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: IVectorShape,\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  fontSize: number,\n  devicePixelRatio: number\n): void {\n  // Clip the cell to make sure drawing doesn't occur beyond bounds\n  const clipRegion = new Path2D();\n  clipRegion.rect(xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n  ctx.clip(clipRegion);\n\n  ctx.beginPath();\n  // Scale the stroke with DPR and font size\n  const cssLineWidth = fontSize / 12;\n  ctx.lineWidth = devicePixelRatio * cssLineWidth;\n  for (const instruction of charDefinition.d.split(' ')) {\n    const type = instruction[0];\n    const f = svgToCanvasInstructionMap[type];\n    if (!f) {\n      console.error(`Could not find drawing instructions for \"${type}\"`);\n      continue;\n    }\n    const args: string[] = instruction.substring(1).split(',');\n    if (!args[0] || !args[1]) {\n      continue;\n    }\n    f(ctx, translateArgs(\n      args,\n      deviceCellWidth,\n      deviceCellHeight,\n      xOffset,\n      yOffset,\n      false,\n      devicePixelRatio,\n      (charDefinition.leftPadding ?? 0) * (cssLineWidth / 2),\n      (charDefinition.rightPadding ?? 0) * (cssLineWidth / 2)\n    ));\n  }\n  if (charDefinition.type === VectorType.STROKE) {\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.stroke();\n  } else {\n    ctx.fill();\n  }\n  ctx.closePath();\n}\n\nfunction clamp(value: number, max: number, min: number = 0): number {\n  return Math.max(Math.min(value, max), min);\n}\n\nconst svgToCanvasInstructionMap: { [index: string]: any } = {\n  'C': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.bezierCurveTo(args[0], args[1], args[2], args[3], args[4], args[5]),\n  'L': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.lineTo(args[0], args[1]),\n  'M': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.moveTo(args[0], args[1])\n};\n\nfunction translateArgs(args: string[], cellWidth: number, cellHeight: number, xOffset: number, yOffset: number, doClamp: boolean, devicePixelRatio: number, leftPadding: number = 0, rightPadding: number = 0): number[] {\n  const result = args.map(e => parseFloat(e) || parseInt(e));\n\n  if (result.length < 2) {\n    throw new Error('Too few arguments for instruction');\n  }\n\n  for (let x = 0; x < result.length; x += 2) {\n    // Translate from 0-1 to 0-cellWidth\n    result[x] *= cellWidth - (leftPadding * devicePixelRatio) - (rightPadding * devicePixelRatio);\n    // Ensure coordinate doesn't escape cell bounds and round to the nearest 0.5 to ensure a crisp\n    // line at 100% devicePixelRatio\n    if (doClamp && result[x] !== 0) {\n      result[x] = clamp(Math.round(result[x] + 0.5) - 0.5, cellWidth, 0);\n    }\n    // Apply the cell's offset (ie. x*cellWidth)\n    result[x] += xOffset + (leftPadding * devicePixelRatio);\n  }\n\n  for (let y = 1; y < result.length; y += 2) {\n    // Translate from 0-1 to 0-cellHeight\n    result[y] *= cellHeight;\n    // Ensure coordinate doesn't escape cell bounds and round to the nearest 0.5 to ensure a crisp\n    // line at 100% devicePixelRatio\n    if (doClamp && result[y] !== 0) {\n      result[y] = clamp(Math.round(result[y] + 0.5) - 0.5, cellHeight, 0);\n    }\n    // Apply the cell's offset (ie. x*cellHeight)\n    result[y] += yOffset;\n  }\n\n  return result;\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { toDisposable } from 'common/Lifecycle';\nimport { IDisposable } from 'common/Types';\n\nexport function observeDevicePixelDimensions(element: HTMLElement, parentWindow: Window & typeof globalThis, callback: (deviceWidth: number, deviceHeight: number) => void): IDisposable {\n  // Observe any resizes to the element and extract the actual pixel size of the element if the\n  // devicePixelContentBoxSize API is supported. This allows correcting rounding errors when\n  // converting between CSS pixels and device pixels which causes blurry rendering when device\n  // pixel ratio is not a round number.\n  let observer: ResizeObserver | undefined = new parentWindow.ResizeObserver((entries) => {\n    const entry = entries.find((entry) => entry.target === element);\n    if (!entry) {\n      return;\n    }\n\n    // Disconnect if devicePixelContentBoxSize isn't supported by the browser\n    if (!('devicePixelContentBoxSize' in entry)) {\n      observer?.disconnect();\n      observer = undefined;\n      return;\n    }\n\n    // Fire the callback, ignore events where the dimensions are 0x0 as the canvas is likely hidden\n    const width = entry.devicePixelContentBoxSize[0].inlineSize;\n    const height = entry.devicePixelContentBoxSize[0].blockSize;\n    if (width > 0 && height > 0) {\n      callback(width, height);\n    }\n  });\n  try {\n    observer.observe(element, { box: ['device-pixel-content-box'] } as any);\n  } catch {\n    observer.disconnect();\n    observer = undefined;\n  }\n  return toDisposable(() => observer?.disconnect());\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDimensions, IRenderDimensions } from 'browser/renderer/shared/Types';\n\nexport function throwIfFalsy<T>(value: T | undefined | null): T {\n  if (!value) {\n    throw new Error('value must not be falsy');\n  }\n  return value;\n}\n\nexport function isPowerlineGlyph(codepoint: number): boolean {\n  // Only return true for Powerline symbols which require\n  // different padding and should be excluded from minimum contrast\n  // ratio standards\n  return 0xE0A4 <= codepoint && codepoint <= 0xE0D6;\n}\n\nexport function isRestrictedPowerlineGlyph(codepoint: number): boolean {\n  return 0xE0B0 <= codepoint && codepoint <= 0xE0B7;\n}\n\nfunction isBoxOrBlockGlyph(codepoint: number): boolean {\n  return 0x2500 <= codepoint && codepoint <= 0x259F;\n}\n\nexport function excludeFromContrastRatioDemands(codepoint: number): boolean {\n  return isPowerlineGlyph(codepoint) || isBoxOrBlockGlyph(codepoint);\n}\n\nexport function createRenderDimensions(): IRenderDimensions {\n  return {\n    css: {\n      canvas: createDimension(),\n      cell: createDimension()\n    },\n    device: {\n      canvas: createDimension(),\n      cell: createDimension(),\n      char: {\n        width: 0,\n        height: 0,\n        left: 0,\n        top: 0\n      }\n    }\n  };\n}\n\nfunction createDimension(): IDimensions {\n  return {\n    width: 0,\n    height: 0\n  };\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ISelectionRenderModel } from 'browser/renderer/shared/Types';\nimport { Terminal } from 'xterm';\n\nclass SelectionRenderModel implements ISelectionRenderModel {\n  public hasSelection!: boolean;\n  public columnSelectMode!: boolean;\n  public viewportStartRow!: number;\n  public viewportEndRow!: number;\n  public viewportCappedStartRow!: number;\n  public viewportCappedEndRow!: number;\n  public startCol!: number;\n  public endCol!: number;\n  public selectionStart: [number, number] | undefined;\n  public selectionEnd: [number, number] | undefined;\n\n  constructor() {\n    this.clear();\n  }\n\n  public clear(): void {\n    this.hasSelection = false;\n    this.columnSelectMode = false;\n    this.viewportStartRow = 0;\n    this.viewportEndRow = 0;\n    this.viewportCappedStartRow = 0;\n    this.viewportCappedEndRow = 0;\n    this.startCol = 0;\n    this.endCol = 0;\n    this.selectionStart = undefined;\n    this.selectionEnd = undefined;\n  }\n\n  public update(terminal: Terminal, start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean = false): void {\n    this.selectionStart = start;\n    this.selectionEnd = end;\n    // Selection does not exist\n    if (!start || !end || (start[0] === end[0] && start[1] === end[1])) {\n      this.clear();\n      return;\n    }\n\n    // Translate from buffer position to viewport position\n    const viewportStartRow = start[1] - terminal.buffer.active.viewportY;\n    const viewportEndRow = end[1] - terminal.buffer.active.viewportY;\n    const viewportCappedStartRow = Math.max(viewportStartRow, 0);\n    const viewportCappedEndRow = Math.min(viewportEndRow, terminal.rows - 1);\n\n    // No need to draw the selection\n    if (viewportCappedStartRow >= terminal.rows || viewportCappedEndRow < 0) {\n      this.clear();\n      return;\n    }\n\n    this.hasSelection = true;\n    this.columnSelectMode = columnSelectMode;\n    this.viewportStartRow = viewportStartRow;\n    this.viewportEndRow = viewportEndRow;\n    this.viewportCappedStartRow = viewportCappedStartRow;\n    this.viewportCappedEndRow = viewportCappedEndRow;\n    this.startCol = start[0];\n    this.endCol = end[0];\n  }\n\n  public isCellSelected(terminal: Terminal, x: number, y: number): boolean {\n    if (!this.hasSelection) {\n      return false;\n    }\n    y -= terminal.buffer.active.viewportY;\n    if (this.columnSelectMode) {\n      if (this.startCol <= this.endCol) {\n        return x >= this.startCol && y >= this.viewportCappedStartRow &&\n          x < this.endCol && y <= this.viewportCappedEndRow;\n      }\n      return x < this.startCol && y >= this.viewportCappedStartRow &&\n        x >= this.endCol && y <= this.viewportCappedEndRow;\n    }\n    return (y > this.viewportStartRow && y < this.viewportEndRow) ||\n      (this.viewportStartRow === this.viewportEndRow && y === this.viewportStartRow && x >= this.startCol && x < this.endCol) ||\n      (this.viewportStartRow < this.viewportEndRow && y === this.viewportEndRow && x < this.endCol) ||\n      (this.viewportStartRow < this.viewportEndRow && y === this.viewportStartRow && x >= this.startCol);\n  }\n}\n\nexport function createSelectionRenderModel(): ISelectionRenderModel {\n  return new SelectionRenderModel();\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IColorContrastCache } from 'browser/Types';\nimport { DIM_OPACITY, TEXT_BASELINE } from 'browser/renderer/shared/Constants';\nimport { tryDrawCustom<PERSON>har } from 'browser/renderer/shared/CustomGlyphs';\nimport { excludeFromContrastRatioDemands, isPowerlineGlyph, isRestrictedPowerlineGlyph, throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { IBoundingBox, ICharAtlasConfig, IRasterizedGlyph, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { NULL_COLOR, color, rgba } from 'common/Color';\nimport { EventEmitter } from 'common/EventEmitter';\nimport { FourKeyMap } from 'common/MultiKeyMap';\nimport { IdleTaskQueue } from 'common/TaskQueue';\nimport { IColor } from 'common/Types';\nimport { AttributeData } from 'common/buffer/AttributeData';\nimport { Attributes, DEFAULT_COLOR, DEFAULT_EXT, UnderlineStyle } from 'common/buffer/Constants';\nimport { traceCall } from 'common/services/LogService';\nimport { IUnicodeService } from 'common/services/Services';\n\n/**\n * A shared object which is used to draw nothing for a particular cell.\n */\nconst NULL_RASTERIZED_GLYPH: IRasterizedGlyph = {\n  texturePage: 0,\n  texturePosition: { x: 0, y: 0 },\n  texturePositionClipSpace: { x: 0, y: 0 },\n  offset: { x: 0, y: 0 },\n  size: { x: 0, y: 0 },\n  sizeClipSpace: { x: 0, y: 0 }\n};\n\nconst TMP_CANVAS_GLYPH_PADDING = 2;\n\nconst enum Constants {\n  /**\n   * The amount of pixel padding to allow in each row. Setting this to zero would make the atlas\n   * page pack as tightly as possible, but more pages would end up being created as a result.\n   */\n  ROW_PIXEL_THRESHOLD = 2,\n  /**\n   * The maximum texture size regardless of what the actual hardware maximum turns out to be. This\n   * is enforced to ensure uploading the texture still finishes in a reasonable amount of time. A\n   * 4096 squared image takes up 16MB of GPU memory.\n   */\n  FORCED_MAX_TEXTURE_SIZE = 4096\n}\n\ninterface ICharAtlasActiveRow {\n  x: number;\n  y: number;\n  height: number;\n}\n\n// Work variables to avoid garbage collection\nlet $glyph = undefined;\n\nexport class TextureAtlas implements ITextureAtlas {\n  private _didWarmUp: boolean = false;\n\n  private _cacheMap: FourKeyMap<number, number, number, number, IRasterizedGlyph> = new FourKeyMap();\n  private _cacheMapCombined: FourKeyMap<string, number, number, number, IRasterizedGlyph> = new FourKeyMap();\n\n  // The texture that the atlas is drawn to\n  private _pages: AtlasPage[] = [];\n  public get pages(): { canvas: HTMLCanvasElement, version: number }[] { return this._pages; }\n\n  // The set of atlas pages that can be written to\n  private _activePages: AtlasPage[] = [];\n\n  private _tmpCanvas: HTMLCanvasElement;\n  // A temporary context that glyphs are drawn to before being transfered to the atlas.\n  private _tmpCtx: CanvasRenderingContext2D;\n\n  private _workBoundingBox: IBoundingBox = { top: 0, left: 0, bottom: 0, right: 0 };\n  private _workAttributeData: AttributeData = new AttributeData();\n\n  private _textureSize: number = 512;\n\n  public static maxAtlasPages: number | undefined;\n  public static maxTextureSize: number | undefined;\n\n  private readonly _onAddTextureAtlasCanvas = new EventEmitter<HTMLCanvasElement>();\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n  private readonly _onRemoveTextureAtlasCanvas = new EventEmitter<HTMLCanvasElement>();\n  public readonly onRemoveTextureAtlasCanvas = this._onRemoveTextureAtlasCanvas.event;\n\n  constructor(\n    private readonly _document: Document,\n    private readonly _config: ICharAtlasConfig,\n    private readonly _unicodeService: IUnicodeService\n  ) {\n    this._createNewPage();\n    this._tmpCanvas = createCanvas(\n      _document,\n      this._config.deviceCellWidth * 4 + TMP_CANVAS_GLYPH_PADDING * 2,\n      this._config.deviceCellHeight + TMP_CANVAS_GLYPH_PADDING * 2\n    );\n    this._tmpCtx = throwIfFalsy(this._tmpCanvas.getContext('2d', {\n      alpha: this._config.allowTransparency,\n      willReadFrequently: true\n    }));\n  }\n\n  public dispose(): void {\n    for (const page of this.pages) {\n      page.canvas.remove();\n    }\n    this._onAddTextureAtlasCanvas.dispose();\n  }\n\n  public warmUp(): void {\n    if (!this._didWarmUp) {\n      this._doWarmUp();\n      this._didWarmUp = true;\n    }\n  }\n\n  private _doWarmUp(): void {\n    // Pre-fill with ASCII 33-126, this is not urgent and done in idle callbacks\n    const queue = new IdleTaskQueue();\n    for (let i = 33; i < 126; i++) {\n      queue.enqueue(() => {\n        if (!this._cacheMap.get(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT)) {\n          const rasterizedGlyph = this._drawToCache(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT);\n          this._cacheMap.set(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT, rasterizedGlyph);\n        }\n      });\n    }\n  }\n\n  private _requestClearModel = false;\n  public beginFrame(): boolean {\n    return this._requestClearModel;\n  }\n\n  public clearTexture(): void {\n    if (this._pages[0].currentRow.x === 0 && this._pages[0].currentRow.y === 0) {\n      return;\n    }\n    for (const page of this._pages) {\n      page.clear();\n    }\n    this._cacheMap.clear();\n    this._cacheMapCombined.clear();\n    this._didWarmUp = false;\n  }\n\n  private _createNewPage(): AtlasPage {\n    // Try merge the set of the 4 most used pages of the largest size. This is is deferred to a\n    // microtask to ensure it does not interrupt textures that will be rendered in the current\n    // animation frame which would result in blank rendered areas. This is actually not that\n    // expensive relative to drawing the glyphs, so there is no need to wait for an idle callback.\n    if (TextureAtlas.maxAtlasPages && this._pages.length >= Math.max(4, TextureAtlas.maxAtlasPages)) {\n      // Find the set of the largest 4 images, below the maximum size, with the highest\n      // percentages used\n      const pagesBySize = this._pages.filter(e => {\n        return e.canvas.width * 2 <= (TextureAtlas.maxTextureSize || Constants.FORCED_MAX_TEXTURE_SIZE);\n      }).sort((a, b) => {\n        if (b.canvas.width !== a.canvas.width) {\n          return b.canvas.width - a.canvas.width;\n        }\n        return b.percentageUsed - a.percentageUsed;\n      });\n      let sameSizeI = -1;\n      let size = 0;\n      for (let i = 0; i < pagesBySize.length; i++) {\n        if (pagesBySize[i].canvas.width !== size) {\n          sameSizeI = i;\n          size = pagesBySize[i].canvas.width;\n        } else if (i - sameSizeI === 3) {\n          break;\n        }\n      }\n\n      // Gather details of the merge\n      const mergingPages = pagesBySize.slice(sameSizeI, sameSizeI + 4);\n      const sortedMergingPagesIndexes = mergingPages.map(e => e.glyphs[0].texturePage).sort((a, b) => a > b ? 1 : -1);\n      const mergedPageIndex = this.pages.length - mergingPages.length;\n\n      // Merge into the new page\n      const mergedPage = this._mergePages(mergingPages, mergedPageIndex);\n      mergedPage.version++;\n\n      // Delete the pages, shifting glyph texture pages as needed\n      for (let i = sortedMergingPagesIndexes.length - 1; i >= 0; i--) {\n        this._deletePage(sortedMergingPagesIndexes[i]);\n      }\n\n      // Add the new merged page to the end\n      this.pages.push(mergedPage);\n\n      // Request the model to be cleared to refresh all texture pages.\n      this._requestClearModel = true;\n      this._onAddTextureAtlasCanvas.fire(mergedPage.canvas);\n    }\n\n    // All new atlas pages are created small as they are highly dynamic\n    const newPage = new AtlasPage(this._document, this._textureSize);\n    this._pages.push(newPage);\n    this._activePages.push(newPage);\n    this._onAddTextureAtlasCanvas.fire(newPage.canvas);\n    return newPage;\n  }\n\n  private _mergePages(mergingPages: AtlasPage[], mergedPageIndex: number): AtlasPage {\n    const mergedSize = mergingPages[0].canvas.width * 2;\n    const mergedPage = new AtlasPage(this._document, mergedSize, mergingPages);\n    for (const [i, p] of mergingPages.entries()) {\n      const xOffset = i * p.canvas.width % mergedSize;\n      const yOffset = Math.floor(i / 2) * p.canvas.height;\n      mergedPage.ctx.drawImage(p.canvas, xOffset, yOffset);\n      for (const g of p.glyphs) {\n        g.texturePage = mergedPageIndex;\n        g.sizeClipSpace.x = g.size.x / mergedSize;\n        g.sizeClipSpace.y = g.size.y / mergedSize;\n        g.texturePosition.x += xOffset;\n        g.texturePosition.y += yOffset;\n        g.texturePositionClipSpace.x = g.texturePosition.x / mergedSize;\n        g.texturePositionClipSpace.y = g.texturePosition.y / mergedSize;\n      }\n\n      this._onRemoveTextureAtlasCanvas.fire(p.canvas);\n\n      // Remove the merging page from active pages if it was there\n      const index = this._activePages.indexOf(p);\n      if (index !== -1) {\n        this._activePages.splice(index, 1);\n      }\n    }\n    return mergedPage;\n  }\n\n  private _deletePage(pageIndex: number): void {\n    this._pages.splice(pageIndex, 1);\n    for (let j = pageIndex; j < this._pages.length; j++) {\n      const adjustingPage = this._pages[j];\n      for (const g of adjustingPage.glyphs) {\n        g.texturePage--;\n      }\n      adjustingPage.version++;\n    }\n  }\n\n  public getRasterizedGlyphCombinedChar(chars: string, bg: number, fg: number, ext: number, restrictToCellHeight: boolean): IRasterizedGlyph {\n    return this._getFromCacheMap(this._cacheMapCombined, chars, bg, fg, ext, restrictToCellHeight);\n  }\n\n  public getRasterizedGlyph(code: number, bg: number, fg: number, ext: number, restrictToCellHeight: boolean): IRasterizedGlyph {\n    return this._getFromCacheMap(this._cacheMap, code, bg, fg, ext, restrictToCellHeight);\n  }\n\n  /**\n   * Gets the glyphs texture coords, drawing the texture if it's not already\n   */\n  private _getFromCacheMap(\n    cacheMap: FourKeyMap<string | number, number, number, number, IRasterizedGlyph>,\n    key: string | number,\n    bg: number,\n    fg: number,\n    ext: number,\n    restrictToCellHeight: boolean = false\n  ): IRasterizedGlyph {\n    $glyph = cacheMap.get(key, bg, fg, ext);\n    if (!$glyph) {\n      $glyph = this._drawToCache(key, bg, fg, ext, restrictToCellHeight);\n      cacheMap.set(key, bg, fg, ext, $glyph);\n    }\n    return $glyph;\n  }\n\n  private _getColorFromAnsiIndex(idx: number): IColor {\n    if (idx >= this._config.colors.ansi.length) {\n      throw new Error('No color found for idx ' + idx);\n    }\n    return this._config.colors.ansi[idx];\n  }\n\n  private _getBackgroundColor(bgColorMode: number, bgColor: number, inverse: boolean, dim: boolean): IColor {\n    if (this._config.allowTransparency) {\n      // The background color might have some transparency, so we need to render it as fully\n      // transparent in the atlas. Otherwise we'd end up drawing the transparent background twice\n      // around the anti-aliased edges of the glyph, and it would look too dark.\n      return NULL_COLOR;\n    }\n\n    let result: IColor;\n    switch (bgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        result = this._getColorFromAnsiIndex(bgColor);\n        break;\n      case Attributes.CM_RGB:\n        const arr = AttributeData.toColorRGB(bgColor);\n        // TODO: This object creation is slow\n        result = rgba.toColor(arr[0], arr[1], arr[2]);\n        break;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          result = color.opaque(this._config.colors.foreground);\n        } else {\n          result = this._config.colors.background;\n        }\n        break;\n    }\n\n    return result;\n  }\n\n  private _getForegroundColor(bg: number, bgColorMode: number, bgColor: number, fg: number, fgColorMode: number, fgColor: number, inverse: boolean, dim: boolean, bold: boolean, excludeFromContrastRatioDemands: boolean): IColor {\n    const minimumContrastColor = this._getMinimumContrastColor(bg, bgColorMode, bgColor, fg, fgColorMode, fgColor, false, bold, dim, excludeFromContrastRatioDemands);\n    if (minimumContrastColor) {\n      return minimumContrastColor;\n    }\n\n    let result: IColor;\n    switch (fgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        if (this._config.drawBoldTextInBrightColors && bold && fgColor < 8) {\n          fgColor += 8;\n        }\n        result = this._getColorFromAnsiIndex(fgColor);\n        break;\n      case Attributes.CM_RGB:\n        const arr = AttributeData.toColorRGB(fgColor);\n        result = rgba.toColor(arr[0], arr[1], arr[2]);\n        break;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          result = this._config.colors.background;\n        } else {\n          result = this._config.colors.foreground;\n        }\n    }\n\n    // Always use an opaque color regardless of allowTransparency\n    if (this._config.allowTransparency) {\n      result = color.opaque(result);\n    }\n\n    // Apply dim to the color, opacity is fine to use for the foreground color\n    if (dim) {\n      result = color.multiplyOpacity(result, DIM_OPACITY);\n    }\n\n    return result;\n  }\n\n  private _resolveBackgroundRgba(bgColorMode: number, bgColor: number, inverse: boolean): number {\n    switch (bgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        return this._getColorFromAnsiIndex(bgColor).rgba;\n      case Attributes.CM_RGB:\n        return bgColor << 8;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          return this._config.colors.foreground.rgba;\n        }\n        return this._config.colors.background.rgba;\n    }\n  }\n\n  private _resolveForegroundRgba(fgColorMode: number, fgColor: number, inverse: boolean, bold: boolean): number {\n    switch (fgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        if (this._config.drawBoldTextInBrightColors && bold && fgColor < 8) {\n          fgColor += 8;\n        }\n        return this._getColorFromAnsiIndex(fgColor).rgba;\n      case Attributes.CM_RGB:\n        return fgColor << 8;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          return this._config.colors.background.rgba;\n        }\n        return this._config.colors.foreground.rgba;\n    }\n  }\n\n  private _getMinimumContrastColor(bg: number, bgColorMode: number, bgColor: number, fg: number, fgColorMode: number, fgColor: number, inverse: boolean, bold: boolean, dim: boolean, excludeFromContrastRatioDemands: boolean): IColor | undefined {\n    if (this._config.minimumContrastRatio === 1 || excludeFromContrastRatioDemands) {\n      return undefined;\n    }\n\n    // Try get from cache first\n    const cache = this._getContrastCache(dim);\n    const adjustedColor = cache.getColor(bg, fg);\n    if (adjustedColor !== undefined) {\n      return adjustedColor || undefined;\n    }\n\n    const bgRgba = this._resolveBackgroundRgba(bgColorMode, bgColor, inverse);\n    const fgRgba = this._resolveForegroundRgba(fgColorMode, fgColor, inverse, bold);\n    // Dim cells only require half the contrast, otherwise they wouldn't be distinguishable from\n    // non-dim cells\n    const result = rgba.ensureContrastRatio(bgRgba, fgRgba, this._config.minimumContrastRatio / (dim ? 2 : 1));\n\n    if (!result) {\n      cache.setColor(bg, fg, null);\n      return undefined;\n    }\n\n    const color = rgba.toColor(\n      (result >> 24) & 0xFF,\n      (result >> 16) & 0xFF,\n      (result >> 8) & 0xFF\n    );\n    cache.setColor(bg, fg, color);\n\n    return color;\n  }\n\n  private _getContrastCache(dim: boolean): IColorContrastCache {\n    if (dim) {\n      return this._config.colors.halfContrastCache;\n    }\n    return this._config.colors.contrastCache;\n  }\n\n  @traceCall\n  private _drawToCache(codeOrChars: number | string, bg: number, fg: number, ext: number, restrictToCellHeight: boolean = false): IRasterizedGlyph {\n    const chars = typeof codeOrChars === 'number' ? String.fromCharCode(codeOrChars) : codeOrChars;\n\n    // Uncomment for debugging\n    // console.log(`draw to cache \"${chars}\"`, bg, fg, ext);\n\n    // Allow 1 cell width per character, with a minimum of 2 (CJK), plus some padding. This is used\n    // to draw the glyph to the canvas as well as to restrict the bounding box search to ensure\n    // giant ligatures (eg. =====>) don't impact overall performance.\n    const allowedWidth = Math.min(this._config.deviceCellWidth * Math.max(chars.length, 2) + TMP_CANVAS_GLYPH_PADDING * 2, this._textureSize);\n    if (this._tmpCanvas.width < allowedWidth) {\n      this._tmpCanvas.width = allowedWidth;\n    }\n    // Include line height when drawing glyphs\n    const allowedHeight = Math.min(this._config.deviceCellHeight + TMP_CANVAS_GLYPH_PADDING * 4, this._textureSize);\n    if (this._tmpCanvas.height < allowedHeight) {\n      this._tmpCanvas.height = allowedHeight;\n    }\n    this._tmpCtx.save();\n\n    this._workAttributeData.fg = fg;\n    this._workAttributeData.bg = bg;\n    this._workAttributeData.extended.ext = ext;\n\n    const invisible = !!this._workAttributeData.isInvisible();\n    if (invisible) {\n      return NULL_RASTERIZED_GLYPH;\n    }\n\n    const bold = !!this._workAttributeData.isBold();\n    const inverse = !!this._workAttributeData.isInverse();\n    const dim = !!this._workAttributeData.isDim();\n    const italic = !!this._workAttributeData.isItalic();\n    const underline = !!this._workAttributeData.isUnderline();\n    const strikethrough = !!this._workAttributeData.isStrikethrough();\n    const overline = !!this._workAttributeData.isOverline();\n    let fgColor = this._workAttributeData.getFgColor();\n    let fgColorMode = this._workAttributeData.getFgColorMode();\n    let bgColor = this._workAttributeData.getBgColor();\n    let bgColorMode = this._workAttributeData.getBgColorMode();\n    if (inverse) {\n      const temp = fgColor;\n      fgColor = bgColor;\n      bgColor = temp;\n      const temp2 = fgColorMode;\n      fgColorMode = bgColorMode;\n      bgColorMode = temp2;\n    }\n\n    // draw the background\n    const backgroundColor = this._getBackgroundColor(bgColorMode, bgColor, inverse, dim);\n    // Use a 'copy' composite operation to clear any existing glyph out of _tmpCtxWithAlpha,\n    // regardless of transparency in backgroundColor\n    this._tmpCtx.globalCompositeOperation = 'copy';\n    this._tmpCtx.fillStyle = backgroundColor.css;\n    this._tmpCtx.fillRect(0, 0, this._tmpCanvas.width, this._tmpCanvas.height);\n    this._tmpCtx.globalCompositeOperation = 'source-over';\n\n    // draw the foreground/glyph\n    const fontWeight = bold ? this._config.fontWeightBold : this._config.fontWeight;\n    const fontStyle = italic ? 'italic' : '';\n    this._tmpCtx.font =\n      `${fontStyle} ${fontWeight} ${this._config.fontSize * this._config.devicePixelRatio}px ${this._config.fontFamily}`;\n    this._tmpCtx.textBaseline = TEXT_BASELINE;\n\n    const powerlineGlyph = chars.length === 1 && isPowerlineGlyph(chars.charCodeAt(0));\n    const restrictedPowerlineGlyph = chars.length === 1 && isRestrictedPowerlineGlyph(chars.charCodeAt(0));\n    const foregroundColor = this._getForegroundColor(bg, bgColorMode, bgColor, fg, fgColorMode, fgColor, inverse, dim, bold, excludeFromContrastRatioDemands(chars.charCodeAt(0)));\n    this._tmpCtx.fillStyle = foregroundColor.css;\n\n    // For powerline glyphs left/top padding is excluded (https://github.com/microsoft/vscode/issues/120129)\n    const padding = restrictedPowerlineGlyph ? 0 : TMP_CANVAS_GLYPH_PADDING * 2;\n\n    // Draw custom characters if applicable\n    let customGlyph = false;\n    if (this._config.customGlyphs !== false) {\n      customGlyph = tryDrawCustomChar(this._tmpCtx, chars, padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight, this._config.fontSize, this._config.devicePixelRatio);\n    }\n\n    // Whether to clear pixels based on a threshold difference between the glyph color and the\n    // background color. This should be disabled when the glyph contains multiple colors such as\n    // underline colors to prevent important colors could get cleared.\n    let enableClearThresholdCheck = !powerlineGlyph;\n\n    let chWidth: number;\n    if (typeof codeOrChars === 'number') {\n      chWidth = this._unicodeService.wcwidth(codeOrChars);\n    } else {\n      chWidth = this._unicodeService.getStringCellWidth(codeOrChars);\n    }\n\n    // Draw underline\n    if (underline) {\n      this._tmpCtx.save();\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 15));\n      // When the line width is odd, draw at a 0.5 position\n      const yOffset = lineWidth % 2 === 1 ? 0.5 : 0;\n      this._tmpCtx.lineWidth = lineWidth;\n\n      // Underline color\n      if (this._workAttributeData.isUnderlineColorDefault()) {\n        this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      } else if (this._workAttributeData.isUnderlineColorRGB()) {\n        enableClearThresholdCheck = false;\n        this._tmpCtx.strokeStyle = `rgb(${AttributeData.toColorRGB(this._workAttributeData.getUnderlineColor()).join(',')})`;\n      } else {\n        enableClearThresholdCheck = false;\n        let fg = this._workAttributeData.getUnderlineColor();\n        if (this._config.drawBoldTextInBrightColors && this._workAttributeData.isBold() && fg < 8) {\n          fg += 8;\n        }\n        this._tmpCtx.strokeStyle = this._getColorFromAnsiIndex(fg).css;\n      }\n\n      // Underline style/stroke\n      this._tmpCtx.beginPath();\n      const xLeft = padding;\n      const yTop = Math.ceil(padding + this._config.deviceCharHeight) - yOffset - (restrictToCellHeight ? lineWidth * 2 : 0);\n      const yMid = yTop + lineWidth;\n      const yBot = yTop + lineWidth * 2;\n\n      for (let i = 0; i < chWidth; i++) {\n        this._tmpCtx.save();\n        const xChLeft = xLeft + i * this._config.deviceCellWidth;\n        const xChRight = xLeft + (i + 1) * this._config.deviceCellWidth;\n        const xChMid = xChLeft + this._config.deviceCellWidth / 2;\n        switch (this._workAttributeData.extended.underlineStyle) {\n          case UnderlineStyle.DOUBLE:\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            this._tmpCtx.moveTo(xChLeft, yBot);\n            this._tmpCtx.lineTo(xChRight, yBot);\n            break;\n          case UnderlineStyle.CURLY:\n            // Choose the bezier top and bottom based on the device pixel ratio, the curly line is\n            // made taller when the line width is  as otherwise it's not very clear otherwise.\n            const yCurlyBot = lineWidth <= 1 ? yBot : Math.ceil(padding + this._config.deviceCharHeight - lineWidth / 2) - yOffset;\n            const yCurlyTop = lineWidth <= 1 ? yTop : Math.ceil(padding + this._config.deviceCharHeight + lineWidth / 2) - yOffset;\n            // Clip the left and right edges of the underline such that it can be drawn just outside\n            // the edge of the cell to ensure a continuous stroke when there are multiple underlined\n            // glyphs adjacent to one another.\n            const clipRegion = new Path2D();\n            clipRegion.rect(xChLeft, yTop, this._config.deviceCellWidth, yBot - yTop);\n            this._tmpCtx.clip(clipRegion);\n            // Start 1/2 cell before and end 1/2 cells after to ensure a smooth curve with other\n            // cells\n            this._tmpCtx.moveTo(xChLeft - this._config.deviceCellWidth / 2, yMid);\n            this._tmpCtx.bezierCurveTo(\n              xChLeft - this._config.deviceCellWidth / 2, yCurlyTop,\n              xChLeft, yCurlyTop,\n              xChLeft, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChLeft, yCurlyBot,\n              xChMid, yCurlyBot,\n              xChMid, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChMid, yCurlyTop,\n              xChRight, yCurlyTop,\n              xChRight, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChRight, yCurlyBot,\n              xChRight + this._config.deviceCellWidth / 2, yCurlyBot,\n              xChRight + this._config.deviceCellWidth / 2, yMid\n            );\n            break;\n          case UnderlineStyle.DOTTED:\n            this._tmpCtx.setLineDash([Math.round(lineWidth), Math.round(lineWidth)]);\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n          case UnderlineStyle.DASHED:\n            this._tmpCtx.setLineDash([this._config.devicePixelRatio * 4, this._config.devicePixelRatio * 3]);\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n          case UnderlineStyle.SINGLE:\n          default:\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n        }\n        this._tmpCtx.stroke();\n        this._tmpCtx.restore();\n      }\n      this._tmpCtx.restore();\n\n      // Draw stroke in the background color for non custom characters in order to give an outline\n      // between the text and the underline. Only do this when font size is >= 12 as the underline\n      // looks odd when the font size is too small\n      if (!customGlyph && this._config.fontSize >= 12) {\n        // This only works when transparency is disabled because it's not clear how to clear stroked\n        // text\n        if (!this._config.allowTransparency && chars !== ' ') {\n          // Measure the text, only draw the stroke if there is a descent beyond an alphabetic text\n          // baseline\n          this._tmpCtx.save();\n          this._tmpCtx.textBaseline = 'alphabetic';\n          const metrics = this._tmpCtx.measureText(chars);\n          this._tmpCtx.restore();\n          if ('actualBoundingBoxDescent' in metrics && metrics.actualBoundingBoxDescent > 0) {\n            // This translates to 1/2 the line width in either direction\n            this._tmpCtx.save();\n            // Clip the region to only draw in valid pixels near the underline to avoid a slight\n            // outline around the whole glyph, as well as additional pixels in the glyph at the top\n            // which would increase GPU memory demands\n            const clipRegion = new Path2D();\n            clipRegion.rect(xLeft, yTop - Math.ceil(lineWidth / 2), this._config.deviceCellWidth * chWidth, yBot - yTop + Math.ceil(lineWidth / 2));\n            this._tmpCtx.clip(clipRegion);\n            this._tmpCtx.lineWidth = this._config.devicePixelRatio * 3;\n            this._tmpCtx.strokeStyle = backgroundColor.css;\n            this._tmpCtx.strokeText(chars, padding, padding + this._config.deviceCharHeight);\n            this._tmpCtx.restore();\n          }\n        }\n      }\n    }\n\n    // Overline\n    if (overline) {\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 15));\n      const yOffset = lineWidth % 2 === 1 ? 0.5 : 0;\n      this._tmpCtx.lineWidth = lineWidth;\n      this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      this._tmpCtx.beginPath();\n      this._tmpCtx.moveTo(padding, padding + yOffset);\n      this._tmpCtx.lineTo(padding + this._config.deviceCharWidth * chWidth, padding + yOffset);\n      this._tmpCtx.stroke();\n    }\n\n    // Draw the character\n    if (!customGlyph) {\n      this._tmpCtx.fillText(chars, padding, padding + this._config.deviceCharHeight);\n    }\n\n    // If this character is underscore and beyond the cell bounds, shift it up until it is visible\n    // even on the bottom row, try for a maximum of 5 pixels.\n    if (chars === '_' && !this._config.allowTransparency) {\n      let isBeyondCellBounds = clearColor(this._tmpCtx.getImageData(padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight), backgroundColor, foregroundColor, enableClearThresholdCheck);\n      if (isBeyondCellBounds) {\n        for (let offset = 1; offset <= 5; offset++) {\n          this._tmpCtx.save();\n          this._tmpCtx.fillStyle = backgroundColor.css;\n          this._tmpCtx.fillRect(0, 0, this._tmpCanvas.width, this._tmpCanvas.height);\n          this._tmpCtx.restore();\n          this._tmpCtx.fillText(chars, padding, padding + this._config.deviceCharHeight - offset);\n          isBeyondCellBounds = clearColor(this._tmpCtx.getImageData(padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight), backgroundColor, foregroundColor, enableClearThresholdCheck);\n          if (!isBeyondCellBounds) {\n            break;\n          }\n        }\n      }\n    }\n\n    // Draw strokethrough\n    if (strikethrough) {\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 10));\n      const yOffset = this._tmpCtx.lineWidth % 2 === 1 ? 0.5 : 0; // When the width is odd, draw at 0.5 position\n      this._tmpCtx.lineWidth = lineWidth;\n      this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      this._tmpCtx.beginPath();\n      this._tmpCtx.moveTo(padding, padding + Math.floor(this._config.deviceCharHeight / 2) - yOffset);\n      this._tmpCtx.lineTo(padding + this._config.deviceCharWidth * chWidth, padding + Math.floor(this._config.deviceCharHeight / 2) - yOffset);\n      this._tmpCtx.stroke();\n    }\n\n    this._tmpCtx.restore();\n\n    // clear the background from the character to avoid issues with drawing over the previous\n    // character if it extends past it's bounds\n    const imageData = this._tmpCtx.getImageData(\n      0, 0, this._tmpCanvas.width, this._tmpCanvas.height\n    );\n\n    // Clear out the background color and determine if the glyph is empty.\n    let isEmpty: boolean;\n    if (!this._config.allowTransparency) {\n      isEmpty = clearColor(imageData, backgroundColor, foregroundColor, enableClearThresholdCheck);\n    } else {\n      isEmpty = checkCompletelyTransparent(imageData);\n    }\n\n    // Handle empty glyphs\n    if (isEmpty) {\n      return NULL_RASTERIZED_GLYPH;\n    }\n\n    const rasterizedGlyph = this._findGlyphBoundingBox(imageData, this._workBoundingBox, allowedWidth, restrictedPowerlineGlyph, customGlyph, padding);\n\n    // Find the best atlas row to use\n    let activePage: AtlasPage;\n    let activeRow: ICharAtlasActiveRow;\n    while (true) {\n      // If there are no active pages (the last smallest 4 were merged), create a new one\n      if (this._activePages.length === 0) {\n        const newPage = this._createNewPage();\n        activePage = newPage;\n        activeRow = newPage.currentRow;\n        activeRow.height = rasterizedGlyph.size.y;\n        break;\n      }\n\n      // Get the best current row from all active pages\n      activePage = this._activePages[this._activePages.length - 1];\n      activeRow = activePage.currentRow;\n      for (const p of this._activePages) {\n        if (rasterizedGlyph.size.y <= p.currentRow.height) {\n          activePage = p;\n          activeRow = p.currentRow;\n        }\n      }\n\n      // TODO: This algorithm could be simplified:\n      // - Search for the page with ROW_PIXEL_THRESHOLD in mind\n      // - Keep track of current/fixed rows in a Map\n\n      // Replace the best current row with a fixed row if there is one at least as good as the\n      // current row. Search in reverse to prioritize filling in older pages.\n      for (let i = this._activePages.length - 1; i >= 0; i--) {\n        for (const row of this._activePages[i].fixedRows) {\n          if (row.height <= activeRow.height && rasterizedGlyph.size.y <= row.height) {\n            activePage = this._activePages[i];\n            activeRow = row;\n          }\n        }\n      }\n\n      // Create a new page if too much vertical space would be wasted or there is not enough room\n      // left in the page. The previous active row will become fixed in the process as it now has a\n      // fixed height\n      if (activeRow.y + rasterizedGlyph.size.y >= activePage.canvas.height || activeRow.height > rasterizedGlyph.size.y + Constants.ROW_PIXEL_THRESHOLD) {\n        // Create the new fixed height row, creating a new page if there isn't enough room on the\n        // current page\n        let wasPageAndRowFound = false;\n        if (activePage.currentRow.y + activePage.currentRow.height + rasterizedGlyph.size.y >= activePage.canvas.height) {\n          // Find the first page with room to create the new row on\n          let candidatePage: AtlasPage | undefined;\n          for (const p of this._activePages) {\n            if (p.currentRow.y + p.currentRow.height + rasterizedGlyph.size.y < p.canvas.height) {\n              candidatePage = p;\n              break;\n            }\n          }\n          if (candidatePage) {\n            activePage = candidatePage;\n          } else {\n            // Before creating a new atlas page that would trigger a page merge, check if the\n            // current active row is sufficient when ignoring the ROW_PIXEL_THRESHOLD. This will\n            // improve texture utilization by using the available space before the page is merged\n            // and becomes static.\n            if (\n              TextureAtlas.maxAtlasPages &&\n              this._pages.length >= TextureAtlas.maxAtlasPages &&\n              activeRow.y + rasterizedGlyph.size.y <= activePage.canvas.height &&\n              activeRow.height >= rasterizedGlyph.size.y &&\n              activeRow.x + rasterizedGlyph.size.x <= activePage.canvas.width\n            ) {\n              // activePage and activeRow is already valid\n              wasPageAndRowFound = true;\n            } else {\n              // Create a new page if there is no room\n              const newPage = this._createNewPage();\n              activePage = newPage;\n              activeRow = newPage.currentRow;\n              activeRow.height = rasterizedGlyph.size.y;\n              wasPageAndRowFound = true;\n            }\n          }\n        }\n        if (!wasPageAndRowFound) {\n          // Fix the current row as the new row is being added below\n          if (activePage.currentRow.height > 0) {\n            activePage.fixedRows.push(activePage.currentRow);\n          }\n          activeRow = {\n            x: 0,\n            y: activePage.currentRow.y + activePage.currentRow.height,\n            height: rasterizedGlyph.size.y\n          };\n          activePage.fixedRows.push(activeRow);\n\n          // Create the new current row below the new fixed height row\n          activePage.currentRow = {\n            x: 0,\n            y: activeRow.y + activeRow.height,\n            height: 0\n          };\n        }\n        // TODO: Remove pages from _activePages when all rows are filled\n      }\n\n      // Exit the loop if there is enough room in the row\n      if (activeRow.x + rasterizedGlyph.size.x <= activePage.canvas.width) {\n        break;\n      }\n\n      // If there is not enough room in the current row, finish it and try again\n      if (activeRow === activePage.currentRow) {\n        activeRow.x = 0;\n        activeRow.y += activeRow.height;\n        activeRow.height = 0;\n      } else {\n        activePage.fixedRows.splice(activePage.fixedRows.indexOf(activeRow), 1);\n      }\n    }\n\n    // Record texture position\n    rasterizedGlyph.texturePage = this._pages.indexOf(activePage);\n    rasterizedGlyph.texturePosition.x = activeRow.x;\n    rasterizedGlyph.texturePosition.y = activeRow.y;\n    rasterizedGlyph.texturePositionClipSpace.x = activeRow.x / activePage.canvas.width;\n    rasterizedGlyph.texturePositionClipSpace.y = activeRow.y / activePage.canvas.height;\n\n    // Fix the clipspace position as pages may be of differing size\n    rasterizedGlyph.sizeClipSpace.x /= activePage.canvas.width;\n    rasterizedGlyph.sizeClipSpace.y /= activePage.canvas.height;\n\n    // Update atlas current row, for fixed rows the glyph height will never be larger than the row\n    // height\n    activeRow.height = Math.max(activeRow.height, rasterizedGlyph.size.y);\n    activeRow.x += rasterizedGlyph.size.x;\n\n    // putImageData doesn't do any blending, so it will overwrite any existing cache entry for us\n    activePage.ctx.putImageData(\n      imageData,\n      rasterizedGlyph.texturePosition.x - this._workBoundingBox.left,\n      rasterizedGlyph.texturePosition.y - this._workBoundingBox.top,\n      this._workBoundingBox.left,\n      this._workBoundingBox.top,\n      rasterizedGlyph.size.x,\n      rasterizedGlyph.size.y\n    );\n    activePage.addGlyph(rasterizedGlyph);\n    activePage.version++;\n\n    return rasterizedGlyph;\n  }\n\n  /**\n   * Given an ImageData object, find the bounding box of the non-transparent\n   * portion of the texture and return an IRasterizedGlyph with these\n   * dimensions.\n   * @param imageData The image data to read.\n   * @param boundingBox An IBoundingBox to put the clipped bounding box values.\n   */\n  private _findGlyphBoundingBox(imageData: ImageData, boundingBox: IBoundingBox, allowedWidth: number, restrictedGlyph: boolean, customGlyph: boolean, padding: number): IRasterizedGlyph {\n    boundingBox.top = 0;\n    const height = restrictedGlyph ? this._config.deviceCellHeight : this._tmpCanvas.height;\n    const width = restrictedGlyph ? this._config.deviceCellWidth : allowedWidth;\n    let found = false;\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.top = y;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.left = 0;\n    found = false;\n    for (let x = 0; x < padding + width; x++) {\n      for (let y = 0; y < height; y++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.left = x;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.right = width;\n    found = false;\n    for (let x = padding + width - 1; x >= padding; x--) {\n      for (let y = 0; y < height; y++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.right = x;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.bottom = height;\n    found = false;\n    for (let y = height - 1; y >= 0; y--) {\n      for (let x = 0; x < width; x++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.bottom = y;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    return {\n      texturePage: 0,\n      texturePosition: { x: 0, y: 0 },\n      texturePositionClipSpace: { x: 0, y: 0 },\n      size: {\n        x: boundingBox.right - boundingBox.left + 1,\n        y: boundingBox.bottom - boundingBox.top + 1\n      },\n      sizeClipSpace: {\n        x: (boundingBox.right - boundingBox.left + 1),\n        y: (boundingBox.bottom - boundingBox.top + 1)\n      },\n      offset: {\n        x: -boundingBox.left + padding + ((restrictedGlyph || customGlyph) ? Math.floor((this._config.deviceCellWidth - this._config.deviceCharWidth) / 2) : 0),\n        y: -boundingBox.top + padding + ((restrictedGlyph || customGlyph) ? this._config.lineHeight === 1 ? 0 : Math.round((this._config.deviceCellHeight - this._config.deviceCharHeight) / 2) : 0)\n      }\n    };\n  }\n}\n\nclass AtlasPage {\n  public readonly canvas: HTMLCanvasElement;\n  public readonly ctx: CanvasRenderingContext2D;\n\n  private _usedPixels: number = 0;\n  public get percentageUsed(): number { return this._usedPixels / (this.canvas.width * this.canvas.height); }\n\n  private readonly _glyphs: IRasterizedGlyph[] = [];\n  public get glyphs(): ReadonlyArray<IRasterizedGlyph> { return this._glyphs; }\n  public addGlyph(glyph: IRasterizedGlyph): void {\n    this._glyphs.push(glyph);\n    this._usedPixels += glyph.size.x * glyph.size.y;\n  }\n\n  /**\n   * Used to check whether the canvas of the atlas page has changed.\n   */\n  public version = 0;\n\n  // Texture atlas current positioning data. The texture packing strategy used is to fill from\n  // left-to-right and top-to-bottom. When the glyph being written is less than half of the current\n  // row's height, the following happens:\n  //\n  // - The current row becomes the fixed height row A\n  // - A new fixed height row B the exact size of the glyph is created below the current row\n  // - A new dynamic height current row is created below B\n  //\n  // This strategy does a good job preventing space being wasted for very short glyphs such as\n  // underscores, hyphens etc. or those with underlines rendered.\n  public currentRow: ICharAtlasActiveRow = {\n    x: 0,\n    y: 0,\n    height: 0\n  };\n  public readonly fixedRows: ICharAtlasActiveRow[] = [];\n\n  constructor(\n    document: Document,\n    size: number,\n    sourcePages?: AtlasPage[]\n  ) {\n    if (sourcePages) {\n      for (const p of sourcePages) {\n        this._glyphs.push(...p.glyphs);\n        this._usedPixels += p._usedPixels;\n      }\n    }\n    this.canvas = createCanvas(document, size, size);\n    // The canvas needs alpha because we use clearColor to convert the background color to alpha.\n    // It might also contain some characters with transparent backgrounds if allowTransparency is\n    // set.\n    this.ctx = throwIfFalsy(this.canvas.getContext('2d', { alpha: true }));\n  }\n\n  public clear(): void {\n    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    this.currentRow.x = 0;\n    this.currentRow.y = 0;\n    this.currentRow.height = 0;\n    this.fixedRows.length = 0;\n    this.version++;\n  }\n}\n\n/**\n * Makes a particular rgb color and colors that are nearly the same in an ImageData completely\n * transparent.\n * @returns True if the result is \"empty\", meaning all pixels are fully transparent.\n */\nfunction clearColor(imageData: ImageData, bg: IColor, fg: IColor, enableThresholdCheck: boolean): boolean {\n  // Get color channels\n  const r = bg.rgba >>> 24;\n  const g = bg.rgba >>> 16 & 0xFF;\n  const b = bg.rgba >>> 8 & 0xFF;\n  const fgR = fg.rgba >>> 24;\n  const fgG = fg.rgba >>> 16 & 0xFF;\n  const fgB = fg.rgba >>> 8 & 0xFF;\n\n  // Calculate a threshold that when below a color will be treated as transpart when the sum of\n  // channel value differs. This helps improve rendering when glyphs overlap with others. This\n  // threshold is calculated relative to the difference between the background and foreground to\n  // ensure important details of the glyph are always shown, even when the contrast ratio is low.\n  // The number 12 is largely arbitrary to ensure the pixels that escape the cell in the test case\n  // were covered (fg=#8ae234, bg=#c4a000).\n  const threshold = Math.floor((Math.abs(r - fgR) + Math.abs(g - fgG) + Math.abs(b - fgB)) / 12);\n\n  // Set alpha channel of relevent pixels to 0\n  let isEmpty = true;\n  for (let offset = 0; offset < imageData.data.length; offset += 4) {\n    // Check exact match\n    if (imageData.data[offset] === r &&\n        imageData.data[offset + 1] === g &&\n        imageData.data[offset + 2] === b) {\n      imageData.data[offset + 3] = 0;\n    } else {\n      // Check the threshold based difference\n      if (enableThresholdCheck &&\n          (Math.abs(imageData.data[offset] - r) +\n          Math.abs(imageData.data[offset + 1] - g) +\n          Math.abs(imageData.data[offset + 2] - b)) < threshold) {\n        imageData.data[offset + 3] = 0;\n      } else {\n        isEmpty = false;\n      }\n    }\n  }\n\n  return isEmpty;\n}\n\nfunction checkCompletelyTransparent(imageData: ImageData): boolean {\n  for (let offset = 0; offset < imageData.data.length; offset += 4) {\n    if (imageData.data[offset + 3] > 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction createCanvas(document: Document, width: number, height: number): HTMLCanvasElement {\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isNode } from 'common/Platform';\nimport { IColor, IColorRGB } from 'common/Types';\n\nlet $r = 0;\nlet $g = 0;\nlet $b = 0;\nlet $a = 0;\n\nexport const NULL_COLOR: IColor = {\n  css: '#00000000',\n  rgba: 0\n};\n\n/**\n * Helper functions where the source type is \"channels\" (individual color channels as numbers).\n */\nexport namespace channels {\n  export function toCss(r: number, g: number, b: number, a?: number): string {\n    if (a !== undefined) {\n      return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}${toPaddedHex(a)}`;\n    }\n    return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}`;\n  }\n\n  export function toRgba(r: number, g: number, b: number, a: number = 0xFF): number {\n    // Note: The aggregated number is RGBA32 (BE), thus needs to be converted to ABGR32\n    // on LE systems, before it can be used for direct 32-bit buffer writes.\n    // >>> 0 forces an unsigned int\n    return (r << 24 | g << 16 | b << 8 | a) >>> 0;\n  }\n}\n\n/**\n * Helper functions where the source type is `IColor`.\n */\nexport namespace color {\n  export function blend(bg: IColor, fg: IColor): IColor {\n    $a = (fg.rgba & 0xFF) / 255;\n    if ($a === 1) {\n      return {\n        css: fg.css,\n        rgba: fg.rgba\n      };\n    }\n    const fgR = (fg.rgba >> 24) & 0xFF;\n    const fgG = (fg.rgba >> 16) & 0xFF;\n    const fgB = (fg.rgba >> 8) & 0xFF;\n    const bgR = (bg.rgba >> 24) & 0xFF;\n    const bgG = (bg.rgba >> 16) & 0xFF;\n    const bgB = (bg.rgba >> 8) & 0xFF;\n    $r = bgR + Math.round((fgR - bgR) * $a);\n    $g = bgG + Math.round((fgG - bgG) * $a);\n    $b = bgB + Math.round((fgB - bgB) * $a);\n    const css = channels.toCss($r, $g, $b);\n    const rgba = channels.toRgba($r, $g, $b);\n    return { css, rgba };\n  }\n\n  export function isOpaque(color: IColor): boolean {\n    return (color.rgba & 0xFF) === 0xFF;\n  }\n\n  export function ensureContrastRatio(bg: IColor, fg: IColor, ratio: number): IColor | undefined {\n    const result = rgba.ensureContrastRatio(bg.rgba, fg.rgba, ratio);\n    if (!result) {\n      return undefined;\n    }\n    return rgba.toColor(\n      (result >> 24 & 0xFF),\n      (result >> 16 & 0xFF),\n      (result >> 8  & 0xFF)\n    );\n  }\n\n  export function opaque(color: IColor): IColor {\n    const rgbaColor = (color.rgba | 0xFF) >>> 0;\n    [$r, $g, $b] = rgba.toChannels(rgbaColor);\n    return {\n      css: channels.toCss($r, $g, $b),\n      rgba: rgbaColor\n    };\n  }\n\n  export function opacity(color: IColor, opacity: number): IColor {\n    $a = Math.round(opacity * 0xFF);\n    [$r, $g, $b] = rgba.toChannels(color.rgba);\n    return {\n      css: channels.toCss($r, $g, $b, $a),\n      rgba: channels.toRgba($r, $g, $b, $a)\n    };\n  }\n\n  export function multiplyOpacity(color: IColor, factor: number): IColor {\n    $a = color.rgba & 0xFF;\n    return opacity(color, ($a * factor) / 0xFF);\n  }\n\n  export function toColorRGB(color: IColor): IColorRGB {\n    return [(color.rgba >> 24) & 0xFF, (color.rgba >> 16) & 0xFF, (color.rgba >> 8) & 0xFF];\n  }\n}\n\n/**\n * Helper functions where the source type is \"css\" (string: '#rgb', '#rgba', '#rrggbb',\n * '#rrggbbaa').\n */\nexport namespace css {\n  let $ctx: CanvasRenderingContext2D | undefined;\n  let $litmusColor: CanvasGradient | undefined;\n  if (!isNode) {\n    const canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    const ctx = canvas.getContext('2d', {\n      willReadFrequently: true\n    });\n    if (ctx) {\n      $ctx = ctx;\n      $ctx.globalCompositeOperation = 'copy';\n      $litmusColor = $ctx.createLinearGradient(0, 0, 1, 1);\n    }\n  }\n\n  /**\n   * Converts a css string to an IColor, this should handle all valid CSS color strings and will\n   * throw if it's invalid. The ideal format to use is `#rrggbb[aa]` as it's the fastest to parse.\n   *\n   * Only `#rgb[a]`, `#rrggbb[aa]`, `rgb()` and `rgba()` formats are supported when run in a Node\n   * environment.\n   */\n  export function toColor(css: string): IColor {\n    // Formats: #rgb[a] and #rrggbb[aa]\n    if (css.match(/#[\\da-f]{3,8}/i)) {\n      switch (css.length) {\n        case 4: { // #rgb\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          return rgba.toColor($r, $g, $b);\n        }\n        case 5: { // #rgba\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          $a = parseInt(css.slice(4, 5).repeat(2), 16);\n          return rgba.toColor($r, $g, $b, $a);\n        }\n        case 7: // #rrggbb\n          return {\n            css,\n            rgba: (parseInt(css.slice(1), 16) << 8 | 0xFF) >>> 0\n          };\n        case 9: // #rrggbbaa\n          return {\n            css,\n            rgba: parseInt(css.slice(1), 16) >>> 0\n          };\n      }\n    }\n\n    // Formats: rgb() or rgba()\n    const rgbaMatch = css.match(/rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*(,\\s*(0|1|\\d?\\.(\\d+))\\s*)?\\)/);\n    if (rgbaMatch) {\n      $r = parseInt(rgbaMatch[1]);\n      $g = parseInt(rgbaMatch[2]);\n      $b = parseInt(rgbaMatch[3]);\n      $a = Math.round((rgbaMatch[5] === undefined ? 1 : parseFloat(rgbaMatch[5])) * 0xFF);\n      return rgba.toColor($r, $g, $b, $a);\n    }\n\n    // Validate the context is available for canvas-based color parsing\n    if (!$ctx || !$litmusColor) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Validate the color using canvas fillStyle\n    // See https://html.spec.whatwg.org/multipage/canvas.html#fill-and-stroke-styles\n    $ctx.fillStyle = $litmusColor;\n    $ctx.fillStyle = css;\n    if (typeof $ctx.fillStyle !== 'string') {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    $ctx.fillRect(0, 0, 1, 1);\n    [$r, $g, $b, $a] = $ctx.getImageData(0, 0, 1, 1).data;\n\n    // Validate the color is non-transparent as color hue gets lost when drawn to the canvas\n    if ($a !== 0xFF) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Extract the color from the canvas' fillStyle property which exposes the color value in rgba()\n    // format\n    // See https://html.spec.whatwg.org/multipage/canvas.html#serialisation-of-a-color\n    return {\n      rgba: channels.toRgba($r, $g, $b, $a),\n      css\n    };\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgb\" (number: 0xrrggbb).\n */\nexport namespace rgb {\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param rgb The color to use.\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance(rgb: number): number {\n    return relativeLuminance2(\n      (rgb >> 16) & 0xFF,\n      (rgb >> 8 ) & 0xFF,\n      (rgb      ) & 0xFF);\n  }\n\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param r The red channel (0x00 to 0xFF).\n   * @param g The green channel (0x00 to 0xFF).\n   * @param b The blue channel (0x00 to 0xFF).\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance2(r: number, g: number, b: number): number {\n    const rs = r / 255;\n    const gs = g / 255;\n    const bs = b / 255;\n    const rr = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);\n    const rg = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);\n    const rb = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);\n    return rr * 0.2126 + rg * 0.7152 + rb * 0.0722;\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgba\" (number: 0xrrggbbaa).\n */\nexport namespace rgba {\n  /**\n   * Given a foreground color and a background color, either increase or reduce the luminance of the\n   * foreground color until the specified contrast ratio is met. If pure white or black is hit\n   * without the contrast ratio being met, go the other direction using the background color as the\n   * foreground color and take either the first or second result depending on which has the higher\n   * contrast ratio.\n   *\n   * `undefined` will be returned if the contrast ratio is already met.\n   *\n   * @param bgRgba The background color in rgba format.\n   * @param fgRgba The foreground color in rgba format.\n   * @param ratio The contrast ratio to achieve.\n   */\n  export function ensureContrastRatio(bgRgba: number, fgRgba: number, ratio: number): number | undefined {\n    const bgL = rgb.relativeLuminance(bgRgba >> 8);\n    const fgL = rgb.relativeLuminance(fgRgba >> 8);\n    const cr = contrastRatio(bgL, fgL);\n    if (cr < ratio) {\n      if (fgL < bgL) {\n        const resultA = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n        if (resultARatio < ratio) {\n          const resultB = increaseLuminance(bgRgba, fgRgba, ratio);\n          const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n          return resultARatio > resultBRatio ? resultA : resultB;\n        }\n        return resultA;\n      }\n      const resultA = increaseLuminance(bgRgba, fgRgba, ratio);\n      const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n      if (resultARatio < ratio) {\n        const resultB = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n        return resultARatio > resultBRatio ? resultA : resultB;\n      }\n      return resultA;\n    }\n    return undefined;\n  }\n\n  export function reduceLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to reducing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR > 0 || fgG > 0 || fgB > 0)) {\n      // Reduce by 10% until the ratio is hit\n      fgR -= Math.max(0, Math.ceil(fgR * 0.1));\n      fgG -= Math.max(0, Math.ceil(fgG * 0.1));\n      fgB -= Math.max(0, Math.ceil(fgB * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  export function increaseLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to increasing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR < 0xFF || fgG < 0xFF || fgB < 0xFF)) {\n      // Increase by 10% until the ratio is hit\n      fgR = Math.min(0xFF, fgR + Math.ceil((255 - fgR) * 0.1));\n      fgG = Math.min(0xFF, fgG + Math.ceil((255 - fgG) * 0.1));\n      fgB = Math.min(0xFF, fgB + Math.ceil((255 - fgB) * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  // FIXME: Move this to channels NS?\n  export function toChannels(value: number): [number, number, number, number] {\n    return [(value >> 24) & 0xFF, (value >> 16) & 0xFF, (value >> 8) & 0xFF, value & 0xFF];\n  }\n\n  export function toColor(r: number, g: number, b: number, a?: number): IColor {\n    return {\n      css: channels.toCss(r, g, b, a),\n      rgba: channels.toRgba(r, g, b, a)\n    };\n  }\n}\n\nexport function toPaddedHex(c: number): string {\n  const s = c.toString(16);\n  return s.length < 2 ? '0' + s : s;\n}\n\n/**\n * Gets the contrast ratio between two relative luminance values.\n * @param l1 The first relative luminance.\n * @param l2 The first relative luminance.\n * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef\n */\nexport function contrastRatio(l1: number, l2: number): number {\n  if (l1 < l2) {\n    return (l2 + 0.05) / (l1 + 0.05);\n  }\n  return (l1 + 0.05) / (l2 + 0.05);\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\ninterface IListener<T, U = void> {\n  (arg1: T, arg2: U): void;\n}\n\nexport interface IEvent<T, U = void> {\n  (listener: (arg1: T, arg2: U) => any): IDisposable;\n}\n\nexport interface IEventEmitter<T, U = void> {\n  event: IEvent<T, U>;\n  fire(arg1: T, arg2: U): void;\n  dispose(): void;\n}\n\nexport class EventEmitter<T, U = void> implements IEventEmitter<T, U> {\n  private _listeners: IListener<T, U>[] = [];\n  private _event?: IEvent<T, U>;\n  private _disposed: boolean = false;\n\n  public get event(): IEvent<T, U> {\n    if (!this._event) {\n      this._event = (listener: (arg1: T, arg2: U) => any) => {\n        this._listeners.push(listener);\n        const disposable = {\n          dispose: () => {\n            if (!this._disposed) {\n              for (let i = 0; i < this._listeners.length; i++) {\n                if (this._listeners[i] === listener) {\n                  this._listeners.splice(i, 1);\n                  return;\n                }\n              }\n            }\n          }\n        };\n        return disposable;\n      };\n    }\n    return this._event;\n  }\n\n  public fire(arg1: T, arg2: U): void {\n    const queue: IListener<T, U>[] = [];\n    for (let i = 0; i < this._listeners.length; i++) {\n      queue.push(this._listeners[i]);\n    }\n    for (let i = 0; i < queue.length; i++) {\n      queue[i].call(undefined, arg1, arg2);\n    }\n  }\n\n  public dispose(): void {\n    this.clearListeners();\n    this._disposed = true;\n  }\n\n  public clearListeners(): void {\n    if (this._listeners) {\n      this._listeners.length = 0;\n    }\n  }\n}\n\nexport function forwardEvent<T>(from: IEvent<T>, to: IEventEmitter<T>): IDisposable {\n  return from(e => to.fire(e));\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\n/**\n * A base class that can be extended to provide convenience methods for managing the lifecycle of an\n * object and its components.\n */\nexport abstract class Disposable implements IDisposable {\n  protected _disposables: IDisposable[] = [];\n  protected _isDisposed: boolean = false;\n\n  /**\n   * Disposes the object, triggering the `dispose` method on all registered IDisposables.\n   */\n  public dispose(): void {\n    this._isDisposed = true;\n    for (const d of this._disposables) {\n      d.dispose();\n    }\n    this._disposables.length = 0;\n  }\n\n  /**\n   * Registers a disposable object.\n   * @param d The disposable to register.\n   * @returns The disposable.\n   */\n  public register<T extends IDisposable>(d: T): T {\n    this._disposables.push(d);\n    return d;\n  }\n\n  /**\n   * Unregisters a disposable object if it has been registered, if not do\n   * nothing.\n   * @param d The disposable to unregister.\n   */\n  public unregister<T extends IDisposable>(d: T): void {\n    const index = this._disposables.indexOf(d);\n    if (index !== -1) {\n      this._disposables.splice(index, 1);\n    }\n  }\n}\n\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n  private _value?: T;\n  private _isDisposed = false;\n\n  /**\n   * Gets the value if it exists.\n   */\n  public get value(): T | undefined {\n    return this._isDisposed ? undefined : this._value;\n  }\n\n  /**\n   * Sets the value, disposing of the old value if it exists.\n   */\n  public set value(value: T | undefined) {\n    if (this._isDisposed || value === this._value) {\n      return;\n    }\n    this._value?.dispose();\n    this._value = value;\n  }\n\n  /**\n   * Resets the stored value and disposes of the previously stored value.\n   */\n  public clear(): void {\n    this.value = undefined;\n  }\n\n  public dispose(): void {\n    this._isDisposed = true;\n    this._value?.dispose();\n    this._value = undefined;\n  }\n}\n\n/**\n * Wrap a function in a disposable.\n */\nexport function toDisposable(f: () => void): IDisposable {\n  return { dispose: f };\n}\n\n/**\n * Dispose of all disposables in an array and set its length to 0.\n */\nexport function disposeArray(disposables: IDisposable[]): void {\n  for (const d of disposables) {\n    d.dispose();\n  }\n  disposables.length = 0;\n}\n\n/**\n * Creates a disposable that will dispose of an array of disposables when disposed.\n */\nexport function getDisposeArrayDisposable(array: IDisposable[]): IDisposable {\n  return { dispose: () => disposeArray(array) };\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nexport class TwoKeyMap<TFirst extends string | number, TSecond extends string | number, TValue> {\n  private _data: { [bg: string | number]: { [fg: string | number]: TValue | undefined } | undefined } = {};\n\n  public set(first: TFirst, second: TSecond, value: TValue): void {\n    if (!this._data[first]) {\n      this._data[first] = {};\n    }\n    this._data[first as string | number]![second] = value;\n  }\n\n  public get(first: TFirst, second: TSecond): TValue | undefined {\n    return this._data[first as string | number] ? this._data[first as string | number]![second] : undefined;\n  }\n\n  public clear(): void {\n    this._data = {};\n  }\n}\n\nexport class FourKeyMap<TFirst extends string | number, TSecond extends string | number, TThird extends string | number, TFourth extends string | number, TValue> {\n  private _data: TwoKeyMap<TFirst, TSecond, TwoKeyMap<TThird, TFourth, TValue>> = new TwoKeyMap();\n\n  public set(first: TFirst, second: TSecond, third: TThird, fourth: TFourth, value: TValue): void {\n    if (!this._data.get(first, second)) {\n      this._data.set(first, second, new TwoKeyMap());\n    }\n    this._data.get(first, second)!.set(third, fourth, value);\n  }\n\n  public get(first: TFirst, second: TSecond, third: TThird, fourth: TFourth): TValue | undefined {\n    return this._data.get(first, second)?.get(third, fourth);\n  }\n\n  public clear(): void {\n    this._data.clear();\n  }\n}\n", "/**\n * Copyright (c) 2016 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\ninterface INavigator {\n  userAgent: string;\n  language: string;\n  platform: string;\n}\n\n// We're declaring a navigator global here as we expect it in all runtimes (node and browser), but\n// we want this module to live in common.\ndeclare const navigator: INavigator;\n\nexport const isNode = (typeof navigator === 'undefined') ? true : false;\nconst userAgent = (isNode) ? 'node' : navigator.userAgent;\nconst platform = (isNode) ? 'node' : navigator.platform;\n\nexport const isFirefox = userAgent.includes('Firefox');\nexport const isLegacyEdge = userAgent.includes('Edge');\nexport const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);\nexport function getSafariVersion(): number {\n  if (!isSafari) {\n    return 0;\n  }\n  const majorVersion = userAgent.match(/Version\\/(\\d+)/);\n  if (majorVersion === null || majorVersion.length < 2) {\n    return 0;\n  }\n  return parseInt(majorVersion[1]);\n}\n\n// Find the users platform. We use this to interpret the meta key\n// and ISO third level shifts.\n// http://stackoverflow.com/q/19877924/577598\nexport const isMac = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'].includes(platform);\nexport const isIpad = platform === 'iPad';\nexport const isIphone = platform === 'iPhone';\nexport const isWindows = ['Windows', 'Win16', 'Win32', 'WinCE'].includes(platform);\nexport const isLinux = platform.indexOf('Linux') >= 0;\n// Note that when this is true, isLinux will also be true.\nexport const isChromeOS = /\\bCrOS\\b/.test(userAgent);\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isNode } from 'common/Platform';\n\ninterface ITaskQueue {\n  /**\n   * Adds a task to the queue which will run in a future idle callback.\n   * To avoid perceivable stalls on the mainthread, tasks with heavy workload\n   * should split their work into smaller pieces and return `true` to get\n   * called again until the work is done (on falsy return value).\n   */\n  enqueue(task: () => boolean | void): void;\n\n  /**\n   * Flushes the queue, running all remaining tasks synchronously.\n   */\n  flush(): void;\n\n  /**\n   * Clears any remaining tasks from the queue, these will not be run.\n   */\n  clear(): void;\n}\n\ninterface ITaskDeadline {\n  timeRemaining(): number;\n}\ntype CallbackWithDeadline = (deadline: ITaskDeadline) => void;\n\nabstract class TaskQueue implements ITaskQueue {\n  private _tasks: (() => boolean | void)[] = [];\n  private _idleCallback?: number;\n  private _i = 0;\n\n  protected abstract _requestCallback(callback: CallbackWithDeadline): number;\n  protected abstract _cancelCallback(identifier: number): void;\n\n  public enqueue(task: () => boolean | void): void {\n    this._tasks.push(task);\n    this._start();\n  }\n\n  public flush(): void {\n    while (this._i < this._tasks.length) {\n      if (!this._tasks[this._i]()) {\n        this._i++;\n      }\n    }\n    this.clear();\n  }\n\n  public clear(): void {\n    if (this._idleCallback) {\n      this._cancelCallback(this._idleCallback);\n      this._idleCallback = undefined;\n    }\n    this._i = 0;\n    this._tasks.length = 0;\n  }\n\n  private _start(): void {\n    if (!this._idleCallback) {\n      this._idleCallback = this._requestCallback(this._process.bind(this));\n    }\n  }\n\n  private _process(deadline: ITaskDeadline): void {\n    this._idleCallback = undefined;\n    let taskDuration = 0;\n    let longestTask = 0;\n    let lastDeadlineRemaining = deadline.timeRemaining();\n    let deadlineRemaining = 0;\n    while (this._i < this._tasks.length) {\n      taskDuration = Date.now();\n      if (!this._tasks[this._i]()) {\n        this._i++;\n      }\n      // other than performance.now, Date.now might not be stable (changes on wall clock changes),\n      // this is not an issue here as a clock change during a short running task is very unlikely\n      // in case it still happened and leads to negative duration, simply assume 1 msec\n      taskDuration = Math.max(1, Date.now() - taskDuration);\n      longestTask = Math.max(taskDuration, longestTask);\n      // Guess the following task will take a similar time to the longest task in this batch, allow\n      // additional room to try avoid exceeding the deadline\n      deadlineRemaining = deadline.timeRemaining();\n      if (longestTask * 1.5 > deadlineRemaining) {\n        // Warn when the time exceeding the deadline is over 20ms, if this happens in practice the\n        // task should be split into sub-tasks to ensure the UI remains responsive.\n        if (lastDeadlineRemaining - taskDuration < -20) {\n          console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(lastDeadlineRemaining - taskDuration))}ms`);\n        }\n        this._start();\n        return;\n      }\n      lastDeadlineRemaining = deadlineRemaining;\n    }\n    this.clear();\n  }\n}\n\n/**\n * A queue of that runs tasks over several tasks via setTimeout, trying to maintain above 60 frames\n * per second. The tasks will run in the order they are enqueued, but they will run some time later,\n * and care should be taken to ensure they're non-urgent and will not introduce race conditions.\n */\nexport class PriorityTaskQueue extends TaskQueue {\n  protected _requestCallback(callback: CallbackWithDeadline): number {\n    return setTimeout(() => callback(this._createDeadline(16)));\n  }\n\n  protected _cancelCallback(identifier: number): void {\n    clearTimeout(identifier);\n  }\n\n  private _createDeadline(duration: number): ITaskDeadline {\n    const end = Date.now() + duration;\n    return {\n      timeRemaining: () => Math.max(0, end - Date.now())\n    };\n  }\n}\n\nclass IdleTaskQueueInternal extends TaskQueue {\n  protected _requestCallback(callback: IdleRequestCallback): number {\n    return requestIdleCallback(callback);\n  }\n\n  protected _cancelCallback(identifier: number): void {\n    cancelIdleCallback(identifier);\n  }\n}\n\n/**\n * A queue of that runs tasks over several idle callbacks, trying to respect the idle callback's\n * deadline given by the environment. The tasks will run in the order they are enqueued, but they\n * will run some time later, and care should be taken to ensure they're non-urgent and will not\n * introduce race conditions.\n *\n * This reverts to a {@link PriorityTaskQueue} if the environment does not support idle callbacks.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const IdleTaskQueue = (!isNode && 'requestIdleCallback' in window) ? IdleTaskQueueInternal : PriorityTaskQueue;\n\n/**\n * An object that tracks a single debounced task that will run on the next idle frame. When called\n * multiple times, only the last set task will run.\n */\nexport class DebouncedIdleTask {\n  private _queue: ITaskQueue;\n\n  constructor() {\n    this._queue = new IdleTaskQueue();\n  }\n\n  public set(task: () => boolean | void): void {\n    this._queue.clear();\n    this._queue.enqueue(task);\n  }\n\n  public flush(): void {\n    this._queue.flush();\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IAttributeData, IColorRGB, IExtendedAttrs } from 'common/Types';\nimport { Attributes, FgFlags, BgFlags, UnderlineStyle, ExtFlags } from 'common/buffer/Constants';\n\nexport class AttributeData implements IAttributeData {\n  public static toColorRGB(value: number): IColorRGB {\n    return [\n      value >>> Attributes.RED_SHIFT & 255,\n      value >>> Attributes.GREEN_SHIFT & 255,\n      value & 255\n    ];\n  }\n\n  public static fromColorRGB(value: IColorRGB): number {\n    return (value[0] & 255) << Attributes.RED_SHIFT | (value[1] & 255) << Attributes.GREEN_SHIFT | value[2] & 255;\n  }\n\n  public clone(): IAttributeData {\n    const newObj = new AttributeData();\n    newObj.fg = this.fg;\n    newObj.bg = this.bg;\n    newObj.extended = this.extended.clone();\n    return newObj;\n  }\n\n  // data\n  public fg = 0;\n  public bg = 0;\n  public extended: IExtendedAttrs = new ExtendedAttrs();\n\n  // flags\n  public isInverse(): number       { return this.fg & FgFlags.INVERSE; }\n  public isBold(): number          { return this.fg & FgFlags.BOLD; }\n  public isUnderline(): number     {\n    if (this.hasExtendedAttrs() && this.extended.underlineStyle !== UnderlineStyle.NONE) {\n      return 1;\n    }\n    return this.fg & FgFlags.UNDERLINE;\n  }\n  public isBlink(): number         { return this.fg & FgFlags.BLINK; }\n  public isInvisible(): number     { return this.fg & FgFlags.INVISIBLE; }\n  public isItalic(): number        { return this.bg & BgFlags.ITALIC; }\n  public isDim(): number           { return this.bg & BgFlags.DIM; }\n  public isStrikethrough(): number { return this.fg & FgFlags.STRIKETHROUGH; }\n  public isProtected(): number     { return this.bg & BgFlags.PROTECTED; }\n  public isOverline(): number      { return this.bg & BgFlags.OVERLINE; }\n\n  // color modes\n  public getFgColorMode(): number { return this.fg & Attributes.CM_MASK; }\n  public getBgColorMode(): number { return this.bg & Attributes.CM_MASK; }\n  public isFgRGB(): boolean       { return (this.fg & Attributes.CM_MASK) === Attributes.CM_RGB; }\n  public isBgRGB(): boolean       { return (this.bg & Attributes.CM_MASK) === Attributes.CM_RGB; }\n  public isFgPalette(): boolean   { return (this.fg & Attributes.CM_MASK) === Attributes.CM_P16 || (this.fg & Attributes.CM_MASK) === Attributes.CM_P256; }\n  public isBgPalette(): boolean   { return (this.bg & Attributes.CM_MASK) === Attributes.CM_P16 || (this.bg & Attributes.CM_MASK) === Attributes.CM_P256; }\n  public isFgDefault(): boolean   { return (this.fg & Attributes.CM_MASK) === 0; }\n  public isBgDefault(): boolean   { return (this.bg & Attributes.CM_MASK) === 0; }\n  public isAttributeDefault(): boolean { return this.fg === 0 && this.bg === 0; }\n\n  // colors\n  public getFgColor(): number {\n    switch (this.fg & Attributes.CM_MASK) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:  return this.fg & Attributes.PCOLOR_MASK;\n      case Attributes.CM_RGB:   return this.fg & Attributes.RGB_MASK;\n      default:                  return -1;  // CM_DEFAULT defaults to -1\n    }\n  }\n  public getBgColor(): number {\n    switch (this.bg & Attributes.CM_MASK) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:  return this.bg & Attributes.PCOLOR_MASK;\n      case Attributes.CM_RGB:   return this.bg & Attributes.RGB_MASK;\n      default:                  return -1;  // CM_DEFAULT defaults to -1\n    }\n  }\n\n  // extended attrs\n  public hasExtendedAttrs(): number {\n    return this.bg & BgFlags.HAS_EXTENDED;\n  }\n  public updateExtended(): void {\n    if (this.extended.isEmpty()) {\n      this.bg &= ~BgFlags.HAS_EXTENDED;\n    } else {\n      this.bg |= BgFlags.HAS_EXTENDED;\n    }\n  }\n  public getUnderlineColor(): number {\n    if ((this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor) {\n      switch (this.extended.underlineColor & Attributes.CM_MASK) {\n        case Attributes.CM_P16:\n        case Attributes.CM_P256:  return this.extended.underlineColor & Attributes.PCOLOR_MASK;\n        case Attributes.CM_RGB:   return this.extended.underlineColor & Attributes.RGB_MASK;\n        default:                  return this.getFgColor();\n      }\n    }\n    return this.getFgColor();\n  }\n  public getUnderlineColorMode(): number {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? this.extended.underlineColor & Attributes.CM_MASK\n      : this.getFgColorMode();\n  }\n  public isUnderlineColorRGB(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_RGB\n      : this.isFgRGB();\n  }\n  public isUnderlineColorPalette(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_P16\n          || (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_P256\n      : this.isFgPalette();\n  }\n  public isUnderlineColorDefault(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === 0\n      : this.isFgDefault();\n  }\n  public getUnderlineStyle(): UnderlineStyle {\n    return this.fg & FgFlags.UNDERLINE\n      ? (this.bg & BgFlags.HAS_EXTENDED ? this.extended.underlineStyle : UnderlineStyle.SINGLE)\n      : UnderlineStyle.NONE;\n  }\n}\n\n\n/**\n * Extended attributes for a cell.\n * Holds information about different underline styles and color.\n */\nexport class ExtendedAttrs implements IExtendedAttrs {\n  private _ext: number = 0;\n  public get ext(): number {\n    if (this._urlId) {\n      return (\n        (this._ext & ~ExtFlags.UNDERLINE_STYLE) |\n        (this.underlineStyle << 26)\n      );\n    }\n    return this._ext;\n  }\n  public set ext(value: number) { this._ext = value; }\n\n  public get underlineStyle(): UnderlineStyle {\n    // Always return the URL style if it has one\n    if (this._urlId) {\n      return UnderlineStyle.DASHED;\n    }\n    return (this._ext & ExtFlags.UNDERLINE_STYLE) >> 26;\n  }\n  public set underlineStyle(value: UnderlineStyle) {\n    this._ext &= ~ExtFlags.UNDERLINE_STYLE;\n    this._ext |= (value << 26) & ExtFlags.UNDERLINE_STYLE;\n  }\n\n  public get underlineColor(): number {\n    return this._ext & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n  public set underlineColor(value: number) {\n    this._ext &= ~(Attributes.CM_MASK | Attributes.RGB_MASK);\n    this._ext |= value & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n\n  private _urlId: number = 0;\n  public get urlId(): number {\n    return this._urlId;\n  }\n  public set urlId(value: number) {\n    this._urlId = value;\n  }\n\n  constructor(\n    ext: number = 0,\n    urlId: number = 0\n  ) {\n    this._ext = ext;\n    this._urlId = urlId;\n  }\n\n  public clone(): IExtendedAttrs {\n    return new ExtendedAttrs(this._ext, this._urlId);\n  }\n\n  /**\n   * Convenient method to indicate whether the object holds no additional information,\n   * that needs to be persistant in the buffer.\n   */\n  public isEmpty(): boolean {\n    return this.underlineStyle === UnderlineStyle.NONE && this._urlId === 0;\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { CharData, ICellData, IExtendedAttrs } from 'common/Types';\nimport { stringFromCodePoint } from 'common/input/TextDecoder';\nimport { CHAR_DATA_CHAR_INDEX, CHAR_DATA_WIDTH_INDEX, CHAR_DATA_ATTR_INDEX, Content } from 'common/buffer/Constants';\nimport { AttributeData, ExtendedAttrs } from 'common/buffer/AttributeData';\n\n/**\n * CellData - represents a single Cell in the terminal buffer.\n */\nexport class CellData extends AttributeData implements ICellData {\n  /** Helper to create CellData from CharData. */\n  public static fromCharData(value: CharData): CellData {\n    const obj = new CellData();\n    obj.setFromCharData(value);\n    return obj;\n  }\n  /** Primitives from terminal buffer. */\n  public content = 0;\n  public fg = 0;\n  public bg = 0;\n  public extended: IExtendedAttrs = new ExtendedAttrs();\n  public combinedData = '';\n  /** Whether cell contains a combined string. */\n  public isCombined(): number {\n    return this.content & Content.IS_COMBINED_MASK;\n  }\n  /** Width of the cell. */\n  public getWidth(): number {\n    return this.content >> Content.WIDTH_SHIFT;\n  }\n  /** JS string of the content. */\n  public getChars(): string {\n    if (this.content & Content.IS_COMBINED_MASK) {\n      return this.combinedData;\n    }\n    if (this.content & Content.CODEPOINT_MASK) {\n      return stringFromCodePoint(this.content & Content.CODEPOINT_MASK);\n    }\n    return '';\n  }\n  /**\n   * Codepoint of cell\n   * Note this returns the UTF32 codepoint of single chars,\n   * if content is a combined string it returns the codepoint\n   * of the last char in string to be in line with code in CharData.\n   */\n  public getCode(): number {\n    return (this.isCombined())\n      ? this.combinedData.charCodeAt(this.combinedData.length - 1)\n      : this.content & Content.CODEPOINT_MASK;\n  }\n  /** Set data from CharData */\n  public setFromCharData(value: CharData): void {\n    this.fg = value[CHAR_DATA_ATTR_INDEX];\n    this.bg = 0;\n    let combined = false;\n    // surrogates and combined strings need special treatment\n    if (value[CHAR_DATA_CHAR_INDEX].length > 2) {\n      combined = true;\n    }\n    else if (value[CHAR_DATA_CHAR_INDEX].length === 2) {\n      const code = value[CHAR_DATA_CHAR_INDEX].charCodeAt(0);\n      // if the 2-char string is a surrogate create single codepoint\n      // everything else is combined\n      if (0xD800 <= code && code <= 0xDBFF) {\n        const second = value[CHAR_DATA_CHAR_INDEX].charCodeAt(1);\n        if (0xDC00 <= second && second <= 0xDFFF) {\n          this.content = ((code - 0xD800) * 0x400 + second - 0xDC00 + 0x10000) | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n        }\n        else {\n          combined = true;\n        }\n      }\n      else {\n        combined = true;\n      }\n    }\n    else {\n      this.content = value[CHAR_DATA_CHAR_INDEX].charCodeAt(0) | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n    }\n    if (combined) {\n      this.combinedData = value[CHAR_DATA_CHAR_INDEX];\n      this.content = Content.IS_COMBINED_MASK | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n    }\n  }\n  /** Get data as CharData. */\n  public getAsCharData(): CharData {\n    return [this.fg, this.getChars(), this.getWidth(), this.getCode()];\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nexport const DEFAULT_COLOR = 0;\nexport const DEFAULT_ATTR = (0 << 18) | (DEFAULT_COLOR << 9) | (256 << 0);\nexport const DEFAULT_EXT = 0;\n\nexport const CHAR_DATA_ATTR_INDEX = 0;\nexport const CHAR_DATA_CHAR_INDEX = 1;\nexport const CHAR_DATA_WIDTH_INDEX = 2;\nexport const CHAR_DATA_CODE_INDEX = 3;\n\n/**\n * Null cell - a real empty cell (containing nothing).\n * Note that code should always be 0 for a null cell as\n * several test condition of the buffer line rely on this.\n */\nexport const NULL_CELL_CHAR = '';\nexport const NULL_CELL_WIDTH = 1;\nexport const NULL_CELL_CODE = 0;\n\n/**\n * Whitespace cell.\n * This is meant as a replacement for empty cells when needed\n * during rendering lines to preserve correct aligment.\n */\nexport const WHITESPACE_CELL_CHAR = ' ';\nexport const WHITESPACE_CELL_WIDTH = 1;\nexport const WHITESPACE_CELL_CODE = 32;\n\n/**\n * Bitmasks for accessing data in `content`.\n */\nexport const enum Content {\n  /**\n   * bit 1..21    codepoint, max allowed in UTF32 is 0x10FFFF (21 bits taken)\n   *              read:   `codepoint = content & Content.codepointMask;`\n   *              write:  `content |= codepoint & Content.codepointMask;`\n   *                      shortcut if precondition `codepoint <= 0x10FFFF` is met:\n   *                      `content |= codepoint;`\n   */\n  CODEPOINT_MASK = 0x1FFFFF,\n\n  /**\n   * bit 22       flag indication whether a cell contains combined content\n   *              read:   `isCombined = content & Content.isCombined;`\n   *              set:    `content |= Content.isCombined;`\n   *              clear:  `content &= ~Content.isCombined;`\n   */\n  IS_COMBINED_MASK = 0x200000,  // 1 << 21\n\n  /**\n   * bit 1..22    mask to check whether a cell contains any string data\n   *              we need to check for codepoint and isCombined bits to see\n   *              whether a cell contains anything\n   *              read:   `isEmpty = !(content & Content.hasContent)`\n   */\n  HAS_CONTENT_MASK = 0x3FFFFF,\n\n  /**\n   * bit 23..24   wcwidth value of cell, takes 2 bits (ranges from 0..2)\n   *              read:   `width = (content & Content.widthMask) >> Content.widthShift;`\n   *                      `hasWidth = content & Content.widthMask;`\n   *                      as long as wcwidth is highest value in `content`:\n   *                      `width = content >> Content.widthShift;`\n   *              write:  `content |= (width << Content.widthShift) & Content.widthMask;`\n   *                      shortcut if precondition `0 <= width <= 3` is met:\n   *                      `content |= width << Content.widthShift;`\n   */\n  WIDTH_MASK = 0xC00000,   // 3 << 22\n  WIDTH_SHIFT = 22\n}\n\nexport const enum Attributes {\n  /**\n   * bit 1..8     blue in RGB, color in P256 and P16\n   */\n  BLUE_MASK = 0xFF,\n  BLUE_SHIFT = 0,\n  PCOLOR_MASK = 0xFF,\n  PCOLOR_SHIFT = 0,\n\n  /**\n   * bit 9..16    green in RGB\n   */\n  GREEN_MASK = 0xFF00,\n  GREEN_SHIFT = 8,\n\n  /**\n   * bit 17..24   red in RGB\n   */\n  RED_MASK = 0xFF0000,\n  RED_SHIFT = 16,\n\n  /**\n   * bit 25..26   color mode: DEFAULT (0) | P16 (1) | P256 (2) | RGB (3)\n   */\n  CM_MASK = 0x3000000,\n  CM_DEFAULT = 0,\n  CM_P16 = 0x1000000,\n  CM_P256 = 0x2000000,\n  CM_RGB = 0x3000000,\n\n  /**\n   * bit 1..24  RGB room\n   */\n  RGB_MASK = 0xFFFFFF\n}\n\nexport const enum FgFlags {\n  /**\n   * bit 27..32\n   */\n  INVERSE = 0x4000000,\n  BOLD = 0x8000000,\n  UNDERLINE = 0x10000000,\n  BLINK = 0x20000000,\n  INVISIBLE = 0x40000000,\n  STRIKETHROUGH = 0x80000000,\n}\n\nexport const enum BgFlags {\n  /**\n   * bit 27..32 (upper 2 unused)\n   */\n  ITALIC = 0x4000000,\n  DIM = 0x8000000,\n  HAS_EXTENDED = 0x10000000,\n  PROTECTED = 0x20000000,\n  OVERLINE = 0x40000000\n}\n\nexport const enum ExtFlags {\n  /**\n   * bit 27..32 (upper 3 unused)\n   */\n  UNDERLINE_STYLE = 0x1C000000\n}\n\nexport const enum UnderlineStyle {\n  NONE = 0,\n  SINGLE = 1,\n  DOUBLE = 2,\n  CURLY = 3,\n  DOTTED = 4,\n  DASHED = 5\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\n/**\n * Polyfill - Convert UTF32 codepoint into JS string.\n * Note: The built-in String.fromCodePoint happens to be much slower\n *       due to additional sanity checks. We can avoid them since\n *       we always operate on legal UTF32 (granted by the input decoders)\n *       and use this faster version instead.\n */\nexport function stringFromCodePoint(codePoint: number): string {\n  if (codePoint > 0xFFFF) {\n    codePoint -= 0x10000;\n    return String.fromCharCode((codePoint >> 10) + 0xD800) + String.fromCharCode((codePoint % 0x400) + 0xDC00);\n  }\n  return String.fromCharCode(codePoint);\n}\n\n/**\n * Convert UTF32 char codes into JS string.\n * Basically the same as `stringFromCodePoint` but for multiple codepoints\n * in a loop (which is a lot faster).\n */\nexport function utf32ToString(data: Uint32Array, start: number = 0, end: number = data.length): string {\n  let result = '';\n  for (let i = start; i < end; ++i) {\n    let codepoint = data[i];\n    if (codepoint > 0xFFFF) {\n      // JS strings are encoded as UTF16, thus a non BMP codepoint gets converted into a surrogate\n      // pair conversion rules:\n      //  - subtract 0x10000 from code point, leaving a 20 bit number\n      //  - add high 10 bits to 0xD800  --> first surrogate\n      //  - add low 10 bits to 0xDC00   --> second surrogate\n      codepoint -= 0x10000;\n      result += String.fromCharCode((codepoint >> 10) + 0xD800) + String.fromCharCode((codepoint % 0x400) + 0xDC00);\n    } else {\n      result += String.fromCharCode(codepoint);\n    }\n  }\n  return result;\n}\n\n/**\n * StringToUtf32 - decodes UTF16 sequences into UTF32 codepoints.\n * To keep the decoder in line with JS strings it handles single surrogates as UCS2.\n */\nexport class StringToUtf32 {\n  private _interim: number = 0;\n\n  /**\n   * Clears interim and resets decoder to clean state.\n   */\n  public clear(): void {\n    this._interim = 0;\n  }\n\n  /**\n   * Decode JS string to UTF32 codepoints.\n   * The methods assumes stream input and will store partly transmitted\n   * surrogate pairs and decode them with the next data chunk.\n   * Note: The method does no bound checks for target, therefore make sure\n   * the provided input data does not exceed the size of `target`.\n   * Returns the number of written codepoints in `target`.\n   */\n  public decode(input: string, target: Uint32Array): number {\n    const length = input.length;\n\n    if (!length) {\n      return 0;\n    }\n\n    let size = 0;\n    let startPos = 0;\n\n    // handle leftover surrogate high\n    if (this._interim) {\n      const second = input.charCodeAt(startPos++);\n      if (0xDC00 <= second && second <= 0xDFFF) {\n        target[size++] = (this._interim - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n      } else {\n        // illegal codepoint (USC2 handling)\n        target[size++] = this._interim;\n        target[size++] = second;\n      }\n      this._interim = 0;\n    }\n\n    for (let i = startPos; i < length; ++i) {\n      const code = input.charCodeAt(i);\n      // surrogate pair first\n      if (0xD800 <= code && code <= 0xDBFF) {\n        if (++i >= length) {\n          this._interim = code;\n          return size;\n        }\n        const second = input.charCodeAt(i);\n        if (0xDC00 <= second && second <= 0xDFFF) {\n          target[size++] = (code - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n        } else {\n          // illegal codepoint (USC2 handling)\n          target[size++] = code;\n          target[size++] = second;\n        }\n        continue;\n      }\n      if (code === 0xFEFF) {\n        // BOM\n        continue;\n      }\n      target[size++] = code;\n    }\n    return size;\n  }\n}\n\n/**\n * Utf8Decoder - decodes UTF8 byte sequences into UTF32 codepoints.\n */\nexport class Utf8ToUtf32 {\n  public interim: Uint8Array = new Uint8Array(3);\n\n  /**\n   * Clears interim bytes and resets decoder to clean state.\n   */\n  public clear(): void {\n    this.interim.fill(0);\n  }\n\n  /**\n   * Decodes UTF8 byte sequences in `input` to UTF32 codepoints in `target`.\n   * The methods assumes stream input and will store partly transmitted bytes\n   * and decode them with the next data chunk.\n   * Note: The method does no bound checks for target, therefore make sure\n   * the provided data chunk does not exceed the size of `target`.\n   * Returns the number of written codepoints in `target`.\n   */\n  public decode(input: Uint8Array, target: Uint32Array): number {\n    const length = input.length;\n\n    if (!length) {\n      return 0;\n    }\n\n    let size = 0;\n    let byte1: number;\n    let byte2: number;\n    let byte3: number;\n    let byte4: number;\n    let codepoint = 0;\n    let startPos = 0;\n\n    // handle leftover bytes\n    if (this.interim[0]) {\n      let discardInterim = false;\n      let cp = this.interim[0];\n      cp &= ((((cp & 0xE0) === 0xC0)) ? 0x1F : (((cp & 0xF0) === 0xE0)) ? 0x0F : 0x07);\n      let pos = 0;\n      let tmp: number;\n      while ((tmp = this.interim[++pos] & 0x3F) && pos < 4) {\n        cp <<= 6;\n        cp |= tmp;\n      }\n      // missing bytes - read ahead from input\n      const type = (((this.interim[0] & 0xE0) === 0xC0)) ? 2 : (((this.interim[0] & 0xF0) === 0xE0)) ? 3 : 4;\n      const missing = type - pos;\n      while (startPos < missing) {\n        if (startPos >= length) {\n          return 0;\n        }\n        tmp = input[startPos++];\n        if ((tmp & 0xC0) !== 0x80) {\n          // wrong continuation, discard interim bytes completely\n          startPos--;\n          discardInterim = true;\n          break;\n        } else {\n          // need to save so we can continue short inputs in next call\n          this.interim[pos++] = tmp;\n          cp <<= 6;\n          cp |= tmp & 0x3F;\n        }\n      }\n      if (!discardInterim) {\n        // final test is type dependent\n        if (type === 2) {\n          if (cp < 0x80) {\n            // wrong starter byte\n            startPos--;\n          } else {\n            target[size++] = cp;\n          }\n        } else if (type === 3) {\n          if (cp < 0x0800 || (cp >= 0xD800 && cp <= 0xDFFF) || cp === 0xFEFF) {\n            // illegal codepoint or BOM\n          } else {\n            target[size++] = cp;\n          }\n        } else {\n          if (cp < 0x010000 || cp > 0x10FFFF) {\n            // illegal codepoint\n          } else {\n            target[size++] = cp;\n          }\n        }\n      }\n      this.interim.fill(0);\n    }\n\n    // loop through input\n    const fourStop = length - 4;\n    let i = startPos;\n    while (i < length) {\n      /**\n       * ASCII shortcut with loop unrolled to 4 consecutive ASCII chars.\n       * This is a compromise between speed gain for ASCII\n       * and penalty for non ASCII:\n       * For best ASCII performance the char should be stored directly into target,\n       * but even a single attempt to write to target and compare afterwards\n       * penalizes non ASCII really bad (-50%), thus we load the char into byteX first,\n       * which reduces ASCII performance by ~15%.\n       * This trial for ASCII reduces non ASCII performance by ~10% which seems acceptible\n       * compared to the gains.\n       * Note that this optimization only takes place for 4 consecutive ASCII chars,\n       * for any shorter it bails out. Worst case - all 4 bytes being read but\n       * thrown away due to the last being a non ASCII char (-10% performance).\n       */\n      while (i < fourStop\n        && !((byte1 = input[i]) & 0x80)\n        && !((byte2 = input[i + 1]) & 0x80)\n        && !((byte3 = input[i + 2]) & 0x80)\n        && !((byte4 = input[i + 3]) & 0x80))\n      {\n        target[size++] = byte1;\n        target[size++] = byte2;\n        target[size++] = byte3;\n        target[size++] = byte4;\n        i += 4;\n      }\n\n      // reread byte1\n      byte1 = input[i++];\n\n      // 1 byte\n      if (byte1 < 0x80) {\n        target[size++] = byte1;\n\n        // 2 bytes\n      } else if ((byte1 & 0xE0) === 0xC0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x1F) << 6 | (byte2 & 0x3F);\n        if (codepoint < 0x80) {\n          // wrong starter byte\n          i--;\n          continue;\n        }\n        target[size++] = codepoint;\n\n        // 3 bytes\n      } else if ((byte1 & 0xF0) === 0xE0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          return size;\n        }\n        byte3 = input[i++];\n        if ((byte3 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x0F) << 12 | (byte2 & 0x3F) << 6 | (byte3 & 0x3F);\n        if (codepoint < 0x0800 || (codepoint >= 0xD800 && codepoint <= 0xDFFF) || codepoint === 0xFEFF) {\n          // illegal codepoint or BOM, no i-- here\n          continue;\n        }\n        target[size++] = codepoint;\n\n        // 4 bytes\n      } else if ((byte1 & 0xF8) === 0xF0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          return size;\n        }\n        byte3 = input[i++];\n        if ((byte3 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          this.interim[2] = byte3;\n          return size;\n        }\n        byte4 = input[i++];\n        if ((byte4 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x07) << 18 | (byte2 & 0x3F) << 12 | (byte3 & 0x3F) << 6 | (byte4 & 0x3F);\n        if (codepoint < 0x010000 || codepoint > 0x10FFFF) {\n          // illegal codepoint, no i-- here\n          continue;\n        }\n        target[size++] = codepoint;\n      } else {\n        // illegal byte, just skip\n      }\n    }\n    return size;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Disposable } from 'common/Lifecycle';\nimport { ILogService, IOptionsService, LogLevelEnum } from 'common/services/Services';\n\ntype LogType = (message?: any, ...optionalParams: any[]) => void;\n\ninterface IConsole {\n  log: LogType;\n  error: LogType;\n  info: LogType;\n  trace: LogType;\n  warn: LogType;\n}\n\n// console is available on both node.js and browser contexts but the common\n// module doesn't depend on them so we need to explicitly declare it.\ndeclare const console: IConsole;\n\nconst optionsKeyToLogLevel: { [key: string]: LogLevelEnum } = {\n  trace: LogLevelEnum.TRACE,\n  debug: LogLevelEnum.DEBUG,\n  info: LogLevelEnum.INFO,\n  warn: LogLevelEnum.WARN,\n  error: LogLevelEnum.ERROR,\n  off: LogLevelEnum.OFF\n};\n\nconst LOG_PREFIX = 'xterm.js: ';\n\nexport class LogService extends Disposable implements ILogService {\n  public serviceBrand: any;\n\n  private _logLevel: LogLevelEnum = LogLevelEnum.OFF;\n  public get logLevel(): LogLevelEnum { return this._logLevel; }\n\n  constructor(\n    @IOptionsService private readonly _optionsService: IOptionsService\n  ) {\n    super();\n    this._updateLogLevel();\n    this.register(this._optionsService.onSpecificOptionChange('logLevel', () => this._updateLogLevel()));\n\n    // For trace logging, assume the latest created log service is valid\n    traceLogger = this;\n  }\n\n  private _updateLogLevel(): void {\n    this._logLevel = optionsKeyToLogLevel[this._optionsService.rawOptions.logLevel];\n  }\n\n  private _evalLazyOptionalParams(optionalParams: any[]): void {\n    for (let i = 0; i < optionalParams.length; i++) {\n      if (typeof optionalParams[i] === 'function') {\n        optionalParams[i] = optionalParams[i]();\n      }\n    }\n  }\n\n  private _log(type: LogType, message: string, optionalParams: any[]): void {\n    this._evalLazyOptionalParams(optionalParams);\n    type.call(console, (this._optionsService.options.logger ? '' : LOG_PREFIX) + message, ...optionalParams);\n  }\n\n  public trace(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.TRACE) {\n      this._log(this._optionsService.options.logger?.trace.bind(this._optionsService.options.logger) ?? console.log, message, optionalParams);\n    }\n  }\n\n  public debug(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.DEBUG) {\n      this._log(this._optionsService.options.logger?.debug.bind(this._optionsService.options.logger) ?? console.log, message, optionalParams);\n    }\n  }\n\n  public info(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.INFO) {\n      this._log(this._optionsService.options.logger?.info.bind(this._optionsService.options.logger) ?? console.info, message, optionalParams);\n    }\n  }\n\n  public warn(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.WARN) {\n      this._log(this._optionsService.options.logger?.warn.bind(this._optionsService.options.logger) ?? console.warn, message, optionalParams);\n    }\n  }\n\n  public error(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.ERROR) {\n      this._log(this._optionsService.options.logger?.error.bind(this._optionsService.options.logger) ?? console.error, message, optionalParams);\n    }\n  }\n}\n\nlet traceLogger: ILogService;\nexport function setTraceLogger(logger: ILogService): void {\n  traceLogger = logger;\n}\n\n/**\n * A decorator that can be used to automatically log trace calls to the decorated function.\n */\nexport function traceCall(_target: any, key: string, descriptor: any): any {\n  if (typeof descriptor.value !== 'function') {\n    throw new Error('not supported');\n  }\n  const fnKey = 'value';\n  const fn = descriptor.value;\n  descriptor[fnKey] = function (...args: any[]) {\n    // Early exit\n    if (traceLogger.logLevel !== LogLevelEnum.TRACE) {\n      return fn.apply(this, args);\n    }\n\n    traceLogger.trace(`GlyphRenderer#${fn.name}(${args.map(e => JSON.stringify(e)).join(', ')})`);\n    const result = fn.apply(this, args);\n    traceLogger.trace(`GlyphRenderer#${fn.name} return`, result);\n    return result;\n  };\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n *\n * This was heavily inspired from microsoft/vscode's dependency injection system (MIT).\n */\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IServiceIdentifier } from 'common/services/Services';\n\nconst DI_TARGET = 'di$target';\nconst DI_DEPENDENCIES = 'di$dependencies';\n\nexport const serviceRegistry: Map<string, IServiceIdentifier<any>> = new Map();\n\nexport function getServiceDependencies(ctor: any): { id: IServiceIdentifier<any>, index: number, optional: boolean }[] {\n  return ctor[DI_DEPENDENCIES] || [];\n}\n\nexport function createDecorator<T>(id: string): IServiceIdentifier<T> {\n  if (serviceRegistry.has(id)) {\n    return serviceRegistry.get(id)!;\n  }\n\n  const decorator: any = function (target: Function, key: string, index: number): any {\n    if (arguments.length !== 3) {\n      throw new Error('@IServiceName-decorator can only be used to decorate a parameter');\n    }\n\n    storeServiceDependency(decorator, target, index);\n  };\n\n  decorator.toString = () => id;\n\n  serviceRegistry.set(id, decorator);\n  return decorator;\n}\n\nfunction storeServiceDependency(id: Function, target: Function, index: number): void {\n  if ((target as any)[DI_TARGET] === target) {\n    (target as any)[DI_DEPENDENCIES].push({ id, index });\n  } else {\n    (target as any)[DI_DEPENDENCIES] = [{ id, index }];\n    (target as any)[DI_TARGET] = target;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IEvent, IEventEmitter } from 'common/EventEmitter';\nimport { IBuffer, IBufferSet } from 'common/buffer/Types';\nimport { IDecPrivateModes, ICoreMouseEvent, CoreMouseEncoding, ICoreMouseProtocol, CoreMouseEventType, ICharset, IWindowOptions, IModes, IAttributeData, ScrollSource, IDisposable, IColor, CursorStyle, CursorInactiveStyle, IOscLinkData } from 'common/Types';\nimport { createDecorator } from 'common/services/ServiceRegistry';\nimport { IDecorationOptions, IDecoration, ILinkHandler, IWindowsPty, ILogger } from 'xterm';\n\nexport const IBufferService = createDecorator<IBufferService>('BufferService');\nexport interface IBufferService {\n  serviceBrand: undefined;\n\n  readonly cols: number;\n  readonly rows: number;\n  readonly buffer: IBuffer;\n  readonly buffers: IBufferSet;\n  isUserScrolling: boolean;\n  onResize: IEvent<{ cols: number, rows: number }>;\n  onScroll: IEvent<number>;\n  scroll(eraseAttr: IAttributeData, isWrapped?: boolean): void;\n  scrollLines(disp: number, suppressScrollEvent?: boolean, source?: ScrollSource): void;\n  resize(cols: number, rows: number): void;\n  reset(): void;\n}\n\nexport const ICoreMouseService = createDecorator<ICoreMouseService>('CoreMouseService');\nexport interface ICoreMouseService {\n  activeProtocol: string;\n  activeEncoding: string;\n  areMouseEventsActive: boolean;\n  addProtocol(name: string, protocol: ICoreMouseProtocol): void;\n  addEncoding(name: string, encoding: CoreMouseEncoding): void;\n  reset(): void;\n\n  /**\n   * Triggers a mouse event to be sent.\n   *\n   * Returns true if the event passed all protocol restrictions and a report\n   * was sent, otherwise false. The return value may be used to decide whether\n   * the default event action in the bowser component should be omitted.\n   *\n   * Note: The method will change values of the given event object\n   * to fullfill protocol and encoding restrictions.\n   */\n  triggerMouseEvent(event: ICoreMouseEvent): boolean;\n\n  /**\n   * Event to announce changes in mouse tracking.\n   */\n  onProtocolChange: IEvent<CoreMouseEventType>;\n\n  /**\n   * Human readable version of mouse events.\n   */\n  explainEvents(events: CoreMouseEventType): { [event: string]: boolean };\n}\n\nexport const ICoreService = createDecorator<ICoreService>('CoreService');\nexport interface ICoreService {\n  serviceBrand: undefined;\n\n  /**\n   * Initially the cursor will not be visible until the first time the terminal\n   * is focused.\n   */\n  isCursorInitialized: boolean;\n  isCursorHidden: boolean;\n\n  readonly modes: IModes;\n  readonly decPrivateModes: IDecPrivateModes;\n\n  readonly onData: IEvent<string>;\n  readonly onUserInput: IEvent<void>;\n  readonly onBinary: IEvent<string>;\n  readonly onRequestScrollToBottom: IEvent<void>;\n\n  reset(): void;\n\n  /**\n   * Triggers the onData event in the public API.\n   * @param data The data that is being emitted.\n   * @param wasUserInput Whether the data originated from the user (as opposed to\n   * resulting from parsing incoming data). When true this will also:\n   * - Scroll to the bottom of the buffer if option scrollOnUserInput is true.\n   * - Fire the `onUserInput` event (so selection can be cleared).\n   */\n  triggerDataEvent(data: string, wasUserInput?: boolean): void;\n\n  /**\n   * Triggers the onBinary event in the public API.\n   * @param data The data that is being emitted.\n   */\n  triggerBinaryEvent(data: string): void;\n}\n\nexport const ICharsetService = createDecorator<ICharsetService>('CharsetService');\nexport interface ICharsetService {\n  serviceBrand: undefined;\n\n  charset: ICharset | undefined;\n  readonly glevel: number;\n\n  reset(): void;\n\n  /**\n   * Set the G level of the terminal.\n   * @param g\n   */\n  setgLevel(g: number): void;\n\n  /**\n   * Set the charset for the given G level of the terminal.\n   * @param g\n   * @param charset\n   */\n  setgCharset(g: number, charset: ICharset | undefined): void;\n}\n\nexport interface IServiceIdentifier<T> {\n  (...args: any[]): void;\n  type: T;\n}\n\nexport interface IBrandedService {\n  serviceBrand: undefined;\n}\n\ntype GetLeadingNonServiceArgs<TArgs extends any[]> = TArgs extends [] ? []\n  : TArgs extends [...infer TFirst, infer TLast] ? TLast extends IBrandedService ? GetLeadingNonServiceArgs<TFirst> : TArgs\n    : never;\n\nexport const IInstantiationService = createDecorator<IInstantiationService>('InstantiationService');\nexport interface IInstantiationService {\n  serviceBrand: undefined;\n\n  setService<T>(id: IServiceIdentifier<T>, instance: T): void;\n  getService<T>(id: IServiceIdentifier<T>): T | undefined;\n  createInstance<Ctor extends new (...args: any[]) => any, R extends InstanceType<Ctor>>(t: Ctor, ...args: GetLeadingNonServiceArgs<ConstructorParameters<Ctor>>): R;\n}\n\nexport enum LogLevelEnum {\n  TRACE = 0,\n  DEBUG = 1,\n  INFO = 2,\n  WARN = 3,\n  ERROR = 4,\n  OFF = 5\n}\n\nexport const ILogService = createDecorator<ILogService>('LogService');\nexport interface ILogService {\n  serviceBrand: undefined;\n\n  readonly logLevel: LogLevelEnum;\n\n  trace(message: any, ...optionalParams: any[]): void;\n  debug(message: any, ...optionalParams: any[]): void;\n  info(message: any, ...optionalParams: any[]): void;\n  warn(message: any, ...optionalParams: any[]): void;\n  error(message: any, ...optionalParams: any[]): void;\n}\n\nexport const IOptionsService = createDecorator<IOptionsService>('OptionsService');\nexport interface IOptionsService {\n  serviceBrand: undefined;\n\n  /**\n   * Read only access to the raw options object, this is an internal-only fast path for accessing\n   * single options without any validation as we trust TypeScript to enforce correct usage\n   * internally.\n   */\n  readonly rawOptions: Required<ITerminalOptions>;\n\n  /**\n   * Options as exposed through the public API, this property uses getters and setters with\n   * validation which makes it safer but slower. {@link rawOptions} should be used for pretty much\n   * all internal usage for performance reasons.\n   */\n  readonly options: Required<ITerminalOptions>;\n\n  /**\n   * Adds an event listener for when any option changes.\n   */\n  readonly onOptionChange: IEvent<keyof ITerminalOptions>;\n\n  /**\n   * Adds an event listener for when a specific option changes, this is a convenience method that is\n   * preferred over {@link onOptionChange} when only a single option is being listened to.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  onSpecificOptionChange<T extends keyof ITerminalOptions>(key: T, listener: (arg1: Required<ITerminalOptions>[T]) => any): IDisposable;\n\n  /**\n   * Adds an event listener for when a set of specific options change, this is a convenience method\n   * that is preferred over {@link onOptionChange} when multiple options are being listened to and\n   * handled the same way.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  onMultipleOptionChange(keys: (keyof ITerminalOptions)[], listener: () => any): IDisposable;\n}\n\nexport type FontWeight = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900' | number;\nexport type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'off';\n\nexport interface ITerminalOptions {\n  allowProposedApi?: boolean;\n  allowTransparency?: boolean;\n  altClickMovesCursor?: boolean;\n  cols?: number;\n  convertEol?: boolean;\n  cursorBlink?: boolean;\n  cursorStyle?: CursorStyle;\n  cursorWidth?: number;\n  cursorInactiveStyle?: CursorInactiveStyle;\n  customGlyphs?: boolean;\n  disableStdin?: boolean;\n  drawBoldTextInBrightColors?: boolean;\n  fastScrollModifier?: 'none' | 'alt' | 'ctrl' | 'shift';\n  fastScrollSensitivity?: number;\n  fontSize?: number;\n  fontFamily?: string;\n  fontWeight?: FontWeight;\n  fontWeightBold?: FontWeight;\n  ignoreBracketedPasteMode?: boolean;\n  letterSpacing?: number;\n  lineHeight?: number;\n  linkHandler?: ILinkHandler | null;\n  logLevel?: LogLevel;\n  logger?: ILogger | null;\n  macOptionIsMeta?: boolean;\n  macOptionClickForcesSelection?: boolean;\n  minimumContrastRatio?: number;\n  rightClickSelectsWord?: boolean;\n  rows?: number;\n  screenReaderMode?: boolean;\n  scrollback?: number;\n  scrollOnUserInput?: boolean;\n  scrollSensitivity?: number;\n  smoothScrollDuration?: number;\n  tabStopWidth?: number;\n  theme?: ITheme;\n  windowsMode?: boolean;\n  windowsPty?: IWindowsPty;\n  windowOptions?: IWindowOptions;\n  wordSeparator?: string;\n  overviewRulerWidth?: number;\n\n  [key: string]: any;\n  cancelEvents: boolean;\n  termName: string;\n}\n\nexport interface ITheme {\n  foreground?: string;\n  background?: string;\n  cursor?: string;\n  cursorAccent?: string;\n  selectionForeground?: string;\n  selectionBackground?: string;\n  selectionInactiveBackground?: string;\n  black?: string;\n  red?: string;\n  green?: string;\n  yellow?: string;\n  blue?: string;\n  magenta?: string;\n  cyan?: string;\n  white?: string;\n  brightBlack?: string;\n  brightRed?: string;\n  brightGreen?: string;\n  brightYellow?: string;\n  brightBlue?: string;\n  brightMagenta?: string;\n  brightCyan?: string;\n  brightWhite?: string;\n  extendedAnsi?: string[];\n}\n\nexport const IOscLinkService = createDecorator<IOscLinkService>('OscLinkService');\nexport interface IOscLinkService {\n  serviceBrand: undefined;\n  /**\n   * Registers a link to the service, returning the link ID. The link data is managed by this\n   * service and will be freed when this current cursor position is trimmed off the buffer.\n   */\n  registerLink(linkData: IOscLinkData): number;\n  /**\n   * Adds a line to a link if needed.\n   */\n  addLineToLink(linkId: number, y: number): void;\n  /** Get the link data associated with a link ID. */\n  getLinkData(linkId: number): IOscLinkData | undefined;\n}\n\nexport const IUnicodeService = createDecorator<IUnicodeService>('UnicodeService');\nexport interface IUnicodeService {\n  serviceBrand: undefined;\n  /** Register an Unicode version provider. */\n  register(provider: IUnicodeVersionProvider): void;\n  /** Registered Unicode versions. */\n  readonly versions: string[];\n  /** Currently active version. */\n  activeVersion: string;\n  /** Event triggered, when activate version changed. */\n  readonly onChange: IEvent<string>;\n\n  /**\n   * Unicode version dependent\n   */\n  wcwidth(codepoint: number): number;\n  getStringCellWidth(s: string): number;\n}\n\nexport interface IUnicodeVersionProvider {\n  readonly version: string;\n  wcwidth(ucs: number): 0 | 1 | 2;\n}\n\nexport const IDecorationService = createDecorator<IDecorationService>('DecorationService');\nexport interface IDecorationService extends IDisposable {\n  serviceBrand: undefined;\n  readonly decorations: IterableIterator<IInternalDecoration>;\n  readonly onDecorationRegistered: IEvent<IInternalDecoration>;\n  readonly onDecorationRemoved: IEvent<IInternalDecoration>;\n  registerDecoration(decorationOptions: IDecorationOptions): IDecoration | undefined;\n  reset(): void;\n  /**\n   * Trigger a callback over the decoration at a cell (in no particular order). This uses a callback\n   * instead of an iterator as it's typically used in hot code paths.\n   */\n  forEachDecorationAtCell(x: number, line: number, layer: 'bottom' | 'top' | undefined, callback: (decoration: IInternalDecoration) => void): void;\n}\nexport interface IInternalDecoration extends IDecoration {\n  readonly options: IDecorationOptions;\n  readonly backgroundColorRGB: IColor | undefined;\n  readonly foregroundColorRGB: IColor | undefined;\n  readonly onRenderEmitter: IEventEmitter<HTMLElement>;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICharacterJoinerService, ICharSizeService, ICoreBrowserService, IRenderService, IThemeService } from 'browser/services/Services';\nimport { ITerminal } from 'browser/Types';\nimport { EventEmitter, forwardEvent } from 'common/EventEmitter';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { getSafariVersion, isSafari } from 'common/Platform';\nimport { ICoreService, IDecorationService, ILogService, IOptionsService } from 'common/services/Services';\nimport { ITerminalAddon, Terminal } from 'xterm';\nimport { WebglRenderer } from './WebglRenderer';\nimport { setTraceLogger } from 'common/services/LogService';\n\nexport class WebglAddon extends Disposable implements ITerminalAddon {\n  private _terminal?: Terminal;\n  private _renderer?: WebglRenderer;\n\n  private readonly _onChangeTextureAtlas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onChangeTextureAtlas = this._onChangeTextureAtlas.event;\n  private readonly _onAddTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n  private readonly _onRemoveTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onRemoveTextureAtlasCanvas = this._onRemoveTextureAtlasCanvas.event;\n  private readonly _onContextLoss = this.register(new EventEmitter<void>());\n  public readonly onContextLoss = this._onContextLoss.event;\n\n  constructor(\n    private _preserveDrawingBuffer?: boolean\n  ) {\n    if (isSafari && getSafariVersion() < 16) {\n      throw new Error('Webgl2 is only supported on Safari 16 and above');\n    }\n    super();\n  }\n\n  public activate(terminal: Terminal): void {\n    const core = (terminal as any)._core as ITerminal;\n    if (!terminal.element) {\n      this.register(core.onWillOpen(() => this.activate(terminal)));\n      return;\n    }\n\n    this._terminal = terminal;\n    const coreService: ICoreService = core.coreService;\n    const optionsService: IOptionsService = core.optionsService;\n\n    const unsafeCore = core as any;\n    const renderService: IRenderService = unsafeCore._renderService;\n    const characterJoinerService: ICharacterJoinerService = unsafeCore._characterJoinerService;\n    const charSizeService: ICharSizeService = unsafeCore._charSizeService;\n    const coreBrowserService: ICoreBrowserService = unsafeCore._coreBrowserService;\n    const decorationService: IDecorationService = unsafeCore._decorationService;\n    const logService: ILogService = unsafeCore._logService;\n    const themeService: IThemeService = unsafeCore._themeService;\n\n    // Set trace logger just in case it hasn't been yet which could happen when the addon is\n    // bundled separately to the core module\n    setTraceLogger(logService);\n\n    this._renderer = this.register(new WebglRenderer(\n      terminal,\n      characterJoinerService,\n      charSizeService,\n      coreBrowserService,\n      coreService,\n      decorationService,\n      optionsService,\n      themeService,\n      this._preserveDrawingBuffer\n    ));\n    this.register(forwardEvent(this._renderer.onContextLoss, this._onContextLoss));\n    this.register(forwardEvent(this._renderer.onChangeTextureAtlas, this._onChangeTextureAtlas));\n    this.register(forwardEvent(this._renderer.onAddTextureAtlasCanvas, this._onAddTextureAtlasCanvas));\n    this.register(forwardEvent(this._renderer.onRemoveTextureAtlasCanvas, this._onRemoveTextureAtlasCanvas));\n    renderService.setRenderer(this._renderer);\n\n    this.register(toDisposable(() => {\n      const renderService: IRenderService = (this._terminal as any)._core._renderService;\n      renderService.setRenderer((this._terminal as any)._core._createRenderer());\n      renderService.handleResize(terminal.cols, terminal.rows);\n    }));\n  }\n\n  public get textureAtlas(): HTMLCanvasElement | undefined {\n    return this._renderer?.textureAtlas;\n  }\n\n  public clearTextureAtlas(): void {\n    this._renderer?.clearTextureAtlas();\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAoB,aAAID,EAAAA,IAExBD,EAAiB,aAAIC,EAAAA;IACtB,EAAEK,MAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,EAAA,KAAA,SAAAC,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAC,IAAAC,KAAA,UAAA,QAAAC,KAAAD,KAAA,IAAAJ,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAAG,KAAA,QAAA,SAAAN,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAI,KAAAP,GAAA,SAAA,GAAAO,MAAA,GAAAA;AAAA,eAAAH,KAAAJ,GAAAO,EAAA,OAAAD,MAAAD,KAAA,IAAAD,GAAAE,EAAA,IAAAD,KAAA,IAAAD,GAAAH,IAAAC,IAAAI,EAAA,IAAAF,GAAAH,IAAAC,EAAA,MAAAI;AAAA,iBAAAD,KAAA,KAAAC,MAAA,OAAA,eAAAL,IAAAC,IAAAI,EAAA,GAAAA;QAAA;AAAA,eAAA,eAAAL,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAA;ACJT,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GAoEMM,IAAmB,IACnBC,IAAiBD,IAAmBE,aAAaC;AAIvD,YACIC,GADAC,IAAK,GAELC,IAAmB,GACnBC,IAAiB;QAErB,MAAaC,UAAsB,EAAAC,WAAAA;UAoBjC,YACmBC,IACAC,IACTC,IAAAA;AAERC,kBAAAA,GAJiB,KAAAH,YAAAA,IACA,KAAAC,MAAAA,IACT,KAAAC,cAAAA,IAbF,KAAAE,gBAAwB,GACf,KAAAC,YAAuB,EACtCC,OAAO,GACPC,YAAY,IAAIf,aAAa,CAAA,GAC7BgB,mBAAmB,CACjB,IAAIhB,aAAa,CAAA,GACjB,IAAIA,aAAa,CAAA,CAAA,EAAA;AAWnB,kBAAMiB,KAAKC,KAAKT;AAAAA,uBAEZ,EAAAU,aAAaC,kBAEf,EAAAD,aAAaC,gBAAgBC,KAAKC,IAAI,KAAI,GAAA,EAAAC,cAAaN,GAAGO,aAAaP,GAAGQ,uBAAAA,CAAAA,CAAAA,GAE1E,EAAAN,aAAaO,kBAAiB,GAAA,EAAAH,cAAaN,GAAGO,aAAaP,GAAGU,gBAAAA,CAAAA,IAGhET,KAAKU,YAAW,GAAA,EAAAL,eAAa,GAAA,EAAAM,eAAcZ,IA1FpB,ypBAsB3B,SAAoCa,IAAAA;AAClC,kBAAIC,KAAsB;AAC1B,uBAASvC,KAAI,GAAGA,KAAIsC,IAA+BtC;AACjDuC,gBAAAA,MAAuB,0BAA0BvC,EAAAA,oCAAqCA,EAAAA;AAExF,qBAAO;;;;;;8BAMqBsC,EAAAA;;;;;;;MAOxBC,EAAAA;;YAEN,EAgDkG,EAAAZ,aAAaC,aAAAA,CAAAA,CAAAA,GAC3GF,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAGiB,cAAchB,KAAKU,QAAAA,CAAAA,CAAAA,GAGvDV,KAAKiB,uBAAsB,GAAA,EAAAZ,cAAaN,GAAGmB,mBAAmBlB,KAAKU,UAAU,cAAA,CAAA,GAC7EV,KAAKmB,uBAAsB,GAAA,EAAAd,cAAaN,GAAGmB,mBAAmBlB,KAAKU,UAAU,cAAA,CAAA,GAC7EV,KAAKoB,oBAAmB,GAAA,EAAAf,cAAaN,GAAGmB,mBAAmBlB,KAAKU,UAAU,WAAA,CAAA,GAG1EV,KAAKqB,qBAAqBtB,GAAGuB,kBAAAA,GAC7BvB,GAAGwB,gBAAgBvB,KAAKqB,kBAAAA;AAGxB,kBAAMG,KAAmB,IAAI1C,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,CAAA,GAC1D2C,KAAyB1B,GAAG2B,aAAAA;AAClC1B,iBAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAaF,EAAAA,CAAAA,CAAAA,GACjD1B,GAAG6B,WAAW7B,GAAG8B,cAAcJ,EAAAA,GAC/B1B,GAAG+B,WAAW/B,GAAG8B,cAAcL,IAAkBzB,GAAGgC,WAAAA,GACpDhC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAAiC,GAAGjC,KAAKT,IAAI2C,OAAAA,OAAc,GAAG,CAAA;AAKrF,kBAAMC,KAAyB,IAAIC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAA,CAAA,GAClDC,KAAuBtC,GAAG2B,aAAAA;AAChC1B,iBAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAaU,EAAAA,CAAAA,CAAAA,GACjDtC,GAAG6B,WAAW7B,GAAGuC,sBAAsBD,EAAAA,GACvCtC,GAAG+B,WAAW/B,GAAGuC,sBAAsBH,IAAwBpC,GAAGgC,WAAAA,GAGlE/B,KAAKuC,qBAAoB,GAAA,EAAAlC,cAAaN,GAAG2B,aAAAA,CAAAA,GACzC1B,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAa3B,KAAKuC,iBAAAA,CAAAA,CAAAA,GACtDxC,GAAG6B,WAAW7B,GAAG8B,cAAc7B,KAAKuC,iBAAAA,GACpCxC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA8B,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,CAAA,GACzFkB,GAAGyC,oBAAoB,GAA8B,CAAA,GACrDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA4B,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,IAAIC,aAAaC,iBAAAA,GACxGgB,GAAGyC,oBAAoB,GAA4B,CAAA,GACnDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA+B,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,IAAIC,aAAaC,iBAAAA,GAC3GgB,GAAGyC,oBAAoB,GAA+B,CAAA,GACtDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAAgC,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,IAAIC,aAAaC,iBAAAA,GAC5GgB,GAAGyC,oBAAoB,GAAgC,CAAA,GACvDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA+B,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,IAAIC,aAAaC,iBAAAA,GAC3GgB,GAAGyC,oBAAoB,GAA+B,CAAA,GACtDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAAqC,GAAGlC,GAAGmC,OAAAA,OAAcrD,GAAgB,IAAIC,aAAaC,iBAAAA,GACjHgB,GAAGyC,oBAAoB,GAAqC,CAAA,GAG5DzC,GAAG0C,WAAWzC,KAAKU,QAAAA;AACnB,kBAAMgC,KAAe,IAAIC,WAAW,EAAA1C,aAAaC,aAAAA;AACjD,qBAAS5B,KAAI,GAAGA,KAAI,EAAA2B,aAAaC,eAAe5B;AAC9CoE,cAAAA,GAAapE,EAAAA,IAAKA;AAEpByB,YAAAA,GAAG6C,WAAW5C,KAAKoB,kBAAkBsB,EAAAA,GACrC3C,GAAG8C,iBAAiB7C,KAAKiB,qBAAAA,OAA4B,EAAA6B,iBAAAA,GAIrD9C,KAAK+C,iBAAiB,CAAA;AACtB,qBAASzE,KAAI,GAAGA,KAAI,EAAA2B,aAAaC,eAAe5B,MAAK;AACnD,oBAAM0E,KAAY,IAAI,EAAAC,WAAU,GAAA,EAAA5C,cAAaN,GAAGmD,cAAAA,CAAAA,CAAAA;AAChDlD,mBAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAGoD,cAAcH,GAAUI,OAAAA,CAAAA,CAAAA,GAC5DrD,GAAGsD,cAActD,GAAGuD,WAAWhF,EAAAA,GAC/ByB,GAAGwD,YAAYxD,GAAGyD,YAAYR,GAAUI,OAAAA,GACxCrD,GAAG0D,cAAc1D,GAAGyD,YAAYzD,GAAG2D,gBAAgB3D,GAAG4D,aAAAA,GACtD5D,GAAG0D,cAAc1D,GAAGyD,YAAYzD,GAAG6D,gBAAgB7D,GAAG4D,aAAAA,GACtD5D,GAAG8D,WAAW9D,GAAGyD,YAAY,GAAGzD,GAAG+D,MAAM,GAAG,GAAG,GAAG/D,GAAG+D,MAAM/D,GAAGgE,eAAe,IAAI3B,WAAW,CAAC,KAAK,GAAG,GAAG,GAAA,CAAA,CAAA,GACxGpC,KAAK+C,eAAezE,EAAAA,IAAK0E;YAAAA;AAI3BjD,YAAAA,GAAGiE,OAAOjE,GAAGkE,KAAAA,GACblE,GAAGmE,UAAUnE,GAAGoE,WAAWpE,GAAGqE,mBAAAA,GAG9BpE,KAAKqE,aAAAA;UACP;UAEO,aAAAC;AACL,mBAAA,CAAOtE,KAAKuE,UAASvE,KAAKuE,OAAOD,WAAAA;UACnC;UAGO,WAAWE,IAAWC,IAAWC,IAAcC,IAAYC,IAAYC,IAAaC,IAAeC,IAAAA;AAKxG/E,iBAAKgF,YAAYhF,KAAKL,UAAUE,YAAY2E,IAAGC,IAAGC,IAAMC,IAAIC,IAAIC,IAAKC,IAAOC,EAAAA;UAC9E;UAEQ,YAAYE,IAAqBT,IAAWC,IAAWC,IAA0BC,IAAYC,IAAYC,IAAaC,IAAeC,IAAAA;AAC3I9F,iBAAMwF,KAAIzE,KAAKV,UAAU4F,OAAOV,MAAK5F,GAIjC8F,OAAS,EAAAS,kBAAAA,WAAkBT,KAK1B1E,KAAKuE,WAMRvF,IADE8F,MAASA,GAAMM,SAAS,IACjBpF,KAAKuE,OAAOc,+BAA+BP,IAAOH,IAAIC,IAAIC,IAAAA,KAAK,IAE/D7E,KAAKuE,OAAOe,mBAAmBZ,IAAMC,IAAIC,IAAIC,IAAAA,KAAK,GAG7D3F,IAAmBiB,KAAKoF,OAAOvF,KAAKR,YAAYgG,OAAOC,KAAKC,QAAQ1F,KAAKR,YAAYgG,OAAOG,KAAKD,SAAS,CAAA,GACtGf,OAAOI,MAAU/F,EAAO4G,OAAOpB,IAAItF,KACrCC,IAAiBH,EAAO4G,OAAOpB,IAAItF,GAEnC+F,GAAMhG,CAAAA,IAAAA,EAAYD,EAAO4G,OAAOpB,IAAIrF,KAAkBa,KAAKR,YAAYgG,OAAOG,KAAKE,MACnFZ,GAAMhG,IAAK,CAAA,IAAA,CAAMD,EAAO4G,OAAOnB,IAAIzE,KAAKR,YAAYgG,OAAOG,KAAKG,KAEhEb,GAAMhG,IAAK,CAAA,KAAMD,EAAO+G,KAAKvB,IAAIrF,KAAkBa,KAAKR,YAAYgG,OAAOQ,OAAON,OAClFT,GAAMhG,IAAK,CAAA,IAAKD,EAAO+G,KAAKtB,IAAIzE,KAAKR,YAAYgG,OAAOQ,OAAOC,QAE/DhB,GAAMhG,IAAK,CAAA,IAAKD,EAAOkH,aAEvBjB,GAAMhG,IAAK,CAAA,IAAKD,EAAOmH,yBAAyB3B,IAAIrF,IAAiBa,KAAKuE,OAAO6B,MAAMpH,EAAOkH,WAAAA,EAAaF,OAAON,OAClHT,GAAMhG,IAAK,CAAA,IAAKD,EAAOmH,yBAAyB1B,GAEhDQ,GAAMhG,IAAK,CAAA,IAAKD,EAAOqH,cAAc7B,IAAIrF,IAAiBa,KAAKuE,OAAO6B,MAAMpH,EAAOkH,WAAAA,EAAaF,OAAON,OACvGT,GAAMhG,IAAK,CAAA,IAAKD,EAAOqH,cAAc5B,MAGrCQ,GAAMhG,CAAAA,IAAAA,CAAWD,EAAO4G,OAAOpB,IAAIxE,KAAKR,YAAYgG,OAAOG,KAAKE,MAChEZ,GAAMhG,IAAK,CAAA,IAAA,CAAMD,EAAO4G,OAAOnB,IAAIzE,KAAKR,YAAYgG,OAAOG,KAAKG,KAEhEb,GAAMhG,IAAK,CAAA,IAAKD,EAAO+G,KAAKvB,IAAIxE,KAAKR,YAAYgG,OAAOQ,OAAON,OAC/DT,GAAMhG,IAAK,CAAA,IAAKD,EAAO+G,KAAKtB,IAAIzE,KAAKR,YAAYgG,OAAOQ,OAAOC,QAE/DhB,GAAMhG,IAAK,CAAA,IAAKD,EAAOkH,aAEvBjB,GAAMhG,IAAK,CAAA,IAAKD,EAAOmH,yBAAyB3B,GAChDS,GAAMhG,IAAK,CAAA,IAAKD,EAAOmH,yBAAyB1B,GAEhDQ,GAAMhG,IAAK,CAAA,IAAKD,EAAOqH,cAAc7B,GACrCS,GAAMhG,IAAK,CAAA,IAAKD,EAAOqH,cAAc5B,MA9CrCQ,GAAMqB,KAAK,GAAGrH,GAAIA,IAAKL,IAAmB,IAnJlB,CAAA;UAoM5B;UAEO,QAAA2H;AACL,kBAAMC,KAAWxG,KAAKV,WAChBmH,KAAWD,GAAStB,OAAOsB,GAASE,OAAO9H;AAG7CoB,iBAAKL,UAAUC,UAAU6G,KAC3BzG,KAAKL,UAAUE,aAAa,IAAIf,aAAa2H,EAAAA,IAE7CzG,KAAKL,UAAUE,WAAWyG,KAAK,CAAA;AAEjC,gBAAIhI,KAAI;AACR,mBAAOA,KAAI0B,KAAKL,UAAUG,kBAAkBsF,QAAQ9G;AAC9C0B,mBAAKL,UAAUC,UAAU6G,KAC3BzG,KAAKL,UAAUG,kBAAkBxB,EAAAA,IAAK,IAAIQ,aAAa2H,EAAAA,IAEvDzG,KAAKL,UAAUG,kBAAkBxB,EAAAA,EAAGgI,KAAK,CAAA;AAG7CtG,iBAAKL,UAAUC,QAAQ6G,IACvBnI,KAAI;AACJ,qBAASmG,KAAI,GAAGA,KAAI+B,GAASE,MAAMjC;AACjC,uBAASD,KAAI,GAAGA,KAAIgC,GAAStB,MAAMV;AACjCxE,qBAAKL,UAAUE,WAAWvB,KAAI,CAAA,IAAKkG,KAAIgC,GAAStB,MAChDlF,KAAKL,UAAUE,WAAWvB,KAAI,EAAA,IAAMmG,KAAI+B,GAASE,MACjDpI,MAAKM;UAGX;UAEO,eAAAyF;AACL,kBAAMtE,KAAKC,KAAKT;AAChBQ,YAAAA,GAAG0C,WAAWzC,KAAKU,QAAAA,GACnBX,GAAG4G,SAAS,GAAG,GAAG5G,GAAGiG,OAAON,OAAO3F,GAAGiG,OAAOC,MAAAA,GAC7ClG,GAAG6G,UAAU5G,KAAKmB,qBAAqBpB,GAAGiG,OAAON,OAAO3F,GAAGiG,OAAOC,MAAAA,GAClEjG,KAAKuG,MAAAA;UACP;UAEO,OAAOM,IAAAA;AACZ,gBAAA,CAAK7G,KAAKuE;AACR;AAGF,kBAAMxE,KAAKC,KAAKT;AAEhBQ,YAAAA,GAAG0C,WAAWzC,KAAKU,QAAAA,GACnBX,GAAGwB,gBAAgBvB,KAAKqB,kBAAAA,GAGxBrB,KAAKN,iBAAiBM,KAAKN,gBAAgB,KAAK;AAChD,kBAAMoH,KAAe9G,KAAKL,UAAUG,kBAAkBE,KAAKN,aAAAA;AAS3D,gBAAIqH,KAAe;AACnB,qBAAStC,KAAI,GAAGA,KAAIoC,GAAYG,YAAY5B,QAAQX,MAAK;AACvD,oBAAMwC,KAAKxC,KAAIzE,KAAKV,UAAU4F,OAAOtG,GAC/BsI,KAAMlH,KAAKL,UAAUE,WAAWsH,SAASF,IAAIA,KAAKJ,GAAYG,YAAYvC,EAAAA,IAAK7F,CAAAA;AACrFkI,cAAAA,GAAaM,IAAIF,IAAKH,EAAAA,GACtBA,MAAgBG,GAAI9B;YAAAA;AAItBrF,YAAAA,GAAG6B,WAAW7B,GAAG8B,cAAc7B,KAAKuC,iBAAAA,GACpCxC,GAAG+B,WAAW/B,GAAG8B,cAAciF,GAAaK,SAAS,GAAGJ,EAAAA,GAAehH,GAAGsH,WAAAA;AAG1E,qBAAS/I,KAAI,GAAGA,KAAI0B,KAAKuE,OAAO6B,MAAMhB,QAAQ9G;AACxC0B,mBAAKuE,OAAO6B,MAAM9H,EAAAA,EAAGgJ,YAAYtH,KAAK+C,eAAezE,EAAAA,EAAGgJ,WAC1DtH,KAAKuH,sBAAsBxH,IAAIC,KAAKuE,QAAQjG,EAAAA;AAKhDyB,YAAAA,GAAGyH,sBAAsBzH,GAAG0H,gBAAgB,GAAG1H,GAAGgE,eAAe,GAAGgD,KAAenI,CAAAA;UACrF;UAEO,SAAS8I,IAAAA;AACd1H,iBAAKuE,SAASmD;AACd,uBAAW1E,MAAahD,KAAK+C;AAC3BC,cAAAA,GAAUsE,UAAAA;UAEd;UAEQ,sBAAsBvH,IAA6B2H,IAAsBpJ,IAAAA;AAC/EyB,YAAAA,GAAGsD,cAActD,GAAGuD,WAAWhF,EAAAA,GAC/ByB,GAAGwD,YAAYxD,GAAGyD,YAAYxD,KAAK+C,eAAezE,EAAAA,EAAG8E,OAAAA,GACrDrD,GAAG0D,cAAc1D,GAAGyD,YAAYzD,GAAG2D,gBAAgB3D,GAAG4D,aAAAA,GACtD5D,GAAG0D,cAAc1D,GAAGyD,YAAYzD,GAAG6D,gBAAgB7D,GAAG4D,aAAAA,GACtD5D,GAAG8D,WAAW9D,GAAGyD,YAAY,GAAGzD,GAAG+D,MAAM/D,GAAG+D,MAAM/D,GAAGgE,eAAe2D,GAAMtB,MAAM9H,EAAAA,EAAG0H,MAAAA,GACnFjG,GAAG4H,eAAe5H,GAAGyD,UAAAA,GACrBxD,KAAK+C,eAAezE,EAAAA,EAAGgJ,UAAUI,GAAMtB,MAAM9H,EAAAA,EAAGgJ;UAClD;UAEO,cAAcM,IAAAA;AACnB5H,iBAAKR,cAAcoI;UACrB;QAAA;AAlSF,QAAAvJ,GAAA,gBAAA,GA6HSE,GAAA,CADN,EAAAsJ,SAAAA,GAAAA,EAAAA,WAAAA,cAAAA,IAAAA;MAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAA,OAAAA;AAAAA,eAAAA,eAAAA,IAAAA,cAAAA,EAAAA,OAAAA,KAAAA,CAAAA,GAAAA,GAAAA,oBAAAA;AClNH,cAAAtJ,KAAAD,GAAA,GAAA,GAKA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GAqCMwJ,IADwB,IACsBhJ,aAAaC;QAIjE,MAAMgJ,EAAAA;UAIJ,cAAAC;AACEhI,iBAAKH,aAAa,IAAIf,aAPgB,GAAA,GAQtCkB,KAAKJ,QAAQ;UACf;QAAA;AAIF,YAAIqI,IAAQ,GACRC,IAAM,GACNC,IAAM,GACNC,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK;QAET,MAAaC,UAA0B,EAAAnJ,WAAAA;UAYrC,YACUC,IACAC,IACAC,IACSiJ,IAAAA;AAEjBhJ,kBAAAA,GALQ,KAAAH,YAAAA,IACA,KAAAC,MAAAA,IACA,KAAAC,cAAAA,IACS,KAAAiJ,gBAAAA,IAPX,KAAA9I,YAAsB,IAAIoI,KAC1B,KAAAW,kBAA4B,IAAIX;AAUtC,kBAAMhI,KAAKC,KAAKT;AAEhBS,iBAAKU,YAAW,GAAAnC,GAAA8B,eAAa,GAAA,EAAAM,eAAcZ,IAzEpB,wYAgBE,6HAAA,CAAA,GA0DzBC,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAGiB,cAAchB,KAAKU,QAAAA,CAAAA,CAAAA,GAGvDV,KAAKiB,uBAAsB,GAAA1C,GAAA8B,cAAaN,GAAGmB,mBAAmBlB,KAAKU,UAAU,cAAA,CAAA,GAG7EV,KAAKqB,qBAAqBtB,GAAGuB,kBAAAA,GAC7BvB,GAAGwB,gBAAgBvB,KAAKqB,kBAAAA;AAGxB,kBAAMG,KAAmB,IAAI1C,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,CAAA,GAC1D2C,KAAyB1B,GAAG2B,aAAAA;AAClC1B,iBAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAaF,EAAAA,CAAAA,CAAAA,GACjD1B,GAAG6B,WAAW7B,GAAG8B,cAAcJ,EAAAA,GAC/B1B,GAAG+B,WAAW/B,GAAG8B,cAAcL,IAAkBzB,GAAGgC,WAAAA,GACpDhC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAAiC,GAAGjC,KAAKT,IAAI2C,OAAAA,OAAc,GAAG,CAAA;AAKrF,kBAAMC,KAAyB,IAAIC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAA,CAAA,GAClDC,KAAuBtC,GAAG2B,aAAAA;AAChC1B,iBAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAaU,EAAAA,CAAAA,CAAAA,GACjDtC,GAAG6B,WAAW7B,GAAGuC,sBAAsBD,EAAAA,GACvCtC,GAAG+B,WAAW/B,GAAGuC,sBAAsBH,IAAwBpC,GAAGgC,WAAAA,GAGlE/B,KAAKuC,qBAAoB,GAAAhE,GAAA8B,cAAaN,GAAG2B,aAAAA,CAAAA,GACzC1B,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAMhB,GAAG4B,aAAa3B,KAAKuC,iBAAAA,CAAAA,CAAAA,GACtDxC,GAAG6B,WAAW7B,GAAG8B,cAAc7B,KAAKuC,iBAAAA,GACpCxC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAAgC,GAAGlC,GAAGmC,OAAAA,OAAc4F,GAAqB,CAAA,GAChG/H,GAAGyC,oBAAoB,GAAgC,CAAA,GACvDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA4B,GAAGlC,GAAGmC,OAAAA,OAAc4F,GAAqB,IAAIhJ,aAAaC,iBAAAA,GAC7GgB,GAAGyC,oBAAoB,GAA4B,CAAA,GACnDzC,GAAGiC,wBAAwB,CAAA,GAC3BjC,GAAGkC,oBAAoB,GAA6B,GAAGlC,GAAGmC,OAAAA,OAAc4F,GAAqB,IAAIhJ,aAAaC,iBAAAA,GAC9GgB,GAAGyC,oBAAoB,GAA6B,CAAA,GAEpDxC,KAAK2I,oBAAoBF,GAAcG,MAAAA,GACvC5I,KAAKc,SAASd,KAAKyI,cAAcI,eAAezK,CAAAA,OAAAA;AAC9C4B,mBAAK2I,oBAAoBvK,EAAAA,GACzB4B,KAAK8I,yBAAAA;YAA0B,CAAA,CAAA;UAEnC;UAEO,oBAAAC;AACL/I,iBAAKgJ,gBAAgBhJ,KAAKL,SAAAA;UAC5B;UAEO,eAAAsJ;AACLjJ,iBAAKgJ,gBAAgBhJ,KAAK0I,eAAAA;UAC5B;UAEQ,gBAAgBQ,IAAAA;AACtB,kBAAMnJ,KAAKC,KAAKT;AAEhBQ,YAAAA,GAAG0C,WAAWzC,KAAKU,QAAAA,GAEnBX,GAAGwB,gBAAgBvB,KAAKqB,kBAAAA,GAExBtB,GAAG8C,iBAAiB7C,KAAKiB,qBAAAA,OAA4B,EAAA6B,iBAAAA,GAGrD/C,GAAG6B,WAAW7B,GAAG8B,cAAc7B,KAAKuC,iBAAAA,GACpCxC,GAAG+B,WAAW/B,GAAG8B,cAAcqH,GAASrJ,YAAYE,GAAGoJ,YAAAA,GACvDpJ,GAAGyH,sBAAsBxH,KAAKT,IAAIkI,gBAAgB,GAAG1H,GAAGgE,eAAe,GAAGmF,GAAStJ,KAAAA;UACrF;UAEO,eAAAyE;AACLrE,iBAAK8I,yBAAAA;UACP;UAEO,cAAclB,IAAAA;AACnB5H,iBAAKR,cAAcoI;UACrB;UAEQ,oBAAoBgB,IAAAA;AAC1B5I,iBAAKoJ,WAAWpJ,KAAKqJ,qBAAqBT,GAAOU,UAAAA,GACjDtJ,KAAKuJ,eAAevJ,KAAKqJ,qBAAqBT,GAAOY,MAAAA;UACvD;UAEQ,2BAAAV;AAEN9I,iBAAKyJ,mBACHzJ,KAAKL,UAAUE,YACf,GACA,GACA,GACAG,KAAKV,UAAU4F,OAAOlF,KAAKR,YAAYgG,OAAOC,KAAKC,OACnD1F,KAAKV,UAAUoH,OAAO1G,KAAKR,YAAYgG,OAAOC,KAAKQ,QACnDjG,KAAKoJ,QAAAA;UAET;UAEO,kBAAkBM,IAAAA;AACvB,kBAAMlD,KAAWxG,KAAKV,WAChB4J,KAAWlJ,KAAKL;AAGtB,gBACI8E,IACAD,IACAmF,IACAC,IACAC,IACAC,IACAC,IACApF,IACAC,IACAoF,IACApE,IAXAqE,KAAiB;AAarB,iBAAKxF,KAAI,GAAGA,KAAI+B,GAASE,MAAMjC,MAAK;AAKlC,mBAJAkF,KAAAA,IACAC,KAAY,GACZC,KAAY,GACZC,KAAAA,OACKtF,KAAI,GAAGA,KAAIgC,GAAStB,MAAMV;AAC7BuF,gBAAAA,MAAetF,KAAI+B,GAAStB,OAAQV,MAAK,EAAA0F,gCACzCvF,KAAK+E,GAAMS,MAAMJ,KAAa,EAAAK,sBAAAA,GAC9BxF,KAAK8E,GAAMS,MAAMJ,KAAa,EAAAM,sBAAAA,GAC9BL,KAAAA,CAAAA,EAAkB,WAALpF,MACTD,OAAOiF,MAAchF,OAAOiF,OAAcC,MAAkBE,UAE5C,MAAdJ,MAAoBE,MAAgC,MAAdD,QACxCjE,KA/KkB,IA+KTqE,MACTjK,KAAKsK,iBAAiBpB,IAAUtD,IAAQiE,IAAWD,IAAWD,IAAenF,IAAGC,EAAAA,IAElFkF,KAAgBnF,IAChBoF,KAAYjF,IACZkF,KAAYjF,IACZkF,KAAiBE;AAAAA,eAIH,MAAdJ,MAAoBE,MAAgC,MAAdD,QACxCjE,KA1LsB,IA0LbqE,MACTjK,KAAKsK,iBAAiBpB,IAAUtD,IAAQiE,IAAWD,IAAWD,IAAenD,GAAStB,MAAMT,EAAAA;YAAAA;AAGhGyE,YAAAA,GAAStJ,QAAQqK;UACnB;UAEO,aAAaP,IAAAA;AAClB,kBAAMR,KAAWlJ,KAAK0I,iBAChBc,KAASE,GAAMF;AACrB,gBAAA,CAAKA,MAA2B,YAAjBA,GAAOe;AAEpB,qBAAA,MADArB,GAAStJ,QAAQ;AAInB,gBAAIgG,IACAqE,KAAiB;AAEA,sBAAjBT,GAAOe,SAAoC,cAAjBf,GAAOe,UAEnC3E,KA9MwB,IA8MfqE,MACTjK,KAAKyJ,mBACHP,GAASrJ,YACT+F,IACA4D,GAAOhF,IAAIxE,KAAKR,YAAYgG,OAAOC,KAAKC,OACxC8D,GAAO/E,IAAIzE,KAAKR,YAAYgG,OAAOC,KAAKQ,QACvB,UAAjBuD,GAAOe,QAAkBf,GAAOgB,MAAMhB,GAAOiB,cAAcjB,GAAOgB,KAClExK,KAAKR,YAAYgG,OAAOC,KAAKQ,QAC7BjG,KAAKuJ,YAAAA,IAGY,gBAAjBC,GAAOe,SAA0C,cAAjBf,GAAOe,UAEzC3E,KA3NwB,IA2NfqE,MACTjK,KAAKyJ,mBACHP,GAASrJ,YACT+F,IACA4D,GAAOhF,IAAIxE,KAAKR,YAAYgG,OAAOC,KAAKC,QACvC8D,GAAO/E,IAAI,KAAKzE,KAAKR,YAAYgG,OAAOC,KAAKQ,SAASuD,GAAOgB,KAC9DhB,GAAO9D,QAAQ1F,KAAKR,YAAYgG,OAAOC,KAAKC,OAC5C8D,GAAOgB,KACPxK,KAAKuJ,YAAAA,IAGY,cAAjBC,GAAOe,UAET3E,KAxOwB,IAwOfqE,MACTjK,KAAKyJ,mBACHP,GAASrJ,YACT+F,IACA4D,GAAOhF,IAAIxE,KAAKR,YAAYgG,OAAOC,KAAKC,OACxC8D,GAAO/E,IAAIzE,KAAKR,YAAYgG,OAAOC,KAAKQ,QACxCuD,GAAO9D,QAAQ1F,KAAKR,YAAYgG,OAAOC,KAAKC,OAC5C8D,GAAOgB,KACPxK,KAAKuJ,YAAAA,GAGP3D,KAnPwB,IAmPfqE,MACTjK,KAAKyJ,mBACHP,GAASrJ,YACT+F,KACC4D,GAAOhF,IAAIgF,GAAO9D,SAAS1F,KAAKR,YAAYgG,OAAOC,KAAKC,QAAQ8D,GAAOgB,KACxEhB,GAAO/E,IAAIzE,KAAKR,YAAYgG,OAAOC,KAAKQ,QACxCuD,GAAOgB,KACPxK,KAAKR,YAAYgG,OAAOC,KAAKQ,QAC7BjG,KAAKuJ,YAAAA,IAITL,GAAStJ,QAAQqK;UACnB;UAEQ,iBAAiBf,IAAoBtD,IAAgBhB,IAAYD,IAAY+F,IAAgBC,IAAclG,IAAAA;AACjH,gBAAS,WAALG;AACF,sBAAa,WAALA,IAAAA;gBACN,KAAK;gBACL,KAAK;AACHqD,sBAAQjI,KAAKyI,cAAcG,OAAOgC,KAAU,MAALhG,EAAAA,EAA6BiG;AACpE;gBACF,KAAK;AACH5C,uBAAc,WAALrD,OAA6B;AACtC;gBAEF;AACEqD,sBAAQjI,KAAKyI,cAAcG,OAAOkC,WAAWD;cAAAA;;AAGjD,sBAAa,WAALlG,IAAAA;gBACN,KAAK;gBACL,KAAK;AACHsD,sBAAQjI,KAAKyI,cAAcG,OAAOgC,KAAU,MAALjG,EAAAA,EAA6BkG;AACpE;gBACF,KAAK;AACH5C,uBAAc,WAALtD,OAA6B;AACtC;gBAEF;AACEsD,sBAAQjI,KAAKyI,cAAcG,OAAOU,WAAWuB;cAAAA;AAI/C3B,YAAAA,GAASrJ,WAAWuF,SAASQ,KAAS,MACxCsD,GAASrJ,cAAa,GAAA,EAAAkL,oBAAmB7B,GAASrJ,YAAYG,KAAKV,UAAUoH,OAAO1G,KAAKV,UAAU4F,OAhS3E,CAAA,IAkS1BgD,IAAMwC,KAAS1K,KAAKR,YAAYgG,OAAOC,KAAKC,OAC5CyC,IAAM1D,KAAIzE,KAAKR,YAAYgG,OAAOC,KAAKQ,QACvCmC,KAAOH,KAAS,KAAM,OAAQ,KAC9BI,KAAOJ,KAAS,KAAM,OAAQ,KAC9BK,KAAOL,KAAS,IAAM,OAAQ,KAC9BM,IAAK,GAELvI,KAAKgL,cAAc9B,GAASrJ,YAAY+F,IAAQsC,GAAKC,IAAMwC,KAAOD,MAAU1K,KAAKR,YAAYgG,OAAOC,KAAKC,OAAO1F,KAAKR,YAAYgG,OAAOC,KAAKQ,QAAQmC,GAAIC,GAAIC,GAAIC,CAAAA;UACnK;UAEQ,cAActD,IAAqBW,IAAgBqF,IAAYC,IAAYxF,IAAeO,IAAgBzH,IAAW2M,IAAWC,IAAWzM,IAAAA;AACjJsG,YAAAA,GAAMW,EAAAA,IAAcqF,KAAKjL,KAAKR,YAAYgG,OAAOQ,OAAON,OACxDT,GAAMW,KAAS,CAAA,IAAKsF,KAAKlL,KAAKR,YAAYgG,OAAOQ,OAAOC,QACxDhB,GAAMW,KAAS,CAAA,IAAKF,KAAQ1F,KAAKR,YAAYgG,OAAOQ,OAAON,OAC3DT,GAAMW,KAAS,CAAA,IAAKK,KAASjG,KAAKR,YAAYgG,OAAOQ,OAAOC,QAC5DhB,GAAMW,KAAS,CAAA,IAAKpH,IACpByG,GAAMW,KAAS,CAAA,IAAKuF,IACpBlG,GAAMW,KAAS,CAAA,IAAKwF,IACpBnG,GAAMW,KAAS,CAAA,IAAKjH;UACtB;UAEQ,mBAAmBsG,IAAqBW,IAAgBqF,IAAYC,IAAYxF,IAAeO,IAAgBoF,IAAAA;AACrHpG,YAAAA,GAAMW,EAAAA,IAAcqF,KAAKjL,KAAKR,YAAYgG,OAAOQ,OAAON,OACxDT,GAAMW,KAAS,CAAA,IAAKsF,KAAKlL,KAAKR,YAAYgG,OAAOQ,OAAOC,QACxDhB,GAAMW,KAAS,CAAA,IAAKF,KAAQ1F,KAAKR,YAAYgG,OAAOQ,OAAON,OAC3DT,GAAMW,KAAS,CAAA,IAAKK,KAASjG,KAAKR,YAAYgG,OAAOQ,OAAOC,QAC5DhB,GAAMW,KAAS,CAAA,IAAKyF,GAAM,CAAA,GAC1BpG,GAAMW,KAAS,CAAA,IAAKyF,GAAM,CAAA,GAC1BpG,GAAMW,KAAS,CAAA,IAAKyF,GAAM,CAAA,GAC1BpG,GAAMW,KAAS,CAAA,IAAKyF,GAAM,CAAA;UAC5B;UAEQ,qBAAqBA,IAAAA;AAC3B,mBAAO,IAAIvM,aAAa,EACpBuM,GAAMR,QAAQ,KAAM,OAAQ,MAC5BQ,GAAMR,QAAQ,KAAM,OAAQ,MAC5BQ,GAAMR,QAAQ,IAAM,OAAQ,MACR,MAApBQ,GAAU,QAAkB,GAAA,CAAA;UAElC;QAAA;AAjTF,QAAAhN,GAAA,oBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,cAAAA,GAAA,yBAAAA,GAAA,0BAAAA,GAAA,yBAAAA,GAAA,yBAAAA,GAAA,iCAAA;ACpEA,cAAAE,KAAAD,GAAA,GAAA;AAEa,QAAAD,GAAA6L,iCAAiC,GACjC7L,GAAA+L,yBAAyB,GACzB/L,GAAAgM,yBAAyB,GACzBhM,GAAAiN,0BAA0B,GAE1BjN,GAAAkN,yBAAyB,YAEtClN,GAAA,cAAA,MAAA;UAME,cAAA2J;AACEhI,iBAAKmK,QAAQ,IAAIqB,YAAY,CAAA,GAC7BxL,KAAKgH,cAAc,IAAIwE,YAAY,CAAA,GACnCxL,KAAKyL,aAAY,GAAAlN,GAAAmN,4BAAAA;UACnB;UAEO,OAAOxG,IAAcwB,IAAAA;AAC1B,kBAAMiF,KAAazG,KAAOwB,KAAOrI,GAAA6L;AAC7ByB,YAAAA,OAAe3L,KAAKmK,MAAM/E,WAC5BpF,KAAKmK,QAAQ,IAAIqB,YAAYG,EAAAA,GAC7B3L,KAAKgH,cAAc,IAAIwE,YAAY9E,EAAAA;UAEvC;UAEO,QAAAH;AACLvG,iBAAKmK,MAAM7D,KAAK,GAAG,CAAA,GACnBtG,KAAKgH,YAAYV,KAAK,GAAG,CAAA;UAC3B;QAAA;MAAA,GAAA,KAAA,SAAAlI,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAC,IAAAC,KAAA,UAAA,QAAAC,KAAAD,KAAA,IAAAJ,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAAG,KAAA,QAAA,SAAAN,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAI,KAAAP,GAAA,SAAA,GAAAO,MAAA,GAAAA;AAAA,eAAAH,KAAAJ,GAAAO,EAAA,OAAAD,MAAAD,KAAA,IAAAD,GAAAE,EAAA,IAAAD,KAAA,IAAAD,GAAAH,IAAAC,IAAAI,EAAA,IAAAF,GAAAH,IAAAC,EAAA,MAAAI;AAAA,iBAAAD,KAAA,KAAAC,MAAA,OAAA,eAAAL,IAAAC,IAAAI,EAAA,GAAAA;QAAA;AAAA,eAAA,eAAAL,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,iBAAAA,GAAA,gBAAA;AClCF,cAAA,IAAAC,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA;QAGA,MAAasN,UAAsB,EAAAvM,WAAAA;UAiCjC,YACUC,IACSuM,IACAC,IACAC,IACAC,IACAC,IACAC,IACAzD,IACjB0D,IAAAA;AAEA1M,kBAAAA,GAVQ,KAAAH,YAAAA,IACS,KAAAuM,0BAAAA,IACA,KAAAC,mBAAAA,IACA,KAAAC,sBAAAA,IACA,KAAAC,eAAAA,IACA,KAAAC,qBAAAA,IACA,KAAAC,kBAAAA,IACA,KAAAzD,gBAAAA,IAvCX,KAAA2D,2BAAuE,IAAI,EAAAC,qBAC3E,KAAAC,uBAAuBtM,KAAKc,SAAS,IAAI,EAAAuL,mBAAAA,GAIzC,KAAAE,SAAsB,IAAI,EAAAC,eAC1B,KAAAC,YAAsB,IAAI,EAAAC,YAK1B,KAAAC,qBAA2D3M,KAAKc,SAAS,IAAI,EAAAuL,mBAAAA,GAC7E,KAAAO,iBAAmD5M,KAAKc,SAAS,IAAI,EAAAuL,mBAAAA,GAQ5D,KAAAQ,wBAAwB7M,KAAKc,SAAS,IAAI,EAAAgM,cAAAA,GAC3C,KAAAC,uBAAuB/M,KAAK6M,sBAAsBG,OACjD,KAAAC,2BAA2BjN,KAAKc,SAAS,IAAI,EAAAgM,cAAAA,GAC9C,KAAAI,0BAA0BlN,KAAKiN,yBAAyBD,OACvD,KAAAG,8BAA8BnN,KAAKc,SAAS,IAAI,EAAAgM,cAAAA,GACjD,KAAAM,6BAA6BpN,KAAKmN,4BAA4BH,OAC7D,KAAAK,mBAAmBrN,KAAKc,SAAS,IAAI,EAAAgM,cAAAA,GACtC,KAAAQ,kBAAkBtN,KAAKqN,iBAAiBL,OACvC,KAAAO,iBAAiBvN,KAAKc,SAAS,IAAI,EAAAgM,cAAAA,GACpC,KAAAU,gBAAgBxN,KAAKuN,eAAeP,OAelDhN,KAAKc,SAASd,KAAKyI,cAAcI,eAAe,MAAM7I,KAAKyN,mBAAAA,CAAAA,CAAAA,GAE3DzN,KAAK0N,qBAAqB,IAAI,EAAAC,kBAAkB3N,KAAKV,WAAWU,KAAKuM,OAAOd,WAAWzL,KAAKiM,oBAAoBjM,KAAK+L,qBAAqB/L,KAAKyI,aAAAA,GAE/IzI,KAAK4N,QAAS5N,KAAKV,UAAkBsO,OAErC5N,KAAK6N,gBAAgB,CACnB,IAAI,EAAAC,gBAAgB9N,KAAK4N,MAAMG,eAAgB,GAAG/N,KAAKV,WAAWU,KAAK4N,MAAMI,YAAYhO,KAAK+L,qBAAqBG,IAAiBlM,KAAKyI,aAAAA,CAAAA,GAE3IzI,KAAK4H,cAAa,GAAA,EAAAqG,wBAAAA,GAClBjO,KAAKkO,oBAAoBlO,KAAK+L,oBAAoBvB,KAClDxK,KAAKmO,kBAAAA,GACLnO,KAAKoO,mBAAAA,GACLpO,KAAKc,SAASoL,GAAgBmC,eAAe,MAAMrO,KAAKsO,sBAAAA,CAAAA,CAAAA,GAExDtO,KAAKuO,UAAUC,SAASC,cAAc,QAAA;AAEtC,kBAAMC,KAAoB,EACxBC,WAAAA,OACAC,OAAAA,OACAzC,uBAAAA,GAAAA;AAGF,gBADAnM,KAAKT,MAAMS,KAAKuO,QAAQM,WAAW,UAAUH,EAAAA,GAAAA,CACxC1O,KAAKT;AACR,oBAAM,IAAIuP,MAAM,0BAA0B9O,KAAKT,GAAAA;AAGjDS,iBAAKc,UAAS,GAAA,EAAAiO,0BAAyB/O,KAAKuO,SAAS,oBAAqBnQ,CAAAA,OAAAA;AACxE4Q,sBAAQC,IAAI,iCAAA,GAEZ7Q,GAAE8Q,eAAAA,GAGFlP,KAAKmP,6BAA6BC,WAAW,MAAA;AAC3CpP,qBAAKmP,6BAAAA,QACLH,QAAQK,KAAK,kDAAA,GACbrP,KAAKuN,eAAe+B,KAAKlR,EAAAA;cAAE,GAC1B,GAAA;YAAc,CAAA,CAAA,GAEnB4B,KAAKc,UAAS,GAAA,EAAAiO,0BAAyB/O,KAAKuO,SAAS,wBAAyBnQ,CAAAA,OAAAA;AAC5E4Q,sBAAQK,KAAK,qCAAA,GACbE,aAAavP,KAAKmP,0BAAAA,GAClBnP,KAAKmP,6BAAAA,SAGL,GAAA,EAAAK,yBAAwBxP,KAAKV,SAAAA,GAC7BU,KAAKyP,sBAAAA,GACLzP,KAAK0P,uBAAAA;YAAwB,CAAA,CAAA,GAG/B1P,KAAKc,UAAS,GAAA,EAAA6O,8BAA6B3P,KAAKuO,SAASvO,KAAK+L,oBAAoB6D,QAAQ,CAACC,IAAGC,OAAM9P,KAAK+P,gCAAgCF,IAAGC,EAAAA,CAAAA,CAAAA,GAE5I9P,KAAK4N,MAAMG,cAAeiC,YAAYhQ,KAAKuO,OAAAA,GAAAA,CAE1CvO,KAAK2M,mBAAmBsD,OAAOjQ,KAAK4M,eAAeqD,KAAAA,IAASjQ,KAAKyP,sBAAAA,GAElEzP,KAAKkQ,cAAclQ,KAAK+L,oBAAoB6D,OAAOpB,SAAS2B,KAAKC,SAASpQ,KAAK4N,MAAMG,aAAAA,GAErF/N,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAA;AAAA,kBAAA3C;AACzB,yBAAWiS,MAAKrQ,KAAK6N;AACnBwC,gBAAAA,GAAEC,QAAAA;AAEsB,wBAA1BlS,KAAA4B,KAAKuO,QAAQgC,kBAAAA,WAAanS,MAAAA,GAAEoS,YAAYxQ,KAAKuO,OAAAA,IAC7C,GAAA,EAAAiB,yBAAwBxP,KAAKV,SAAAA;YAAU,CAAA,CAAA;UAE3C;UAEA,IAAA,eAAWmR;AAAAA,gBAAAA;AACT,mBAAsB,UAAfrS,KAAA4B,KAAK0Q,eAAAA,WAAUtS,KAAA,SAAAA,GAAEgI,MAAM,CAAA,EAAGJ;UACnC;UAEQ,qBAAAyH;AACNzN,iBAAK2Q,kBAAAA,GAGL3Q,KAAK4Q,YAAAA,IAAY;UACnB;UAEO,+BAAAC;AAGD7Q,iBAAKkO,sBAAsBlO,KAAK+L,oBAAoBvB,QACtDxK,KAAKkO,oBAAoBlO,KAAK+L,oBAAoBvB,KAClDxK,KAAKqE,aAAarE,KAAKV,UAAU4F,MAAMlF,KAAKV,UAAUoH,IAAAA;UAE1D;UAEO,aAAaxB,IAAcwB,IAAAA;AAAAA,gBAAAA,IAAAA,IAAAA,IAAAA;AAEhC1G,iBAAKmO,kBAAAA,GAELnO,KAAKuM,OAAOuE,OAAO9Q,KAAKV,UAAU4F,MAAMlF,KAAKV,UAAUoH,IAAAA;AAGvD,uBAAW2J,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAES,OAAO9Q,KAAKV,WAAWU,KAAK4H,UAAAA;AAIhC5H,iBAAKuO,QAAQ7I,QAAQ1F,KAAK4H,WAAWpC,OAAOQ,OAAON,OACnD1F,KAAKuO,QAAQtI,SAASjG,KAAK4H,WAAWpC,OAAOQ,OAAOC,QACpDjG,KAAKuO,QAAQhE,MAAM7E,QAAQ,GAAG1F,KAAK4H,WAAWmJ,IAAI/K,OAAON,KAAAA,MACzD1F,KAAKuO,QAAQhE,MAAMtE,SAAS,GAAGjG,KAAK4H,WAAWmJ,IAAI/K,OAAOC,MAAAA,MAG1DjG,KAAK4N,MAAMG,cAAexD,MAAM7E,QAAQ,GAAG1F,KAAK4H,WAAWmJ,IAAI/K,OAAON,KAAAA,MACtE1F,KAAK4N,MAAMG,cAAexD,MAAMtE,SAAS,GAAGjG,KAAK4H,WAAWmJ,IAAI/K,OAAOC,MAAAA,MAE1C,UAA7B3H,KAAA0B,KAAK2M,mBAAmBsD,UAAAA,WAAK3R,MAAAA,GAAE0S,cAAchR,KAAK4H,UAAAA,GACrB,UAA7BrJ,KAAAyB,KAAK2M,mBAAmBsD,UAAAA,WAAK1R,MAAAA,GAAE8F,aAAAA,GACN,UAAzB7F,KAAAwB,KAAK4M,eAAeqD,UAAAA,WAAKzR,MAAAA,GAAEwS,cAAchR,KAAK4H,UAAAA,GACrB,UAAzBnJ,KAAAuB,KAAK4M,eAAeqD,UAAAA,WAAKxR,MAAAA,GAAE4F,aAAAA,GAE3BrE,KAAK2Q,kBAAAA,GAIL3Q,KAAK4Q,YAAAA,KAAY;UACnB;UAEO,wBAAAK;AACLjR,iBAAKqE,aAAarE,KAAKV,UAAU4F,MAAMlF,KAAKV,UAAUoH,IAAAA;UACxD;UAEO,aAAAwK;AAAAA,gBAAAA;AACL,uBAAWb,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEa,WAAWlR,KAAKV,SAAAA;AAEe,sBAAnClB,KAAA4B,KAAKoM,yBAAyB6D,UAAAA,WAAK7R,MAAAA,GAAE+S,MAAAA,GAErCnR,KAAK0P,uBAAAA;UACP;UAEO,cAAA0B;AAAAA,gBAAAA;AACL,uBAAWf,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEe,YAAYpR,KAAKV,SAAAA;AAEc,sBAAnClB,KAAA4B,KAAKoM,yBAAyB6D,UAAAA,WAAK7R,MAAAA,GAAEiT,OAAAA,GAErCrR,KAAK0P,uBAAAA;UACP;UAEO,uBAAuB4B,IAAqCC,IAAmCC,IAAAA;AACpG,uBAAWnB,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEoB,uBAAuBzR,KAAKV,WAAWgS,IAAOC,IAAKC,EAAAA;AAEvDxR,iBAAKuM,OAAOd,UAAUiG,OAAO1R,KAAKV,WAAWgS,IAAOC,IAAKC,EAAAA,GACzDxR,KAAK0P,uBAAAA;UACP;UAEO,mBAAAiC;AAAAA,gBAAAA;AACL,uBAAWtB,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEsB,iBAAiB3R,KAAKV,SAAAA;AAES,sBAAnClB,KAAA4B,KAAKoM,yBAAyB6D,UAAAA,WAAK7R,MAAAA,GAAEwT,sBAAAA;UACvC;UAEQ,wBAAAtD;AACNtO,iBAAKmO,kBAAAA,GACLnO,KAAK2Q,kBAAAA,GACL3Q,KAAKoO,mBAAAA;UACP;UAKQ,wBAAAqB;AAON,mBANAzP,KAAK2M,mBAAmBsD,QAAQ,IAAI,EAAAzH,kBAAkBxI,KAAKV,WAAWU,KAAKT,KAAKS,KAAK4H,YAAY5H,KAAKyI,aAAAA,GACtGzI,KAAK4M,eAAeqD,QAAQ,IAAI,EAAA7Q,cAAcY,KAAKV,WAAWU,KAAKT,KAAKS,KAAK4H,UAAAA,GAG7E5H,KAAKiR,sBAAAA,GAEE,CAACjR,KAAK2M,mBAAmBsD,OAAOjQ,KAAK4M,eAAeqD,KAAAA;UAC7D;UAKQ,oBAAAU;AAAAA,gBAAAA;AACN,gBAAI3Q,KAAK4H,WAAWpC,OAAOG,KAAKD,SAAS,KAAK1F,KAAK4H,WAAWpC,OAAOG,KAAKM,UAAU;AAGlF,qBAAA,MADAjG,KAAKkQ,cAAAA;AAIP,kBAAMxI,MAAQ,GAAA,EAAAmK,qBACZ7R,KAAKV,WACLU,KAAKkM,gBAAgB4F,YACrB9R,KAAKyI,cAAcG,QACnB5I,KAAK4H,WAAWpC,OAAOC,KAAKC,OAC5B1F,KAAK4H,WAAWpC,OAAOC,KAAKQ,QAC5BjG,KAAK4H,WAAWpC,OAAOG,KAAKD,OAC5B1F,KAAK4H,WAAWpC,OAAOG,KAAKM,QAC5BjG,KAAK+L,oBAAoBvB,GAAAA;AAEvBxK,iBAAK0Q,eAAehJ,OACtB1H,KAAK6M,sBAAsByC,KAAK5H,GAAMtB,MAAM,CAAA,EAAGJ,MAAAA,GAC/ChG,KAAKsM,qBAAqB2D,SAAQ,GAAA,EAAA8B,2BAA0B,EAC1D,GAAA,EAAAC,cAAatK,GAAMwF,yBAAyBlN,KAAKiN,wBAAAA,IACjD,GAAA,EAAA+E,cAAatK,GAAM0F,4BAA4BpN,KAAKmN,2BAAAA,CAAAA,CAAAA,IAGxDnN,KAAK0Q,aAAahJ,IAClB1H,KAAK0Q,WAAWuB,OAAAA,GACS,UAAzB7T,KAAA4B,KAAK4M,eAAeqD,UAAAA,WAAK7R,MAAAA,GAAE8T,SAASlS,KAAK0Q,UAAAA;UAC3C;UAOQ,YAAYyB,IAAAA;AAAAA,gBAAAA;AAClBnS,iBAAKuM,OAAOhG,MAAAA,GACR4L,OACuB,UAAzB9T,KAAA2B,KAAK4M,eAAeqD,UAAAA,WAAK5R,MAAAA,GAAEkI,MAAAA;UAE/B;UAEO,oBAAA6L;AAAAA,gBAAAA;AACU,sBAAfhU,KAAA4B,KAAK0Q,eAAAA,WAAUtS,MAAAA,GAAEiU,aAAAA,GACjBrS,KAAK4Q,YAAAA,IAAY,GACjB5Q,KAAK0P,uBAAAA;UACP;UAEO,QAAAnJ;AAAAA,gBAAAA;AACLvG,iBAAK4Q,YAAAA,IAAY;AACjB,uBAAWP,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEiC,MAAMtS,KAAKV,SAAAA;AAGoB,sBAAnClB,KAAA4B,KAAKoM,yBAAyB6D,UAAAA,WAAK7R,MAAAA,GAAEwT,sBAAAA,GACrC5R,KAAKoO,mBAAAA;UACP;UAEO,wBAAwBmE,IAAAA;AAC7B,mBAAA;UACF;UAEO,0BAA0BC,IAAAA;AAC/B,mBAAA;UACF;UAGO,WAAWlB,IAAeC,IAAAA;AAC/B,gBAAA,CAAKvR,KAAKkQ,aAAa;AACrB,kBAAA,EAAIlQ,KAAK+L,oBAAoB6D,OAAOpB,SAAS2B,KAAKC,SAASpQ,KAAK4N,MAAMG,aAAAA,KAAmB/N,KAAK8L,iBAAiBpG,SAAS1F,KAAK8L,iBAAiB7F;AAK5I;AAJAjG,mBAAKmO,kBAAAA,GACLnO,KAAK2Q,kBAAAA,GACL3Q,KAAKkQ,cAAAA;YAAc;AAOvB,uBAAWG,MAAKrQ,KAAK6N;AACnBwC,cAAAA,GAAEoC,kBAAkBzS,KAAKV,WAAWgS,IAAOC,EAAAA;AAGxCvR,iBAAK4M,eAAeqD,SAAUjQ,KAAK2M,mBAAmBsD,UAOvDjQ,KAAK4M,eAAeqD,MAAM3L,WAAAA,KAC5BtE,KAAK4Q,YAAAA,IAAY,GACjB5Q,KAAK0S,aAAa,GAAG1S,KAAKV,UAAUoH,OAAO,CAAA,KAG3C1G,KAAK0S,aAAapB,IAAOC,EAAAA,GAI3BvR,KAAK2M,mBAAmBsD,MAAMlH,kBAAAA,GAC9B/I,KAAK4M,eAAeqD,MAAM0C,OAAO3S,KAAKuM,MAAAA,GACjCvM,KAAKoM,yBAAyB6D,SAAAA,CAASjQ,KAAKoM,yBAAyB6D,MAAM2C,mBAC9E5S,KAAK2M,mBAAmBsD,MAAMhH,aAAAA;UAElC;UAEQ,qBAAAmF;AACFpO,iBAAKV,UAAUuT,QAAQC,cACzB9S,KAAKoM,yBAAyB6D,QAAQ,IAAI,EAAA8C,wBAAwB,MAAA;AAChE/S,mBAAKgT,qBAAAA;YAAsB,GAC1BhT,KAAK+L,mBAAAA,IAER/L,KAAKoM,yBAAyB7F,MAAAA,GAIhCvG,KAAKgT,qBAAAA;UACP;UAEQ,aAAa1B,IAAeC,IAAAA;AAClC,kBAAM/K,KAAWxG,KAAK4N;AACtB,gBAGI7I,IACAN,IACAwO,IACAC,IACAC,IACAC,IACAC,IACAC,IACAxO,IACAJ,IACApG,IACAkG,IACA+O,IAfA9N,KAAkBzF,KAAKyM;AAgB3B6E,YAAAA,KAAQkC,EAAMlC,IAAO9K,GAASE,OAAO,GAAG,CAAA,GACxC6K,KAAMiC,EAAMjC,IAAK/K,GAASE,OAAO,GAAG,CAAA;AAEpC,kBAAM+M,KAAUzT,KAAKV,UAAUoU,OAAOC,OAAOC,QAAQ5T,KAAKV,UAAUoU,OAAOC,OAAOF,SAE5EI,KAAU1T,KAAKC,IAAIJ,KAAKV,UAAUoU,OAAOC,OAAOE,SAASrN,GAAStB,OAAO,CAAA;AAC/E,gBAAI4O,IAAAA;AACJ,kBAAMlB,IACJ5S,KAAKgM,aAAa+H,uBAAAA,CACjB/T,KAAKgM,aAAagI,mBAAAA,CACjBhU,KAAKoM,yBAAyB6D,SAASjQ,KAAKoM,yBAAyB6D,MAAM2C;AAC/E5S,iBAAKuM,OAAO/C,SAAAA;AACZ,gBAAIyK,IAAAA;AAEJ,iBAAKxP,KAAI6M,IAAO7M,MAAK8M,IAAK9M;AAKxB,mBAJAwO,KAAMxO,KAAI+B,GAASkN,OAAOQ,OAC1BhB,KAAO1M,GAASkN,OAAOS,MAAMC,IAAInB,EAAAA,GACjCjT,KAAKuM,OAAOvF,YAAYvC,EAAAA,IAAK,GAC7B0O,KAAenT,KAAK6L,wBAAwBwI,oBAAoBpB,EAAAA,GAC3DzO,KAAI,GAAGA,KAAIgC,GAAStB,MAAMV;AAqE7B,oBApEAO,KAAS/E,KAAK0N,mBAAmB4G,OAAO3P,IACxCuO,GAAKqB,SAAS/P,IAAGiB,EAAAA,GAEP,MAANjB,OACFO,KAAS/E,KAAK0N,mBAAmB4G,OAAO3P,KAI1CyO,KAAAA,OACAC,KAAY7O,IAKR2O,GAAa/N,SAAS,KAAKZ,OAAM2O,GAAa,CAAA,EAAG,CAAA,MACnDC,KAAAA,MACAE,KAAQH,GAAaqB,MAAAA,GAIrB/O,KAAO,IAAIgP,EACThP,IACAyN,GAAMwB,kBAAAA,MAAwBpB,GAAM,CAAA,GAAIA,GAAM,CAAA,CAAA,GAC9CA,GAAM,CAAA,IAAKA,GAAM,CAAA,CAAA,GAInBD,KAAYC,GAAM,CAAA,IAAK,IAGzBxO,KAAQW,GAAKkP,SAAAA,GACbjQ,KAAOe,GAAKmP,QAAAA,GACZtW,MAAMmG,KAAI+B,GAAStB,OAAQV,MAAK,EAAA0F,gCAGhClK,KAAK0N,mBAAmBmH,QAAQpP,IAAMjB,IAAGyO,EAAAA,GAGrCL,KAAmBK,OAAQQ,OACzBjP,OAAMqP,OACR7T,KAAKuM,OAAO/C,SAAS,EACnBhF,GAAGqP,IACHpP,GAAGzE,KAAKV,UAAUoU,OAAOC,OAAOF,SAChC/N,OAAOD,GAAKqP,SAAAA,GACZvK,OAAOvK,KAAK+L,oBAAoBgJ,YAC7BvO,GAASqM,QAAQmC,eAAe,UAAWxO,GAASqM,QAAQoC,qBAC/DxK,aAAajE,GAASqM,QAAQpI,aAC9BD,KAAKxK,KAAKkO,kBAAAA,GAEZ4F,IAAcD,KAAUpO,GAAKqP,SAAAA,IAAa,IAExCtQ,MAAKqP,MAAWrP,MAAKsP,MACnB9T,KAAK+L,oBAAoBgJ,aACmB,aAA7CvO,GAASqM,QAAQmC,eAAe,YAAA,UAChChV,KAAK+L,oBAAoBgJ,aACe,YAAzCvO,GAASqM,QAAQoC,yBACnBjV,KAAK0N,mBAAmB4G,OAAO1P,KAC7B,WAAqB5E,KAAKyI,cAAcG,OAAOsM,aAAarK,QAAQ,IAAI,UAC1E7K,KAAK0N,mBAAmB4G,OAAO3P,KAC7B,WAAqB3E,KAAKyI,cAAcG,OAAOY,OAAOqB,QAAQ,IAAI,YAIpEnG,OAAS,EAAAS,mBACXnF,KAAKuM,OAAOvF,YAAYvC,EAAAA,IAAKD,KAAI,KAI/BxE,KAAKuM,OAAOpC,MAAM7L,EAAAA,MAAOoG,MACzB1E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAA8L,sBAAAA,MAA4BpK,KAAK0N,mBAAmB4G,OAAO3P,MACjF3E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAA+L,sBAAAA,MAA4BrK,KAAK0N,mBAAmB4G,OAAO1P,MACjF5E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAAgN,uBAAAA,MAA6BtL,KAAK0N,mBAAmB4G,OAAOzP,SAItFoP,IAAAA,MAGInP,GAAMM,SAAS,MACjBV,MAAQ,EAAA6G,yBAIVvL,KAAKuM,OAAOpC,MAAM7L,EAAAA,IAAKoG,IACvB1E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAA8L,sBAAAA,IAA0BpK,KAAK0N,mBAAmB4G,OAAO3P,IAC/E3E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAA+L,sBAAAA,IAA0BrK,KAAK0N,mBAAmB4G,OAAO1P,IAC/E5E,KAAKuM,OAAOpC,MAAM7L,KAAI,EAAAgN,uBAAAA,IAA2BtL,KAAK0N,mBAAmB4G,OAAOzP,KAEhF7E,KAAK4M,eAAeqD,MAAOkF,WAAW3Q,IAAGC,IAAGC,IAAM1E,KAAK0N,mBAAmB4G,OAAO3P,IAAI3E,KAAK0N,mBAAmB4G,OAAO1P,IAAI5E,KAAK0N,mBAAmB4G,OAAOzP,KAAKC,IAAOC,EAAAA,GAE/JqO;AAKF,uBAHA3N,KAAOzF,KAAKyM,WAGPjI,MAAKA,KAAI6O,IAAW7O;AACvB+O,oBAAAA,MAAM9O,KAAI+B,GAAStB,OAAQV,MAAK,EAAA0F,gCAChClK,KAAK4M,eAAeqD,MAAOkF,WAAW3Q,IAAGC,IAAG,EAAAU,gBAAgB,GAAG,GAAG,GAAG,EAAAiQ,gBAAgB,CAAA,GACrFpV,KAAKuM,OAAOpC,MAAMoJ,EAAAA,IAAK,EAAApO,gBACvBnF,KAAKuM,OAAOpC,MAAMoJ,KAAI,EAAAnJ,sBAAAA,IAA0BpK,KAAK0N,mBAAmB4G,OAAO3P,IAC/E3E,KAAKuM,OAAOpC,MAAMoJ,KAAI,EAAAlJ,sBAAAA,IAA0BrK,KAAK0N,mBAAmB4G,OAAO1P,IAC/E5E,KAAKuM,OAAOpC,MAAMoJ,KAAI,EAAAjI,uBAAAA,IAA2BtL,KAAK0N,mBAAmB4G,OAAOzP;AAKpFoP,iBACFjU,KAAK2M,mBAAmBsD,MAAOoF,kBAAkBrV,KAAKuM,MAAAA,GAExDvM,KAAK2M,mBAAmBsD,MAAOqF,aAAatV,KAAKuM,MAAAA;UACnD;UAKQ,oBAAA4B;AAEDnO,iBAAK8L,iBAAiBpG,SAAU1F,KAAK8L,iBAAiB7F,WAM3DjG,KAAK4H,WAAWpC,OAAOG,KAAKD,QAAQvF,KAAKoF,MAAMvF,KAAK8L,iBAAiBpG,QAAQ1F,KAAKkO,iBAAAA,GAKlFlO,KAAK4H,WAAWpC,OAAOG,KAAKM,SAAS9F,KAAKoV,KAAKvV,KAAK8L,iBAAiB7F,SAASjG,KAAKkO,iBAAAA,GAKnFlO,KAAK4H,WAAWpC,OAAOC,KAAKQ,SAAS9F,KAAKoF,MAAMvF,KAAK4H,WAAWpC,OAAOG,KAAKM,SAASjG,KAAKkM,gBAAgB4F,WAAW0D,UAAAA,GAIrHxV,KAAK4H,WAAWpC,OAAOG,KAAKG,MAAqD,MAA/C9F,KAAKkM,gBAAgB4F,WAAW0D,aAAmB,IAAIrV,KAAKsV,OAAOzV,KAAK4H,WAAWpC,OAAOC,KAAKQ,SAASjG,KAAK4H,WAAWpC,OAAOG,KAAKM,UAAU,CAAA,GAGhLjG,KAAK4H,WAAWpC,OAAOC,KAAKC,QAAQ1F,KAAK4H,WAAWpC,OAAOG,KAAKD,QAAQvF,KAAKsV,MAAMzV,KAAKkM,gBAAgB4F,WAAW4D,aAAAA,GAInH1V,KAAK4H,WAAWpC,OAAOG,KAAKE,OAAO1F,KAAKoF,MAAMvF,KAAKkM,gBAAgB4F,WAAW4D,gBAAgB,CAAA,GAI9F1V,KAAK4H,WAAWpC,OAAOQ,OAAOC,SAASjG,KAAKV,UAAUoH,OAAO1G,KAAK4H,WAAWpC,OAAOC,KAAKQ,QACzFjG,KAAK4H,WAAWpC,OAAOQ,OAAON,QAAQ1F,KAAKV,UAAU4F,OAAOlF,KAAK4H,WAAWpC,OAAOC,KAAKC,OAOxF1F,KAAK4H,WAAWmJ,IAAI/K,OAAOC,SAAS9F,KAAKsV,MAAMzV,KAAK4H,WAAWpC,OAAOQ,OAAOC,SAASjG,KAAKkO,iBAAAA,GAC3FlO,KAAK4H,WAAWmJ,IAAI/K,OAAON,QAAQvF,KAAKsV,MAAMzV,KAAK4H,WAAWpC,OAAOQ,OAAON,QAAQ1F,KAAKkO,iBAAAA,GAMzFlO,KAAK4H,WAAWmJ,IAAItL,KAAKQ,SAASjG,KAAK4H,WAAWpC,OAAOC,KAAKQ,SAASjG,KAAKkO,mBAC5ElO,KAAK4H,WAAWmJ,IAAItL,KAAKC,QAAQ1F,KAAK4H,WAAWpC,OAAOC,KAAKC,QAAQ1F,KAAKkO;UAC5E;UAEQ,gCAAgCxI,IAAeO,IAAAA;AACjDjG,iBAAKuO,QAAQ7I,UAAUA,MAAS1F,KAAKuO,QAAQtI,WAAWA,OAK5DjG,KAAKuO,QAAQ7I,QAAQA,IACrB1F,KAAKuO,QAAQtI,SAASA,IACtBjG,KAAK0P,uBAAAA;UACP;UAEQ,yBAAAA;AACN1P,iBAAKqN,iBAAiBiC,KAAK,EAAEgC,OAAO,GAAGC,KAAKvR,KAAKV,UAAUoH,OAAO,EAAA,CAAA;UACpE;UAEQ,uBAAAsM;AACN,kBAAMS,KAAUzT,KAAKV,UAAUoU,OAAOC,OAAOF;AAC7CzT,iBAAKqN,iBAAiBiC,KAAK,EAAEgC,OAAOmC,IAASlC,KAAKkC,GAAAA,CAAAA;UACpD;QAAA;AAtjBF,QAAApV,GAAA,gBAAA,GAmSSE,GAAA,CADN,EAAAsJ,SAAAA,GAAAA,EAAAA,WAAAA,cAAAA,IAAAA;QAwRH,MAAa4M,UAAuB,EAAAkB,cAAAA;UASlC,YAAYC,IAAsB9Q,IAAeY,IAAAA;AAC/CjG,kBAAAA,GANK,KAAAoW,UAAkB,GAGlB,KAAAC,eAAuB,IAI5B9V,KAAK4E,KAAKgR,GAAUhR,IACpB5E,KAAK2E,KAAKiR,GAAUjR,IACpB3E,KAAK8V,eAAehR,IACpB9E,KAAK+V,SAASrQ;UAChB;UAEO,aAAAsQ;AAEL,mBAAO;UACT;UAEO,WAAAlB;AACL,mBAAO9U,KAAK+V;UACd;UAEO,WAAApB;AACL,mBAAO3U,KAAK8V;UACd;UAEO,UAAAlB;AAGL,mBAAO;UACT;UAEO,gBAAgB3E,IAAAA;AACrB,kBAAM,IAAInB,MAAM,iBAAA;UAClB;UAEO,gBAAAmH;AACL,mBAAO,CAACjW,KAAK4E,IAAI5E,KAAK2U,SAAAA,GAAY3U,KAAK8U,SAAAA,GAAY9U,KAAK4U,QAAAA,CAAAA;UAC1D;QAAA;AAGF,iBAASpB,EAAMvD,IAAeiG,IAAa9V,KAAc,GAAA;AACvD,iBAAOD,KAAK+V,IAAI/V,KAAKC,IAAI6P,IAAOiG,EAAAA,GAAM9V,EAAAA;QACxC;AA/CA,QAAA/B,GAAA,iBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,YAAAA,GAAA,qBAAAA,GAAA,eAAAA,GAAA,gBAAAA,GAAA,oBAAA;ACnlBA,cAAAE,KAAAD,GAAA,GAAA;AA2BA,iBAAgB6X,EAAapW,IAA2BqW,IAAcC,IAAAA;AACpE,gBAAMC,MAAS,GAAA/X,GAAA8B,cAAaN,GAAGoW,aAAaC,EAAAA,CAAAA;AAI5C,cAHArW,GAAGwW,aAAaD,IAAQD,EAAAA,GACxBtW,GAAGyW,cAAcF,EAAAA,GACDvW,GAAG0W,mBAAmBH,IAAQvW,GAAG2W,cAAAA;AAE/C,mBAAOJ;AAGTtH,kBAAQ2H,MAAM5W,GAAG6W,iBAAiBN,EAAAA,CAAAA,GAClCvW,GAAG8W,aAAaP,EAAAA;QAClB;AAhCa,QAAAjY,GAAAyE,oBAAoB,IAAIhE,aAAa,CAChD,GAAG,GAAG,GAAG,GACT,GAAA,IAAO,GAAG,GACV,GAAG,GAAG,GAAG,GAAA,IACL,GAAG,GAAG,CAAA,CAAA,GAGZT,GAAA,gBAAA,SAA8B0B,IAA2B+W,IAAsBC,IAAAA;AAC7E,gBAAMC,KAAU,GAAAzY,GAAA8B,cAAaN,GAAGY,cAAAA,CAAAA;AAKhC,cAJAZ,GAAGkX,aAAaD,IAAS,GAAAzY,GAAA8B,cAAa8V,EAAapW,IAAIA,GAAGmX,eAAeJ,EAAAA,CAAAA,CAAAA,GACzE/W,GAAGkX,aAAaD,IAAS,GAAAzY,GAAA8B,cAAa8V,EAAapW,IAAIA,GAAGoX,iBAAiBJ,EAAAA,CAAAA,CAAAA,GAC3EhX,GAAGqX,YAAYJ,CAAAA,GACCjX,GAAGsX,oBAAoBL,GAASjX,GAAGuX,WAAAA;AAEjD,mBAAON;AAGThI,kBAAQ2H,MAAM5W,GAAGwX,kBAAkBP,CAAAA,CAAAA,GACnCjX,GAAGiB,cAAcgW,CAAAA;QACnB,GAEA3Y,GAAA,eAAA,GAaAA,GAAA,qBAAA,SAAmCgY,IAAsBH,IAAAA;AACvD,gBAAMsB,KAAYrX,KAAKC,IAAoB,IAAhBiW,GAAOjR,QAAY8Q,EAAAA,GACxCuB,KAAW,IAAI3Y,aAAa0Y,EAAAA;AAClC,mBAASlZ,KAAI,GAAGA,KAAI+X,GAAOjR,QAAQ9G;AACjCmZ,YAAAA,GAASnZ,EAAAA,IAAK+X,GAAO/X,EAAAA;AAEvB,iBAAOmZ;QACT,GAEApZ,GAAA,YAAA,MAAA;UAIE,YAAY+E,IAAAA;AACVpD,iBAAKoD,UAAUA,IACfpD,KAAKsH,UAAAA;UACP;QAAA;MAAA,GAAA,KAAA,CAAAlJ,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAA;ACvDF,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA;QAMA,MAAsBoZ,UAAwB,EAAArY,WAAAA;UAY5C,YACEmH,IACQmR,IACRC,IACAC,IACQC,IACW/L,IACAG,IACAzD,GAAAA;AAEnBhJ,kBAAAA,GARQ,KAAAkY,aAAAA,IAGA,KAAAG,SAAAA,IACW,KAAA/L,sBAAAA,IACA,KAAAG,kBAAAA,IACA,KAAAzD,gBAAAA,GAjBb,KAAAsP,mBAA2B,GAC3B,KAAAC,oBAA4B,GAC5B,KAAAC,mBAA2B,GAC3B,KAAAC,oBAA4B,GAC5B,KAAAC,kBAA0B,GAC1B,KAAAC,iBAAyB,GAe/BpY,KAAKuO,UAAUC,SAASC,cAAc,QAAA,GACtCzO,KAAKuO,QAAQ8J,UAAUC,IAAI,SAASV,EAAAA,QAAAA,GACpC5X,KAAKuO,QAAQhE,MAAMsN,SAASA,GAAOU,SAAAA,GACnCvY,KAAKwY,YAAAA,GACLxY,KAAK2X,WAAW3H,YAAYhQ,KAAKuO,OAAAA,GACjCvO,KAAKc,SAASd,KAAKyI,cAAcI,eAAezK,CAAAA,OAAAA;AAC9C4B,mBAAK2Q,kBAAkBnK,IAAUpI,EAAAA,GACjC4B,KAAKsS,MAAM9L,EAAAA;YAAS,CAAA,CAAA,GAEtBxG,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAA;AACzBf,mBAAKuO,QAAQkK,OAAAA;YAAQ,CAAA,CAAA;UAEzB;UAEQ,cAAAD;AACNxY,iBAAK0Y,QAAO,GAAA,EAAArY,cAAaL,KAAKuO,QAAQM,WAAW,MAAM,EAAE8J,OAAO3Y,KAAK8X,OAAAA,CAAAA,CAAAA,GAEhE9X,KAAK8X,UACR9X,KAAK4Y,UAAAA;UAET;UAEO,WAAWpS,IAAAA;UAA2B;UACtC,YAAYA,IAAAA;UAA2B;UACvC,iBAAiBA,IAAAA;UAA2B;UAC5C,kBAAkBA,IAAoBqS,IAAkBC,IAAAA;UAAuB;UAC/E,uBAAuBtS,IAAoB8K,IAAqCC,IAAmCC,KAAAA,OAA4B;UAAc;UAE1J,iBAAiBhL,IAAoBmS,IAAAA;AAE7C,gBAAIA,OAAU3Y,KAAK8X;AACjB;AAIF,kBAAMiB,KAAY/Y,KAAKuO;AACvBvO,iBAAK8X,SAASa,IAEd3Y,KAAKuO,UAAUvO,KAAKuO,QAAQyK,UAAAA,GAC5BhZ,KAAKwY,YAAAA,GACLxY,KAAK2X,WAAWsB,aAAajZ,KAAKuO,SAASwK,EAAAA,GAG3C/Y,KAAK2Q,kBAAkBnK,IAAUxG,KAAKyI,cAAcG,MAAAA,GACpD5I,KAAKyS,kBAAkBjM,IAAU,GAAGA,GAASE,OAAO,CAAA;UACtD;UAOQ,kBAAkBF,IAAoB0S,IAAAA;AACxClZ,iBAAK+X,oBAAoB,KAAK/X,KAAKgY,qBAAqB,MAG5DhY,KAAK0Q,cAAa,GAAAnS,GAAAsT,qBAAoBrL,IAAUxG,KAAKkM,gBAAgB4F,YAAYoH,IAAUlZ,KAAKiY,kBAAkBjY,KAAKkY,mBAAmBlY,KAAK+X,kBAAkB/X,KAAKgY,mBAAmBhY,KAAK+L,oBAAoBvB,GAAAA,GAClNxK,KAAK0Q,WAAWuB,OAAAA;UAClB;UAEO,OAAOzL,IAAoB2S,IAAAA;AAChCnZ,iBAAKiY,mBAAmBkB,GAAI3T,OAAOC,KAAKC,OACxC1F,KAAKkY,oBAAoBiB,GAAI3T,OAAOC,KAAKQ,QACzCjG,KAAK+X,mBAAmBoB,GAAI3T,OAAOG,KAAKD,OACxC1F,KAAKgY,oBAAoBmB,GAAI3T,OAAOG,KAAKM,QACzCjG,KAAKmY,kBAAkBgB,GAAI3T,OAAOG,KAAKE,MACvC7F,KAAKoY,iBAAiBe,GAAI3T,OAAOG,KAAKG,KACtC9F,KAAKuO,QAAQ7I,QAAQyT,GAAI3T,OAAOQ,OAAON,OACvC1F,KAAKuO,QAAQtI,SAASkT,GAAI3T,OAAOQ,OAAOC,QACxCjG,KAAKuO,QAAQhE,MAAM7E,QAAQ,GAAGyT,GAAIpI,IAAI/K,OAAON,KAAAA,MAC7C1F,KAAKuO,QAAQhE,MAAMtE,SAAS,GAAGkT,GAAIpI,IAAI/K,OAAOC,MAAAA,MAGzCjG,KAAK8X,UACR9X,KAAK4Y,UAAAA,GAGP5Y,KAAK2Q,kBAAkBnK,IAAUxG,KAAKyI,cAAcG,MAAAA;UACtD;UAUU,uBAAuBpE,IAAWC,IAAWiB,KAAgB,GAAA;AACrE1F,iBAAK0Y,KAAKU,SACR5U,KAAIxE,KAAKiY,mBACRxT,KAAI,KAAKzE,KAAKkY,oBAAoBlY,KAAK+L,oBAAoBvB,MAAM,GAClE9E,KAAQ1F,KAAKiY,kBACbjY,KAAK+L,oBAAoBvB,GAAAA;UAC7B;UAKU,YAAAoO;AACJ5Y,iBAAK8X,SACP9X,KAAK0Y,KAAKW,UAAU,GAAG,GAAGrZ,KAAKuO,QAAQ7I,OAAO1F,KAAKuO,QAAQtI,MAAAA,KAE3DjG,KAAK0Y,KAAKY,YAAYtZ,KAAKyI,cAAcG,OAAOU,WAAWyH,KAC3D/Q,KAAK0Y,KAAKU,SAAS,GAAG,GAAGpZ,KAAKuO,QAAQ7I,OAAO1F,KAAKuO,QAAQtI,MAAAA;UAE9D;UASU,YAAYzB,IAAWC,IAAWiB,IAAeO,IAAAA;AACrDjG,iBAAK8X,SACP9X,KAAK0Y,KAAKW,UACR7U,KAAIxE,KAAKiY,kBACTxT,KAAIzE,KAAKkY,mBACTxS,KAAQ1F,KAAKiY,kBACbhS,KAASjG,KAAKkY,iBAAAA,KAEhBlY,KAAK0Y,KAAKY,YAAYtZ,KAAKyI,cAAcG,OAAOU,WAAWyH,KAC3D/Q,KAAK0Y,KAAKU,SACR5U,KAAIxE,KAAKiY,kBACTxT,KAAIzE,KAAKkY,mBACTxS,KAAQ1F,KAAKiY,kBACbhS,KAASjG,KAAKkY,iBAAAA;UAEpB;UAWU,mBAAmB1R,IAAoBf,IAAgBjB,IAAWC,IAAAA;AAC1EzE,iBAAK0Y,KAAKa,OAAOvZ,KAAKwZ,SAAShT,IAAAA,OAAU,KAAO,GAChDxG,KAAK0Y,KAAKe,eAAe,EAAAC,eACzB1Z,KAAK2Z,UAAUnV,IAAGC,IAAGgB,GAAKqP,SAAAA,CAAAA,GAC1B9U,KAAK0Y,KAAKkB,SACRnU,GAAKkP,SAAAA,GACLnQ,KAAIxE,KAAKiY,mBAAmBjY,KAAKmY,iBACjC1T,KAAIzE,KAAKkY,oBAAoBlY,KAAKoY,iBAAiBpY,KAAKgY,iBAAAA;UAC5D;UAQQ,UAAUxT,IAAWC,IAAWiB,IAAAA;AACtC1F,iBAAK0Y,KAAKmB,UAAAA,GACV7Z,KAAK0Y,KAAKoB,KACRtV,KAAIxE,KAAKiY,kBACTxT,KAAIzE,KAAKkY,mBACTxS,KAAQ1F,KAAKiY,kBACbjY,KAAKkY,iBAAAA,GACPlY,KAAK0Y,KAAKqB,KAAAA;UACZ;UAOU,SAASvT,IAAoBwT,IAAiBC,IAAAA;AAItD,mBAAO,GAFWA,KAAW,WAAW,EAAA,IADrBD,KAASxT,GAASqM,QAAQqH,iBAAiB1T,GAASqM,QAAQsH,UAAAA,IAG1C3T,GAASqM,QAAQuH,WAAYpa,KAAK+L,oBAAoBvB,GAAAA,MAAShE,GAASqM,QAAQwH,UAAAA;UACvH;QAAA;AAxMF,QAAAhc,GAAA,kBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAA;ACZA,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAMA,IAAAA,GAAA,GAAA;QAEA,MAAawP,UAAwB,EAAA4J,gBAAAA;UAGnC,YACE4C,IACAzC,IACArR,IACAwH,IACAuM,IACAC,IACAC,IAAAA;AAEAhb,kBAAM+G,IAAU8T,IAAW,QAAQzC,IAAAA,MAAc0C,IAAoBC,IAAgBC,EAAAA,GAErFza,KAAKc,SAASkN,GAAW0M,oBAAoBtc,CAAAA,OAAK4B,KAAK2a,yBAAyBvc,EAAAA,CAAAA,CAAAA,GAChF4B,KAAKc,SAASkN,GAAW4M,oBAAoBxc,CAAAA,OAAK4B,KAAK6a,yBAAyBzc,EAAAA,CAAAA,CAAAA;UAClF;UAEO,OAAOoI,IAAoB2S,IAAAA;AAChC1Z,kBAAMqR,OAAOtK,IAAU2S,EAAAA,GAEvBnZ,KAAK8a,SAAAA;UACP;UAEO,MAAMtU,IAAAA;AACXxG,iBAAK+a,kBAAAA;UACP;UAEQ,oBAAAA;AACN,gBAAI/a,KAAK8a,QAAQ;AACf9a,mBAAKgb,YAAYhb,KAAK8a,OAAO7P,IAAIjL,KAAK8a,OAAO5P,IAAIlL,KAAK8a,OAAO5V,OAAOlF,KAAK8a,OAAO7P,IAAI,CAAA;AACpF,oBAAMgQ,KAAiBjb,KAAK8a,OAAOI,KAAKlb,KAAK8a,OAAO5P,KAAK;AACrD+P,cAAAA,KAAiB,KACnBjb,KAAKgb,YAAY,GAAGhb,KAAK8a,OAAO5P,KAAK,GAAGlL,KAAK8a,OAAO5V,MAAM+V,EAAAA,GAE5Djb,KAAKgb,YAAY,GAAGhb,KAAK8a,OAAOI,IAAIlb,KAAK8a,OAAOK,IAAI,CAAA,GACpDnb,KAAK8a,SAAAA;YAASM;UAElB;UAEQ,yBAAyBhd,IAAAA;AAU/B,gBATIA,GAAEwG,OAAO,EAAAyW,yBACXrb,KAAK0Y,KAAKY,YAAYtZ,KAAKyI,cAAcG,OAAOU,WAAWyH,MAAAA,WAClD3S,GAAEwG,OAAoB,GAAArG,GAAA+c,YAAWld,GAAEwG,EAAAA,IAE5C5E,KAAK0Y,KAAKY,YAAYtZ,KAAKyI,cAAcG,OAAOgC,KAAKxM,GAAEwG,EAAAA,EAAKmM,MAE5D/Q,KAAK0Y,KAAKY,YAAYtZ,KAAKyI,cAAcG,OAAOkC,WAAWiG,KAGzD3S,GAAE8M,OAAO9M,GAAE8c;AAEblb,mBAAKub,uBAAuBnd,GAAE6M,IAAI7M,GAAE8M,IAAI9M,GAAE+c,KAAK/c,GAAE6M,EAAAA;iBAC5C;AAELjL,mBAAKub,uBAAuBnd,GAAE6M,IAAI7M,GAAE8M,IAAI9M,GAAE8G,OAAO9G,GAAE6M,EAAAA;AACnD,uBAASxG,KAAIrG,GAAE8M,KAAK,GAAGzG,KAAIrG,GAAE8c,IAAIzW;AAC/BzE,qBAAKub,uBAAuB,GAAG9W,IAAGrG,GAAE8G,IAAAA;AAEtClF,mBAAKub,uBAAuB,GAAGnd,GAAE8c,IAAI9c,GAAE+c,EAAAA;YAAAA;AAEzCnb,iBAAK8a,SAAS1c;UAChB;UAEQ,yBAAyBA,IAAAA;AAC/B4B,iBAAK+a,kBAAAA;UACP;QAAA;AAlEF,QAAA1c,GAAA,kBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,2BAAA,QCCAA,GAAA,2BAAA,SACEmd,IACApF,IACA7D,IACAM,IAAAA;AAEA2I,UAAAA,GAAKC,iBAAiBrF,IAAM7D,IAASM,EAAAA;AACrC,cAAI6I,IAAAA;AACJ,iBAAO,EACLpL,SAAS,MAAA;AACHoL,kBAGJA,IAAAA,MACAF,GAAKG,oBAAoBvF,IAAM7D,IAASM,EAAAA;UAAQ,EAAA;QAGtD;MAAA,GAAA,KAAA,CAAAzU,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,oBAAA;ACvBA,YAKIud,IALAC,KAAM,GACNC,IAAM,GACNC,IAAAA,OACAC,IAAAA,OACAC,IAAAA;AAGJ,QAAA5d,GAAA,oBAAA,MAAA;UAWE,YACmBiB,IACA4c,IACAjQ,IACAF,IACAtD,IAAAA;AAJA,iBAAAnJ,YAAAA,IACA,KAAA4c,wBAAAA,IACA,KAAAjQ,qBAAAA,IACA,KAAAF,sBAAAA,IACA,KAAAtD,gBAAAA,IAXH,KAAA6L,SAAkD,EAChE1P,IAAI,GACJD,IAAI,GACJE,KAAK,EAAA;UAUP;UAMO,QAAQY,IAAiBjB,IAAWC,GAAAA;AACzCzE,iBAAKsU,OAAO3P,KAAKc,GAAKd,IACtB3E,KAAKsU,OAAO1P,KAAKa,GAAKb,IACtB5E,KAAKsU,OAAOzP,MAAgB,YAAVY,GAAKd,KAA4Bc,GAAK0W,SAAStX,MAAM,GAKvEiX,IAAM,GACND,KAAM,GACNG,IAAAA,OACAD,IAAAA,OACAE,IAAAA,OACAL,KAAU5b,KAAKyI,cAAcG,QAG7B5I,KAAKiM,mBAAmBmQ,wBAAwB5X,IAAGC,GAAG,UAAU4X,CAAAA,OAAAA;AAC1DA,cAAAA,GAAEC,uBACJR,IAAMO,GAAEC,mBAAmBzR,QAAQ,IAAI,UACvCmR,IAAAA,OAEEK,GAAEE,uBACJV,KAAMQ,GAAEE,mBAAmB1R,QAAQ,IAAI,UACvCkR,IAAAA;YAAS,CAAA,GAKbE,IAAcjc,KAAKkc,sBAAsBM,eAAexc,KAAKV,WAAWkF,IAAGC,CAAAA,GACvEwX,MACFH,KAAO9b,KAAK+L,oBAAoBgJ,YAAY6G,GAAQa,4BAA4Bb,GAAQc,mCAAmC7R,QAAQ,IAAI,UACvImR,IAAAA,MACIJ,GAAQe,wBACVd,KAAMD,GAAQe,oBAAoB9R,QAAQ,IAAI,UAC9CkR,IAAAA,QAKJ/b,KAAKiM,mBAAmBmQ,wBAAwB5X,IAAGC,GAAG,OAAO4X,CAAAA,OAAAA;AACvDA,cAAAA,GAAEC,uBACJR,IAAMO,GAAEC,mBAAmBzR,QAAQ,IAAI,UACvCmR,IAAAA,OAEEK,GAAEE,uBACJV,KAAMQ,GAAEE,mBAAmB1R,QAAQ,IAAI,UACvCkR,IAAAA;YAAS,CAAA,GAMTC,MAGAF,IAFEG,IAAAA,YAEKxW,GAAKd,KAAAA,aAA4CmX,IAAM,WAAA,YAGvDrW,GAAKd,KAA6BmX,IAAM,WAG/CC,MAEFF,KAAAA,YAAOpW,GAAKb,KAAAA,YAAgDiX,KAAM,WAK/C,WAAjB7b,KAAKsU,OAAO1P,OACVoX,KAAAA,CAAWD,MAGXF,KAD4C,MAAxB,WAAjB7b,KAAKsU,OAAO3P,MAAAA,aACR3E,KAAKsU,OAAO1P,KAAuG,WAA/BgX,GAAQtS,WAAWuB,QAAQ,IAAuC,WAAA,aAEtJ7K,KAAKsU,OAAO1P,KAAuF,WAAjB5E,KAAKsU,OAAO3P,IAEvGoX,IAAAA,OAAS,CAENC,KAAUD,MAGXD,IAD4C,MAAxB,WAAjB9b,KAAKsU,OAAO1P,MAAAA,YACR5E,KAAKsU,OAAO3P,KAAqF,WAA/BiX,GAAQ9Q,WAAWD,QAAQ,IAAuC,WAAA,YAEpI7K,KAAKsU,OAAO3P,KAAqE,WAAjB3E,KAAKsU,OAAO1P,IAErFoX,IAAAA,QAKJJ,KAAAA,QAGA5b,KAAKsU,OAAO3P,KAAKqX,IAASF,IAAM9b,KAAKsU,OAAO3P,IAC5C3E,KAAKsU,OAAO1P,KAAKmX,IAASF,KAAM7b,KAAKsU,OAAO1P;UAC9C;QAAA;MAAA,GAAA,KAAA,CAAAxG,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,0BAAAA,GAAA,sBAAA;AClIF,cAAAE,KAAAD,GAAA,GAAA,GAIA,IAAAA,GAAA,GAAA,GAUMse,IAA4C,CAAA;AAMlD,QAAAve,GAAA,sBAAA,SACEmI,IACAqM,IACAjK,IACAiU,GACAC,GACAC,GACAC,GACAC,GAAAA;AAEA,gBAAMC,KAAY,GAAA,EAAAC,gBAAeN,GAAiBC,GAAkBC,GAAiBC,GAAkBnK,IAASjK,IAAQqU,CAAAA;AAGxH,mBAAS3e,KAAI,GAAGA,KAAIse,EAAexX,QAAQ9G,MAAK;AAC9C,kBAAM8e,KAAQR,EAAete,EAAAA,GACvB+e,KAAeD,GAAME,QAAQC,QAAQ/W,EAAAA;AAC3C,gBAAI6W,MAAgB,GAAG;AACrB,mBAAI,GAAA,EAAAG,cAAaJ,GAAMK,QAAQP,CAAAA;AAC7B,uBAAOE,GAAM1V;AAGc,oBAAzB0V,GAAME,QAAQlY,UAChBgY,GAAM1V,MAAM4I,QAAAA,GACZsM,EAAec,OAAOpf,IAAG,CAAA,KAEzB8e,GAAME,QAAQI,OAAOL,IAAc,CAAA;AAErC;YAAA;UAAA;AAKJ,mBAAS/e,KAAI,GAAGA,KAAIse,EAAexX,QAAQ9G,MAAK;AAC9C,kBAAM8e,KAAQR,EAAete,EAAAA;AAC7B,iBAAI,GAAA,EAAAkf,cAAaJ,GAAMK,QAAQP,CAAAA;AAG7B,qBADAE,GAAME,QAAQK,KAAKnX,EAAAA,GACZ4W,GAAM1V;UAAAA;AAIjB,gBAAMkW,IAAmBpX,GAAiBoH,OACpCiQ,IAAoC,EACxCnW,OAAO,IAAInJ,GAAA0B,aAAauO,UAAU0O,GAAWU,EAAKE,cAAAA,GAClDL,QAAQP,GACRI,SAAS,CAAC9W,EAAAA,EAAAA;AAGZ,iBADAoW,EAAee,KAAKE,CAAAA,GACbA,EAASnW;QAClB,GAMArJ,GAAA,0BAAA,SAAwCmI,IAAAA;AACtC,mBAASlI,KAAI,GAAGA,KAAIse,EAAexX,QAAQ9G,MAAK;AAC9C,kBAAMyf,KAAQnB,EAAete,EAAAA,EAAGgf,QAAQC,QAAQ/W,EAAAA;AAChD,gBAAA,OAAIuX,IAAc;AACyB,oBAArCnB,EAAete,EAAAA,EAAGgf,QAAQlY,UAE5BwX,EAAete,EAAAA,EAAGoJ,MAAM4I,QAAAA,GACxBsM,EAAec,OAAOpf,IAAG,CAAA,KAGzBse,EAAete,EAAAA,EAAGgf,QAAQI,OAAOK,IAAO,CAAA;AAE1C;YAAA;UAAA;QAGN;MAAA,GAAA,KAAA,CAAA3f,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,eAAAA,GAAA,iBAAA;ACtFA,cAAAE,KAAAD,GAAA,GAAA;AAEA,QAAAD,GAAA,iBAAA,SAA+Bwe,IAAyBC,IAA0BC,IAAyBC,GAA0BnK,GAAqCjK,GAA0BqU,GAAAA;AAElM,gBAAMe,IAA0B,EAC9BlT,YAAYlC,EAAOkC,YACnBxB,YAAYV,EAAOU,YACnBE,QAAQjL,GAAA0f,YACR/I,cAAc3W,GAAA0f,YACdtB,qBAAqBpe,GAAA0f,YACrBC,gCAAgC3f,GAAA0f,YAChCxB,2BAA2Ble,GAAA0f,YAC3BE,wCAAwC5f,GAAA0f,YACxCvB,mCAAmCne,GAAA0f,YAGnCrT,MAAMhC,EAAOgC,KAAKwT,MAAAA,GAClBC,eAAezV,EAAOyV,eACtBC,mBAAmB1V,EAAO0V,kBAAAA;AAE5B,iBAAO,EACLC,cAAc1L,EAAQ0L,cACtBtB,kBAAAA,GACAvH,eAAe7C,EAAQ6C,eACvBF,YAAY3C,EAAQ2C,YACpBqH,iBAAiBA,IACjBC,kBAAkBA,IAClBC,iBAAiBA,IACjBC,kBAAkBA,GAClB3C,YAAYxH,EAAQwH,YACpBD,UAAUvH,EAAQuH,UAClBD,YAAYtH,EAAQsH,YACpBD,gBAAgBrH,EAAQqH,gBACxBsE,mBAAmB3L,EAAQ2L,mBAC3BC,4BAA4B5L,EAAQ4L,4BACpCC,sBAAsB7L,EAAQ6L,sBAC9B9V,QAAQoV,EAAAA;QAEZ,GAEA3f,GAAA,eAAA,SAA6BM,IAAqByM,IAAAA;AAChD,mBAAS9M,KAAI,GAAGA,KAAIK,GAAEiK,OAAOgC,KAAKxF,QAAQ9G;AACxC,gBAAIK,GAAEiK,OAAOgC,KAAKtM,EAAAA,EAAGuM,SAASO,GAAExC,OAAOgC,KAAKtM,EAAAA,EAAGuM;AAC7C,qBAAA;AAGJ,iBAAOlM,GAAEse,qBAAqB7R,GAAE6R,oBAC5Bte,GAAE4f,iBAAiBnT,GAAEmT,gBACrB5f,GAAE6W,eAAepK,GAAEoK,cACnB7W,GAAE+W,kBAAkBtK,GAAEsK,iBACtB/W,GAAE0b,eAAejP,GAAEiP,cACnB1b,GAAEyb,aAAahP,GAAEgP,YACjBzb,GAAEwb,eAAe/O,GAAE+O,cACnBxb,GAAEub,mBAAmB9O,GAAE8O,kBACvBvb,GAAE6f,sBAAsBpT,GAAEoT,qBAC1B7f,GAAEoe,oBAAoB3R,GAAE2R,mBACxBpe,GAAEqe,qBAAqB5R,GAAE4R,oBACzBre,GAAE8f,+BAA+BrT,GAAEqT,8BACnC9f,GAAE+f,yBAAyBtT,GAAEsT,wBAC7B/f,GAAEiK,OAAOkC,WAAWD,SAASO,GAAExC,OAAOkC,WAAWD,QACjDlM,GAAEiK,OAAOU,WAAWuB,SAASO,GAAExC,OAAOU,WAAWuB;QACvD,GAEAxM,GAAA,aAAA,SAA2BsgB,IAAAA;AACzB,iBAA4C,aAAxB,WAAZA,OAA8F,aAAxB,WAAZA;QACpE;MAAA,GAAA,KAAA,CAAAvgB,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAAA,GAAA,cAAAA,GAAA,yBAAA;ACrEA,cAAAE,KAAAD,GAAA,GAAA;AAEa,QAAAD,GAAAgd,yBAAyB,KAEzBhd,GAAAugB,cAAc,KAIdvgB,GAAAqb,gBAAoCnb,GAAAsgB,aAAatgB,GAAAugB,eAAe,WAAW;MAAA,GAAA,KAAA,CAAA1gB,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,0BAAA;ACDxF,QAAAA,GAAA,0BAAA,MAAA;UAcE,YACU0gB,IACAhT,IAAAA;AADA,iBAAAgT,kBAAAA,IACA,KAAAhT,sBAAAA,IAER/L,KAAK4S,kBAAAA,MACD5S,KAAK+L,oBAAoBgJ,aAC3B/U,KAAKgf,iBAAAA;UAET;UAEA,IAAA,WAAWC;AAAsB,mBAAA,EAASjf,KAAKkf,sBAAsBlf,KAAKmf;UAAiB;UAEpF,UAAA7O;AACDtQ,iBAAKmf,mBACPnf,KAAK+L,oBAAoB6D,OAAOwP,cAAcpf,KAAKmf,cAAAA,GACnDnf,KAAKmf,iBAAAA,SAEHnf,KAAKkf,uBACPlf,KAAK+L,oBAAoB6D,OAAOL,aAAavP,KAAKkf,kBAAAA,GAClDlf,KAAKkf,qBAAAA,SAEHlf,KAAKqf,oBACPrf,KAAK+L,oBAAoB6D,OAAO0P,qBAAqBtf,KAAKqf,eAAAA,GAC1Drf,KAAKqf,kBAAAA;UAET;UAEO,wBAAAzN;AACD5R,iBAAKif,aAITjf,KAAKuf,0BAA0BC,KAAKC,IAAAA,GAEpCzf,KAAK4S,kBAAAA,MACA5S,KAAKqf,oBACRrf,KAAKqf,kBAAkBrf,KAAK+L,oBAAoB6D,OAAO8P,sBAAsB,MAAA;AAC3E1f,mBAAK+e,gBAAAA,GACL/e,KAAKqf,kBAAAA;YAA2B,CAAA;UAGtC;UAEQ,iBAAiBM,KAAsBC,KAAAA;AAEzC5f,iBAAKmf,mBACPnf,KAAK+L,oBAAoB6D,OAAOwP,cAAcpf,KAAKmf,cAAAA,GACnDnf,KAAKmf,iBAAAA,SAOPnf,KAAKkf,qBAAqBlf,KAAK+L,oBAAoB6D,OAAOR,WAAW,MAAA;AAGnE,kBAAIpP,KAAKuf,yBAAyB;AAChC,sBAAMM,KA1ES,OA0EgBL,KAAKC,IAAAA,IAAQzf,KAAKuf;AAEjD,oBADAvf,KAAKuf,0BAAAA,QACDM,KAAO;AAET,yBAAA,KADA7f,KAAKgf,iBAAiBa,EAAAA;cAAAA;AAM1B7f,mBAAK4S,kBAAAA,OACL5S,KAAKqf,kBAAkBrf,KAAK+L,oBAAoB6D,OAAO8P,sBAAsB,MAAA;AAC3E1f,qBAAK+e,gBAAAA,GACL/e,KAAKqf,kBAAAA;cAA2B,CAAA,GAIlCrf,KAAKmf,iBAAiBnf,KAAK+L,oBAAoB6D,OAAOkQ,YAAY,MAAA;AAEhE,oBAAI9f,KAAKuf,yBAAyB;AAGhC,wBAAMM,KA/FO,OA+FkBL,KAAKC,IAAAA,IAAQzf,KAAKuf;AAGjD,yBAFAvf,KAAKuf,0BAAAA,QAA0BnE,KAC/Bpb,KAAKgf,iBAAiBa,EAAAA;gBAAAA;AAKxB7f,qBAAK4S,kBAAAA,CAAmB5S,KAAK4S,iBAC7B5S,KAAKqf,kBAAkBrf,KAAK+L,oBAAoB6D,OAAO8P,sBAAsB,MAAA;AAC3E1f,uBAAK+e,gBAAAA,GACL/e,KAAKqf,kBAAAA;gBAA2B,CAAA;cAChC,GA1Ga,GAAA;YA2GC,GACjBM,EAAAA;UACL;UAEO,QAAAxO;AACLnR,iBAAK4S,kBAAAA,MACD5S,KAAKmf,mBACPnf,KAAK+L,oBAAoB6D,OAAOwP,cAAcpf,KAAKmf,cAAAA,GACnDnf,KAAKmf,iBAAAA,SAEHnf,KAAKkf,uBACPlf,KAAK+L,oBAAoB6D,OAAOL,aAAavP,KAAKkf,kBAAAA,GAClDlf,KAAKkf,qBAAAA,SAEHlf,KAAKqf,oBACPrf,KAAK+L,oBAAoB6D,OAAO0P,qBAAqBtf,KAAKqf,eAAAA,GAC1Drf,KAAKqf,kBAAAA;UAET;UAEO,SAAAhO;AAELrR,iBAAKmR,MAAAA,GAELnR,KAAKuf,0BAAAA,QACLvf,KAAKgf,iBAAAA,GACLhf,KAAK4R,sBAAAA;UACP;QAAA;MAAA,GAAA,KAAA,CAAAxT,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,oBAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,0BAAA;AC3IF,cAAAE,KAAAD,GAAA,GAAA;AASa,QAAAD,GAAA0hB,0BAA2E,EAEtF,KAAK,CAAC,EAAEvb,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAG7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAG7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGzD,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGzH,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CACX,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrD,MAAa,CACX,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrD,MAAa,CAAC,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAEtL,GAAG,GAAGC,GAAG,GAAGoL,GAAG,GAAGC,GAAG,EAAA,CAAA,EAAA;AASnE,cAAMkQ,IAAgF,EAEpF,KAAK,CACH,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,CAAA,GAEZ,KAAK,CACH,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,CAAA,GAEN,KAAK,CACH,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,CAAA,EAAA;AAgDK,QAAA3hB,GAAA4hB,wBAAwH,EAEnI,KAAK,EAAE,GAAgB,cAAA,GACvB,KAAK,EAAE,GAAc,cAAA,GACrB,KAAK,EAAE,GAAgB,cAAA,GACvB,KAAK,EAAE,GAAc,cAAA,GACrB,KAAK,EAAE,GAAgB,sBAAA,GACvB,KAAK,EAAE,GAAc,sBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,0BAAA,GACvB,KAAK,EAAE,GAAc,0BAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GAGrB,KAAK,EAAE,GAAgB,CAACC,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GACxF,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GACtF,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,cAAgB,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,aAAe,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,cAAgB,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,aAAe,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,mBAA+B,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAClH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,YAAAA,GACpG,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,kBAA8B,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAClH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GAC3G,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,SAAAA,GACvG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAChH,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAC7G,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAChH,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,kBAA8B,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GAC9G,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAG1O,KAAK,EAAE,GAAgB,YAAA,GACvB,KAAK,EAAE,GAAgB,YAAA,GACvB,KAAK,EAAE,GAAgB,sBAAA,GAGvB,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAG3F,KAAK,EAAE,GAAgB,8BAAA,GACvB,KAAK,EAAE,GAAc,8BAAA,GACrB,KAAK,EAAE,GAAgB,wDAAA,GACvB,KAAK,EAAE,GAAc,wDAAA,GACrB,KAAK,EAAE,GAAgB,8DAAA,GACvB,KAAK,EAAE,GAAc,8DAAA,GACrB,KAAK,EAAE,GAAgB,8BAAA,GACvB,KAAK,EAAE,GAAc,8BAAA,GACrB,KAAK,EAAE,GAAgB,wDAAA,GACvB,KAAK,EAAE,GAAc,wDAAA,GACrB,KAAK,EAAE,GAAgB,uDAAA,GACvB,KAAK,EAAE,GAAc,uDAAA,GAGrB,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,EAAA,GA0BlF9hB,GAAA+hB,uBAA0D,EAErE,KAAY,EAAE/D,GAAG,mBAAmBjG,MAAM,GAAiBiK,cAAc,EAAA,GAEzE,KAAY,EAAEhE,GAAG,yBAAyBjG,MAAM,GAAmBkK,aAAa,GAAGD,cAAc,EAAA,GAEjG,KAAY,EAAEhE,GAAG,mBAAmBjG,MAAM,GAAiBkK,aAAa,EAAA,GAExE,KAAY,EAAEjE,GAAG,uBAAuBjG,MAAM,GAAmBkK,aAAa,GAAGD,cAAc,EAAA,GAE/F,KAAY,EAAEhE,GAAG,wDAAwDjG,MAAM,GAAiBiK,cAAc,EAAA,GAE9G,KAAY,EAAEhE,GAAG,sDAAsDjG,MAAM,GAAmBiK,cAAc,EAAA,GAE9G,KAAY,EAAEhE,GAAG,wDAAwDjG,MAAM,GAAiBkK,aAAa,EAAA,GAE7G,KAAY,EAAEjE,GAAG,2DAA2DjG,MAAM,GAAmBkK,aAAa,EAAA,GAElH,KAAY,EAAEjE,GAAG,8BAA8BjG,MAAM,EAAA,GAErD,KAAY,EAAEiG,GAAG,qBAAqBjG,MAAM,GAAmBkK,aAAa,GAAGD,cAAc,EAAA,GAE7F,KAAY,EAAEhE,GAAG,8BAA8BjG,MAAM,EAAA,GAErD,KAAY,EAAEiG,GAAG,8BAA8BjG,MAAM,EAAA,GAErD,KAAY,EAAEiG,GAAG,qBAAqBjG,MAAM,GAAmBkK,aAAa,GAAGD,cAAc,EAAA,GAE7F,KAAY,EAAEhE,GAAG,8BAA8BjG,MAAM,EAAA,EAAA,GAGvD/X,GAAA+hB,qBAAA,GAAA,IAAmC/hB,GAAA+hB,qBAAqB,GAAA,GAExD/hB,GAAA+hB,qBAAA,GAAA,IAAmC/hB,GAAA+hB,qBAAqB,GAAA,GAMxD/hB,GAAA,oBAAA,SACEkiB,IACAC,IACAC,IACAC,GACA7D,GACAC,GACA1C,GACA6C,GAAAA;AAEA,gBAAM0D,IAAyBtiB,GAAA0hB,wBAAwBS,EAAAA;AACvD,cAAIG;AAEF,mBAwBJ,SACEJ,IACAK,IACAH,IACAC,IACA7D,IACAC,IAAAA;AAEA,uBAASxe,KAAI,GAAGA,KAAIsiB,GAAexb,QAAQ9G,MAAK;AAC9C,sBAAMuiB,KAAMD,GAAetiB,EAAAA,GACrBwiB,KAAUjE,KAAkB,GAC5BkE,KAAUjE,KAAmB;AACnCyD,gBAAAA,GAAInH,SACFqH,KAAUI,GAAIrc,IAAIsc,IAClBJ,KAAUG,GAAIpc,IAAIsc,IAClBF,GAAIhR,IAAIiR,IACRD,GAAI/Q,IAAIiR,EAAAA;cAAAA;YAGd,EA5CyBR,IAAKI,GAAwBF,IAASC,GAAS7D,GAAiBC,CAAAA,GAAAA;AAIvF,gBAAMkE,IAAoBhB,EAA4BQ,EAAAA;AACtD,cAAIQ;AAEF,mBAyCJ,SACET,IACAK,IACAH,IACAC,IACA7D,IACAC,IAAAA;AAEA,kBAAImE,KAAaC,EAAe9M,IAAIwM,EAAAA;AAC/BK,cAAAA,OACHA,KAAa,oBAAIE,OACjBD,EAAe9Z,IAAIwZ,IAAgBK,EAAAA;AAErC,oBAAM3H,KAAYiH,GAAIjH;AACtB,kBAAyB,YAAA,OAAdA;AACT,sBAAM,IAAIxK,MAAM,8BAA8BwK,EAAAA,GAAAA;AAEhD,kBAAI8H,KAAUH,GAAW7M,IAAIkF,EAAAA;AAC7B,kBAAA,CAAK8H,IAAS;AACZ,sBAAM1b,KAAQkb,GAAe,CAAA,EAAGxb,QAC1Ba,KAAS2a,GAAexb,QACxBic,KAAY7S,SAASC,cAAc,QAAA;AACzC4S,gBAAAA,GAAU3b,QAAQA,IAClB2b,GAAUpb,SAASA;AACnB,sBAAMqb,MAAS,GAAA/iB,GAAA8B,cAAaghB,GAAUxS,WAAW,IAAA,CAAA,GAC3C0S,KAAY,IAAIC,UAAU9b,IAAOO,EAAAA;AAGvC,oBAAIzH,IACA2M,IACAC,IACAzM;AACJ,oBAAI2a,GAAUmI,WAAW,GAAA;AACvBjjB,kBAAAA,KAAIkjB,SAASpI,GAAU8E,MAAM,GAAG,CAAA,GAAI,EAAA,GACpCjT,KAAIuW,SAASpI,GAAU8E,MAAM,GAAG,CAAA,GAAI,EAAA,GACpChT,KAAIsW,SAASpI,GAAU8E,MAAM,GAAG,CAAA,GAAI,EAAA,GACpCzf,KAAI2a,GAAUlU,SAAS,KAAKsc,SAASpI,GAAU8E,MAAM,GAAG,CAAA,GAAI,EAAA,KAAO;qBAC9D;AAAA,sBAAA,CAAI9E,GAAUmI,WAAW,MAAA;AAG9B,0BAAM,IAAI3S,MAAM,sCAAsCwK,EAAAA,8BAAAA;AAAAA,mBAFpD9a,IAAG2M,IAAGC,IAAGzM,EAAAA,IAAK2a,GAAUqI,UAAU,GAAGrI,GAAUlU,SAAS,CAAA,EAAGwc,MAAM,GAAA,EAAKC,IAAIzjB,CAAAA,OAAK0jB,WAAW1jB,EAAAA,CAAAA;gBAAAA;AAK9F,yBAASqG,KAAI,GAAGA,KAAIwB,IAAQxB;AAC1B,2BAASD,KAAI,GAAGA,KAAIkB,IAAOlB;AACzB+c,oBAAAA,GAAUQ,KAAuB,KAAjBtd,KAAIiB,KAAQlB,GAAAA,IAAchG,IAC1C+iB,GAAUQ,KAAuB,KAAjBtd,KAAIiB,KAAQlB,MAAS,CAAA,IAAK2G,IAC1CoW,GAAUQ,KAAuB,KAAjBtd,KAAIiB,KAAQlB,MAAS,CAAA,IAAK4G,IAC1CmW,GAAUQ,KAAuB,KAAjBtd,KAAIiB,KAAQlB,MAAS,CAAA,IAAKoc,GAAenc,EAAAA,EAAGD,EAAAA,KAAU,MAAJ7F;AAGtE2iB,gBAAAA,GAAOU,aAAaT,IAAW,GAAG,CAAA,GAClCH,MAAU,GAAA7iB,GAAA8B,cAAakgB,GAAI0B,cAAcZ,IAAW,IAAA,CAAA,GACpDJ,GAAW7Z,IAAIkS,IAAW8H,EAAAA;cAAAA;AAE5Bb,cAAAA,GAAIjH,YAAY8H,IAChBb,GAAInH,SAASqH,IAASC,IAAS7D,IAAiBC,EAAAA;YAClD,EAnGoByD,IAAKS,GAAmBP,IAASC,GAAS7D,GAAiBC,CAAAA,GAAAA;AAI7E,gBAAMoF,IAAuB7jB,GAAA4hB,sBAAsBO,EAAAA;AACnD,cAAI0B;AAEF,mBAsIJ,SACE3B,IACAK,IACAH,IACAC,IACA7D,IACAC,IACAG,IAAAA;AAEAsD,cAAAA,GAAI4B,cAAc5B,GAAIjH;AACtB,yBAAK,CAAOa,IAAYiI,EAAAA,KAAiBC,OAAOC,QAAQ1B,EAAAA,GAAiB;AAGvE,oBAAI2B;AAFJhC,gBAAAA,GAAI1G,UAAAA,GACJ0G,GAAIiC,YAAYvF,KAAmBwF,OAAOf,SAASvH,EAAAA,GAKjDoI,KAH0B,cAAA,OAAjBH,KAGYA,GAFV,MACA,OAAMtF,KAAmBD,EAAAA,IAGfuF;AAEvB,2BAAWM,MAAeH,GAAmBX,MAAM,GAAA,GAAM;AACvD,wBAAMxL,KAAOsM,GAAY,CAAA,GACnBC,KAAIC,EAA0BxM,EAAAA;AACpC,sBAAA,CAAKuM,IAAG;AACN3T,4BAAQ2H,MAAM,4CAA4CP,EAAAA,GAAAA;AAC1D;kBAAA;AAEF,wBAAMyM,KAAiBH,GAAYf,UAAU,CAAA,EAAGC,MAAM,GAAA;AACjDiB,kBAAAA,GAAK,CAAA,KAAOA,GAAK,CAAA,KAGtBF,GAAEpC,IAAKuC,EAAcD,IAAMhG,IAAiBC,IAAkB2D,IAASC,IAAAA,MAAezD,EAAAA,CAAAA;gBAAAA;AAExFsD,gBAAAA,GAAIwC,OAAAA,GACJxC,GAAIyC,UAAAA;cAAAA;YAER,EA5KuBzC,IAAK2B,GAAsBzB,IAASC,GAAS7D,GAAiBC,GAAkBG,CAAAA,GAAAA;AAIrG,gBAAMgG,IAAsB5kB,GAAA+hB,qBAAqBI,EAAAA;AACjD,iBAAA,CAAA,CAAIyC,MAyKN,SACE1C,IACAK,IACAH,IACAC,IACA7D,IACAC,IACA1C,IACA6C,IAAAA;AAAAA,gBAAAA,IAAAA;AAGA,kBAAMiG,KAAa,IAAIC;AACvBD,YAAAA,GAAWpJ,KAAK2G,IAASC,IAAS7D,IAAiBC,EAAAA,GACnDyD,GAAIxG,KAAKmJ,EAAAA,GAET3C,GAAI1G,UAAAA;AAEJ,kBAAMuJ,KAAehJ,KAAW;AAChCmG,YAAAA,GAAIiC,YAAYvF,KAAmBmG;AACnC,uBAAWV,MAAe9B,GAAevE,EAAEuF,MAAM,GAAA,GAAM;AACrD,oBAAMxL,KAAOsM,GAAY,CAAA,GACnBC,KAAIC,EAA0BxM,EAAAA;AACpC,kBAAA,CAAKuM,IAAG;AACN3T,wBAAQ2H,MAAM,4CAA4CP,EAAAA,GAAAA;AAC1D;cAAA;AAEF,oBAAMyM,KAAiBH,GAAYf,UAAU,CAAA,EAAGC,MAAM,GAAA;AACjDiB,cAAAA,GAAK,CAAA,KAAOA,GAAK,CAAA,KAGtBF,GAAEpC,IAAKuC,EACLD,IACAhG,IACAC,IACA2D,IACAC,IAAAA,OAEAzD,KAC2B,UAA1BuD,KAAAI,GAAeN,gBAAAA,WAAWE,KAAAA,KAAI,MAAM4C,KAAe,KACxB,UAA3B/G,KAAAuE,GAAeP,iBAAAA,WAAYhE,KAAAA,KAAI,MAAM+G,KAAe,EAAA,CAAA;YAAA;AAG7B,kBAAxBxC,GAAexK,QACjBmK,GAAI4B,cAAc5B,GAAIjH,WACtBiH,GAAIwC,OAAAA,KAEJxC,GAAIja,KAAAA,GAENia,GAAIyC,UAAAA;UACN,EAzNsBzC,IAAK0C,GAAqBxC,IAASC,GAAS7D,GAAiBC,GAAkB1C,GAAU6C,CAAAA,GAAAA;QAK/G;AAuBA,cAAMiE,IAAoF,oBAAIC;AA+L9F,iBAAS3N,EAAMvD,IAAeiG,IAAa9V,KAAc,GAAA;AACvD,iBAAOD,KAAK+V,IAAI/V,KAAKC,IAAI6P,IAAOiG,EAAAA,GAAM9V,EAAAA;QACxC;AAEA,cAAMwiB,IAAsD,EAC1D,GAAK,CAACrC,IAA+BsC,OAAmBtC,GAAI8C,cAAcR,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,GAC5H,GAAK,CAACtC,IAA+BsC,OAAmBtC,GAAI+C,OAAOT,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,GACjF,GAAK,CAACtC,IAA+BsC,OAAmBtC,GAAIgD,OAAOV,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,EAAA;AAGnF,iBAASC,EAAcD,IAAgBW,IAAmBC,IAAoBhD,IAAiBC,IAAiBgD,IAAkBzG,IAA0BqD,KAAsB,GAAGD,IAAuB,GAAA;AAC1M,gBAAM/L,IAASuO,GAAKhB,IAAIzjB,CAAAA,OAAK0jB,WAAW1jB,EAAAA,KAAMsjB,SAAStjB,EAAAA,CAAAA;AAEvD,cAAIkW,EAAOlP,SAAS;AAClB,kBAAM,IAAI0J,MAAM,mCAAA;AAGlB,mBAAStK,KAAI,GAAGA,KAAI8P,EAAOlP,QAAQZ,MAAK;AAEtC8P,cAAO9P,EAAAA,KAAMgf,KAAalD,KAAcrD,KAAqBoD,IAAepD,IAGxEyG,MAAyB,MAAdpP,EAAO9P,EAAAA,MACpB8P,EAAO9P,EAAAA,IAAKgP,EAAMrT,KAAKsV,MAAMnB,EAAO9P,EAAAA,IAAK,GAAA,IAAO,KAAKgf,IAAW,CAAA,IAGlElP,EAAO9P,EAAAA,KAAMic,KAAWH,KAAcrD;AAGxC,mBAASxY,KAAI,GAAGA,KAAI6P,EAAOlP,QAAQX,MAAK;AAEtC6P,cAAO7P,EAAAA,KAAMgf,IAGTC,MAAyB,MAAdpP,EAAO7P,EAAAA,MACpB6P,EAAO7P,EAAAA,IAAK+O,EAAMrT,KAAKsV,MAAMnB,EAAO7P,EAAAA,IAAK,GAAA,IAAO,KAAKgf,IAAY,CAAA,IAGnEnP,EAAO7P,EAAAA,KAAMic;AAGf,iBAAOpM;QACT;MAAA,GAAA,IAAA,CAAAlW,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,+BAAA;ACzqBA,cAAAE,KAAAD,GAAA,GAAA;AAGA,QAAAD,GAAA,+BAAA,SAA6CslB,IAAsBC,IAA0CC,IAAAA;AAK3G,cAAIC,IAAuC,IAAIF,GAAaG,eAAgBzB,CAAAA,OAAAA;AAC1E,kBAAMlF,KAAQkF,GAAQ0B,KAAM5G,CAAAA,OAAUA,GAAM6G,WAAWN,EAAAA;AACvD,gBAAA,CAAKvG;AACH;AAIF,gBAAA,EAAM,+BAA+BA;AAGnC,qBAFA0G,QAAAA,KAAAA,EAAUI,WAAAA,GAAAA,MACVJ,IAAAA;AAKF,kBAAMpe,IAAQ0X,GAAM+G,0BAA0B,CAAA,EAAGC,YAC3Cne,IAASmX,GAAM+G,0BAA0B,CAAA,EAAGE;AAC9C3e,gBAAQ,KAAKO,IAAS,KACxB4d,GAASne,GAAOO,CAAAA;UAAAA,CAAAA;AAGpB,cAAA;AACE6d,cAASQ,QAAQX,IAAS,EAAE9C,KAAK,CAAC,0BAAA,EAAA,CAAA;UAAA,SAClCziB,IAAA;AACA0lB,cAASI,WAAAA,GACTJ,IAAAA;UAAW1I;AAEb,kBAAO,GAAA7c,GAAAwC,cAAa,MAAM+iB,QAAAA,IAAAA,SAAAA,EAAUI,WAAAA,CAAAA;QACtC;MAAA,GAAA,KAAA,CAAA9lB,IAAAC,OAAA;AC1BA,iBAAgBkmB,GAAiBC,IAAAA;AAI/B,iBAAO,SAAUA,MAAaA,MAAa;QAC7C;AAAA,eAAA,eAAAnmB,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,yBAAAA,GAAA,kCAAAA,GAAA,6BAAAA,GAAA,mBAAAA,GAAA,eAAA,QAZAA,GAAA,eAAA,SAAgC4R,IAAAA;AAC9B,cAAA,CAAKA;AACH,kBAAM,IAAInB,MAAM,yBAAA;AAElB,iBAAOmB;QACT,GAEA5R,GAAA,mBAAAC,IAOAD,GAAA,6BAAA,SAA2CmmB,IAAAA;AACzC,iBAAO,SAAUA,MAAaA,MAAa;QAC7C,GAMAnmB,GAAA,kCAAA,SAAgDmmB,IAAAA;AAC9C,iBAAOD,GAAiBC,EAAAA,KAL1B,SAA2BA,IAAAA;AACzB,mBAAO,QAAUA,MAAaA,MAAa;UAC7C,EAG0DA,EAAAA;QAC1D,GAEAnmB,GAAA,yBAAA,WAAA;AACE,iBAAO,EACL0S,KAAK,EACH/K,QAiBG,EACLN,OAAO,GACPO,QAAQ,EAAA,GAlBNR,MAgBG,EACLC,OAAO,GACPO,QAAQ,EAAA,EAAA,GAhBRT,QAAQ,EACNQ,QAaG,EACLN,OAAO,GACPO,QAAQ,EAAA,GAdNR,MAYG,EACLC,OAAO,GACPO,QAAQ,EAAA,GAbNN,MAAM,EACJD,OAAO,GACPO,QAAQ,GACRJ,MAAM,GACNC,KAAK,EAAA,EAAA,EAAA;QAIb;MAAA,GAAA,KAAA,CAAA1H,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,6BAAA;QC1CA,MAAMomB,GAAAA;UAYJ,cAAAzc;AACEhI,iBAAKuG,MAAAA;UACP;UAEO,QAAAA;AACLvG,iBAAK0kB,eAAAA,OACL1kB,KAAKwR,mBAAAA,OACLxR,KAAK2kB,mBAAmB,GACxB3kB,KAAK4kB,iBAAiB,GACtB5kB,KAAK6kB,yBAAyB,GAC9B7kB,KAAK8kB,uBAAuB,GAC5B9kB,KAAK+kB,WAAW,GAChB/kB,KAAKglB,SAAS,GACdhlB,KAAKilB,iBAAAA,QACLjlB,KAAKklB,eAAAA;UACP;UAEO,OAAO1e,IAAoB8K,IAAqCC,IAAmCC,KAAAA,OAA4B;AAIpI,gBAHAxR,KAAKilB,iBAAiB3T,IACtBtR,KAAKklB,eAAe3T,IAAAA,CAEfD,MAAAA,CAAUC,MAAQD,GAAM,CAAA,MAAOC,GAAI,CAAA,KAAMD,GAAM,CAAA,MAAOC,GAAI,CAAA;AAE7D,qBAAA,KADAvR,KAAKuG,MAAAA;AAKP,kBAAMoe,IAAmBrT,GAAM,CAAA,IAAK9K,GAASkN,OAAOC,OAAOwR,WACrDP,IAAiBrT,GAAI,CAAA,IAAK/K,GAASkN,OAAOC,OAAOwR,WACjDN,IAAyB1kB,KAAK+V,IAAIyO,GAAkB,CAAA,GACpDG,IAAuB3kB,KAAKC,IAAIwkB,GAAgBpe,GAASE,OAAO,CAAA;AAGlEme,iBAA0Bre,GAASE,QAAQoe,IAAuB,IACpE9kB,KAAKuG,MAAAA,KAIPvG,KAAK0kB,eAAAA,MACL1kB,KAAKwR,mBAAmBA,IACxBxR,KAAK2kB,mBAAmBA,GACxB3kB,KAAK4kB,iBAAiBA,GACtB5kB,KAAK6kB,yBAAyBA,GAC9B7kB,KAAK8kB,uBAAuBA,GAC5B9kB,KAAK+kB,WAAWzT,GAAM,CAAA,GACtBtR,KAAKglB,SAASzT,GAAI,CAAA;UACpB;UAEO,eAAe/K,IAAoBhC,IAAWC,IAAAA;AACnD,mBAAA,CAAA,CAAKzE,KAAK0kB,iBAGVjgB,MAAK+B,GAASkN,OAAOC,OAAOwR,WACxBnlB,KAAKwR,mBACHxR,KAAK+kB,YAAY/kB,KAAKglB,SACjBxgB,MAAKxE,KAAK+kB,YAAYtgB,MAAKzE,KAAK6kB,0BACrCrgB,KAAIxE,KAAKglB,UAAUvgB,MAAKzE,KAAK8kB,uBAE1BtgB,KAAIxE,KAAK+kB,YAAYtgB,MAAKzE,KAAK6kB,0BACpCrgB,MAAKxE,KAAKglB,UAAUvgB,MAAKzE,KAAK8kB,uBAE1BrgB,KAAIzE,KAAK2kB,oBAAoBlgB,KAAIzE,KAAK4kB,kBAC3C5kB,KAAK2kB,qBAAqB3kB,KAAK4kB,kBAAkBngB,OAAMzE,KAAK2kB,oBAAoBngB,MAAKxE,KAAK+kB,YAAYvgB,KAAIxE,KAAKglB,UAC/GhlB,KAAK2kB,mBAAmB3kB,KAAK4kB,kBAAkBngB,OAAMzE,KAAK4kB,kBAAkBpgB,KAAIxE,KAAKglB,UACrFhlB,KAAK2kB,mBAAmB3kB,KAAK4kB,kBAAkBngB,OAAMzE,KAAK2kB,oBAAoBngB,MAAKxE,KAAK+kB;UAC7F;QAAA;AAGF,QAAA1mB,GAAA,6BAAA,WAAA;AACE,iBAAO,IAAIomB;QACb;MAAA,GAAA,KAAA,SAAArmB,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAC,IAAAC,KAAA,UAAA,QAAAC,KAAAD,KAAA,IAAAJ,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAAG,KAAA,QAAA,SAAAN,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAI,KAAAP,GAAA,SAAA,GAAAO,MAAA,GAAAA;AAAA,eAAAH,KAAAJ,GAAAO,EAAA,OAAAD,MAAAD,KAAA,IAAAD,GAAAE,EAAA,IAAAD,KAAA,IAAAD,GAAAH,IAAAC,IAAAI,EAAA,IAAAF,GAAAH,IAAAC,EAAA,MAAAI;AAAA,iBAAAD,KAAA,KAAAC,MAAA,OAAA,eAAAL,IAAAC,IAAAI,EAAA,GAAAA;QAAA;AAAA,eAAA,eAAAL,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,eAAA;ACpFA,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAMM8mB,IAA0C,EAC9Clf,aAAa,GACbmf,iBAAiB,EAAE7gB,GAAG,GAAGC,GAAG,EAAA,GAC5B0B,0BAA0B,EAAE3B,GAAG,GAAGC,GAAG,EAAA,GACrCmB,QAAQ,EAAEpB,GAAG,GAAGC,GAAG,EAAA,GACnBsB,MAAM,EAAEvB,GAAG,GAAGC,GAAG,EAAA,GACjB4B,eAAe,EAAE7B,GAAG,GAAGC,GAAG,EAAA,EAAA;AA0B5B,YAAIzF;QAEJ,MAAaiB,EAAAA;UAQX,IAAA,QAAWmG;AAA4D,mBAAOpG,KAAKslB;UAAQ;UAsB3F,YACmBC,IACAC,IACAC,IAAAA;AAFA,iBAAAF,YAAAA,IACA,KAAAC,UAAAA,IACA,KAAAC,kBAAAA,IAhCX,KAAAC,aAAAA,OAEA,KAAAC,YAA0E,IAAI,EAAAC,cAC9E,KAAAC,oBAAkF,IAAI,EAAAD,cAGtF,KAAAN,SAAsB,CAAA,GAItB,KAAAQ,eAA4B,CAAA,GAM5B,KAAAC,mBAAiC,EAAEjgB,KAAK,GAAGD,MAAM,GAAGmgB,QAAQ,GAAGC,OAAO,EAAA,GACtE,KAAAC,qBAAoC,IAAI,EAAAvQ,iBAExC,KAAAwQ,eAAuB,KAKd,KAAAlZ,2BAA2B,IAAI,EAAAH,gBAChC,KAAAI,0BAA0BlN,KAAKiN,yBAAyBD,OACvD,KAAAG,8BAA8B,IAAI,EAAAL,gBACnC,KAAAM,6BAA6BpN,KAAKmN,4BAA4BH,OA8CtE,KAAAoZ,qBAAAA,OAvCNpmB,KAAKqmB,eAAAA,GACLrmB,KAAKsmB,aAAaC,EAChBhB,IAC+B,IAA/BvlB,KAAKwlB,QAAQ3I,kBAAsB2J,GACnCxmB,KAAKwlB,QAAQ1I,mBAAmB0J,CAAAA,GAElCxmB,KAAKymB,WAAU,GAAA,EAAApmB,cAAaL,KAAKsmB,WAAWzX,WAAW,MAAM,EAC3D8J,OAAO3Y,KAAKwlB,QAAQhH,mBACpBkI,oBAAAA,KAAoB,CAAA,CAAA;UAExB;UAEO,UAAApW;AACL,uBAAWqW,MAAQ3mB,KAAKoG;AACtBugB,cAAAA,GAAK3gB,OAAOyS,OAAAA;AAEdzY,iBAAKiN,yBAAyBqD,QAAAA;UAChC;UAEO,SAAA2B;AACAjS,iBAAK0lB,eACR1lB,KAAK4mB,UAAAA,GACL5mB,KAAK0lB,aAAAA;UAET;UAEQ,YAAAkB;AAEN,kBAAMC,KAAQ,IAAI,EAAAC;AAClB,qBAASxoB,KAAI,IAAIA,KAAI,KAAKA;AACxBuoB,cAAAA,GAAME,QAAQ,MAAA;AACZ,oBAAA,CAAK/mB,KAAK2lB,UAAUvR,IAAI9V,IAAG,EAAA0oB,eAAe,EAAAA,eAAe,EAAAC,WAAAA,GAAc;AACrE,wBAAMC,KAAkBlnB,KAAKmnB,aAAa7oB,IAAG,EAAA0oB,eAAe,EAAAA,eAAe,EAAAC,WAAAA;AAC3EjnB,uBAAK2lB,UAAUve,IAAI9I,IAAG,EAAA0oB,eAAe,EAAAA,eAAe,EAAAC,aAAaC,EAAAA;gBAAAA;cAAAA,CAAAA;UAIzE;UAGO,aAAA5iB;AACL,mBAAOtE,KAAKomB;UACd;UAEO,eAAA/T;AACL,gBAAoC,MAAhCrS,KAAKslB,OAAO,CAAA,EAAG8B,WAAW5iB,KAA2C,MAAhCxE,KAAKslB,OAAO,CAAA,EAAG8B,WAAW3iB,GAAnE;AAGA,yBAAWkiB,MAAQ3mB,KAAKslB;AACtBqB,gBAAAA,GAAKpgB,MAAAA;AAEPvG,mBAAK2lB,UAAUpf,MAAAA,GACfvG,KAAK6lB,kBAAkBtf,MAAAA,GACvBvG,KAAK0lB,aAAAA;YAAa;UACpB;UAEQ,iBAAAW;AAKN,gBAAIpmB,EAAaC,iBAAiBF,KAAKslB,OAAOlgB,UAAUjF,KAAK+V,IAAI,GAAGjW,EAAaC,aAAAA,GAAgB;AAG/F,oBAAMmnB,KAAcrnB,KAAKslB,OAAOgC,OAAOlpB,CAAAA,OACb,IAAjBA,GAAE4H,OAAON,UAAczF,EAAaO,kBAAkB,KAAA,EAC5D+mB,KAAK,CAAC5oB,IAAGyM,OACNA,GAAEpF,OAAON,UAAU/G,GAAEqH,OAAON,QACvB0F,GAAEpF,OAAON,QAAQ/G,GAAEqH,OAAON,QAE5B0F,GAAEoc,iBAAiB7oB,GAAE6oB,cAAAA;AAE9B,kBAAIC,KAAAA,IACA1hB,KAAO;AACX,uBAASzH,KAAI,GAAGA,KAAI+oB,GAAYjiB,QAAQ9G;AACtC,oBAAI+oB,GAAY/oB,EAAAA,EAAG0H,OAAON,UAAUK;AAClC0hB,kBAAAA,KAAYnpB,IACZyH,KAAOshB,GAAY/oB,EAAAA,EAAG0H,OAAON;yBACpBpH,KAAImpB,MAAc;AAC3B;AAKJ,oBAAMC,KAAeL,GAAYjJ,MAAMqJ,IAAWA,KAAY,CAAA,GACxDE,KAA4BD,GAAa7F,IAAIzjB,CAAAA,OAAKA,GAAEwpB,OAAO,CAAA,EAAG1hB,WAAAA,EAAaqhB,KAAK,CAAC5oB,IAAGyM,OAAMzM,KAAIyM,KAAI,IAAA,EAAK,GACvGyc,KAAkB7nB,KAAKoG,MAAMhB,SAASsiB,GAAatiB,QAGnD0iB,KAAa9nB,KAAK+nB,YAAYL,IAAcG,EAAAA;AAClDC,cAAAA,GAAWxgB;AAGX,uBAAShJ,KAAIqpB,GAA0BviB,SAAS,GAAG9G,MAAK,GAAGA;AACzD0B,qBAAKgoB,YAAYL,GAA0BrpB,EAAAA,CAAAA;AAI7C0B,mBAAKoG,MAAMuX,KAAKmK,EAAAA,GAGhB9nB,KAAKomB,qBAAAA,MACLpmB,KAAKiN,yBAAyBqC,KAAKwY,GAAW9hB,MAAAA;YAAAA;AAIhD,kBAAMiiB,KAAU,IAAIC,EAAUloB,KAAKulB,WAAWvlB,KAAKmmB,YAAAA;AAInD,mBAHAnmB,KAAKslB,OAAO3H,KAAKsK,EAAAA,GACjBjoB,KAAK8lB,aAAanI,KAAKsK,EAAAA,GACvBjoB,KAAKiN,yBAAyBqC,KAAK2Y,GAAQjiB,MAAAA,GACpCiiB;UACT;UAEQ,YAAYP,IAA2BG,IAAAA;AAC7C,kBAAMM,KAA4C,IAA/BT,GAAa,CAAA,EAAG1hB,OAAON,OACpCoiB,KAAa,IAAII,EAAUloB,KAAKulB,WAAW4C,IAAYT,EAAAA;AAC7D,uBAAK,CAAOppB,IAAG8pB,EAAAA,KAAMV,GAAapF,QAAAA,GAAW;AAC3C,oBAAM7B,KAAUniB,KAAI8pB,GAAEpiB,OAAON,QAAQyiB,IAC/BzH,KAAUvgB,KAAKoF,MAAMjH,KAAI,CAAA,IAAK8pB,GAAEpiB,OAAOC;AAC7C6hB,cAAAA,GAAWvH,IAAI8H,UAAUD,GAAEpiB,QAAQya,IAASC,EAAAA;AAC5C,yBAAWvV,MAAKid,GAAER;AAChBzc,gBAAAA,GAAEjF,cAAc2hB,IAChB1c,GAAE9E,cAAc7B,IAAI2G,GAAEpF,KAAKvB,IAAI2jB,IAC/Bhd,GAAE9E,cAAc5B,IAAI0G,GAAEpF,KAAKtB,IAAI0jB,IAC/Bhd,GAAEka,gBAAgB7gB,KAAKic,IACvBtV,GAAEka,gBAAgB5gB,KAAKic,IACvBvV,GAAEhF,yBAAyB3B,IAAI2G,GAAEka,gBAAgB7gB,IAAI2jB,IACrDhd,GAAEhF,yBAAyB1B,IAAI0G,GAAEka,gBAAgB5gB,IAAI0jB;AAGvDnoB,mBAAKmN,4BAA4BmC,KAAK8Y,GAAEpiB,MAAAA;AAGxC,oBAAM+X,KAAQ/d,KAAK8lB,aAAavI,QAAQ6K,EAAAA;AAAAA,qBACpCrK,MACF/d,KAAK8lB,aAAapI,OAAOK,IAAO,CAAA;YAAA;AAGpC,mBAAO+J;UACT;UAEQ,YAAYQ,IAAAA;AAClBtoB,iBAAKslB,OAAO5H,OAAO4K,IAAW,CAAA;AAC9B,qBAAS/U,KAAI+U,IAAW/U,KAAIvT,KAAKslB,OAAOlgB,QAAQmO,MAAK;AACnD,oBAAMgV,KAAgBvoB,KAAKslB,OAAO/R,EAAAA;AAClC,yBAAWpI,MAAKod,GAAcX;AAC5Bzc,gBAAAA,GAAEjF;AAEJqiB,cAAAA,GAAcjhB;YAAAA;UAElB;UAEO,+BAA+BxC,IAAeH,IAAYC,IAAYC,IAAa2jB,IAAAA;AACxF,mBAAOxoB,KAAKyoB,iBAAiBzoB,KAAK6lB,mBAAmB/gB,IAAOH,IAAIC,IAAIC,IAAK2jB,EAAAA;UAC3E;UAEO,mBAAmB9jB,IAAcC,IAAYC,IAAYC,IAAa2jB,IAAAA;AAC3E,mBAAOxoB,KAAKyoB,iBAAiBzoB,KAAK2lB,WAAWjhB,IAAMC,IAAIC,IAAIC,IAAK2jB,EAAAA;UAClE;UAKQ,iBACNE,IACAC,IACAhkB,IACAC,IACAC,IACA2jB,KAAAA,OAAgC;AAOhC,mBALAxpB,IAAS0pB,GAAStU,IAAIuU,IAAKhkB,IAAIC,IAAIC,EAAAA,GAC9B7F,MACHA,IAASgB,KAAKmnB,aAAawB,IAAKhkB,IAAIC,IAAIC,IAAK2jB,EAAAA,GAC7CE,GAASthB,IAAIuhB,IAAKhkB,IAAIC,IAAIC,IAAK7F,CAAAA,IAE1BA;UACT;UAEQ,uBAAuB4pB,IAAAA;AAC7B,gBAAIA,MAAO5oB,KAAKwlB,QAAQ5c,OAAOgC,KAAKxF;AAClC,oBAAM,IAAI0J,MAAM,4BAA4B8Z,EAAAA;AAE9C,mBAAO5oB,KAAKwlB,QAAQ5c,OAAOgC,KAAKge,EAAAA;UAClC;UAEQ,oBAAoBC,IAAqBC,IAAiB9e,IAAkBmP,IAAAA;AAClF,gBAAInZ,KAAKwlB,QAAQhH;AAIf,qBAAO,EAAAP;AAGT,gBAAI3J;AACJ,oBAAQuU,IAAAA;cACN,KAAK;cACL,KAAK;AACHvU,gBAAAA,KAAStU,KAAK+oB,uBAAuBD,EAAAA;AACrC;cACF,KAAK;AACH,sBAAME,KAAM,EAAArT,cAAcsT,WAAWH,EAAAA;AAErCxU,gBAAAA,KAAS,EAAAzJ,KAAKqe,QAAQF,GAAI,CAAA,GAAIA,GAAI,CAAA,GAAIA,GAAI,CAAA,CAAA;AAC1C;cAEF;AAEI1U,gBAAAA,KADEtK,KACO,EAAAqB,MAAM8d,OAAOnpB,KAAKwlB,QAAQ5c,OAAOkC,UAAAA,IAEjC9K,KAAKwlB,QAAQ5c,OAAOU;YAAAA;AAKnC,mBAAOgL;UACT;UAEQ,oBAAoB3P,IAAYkkB,IAAqBC,IAAiBlkB,IAAYwkB,IAAqBC,IAAiBrf,IAAkBmP,IAAcmQ,IAAeC,IAAAA;AAC7K,kBAAMC,KAAuBxpB,KAAKypB,yBAAyB9kB,IAAIkkB,IAAaC,IAASlkB,IAAIwkB,IAAaC,IAAAA,OAAgBC,IAAMnQ,IAAKoQ,EAAAA;AACjI,gBAAIC;AACF,qBAAOA;AAGT,gBAAIlV;AACJ,oBAAQ8U,IAAAA;cACN,KAAK;cACL,KAAK;AACCppB,qBAAKwlB,QAAQ/G,8BAA8B6K,MAAQD,KAAU,MAC/DA,MAAW,IAEb/U,KAAStU,KAAK+oB,uBAAuBM,EAAAA;AACrC;cACF,KAAK;AACH,sBAAML,KAAM,EAAArT,cAAcsT,WAAWI,EAAAA;AACrC/U,gBAAAA,KAAS,EAAAzJ,KAAKqe,QAAQF,GAAI,CAAA,GAAIA,GAAI,CAAA,GAAIA,GAAI,CAAA,CAAA;AAC1C;cAEF;AAEI1U,gBAAAA,KADEtK,KACOhK,KAAKwlB,QAAQ5c,OAAOU,aAEpBtJ,KAAKwlB,QAAQ5c,OAAOkC;YAAAA;AAcnC,mBATI9K,KAAKwlB,QAAQhH,sBACflK,KAAS,EAAAjJ,MAAM8d,OAAO7U,EAAAA,IAIpB6E,OACF7E,KAAS,EAAAjJ,MAAMqe,gBAAgBpV,IAAQ,EAAAsK,WAAAA,IAGlCtK;UACT;UAEQ,uBAAuBuU,IAAqBC,IAAiB9e,IAAAA;AACnE,oBAAQ6e,IAAAA;cACN,KAAK;cACL,KAAK;AACH,uBAAO7oB,KAAK+oB,uBAAuBD,EAAAA,EAASje;cAC9C,KAAK;AACH,uBAAOie,MAAW;cAEpB;AACE,uBAAI9e,KACKhK,KAAKwlB,QAAQ5c,OAAOkC,WAAWD,OAEjC7K,KAAKwlB,QAAQ5c,OAAOU,WAAWuB;YAAAA;UAE5C;UAEQ,uBAAuBue,IAAqBC,IAAiBrf,IAAkBsf,IAAAA;AACrF,oBAAQF,IAAAA;cACN,KAAK;cACL,KAAK;AAIH,uBAHIppB,KAAKwlB,QAAQ/G,8BAA8B6K,MAAQD,KAAU,MAC/DA,MAAW,IAENrpB,KAAK+oB,uBAAuBM,EAAAA,EAASxe;cAC9C,KAAK;AACH,uBAAOwe,MAAW;cAEpB;AACE,uBAAIrf,KACKhK,KAAKwlB,QAAQ5c,OAAOU,WAAWuB,OAEjC7K,KAAKwlB,QAAQ5c,OAAOkC,WAAWD;YAAAA;UAE5C;UAEQ,yBAAyBlG,IAAYkkB,IAAqBC,IAAiBlkB,IAAYwkB,IAAqBC,IAAiBrf,IAAkBsf,IAAenQ,IAAcoQ,IAAAA;AAClL,gBAA0C,MAAtCvpB,KAAKwlB,QAAQ9G,wBAA8B6K;AAC7C;AAIF,kBAAMI,KAAQ3pB,KAAK4pB,kBAAkBzQ,EAAAA,GAC/B0Q,KAAgBF,GAAMG,SAASnlB,IAAIC,EAAAA;AACzC,gBAAA,WAAIilB;AACF,qBAAOA,MAAAA;AAGT,kBAAME,KAAS/pB,KAAKgqB,uBAAuBnB,IAAaC,IAAS9e,EAAAA,GAC3DigB,KAASjqB,KAAKkqB,uBAAuBd,IAAaC,IAASrf,IAASsf,EAAAA,GAGpEhV,KAAS,EAAAzJ,KAAKsf,oBAAoBJ,IAAQE,IAAQjqB,KAAKwlB,QAAQ9G,wBAAwBvF,KAAM,IAAI,EAAA;AAEvG,gBAAA,CAAK7E;AAEH,qBAAA,KADAqV,GAAMS,SAASzlB,IAAIC,IAAI,IAAA;AAIzB,kBAAMyG,KAAQ,EAAAR,KAAKqe,QAChB5U,MAAU,KAAM,KAChBA,MAAU,KAAM,KAChBA,MAAU,IAAK,GAAA;AAIlB,mBAFAqV,GAAMS,SAASzlB,IAAIC,IAAIyG,EAAAA,GAEhBA;UACT;UAEQ,kBAAkB8N,IAAAA;AACxB,mBAAIA,KACKnZ,KAAKwlB,QAAQ5c,OAAO0V,oBAEtBte,KAAKwlB,QAAQ5c,OAAOyV;UAC7B;UAGQ,aAAagM,IAA8B1lB,IAAYC,IAAYC,IAAa2jB,KAAAA,OAAgC;AACtH,kBAAM1jB,KAA+B,YAAA,OAAhBulB,KAA2BC,OAAOC,aAAaF,EAAAA,IAAeA,IAQ7EG,KAAerqB,KAAKC,IAAIJ,KAAKwlB,QAAQ3I,kBAAkB1c,KAAK+V,IAAIpR,GAAMM,QAAQ,CAAA,IAAKohB,GAA8BxmB,KAAKmmB,YAAAA;AACxHnmB,iBAAKsmB,WAAW5gB,QAAQ8kB,OAC1BxqB,KAAKsmB,WAAW5gB,QAAQ8kB;AAG1B,kBAAMC,KAAgBtqB,KAAKC,IAAIJ,KAAKwlB,QAAQ1I,mBAAmB0J,GAA8BxmB,KAAKmmB,YAAAA;AAWlG,gBAVInmB,KAAKsmB,WAAWrgB,SAASwkB,OAC3BzqB,KAAKsmB,WAAWrgB,SAASwkB,KAE3BzqB,KAAKymB,QAAQiE,KAAAA,GAEb1qB,KAAKkmB,mBAAmBthB,KAAKA,IAC7B5E,KAAKkmB,mBAAmBvhB,KAAKA,IAC7B3E,KAAKkmB,mBAAmB/J,SAAStX,MAAMA,IAEnB7E,KAAKkmB,mBAAmByE,YAAAA;AAE1C,qBAAOvF;AAGT,kBAAMkE,KAAAA,CAAAA,CAAStpB,KAAKkmB,mBAAmBlM,OAAAA,GACjChQ,KAAAA,CAAAA,CAAYhK,KAAKkmB,mBAAmB0E,UAAAA,GACpCzR,KAAAA,CAAAA,CAAQnZ,KAAKkmB,mBAAmB2E,MAAAA,GAChCC,KAAAA,CAAAA,CAAW9qB,KAAKkmB,mBAAmBjM,SAAAA,GACnC8Q,KAAAA,CAAAA,CAAc/qB,KAAKkmB,mBAAmB8E,YAAAA,GACtCC,IAAAA,CAAAA,CAAkBjrB,KAAKkmB,mBAAmBgF,gBAAAA,GAC1CC,IAAAA,CAAAA,CAAanrB,KAAKkmB,mBAAmBkF,WAAAA;AAC3C,gBAAI/B,IAAUrpB,KAAKkmB,mBAAmBmF,WAAAA,GAClCjC,IAAcppB,KAAKkmB,mBAAmBoF,eAAAA,GACtCxC,IAAU9oB,KAAKkmB,mBAAmBqF,WAAAA,GAClC1C,IAAc7oB,KAAKkmB,mBAAmBsF,eAAAA;AAC1C,gBAAIxhB,IAAS;AACX,oBAAMyhB,KAAOpC;AACbA,kBAAUP,GACVA,IAAU2C;AACV,oBAAMC,KAAQtC;AACdA,kBAAcP,GACdA,IAAc6C;YAAAA;AAIhB,kBAAMC,IAAkB3rB,KAAK4rB,oBAAoB/C,GAAaC,GAAS9e,IAASmP,EAAAA;AAGhFnZ,iBAAKymB,QAAQoF,2BAA2B,QACxC7rB,KAAKymB,QAAQnN,YAAYqS,EAAgB5a,KACzC/Q,KAAKymB,QAAQrN,SAAS,GAAG,GAAGpZ,KAAKsmB,WAAW5gB,OAAO1F,KAAKsmB,WAAWrgB,MAAAA,GACnEjG,KAAKymB,QAAQoF,2BAA2B;AAGxC,kBAAM1R,IAAamP,KAAOtpB,KAAKwlB,QAAQtL,iBAAiBla,KAAKwlB,QAAQrL,YAC/D2R,IAAYhB,KAAS,WAAW;AACtC9qB,iBAAKymB,QAAQlN,OACX,GAAGuS,CAAAA,IAAa3R,CAAAA,IAAcna,KAAKwlB,QAAQpL,WAAWpa,KAAKwlB,QAAQvI,gBAAAA,MAAsBjd,KAAKwlB,QAAQnL,UAAAA,IACxGra,KAAKymB,QAAQhN,eAAe,EAAAC;AAE5B,kBAAMqS,IAAkC,MAAjBjnB,GAAMM,WAAgB,GAAA,EAAAmf,kBAAiBzf,GAAMknB,WAAW,CAAA,CAAA,GACzEC,IAA4C,MAAjBnnB,GAAMM,WAAgB,GAAA,EAAA8mB,4BAA2BpnB,GAAMknB,WAAW,CAAA,CAAA,GAC7FG,IAAkBnsB,KAAKosB,oBAAoBznB,IAAIkkB,GAAaC,GAASlkB,IAAIwkB,GAAaC,GAASrf,IAASmP,IAAKmQ,KAAM,GAAA,EAAAC,iCAAgCzkB,GAAMknB,WAAW,CAAA,CAAA,CAAA;AAC1KhsB,iBAAKymB,QAAQnN,YAAY6S,EAAgBpb;AAGzC,kBAAMsb,IAAUJ,IAA2B,IAAIzF;AAG/C,gBAAI8F,IAAAA;AAAc,sBACdtsB,KAAKwlB,QAAQjH,iBACf+N,KAAc,GAAA,EAAAC,mBAAkBvsB,KAAKymB,SAAS3hB,IAAOunB,GAASA,GAASrsB,KAAKwlB,QAAQ3I,iBAAiB7c,KAAKwlB,QAAQ1I,kBAAkB9c,KAAKwlB,QAAQpL,UAAUpa,KAAKwlB,QAAQvI,gBAAAA;AAM1K,gBAEIuP,GAFAC,IAAAA,CAA6BV;AAUjC,gBANES,IADyB,YAAA,OAAhBnC,KACCrqB,KAAKylB,gBAAgBiH,QAAQrC,EAAAA,IAE7BrqB,KAAKylB,gBAAgBkH,mBAAmBtC,EAAAA,GAIhDU,IAAW;AACb/qB,mBAAKymB,QAAQiE,KAAAA;AACb,oBAAMlI,KAAYriB,KAAK+V,IAAI,GAAG/V,KAAKoF,MAAMvF,KAAKwlB,QAAQpL,WAAWpa,KAAKwlB,QAAQvI,mBAAmB,EAAA,CAAA,GAE3FyD,KAAU8B,KAAY,KAAM,IAAI,MAAM;AAI5C,kBAHAxiB,KAAKymB,QAAQjE,YAAYA,IAGrBxiB,KAAKkmB,mBAAmB0G,wBAAAA;AAC1B5sB,qBAAKymB,QAAQtE,cAAcniB,KAAKymB,QAAQnN;uBAC/BtZ,KAAKkmB,mBAAmB2G,oBAAAA;AACjCJ,oBAAAA,OACAzsB,KAAKymB,QAAQtE,cAAc,OAAO,EAAAxM,cAAcsT,WAAWjpB,KAAKkmB,mBAAmB4G,kBAAAA,CAAAA,EAAqBC,KAAK,GAAA,CAAA;mBACxG;AACLN,oBAAAA;AACA,oBAAI7nB,KAAK5E,KAAKkmB,mBAAmB4G,kBAAAA;AAC7B9sB,qBAAKwlB,QAAQ/G,8BAA8Bze,KAAKkmB,mBAAmBlM,OAAAA,KAAYpV,KAAK,MACtFA,MAAM,IAER5E,KAAKymB,QAAQtE,cAAcniB,KAAK+oB,uBAAuBnkB,EAAAA,EAAImM;cAAAA;AAI7D/Q,mBAAKymB,QAAQ5M,UAAAA;AACb,oBAAMmT,KAAQX,GACRY,KAAO9sB,KAAKoV,KAAK8W,IAAUrsB,KAAKwlB,QAAQxI,gBAAAA,IAAoB0D,MAAW8H,KAAmC,IAAZhG,KAAgB,IAC9G0K,KAAOD,KAAOzK,IACd2K,KAAOF,KAAmB,IAAZzK;AAEpB,uBAASlkB,KAAI,GAAGA,KAAIkuB,GAASluB,MAAK;AAChC0B,qBAAKymB,QAAQiE,KAAAA;AACb,sBAAM0C,KAAUJ,KAAQ1uB,KAAI0B,KAAKwlB,QAAQ3I,iBACnCwQ,KAAWL,MAAS1uB,KAAI,KAAK0B,KAAKwlB,QAAQ3I,iBAC1CyQ,KAASF,KAAUptB,KAAKwlB,QAAQ3I,kBAAkB;AACxD,wBAAQ7c,KAAKkmB,mBAAmB/J,SAASoR,gBAAAA;kBACvC,KAAK;AACHvtB,yBAAKymB,QAAQlD,OAAO6J,IAASH,EAAAA,GAC7BjtB,KAAKymB,QAAQnD,OAAO+J,IAAUJ,EAAAA,GAC9BjtB,KAAKymB,QAAQlD,OAAO6J,IAASD,EAAAA,GAC7BntB,KAAKymB,QAAQnD,OAAO+J,IAAUF,EAAAA;AAC9B;kBACF,KAAK;AAGH,0BAAMK,KAAYhL,MAAa,IAAI2K,KAAOhtB,KAAKoV,KAAK8W,IAAUrsB,KAAKwlB,QAAQxI,mBAAmBwF,KAAY,CAAA,IAAK9B,IACzG+M,KAAYjL,MAAa,IAAIyK,KAAO9sB,KAAKoV,KAAK8W,IAAUrsB,KAAKwlB,QAAQxI,mBAAmBwF,KAAY,CAAA,IAAK9B,IAIzGwC,KAAa,IAAIC;AACvBD,oBAAAA,GAAWpJ,KAAKsT,IAASH,IAAMjtB,KAAKwlB,QAAQ3I,iBAAiBsQ,KAAOF,EAAAA,GACpEjtB,KAAKymB,QAAQ1M,KAAKmJ,EAAAA,GAGlBljB,KAAKymB,QAAQlD,OAAO6J,KAAUptB,KAAKwlB,QAAQ3I,kBAAkB,GAAGqQ,EAAAA,GAChEltB,KAAKymB,QAAQpD,cACX+J,KAAUptB,KAAKwlB,QAAQ3I,kBAAkB,GAAG4Q,IAC5CL,IAASK,IACTL,IAASF,EAAAA,GAEXltB,KAAKymB,QAAQpD,cACX+J,IAASI,IACTF,IAAQE,IACRF,IAAQJ,EAAAA,GAEVltB,KAAKymB,QAAQpD,cACXiK,IAAQG,IACRJ,IAAUI,IACVJ,IAAUH,EAAAA,GAEZltB,KAAKymB,QAAQpD,cACXgK,IAAUG,IACVH,KAAWrtB,KAAKwlB,QAAQ3I,kBAAkB,GAAG2Q,IAC7CH,KAAWrtB,KAAKwlB,QAAQ3I,kBAAkB,GAAGqQ,EAAAA;AAE/C;kBACF,KAAK;AACHltB,yBAAKymB,QAAQiH,YAAY,CAACvtB,KAAKsV,MAAM+M,EAAAA,GAAYriB,KAAKsV,MAAM+M,EAAAA,CAAAA,CAAAA,GAC5DxiB,KAAKymB,QAAQlD,OAAO6J,IAASH,EAAAA,GAC7BjtB,KAAKymB,QAAQnD,OAAO+J,IAAUJ,EAAAA;AAC9B;kBACF,KAAK;AACHjtB,yBAAKymB,QAAQiH,YAAY,CAAiC,IAAhC1tB,KAAKwlB,QAAQvI,kBAAsD,IAAhCjd,KAAKwlB,QAAQvI,gBAAAA,CAAAA,GAC1Ejd,KAAKymB,QAAQlD,OAAO6J,IAASH,EAAAA,GAC7BjtB,KAAKymB,QAAQnD,OAAO+J,IAAUJ,EAAAA;AAC9B;kBAEF;AACEjtB,yBAAKymB,QAAQlD,OAAO6J,IAASH,EAAAA,GAC7BjtB,KAAKymB,QAAQnD,OAAO+J,IAAUJ,EAAAA;gBAAAA;AAGlCjtB,qBAAKymB,QAAQ1D,OAAAA,GACb/iB,KAAKymB,QAAQkH,QAAAA;cAAAA;AAOf,kBALA3tB,KAAKymB,QAAQkH,QAAAA,GAAAA,CAKRrB,KAAetsB,KAAKwlB,QAAQpL,YAAY,MAAA,CAGtCpa,KAAKwlB,QAAQhH,qBAA+B,QAAV1Z,IAAe;AAGpD9E,qBAAKymB,QAAQiE,KAAAA,GACb1qB,KAAKymB,QAAQhN,eAAe;AAC5B,sBAAMmU,KAAU5tB,KAAKymB,QAAQoH,YAAY/oB,EAAAA;AAEzC,oBADA9E,KAAKymB,QAAQkH,QAAAA,GACT,8BAA8BC,MAAWA,GAAQE,2BAA2B,GAAG;AAEjF9tB,uBAAKymB,QAAQiE,KAAAA;AAIb,wBAAMxH,KAAa,IAAIC;AACvBD,kBAAAA,GAAWpJ,KAAKkT,IAAOC,KAAO9sB,KAAKoV,KAAKiN,KAAY,CAAA,GAAIxiB,KAAKwlB,QAAQ3I,kBAAkB2P,GAASW,KAAOF,KAAO9sB,KAAKoV,KAAKiN,KAAY,CAAA,CAAA,GACpIxiB,KAAKymB,QAAQ1M,KAAKmJ,EAAAA,GAClBljB,KAAKymB,QAAQjE,YAA4C,IAAhCxiB,KAAKwlB,QAAQvI,kBACtCjd,KAAKymB,QAAQtE,cAAcwJ,EAAgB5a,KAC3C/Q,KAAKymB,QAAQsH,WAAWjpB,IAAOunB,GAASA,IAAUrsB,KAAKwlB,QAAQxI,gBAAAA,GAC/Dhd,KAAKymB,QAAQkH,QAAAA;gBAAAA;cAAAA;YAAAA;AAOrB,gBAAIxC,GAAU;AACZ,oBAAM3I,KAAYriB,KAAK+V,IAAI,GAAG/V,KAAKoF,MAAMvF,KAAKwlB,QAAQpL,WAAWpa,KAAKwlB,QAAQvI,mBAAmB,EAAA,CAAA,GAC3FyD,KAAU8B,KAAY,KAAM,IAAI,MAAM;AAC5CxiB,mBAAKymB,QAAQjE,YAAYA,IACzBxiB,KAAKymB,QAAQtE,cAAcniB,KAAKymB,QAAQnN,WACxCtZ,KAAKymB,QAAQ5M,UAAAA,GACb7Z,KAAKymB,QAAQlD,OAAO8I,GAASA,IAAU3L,EAAAA,GACvC1gB,KAAKymB,QAAQnD,OAAO+I,IAAUrsB,KAAKwlB,QAAQzI,kBAAkByP,GAASH,IAAU3L,EAAAA,GAChF1gB,KAAKymB,QAAQ1D,OAAAA;YAAAA;AAUf,gBANKuJ,KACHtsB,KAAKymB,QAAQ7M,SAAS9U,IAAOunB,GAASA,IAAUrsB,KAAKwlB,QAAQxI,gBAAAA,GAKjD,QAAVlY,MAAAA,CAAkB9E,KAAKwlB,QAAQhH,mBAAmB;AACpD,kBAAIwP,KAAqBC,EAAWjuB,KAAKymB,QAAQyH,aAAa7B,GAASA,GAASrsB,KAAKwlB,QAAQ3I,iBAAiB7c,KAAKwlB,QAAQ1I,gBAAAA,GAAmB6O,GAAiBQ,GAAiBM,CAAAA;AAChL,kBAAIuB;AACF,yBAASpoB,KAAS,GAAGA,MAAU,MAC7B5F,KAAKymB,QAAQiE,KAAAA,GACb1qB,KAAKymB,QAAQnN,YAAYqS,EAAgB5a,KACzC/Q,KAAKymB,QAAQrN,SAAS,GAAG,GAAGpZ,KAAKsmB,WAAW5gB,OAAO1F,KAAKsmB,WAAWrgB,MAAAA,GACnEjG,KAAKymB,QAAQkH,QAAAA,GACb3tB,KAAKymB,QAAQ7M,SAAS9U,IAAOunB,GAASA,IAAUrsB,KAAKwlB,QAAQxI,mBAAmBpX,EAAAA,GAChFooB,KAAqBC,EAAWjuB,KAAKymB,QAAQyH,aAAa7B,GAASA,GAASrsB,KAAKwlB,QAAQ3I,iBAAiB7c,KAAKwlB,QAAQ1I,gBAAAA,GAAmB6O,GAAiBQ,GAAiBM,CAAAA,GACvKuB,KAP2BpoB;AAAAA;YAAAA;AAetC,gBAAIqlB,GAAe;AACjB,oBAAMzI,KAAYriB,KAAK+V,IAAI,GAAG/V,KAAKoF,MAAMvF,KAAKwlB,QAAQpL,WAAWpa,KAAKwlB,QAAQvI,mBAAmB,EAAA,CAAA,GAC3FyD,KAAU1gB,KAAKymB,QAAQjE,YAAY,KAAM,IAAI,MAAM;AACzDxiB,mBAAKymB,QAAQjE,YAAYA,IACzBxiB,KAAKymB,QAAQtE,cAAcniB,KAAKymB,QAAQnN,WACxCtZ,KAAKymB,QAAQ5M,UAAAA,GACb7Z,KAAKymB,QAAQlD,OAAO8I,GAASA,IAAUlsB,KAAKoF,MAAMvF,KAAKwlB,QAAQxI,mBAAmB,CAAA,IAAK0D,EAAAA,GACvF1gB,KAAKymB,QAAQnD,OAAO+I,IAAUrsB,KAAKwlB,QAAQzI,kBAAkByP,GAASH,IAAUlsB,KAAKoF,MAAMvF,KAAKwlB,QAAQxI,mBAAmB,CAAA,IAAK0D,EAAAA,GAChI1gB,KAAKymB,QAAQ1D,OAAAA;YAAAA;AAGf/iB,iBAAKymB,QAAQkH,QAAAA;AAIb,kBAAMpM,IAAYvhB,KAAKymB,QAAQyH,aAC7B,GAAG,GAAGluB,KAAKsmB,WAAW5gB,OAAO1F,KAAKsmB,WAAWrgB,MAAAA;AAI/C,gBAAIkoB;AAQJ,gBAJEA,IAHGnuB,KAAKwlB,QAAQhH,oBA0WtB,SAAoC+C,IAAAA;AAClC,uBAAS3b,KAAS,GAAGA,KAAS2b,GAAUQ,KAAK3c,QAAQQ,MAAU;AAC7D,oBAAI2b,GAAUQ,KAAKnc,KAAS,CAAA,IAAK;AAC/B,yBAAA;AAGJ,qBAAA;YACF,EA9W2C2b,CAAAA,IAF3B0M,EAAW1M,GAAWoK,GAAiBQ,GAAiBM,CAAAA,GAMhE0B;AACF,qBAAO/I;AAGT,kBAAM8B,IAAkBlnB,KAAKouB,sBAAsB7M,GAAWvhB,KAAK+lB,kBAAkByE,IAAcyB,GAA0BK,GAAaD,CAAAA;AAG1I,gBAAIgC,GACAC;AACJ,uBAAa;AAEX,kBAAiC,MAA7BtuB,KAAK8lB,aAAa1gB,QAAc;AAClC,sBAAM6iB,KAAUjoB,KAAKqmB,eAAAA;AACrBgI,oBAAapG,IACbqG,IAAYrG,GAAQb,YACpBkH,EAAUroB,SAASihB,EAAgBnhB,KAAKtB;AACxC;cAAA;AAIF4pB,kBAAaruB,KAAK8lB,aAAa9lB,KAAK8lB,aAAa1gB,SAAS,CAAA,GAC1DkpB,IAAYD,EAAWjH;AACvB,yBAAWgB,MAAKpoB,KAAK8lB;AACfoB,kBAAgBnhB,KAAKtB,KAAK2jB,GAAEhB,WAAWnhB,WACzCooB,IAAajG,IACbkG,IAAYlG,GAAEhB;AAUlB,uBAAS9oB,KAAI0B,KAAK8lB,aAAa1gB,SAAS,GAAG9G,MAAK,GAAGA;AACjD,2BAAW2U,MAAOjT,KAAK8lB,aAAaxnB,EAAAA,EAAGiwB;AACjCtb,kBAAAA,GAAIhN,UAAUqoB,EAAUroB,UAAUihB,EAAgBnhB,KAAKtB,KAAKwO,GAAIhN,WAClEooB,IAAaruB,KAAK8lB,aAAaxnB,EAAAA,GAC/BgwB,IAAYrb;AAQlB,kBAAIqb,EAAU7pB,IAAIyiB,EAAgBnhB,KAAKtB,KAAK4pB,EAAWroB,OAAOC,UAAUqoB,EAAUroB,SAASihB,EAAgBnhB,KAAKtB,IAAI,GAA+B;AAGjJ,oBAAI+pB,KAAAA;AACJ,oBAAIH,EAAWjH,WAAW3iB,IAAI4pB,EAAWjH,WAAWnhB,SAASihB,EAAgBnhB,KAAKtB,KAAK4pB,EAAWroB,OAAOC,QAAQ;AAE/G,sBAAIwoB;AACJ,6BAAWrG,MAAKpoB,KAAK8lB;AACnB,wBAAIsC,GAAEhB,WAAW3iB,IAAI2jB,GAAEhB,WAAWnhB,SAASihB,EAAgBnhB,KAAKtB,IAAI2jB,GAAEpiB,OAAOC,QAAQ;AACnFwoB,sBAAAA,KAAgBrG;AAChB;oBAAA;AAGJ,sBAAIqG;AACFJ,wBAAaI;2BAOXxuB,EAAaC,iBACbF,KAAKslB,OAAOlgB,UAAUnF,EAAaC,iBACnCouB,EAAU7pB,IAAIyiB,EAAgBnhB,KAAKtB,KAAK4pB,EAAWroB,OAAOC,UAC1DqoB,EAAUroB,UAAUihB,EAAgBnhB,KAAKtB,KACzC6pB,EAAU9pB,IAAI0iB,EAAgBnhB,KAAKvB,KAAK6pB,EAAWroB,OAAON;AAG1D8oB,oBAAAA,KAAAA;uBACK;AAEL,0BAAMvG,KAAUjoB,KAAKqmB,eAAAA;AACrBgI,wBAAapG,IACbqG,IAAYrG,GAAQb,YACpBkH,EAAUroB,SAASihB,EAAgBnhB,KAAKtB,GACxC+pB,KAAAA;kBAAqB;gBAAA;AAItBA,gBAAAA,OAECH,EAAWjH,WAAWnhB,SAAS,KACjCooB,EAAWE,UAAU5Q,KAAK0Q,EAAWjH,UAAAA,GAEvCkH,IAAY,EACV9pB,GAAG,GACHC,GAAG4pB,EAAWjH,WAAW3iB,IAAI4pB,EAAWjH,WAAWnhB,QACnDA,QAAQihB,EAAgBnhB,KAAKtB,EAAAA,GAE/B4pB,EAAWE,UAAU5Q,KAAK2Q,CAAAA,GAG1BD,EAAWjH,aAAa,EACtB5iB,GAAG,GACHC,GAAG6pB,EAAU7pB,IAAI6pB,EAAUroB,QAC3BA,QAAQ,EAAA;cAAA;AAOd,kBAAIqoB,EAAU9pB,IAAI0iB,EAAgBnhB,KAAKvB,KAAK6pB,EAAWroB,OAAON;AAC5D;AAIE4oB,oBAAcD,EAAWjH,cAC3BkH,EAAU9pB,IAAI,GACd8pB,EAAU7pB,KAAK6pB,EAAUroB,QACzBqoB,EAAUroB,SAAS,KAEnBooB,EAAWE,UAAU7Q,OAAO2Q,EAAWE,UAAUhR,QAAQ+Q,CAAAA,GAAY,CAAA;YAAA;AAiCzE,mBA5BApH,EAAgBhhB,cAAclG,KAAKslB,OAAO/H,QAAQ8Q,CAAAA,GAClDnH,EAAgB7B,gBAAgB7gB,IAAI8pB,EAAU9pB,GAC9C0iB,EAAgB7B,gBAAgB5gB,IAAI6pB,EAAU7pB,GAC9CyiB,EAAgB/gB,yBAAyB3B,IAAI8pB,EAAU9pB,IAAI6pB,EAAWroB,OAAON,OAC7EwhB,EAAgB/gB,yBAAyB1B,IAAI6pB,EAAU7pB,IAAI4pB,EAAWroB,OAAOC,QAG7EihB,EAAgB7gB,cAAc7B,KAAK6pB,EAAWroB,OAAON,OACrDwhB,EAAgB7gB,cAAc5B,KAAK4pB,EAAWroB,OAAOC,QAIrDqoB,EAAUroB,SAAS9F,KAAK+V,IAAIoY,EAAUroB,QAAQihB,EAAgBnhB,KAAKtB,CAAAA,GACnE6pB,EAAU9pB,KAAK0iB,EAAgBnhB,KAAKvB,GAGpC6pB,EAAW9N,IAAIyB,aACbT,GACA2F,EAAgB7B,gBAAgB7gB,IAAIxE,KAAK+lB,iBAAiBlgB,MAC1DqhB,EAAgB7B,gBAAgB5gB,IAAIzE,KAAK+lB,iBAAiBjgB,KAC1D9F,KAAK+lB,iBAAiBlgB,MACtB7F,KAAK+lB,iBAAiBjgB,KACtBohB,EAAgBnhB,KAAKvB,GACrB0iB,EAAgBnhB,KAAKtB,CAAAA,GAEvB4pB,EAAWK,SAASxH,CAAAA,GACpBmH,EAAW/mB,WAEJ4f;UACT;UASQ,sBAAsB3F,IAAsBoN,IAA2BnE,IAAsBoE,IAA0BtC,IAAsBD,IAAAA;AACnJsC,YAAAA,GAAY7oB,MAAM;AAClB,kBAAMG,KAAS2oB,KAAkB5uB,KAAKwlB,QAAQ1I,mBAAmB9c,KAAKsmB,WAAWrgB,QAC3EP,KAAQkpB,KAAkB5uB,KAAKwlB,QAAQ3I,kBAAkB2N;AAC/D,gBAAIqE,KAAAA;AACJ,qBAASpqB,KAAI,GAAGA,KAAIwB,IAAQxB,MAAK;AAC/B,uBAASD,KAAI,GAAGA,KAAIkB,IAAOlB,MAAK;AAC9B,sBAAMsqB,KAAcrqB,KAAIzE,KAAKsmB,WAAW5gB,QAAQ,IAAQ,IAAJlB,KAAQ;AAC5D,oBAAoC,MAAhC+c,GAAUQ,KAAK+M,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY7oB,MAAMrB,IAClBoqB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAY9oB,OAAO,GACnBgpB,KAAAA;AACA,qBAASrqB,KAAI,GAAGA,KAAI6nB,KAAU3mB,IAAOlB,MAAK;AACxC,uBAASC,KAAI,GAAGA,KAAIwB,IAAQxB,MAAK;AAC/B,sBAAMqqB,KAAcrqB,KAAIzE,KAAKsmB,WAAW5gB,QAAQ,IAAQ,IAAJlB,KAAQ;AAC5D,oBAAoC,MAAhC+c,GAAUQ,KAAK+M,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY9oB,OAAOrB,IACnBqqB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAY1I,QAAQvgB,IACpBmpB,KAAAA;AACA,qBAASrqB,KAAI6nB,KAAU3mB,KAAQ,GAAGlB,MAAK6nB,IAAS7nB,MAAK;AACnD,uBAASC,KAAI,GAAGA,KAAIwB,IAAQxB,MAAK;AAC/B,sBAAMqqB,KAAcrqB,KAAIzE,KAAKsmB,WAAW5gB,QAAQ,IAAQ,IAAJlB,KAAQ;AAC5D,oBAAoC,MAAhC+c,GAAUQ,KAAK+M,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY1I,QAAQzhB,IACpBqqB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAY3I,SAAS/f,IACrB4oB,KAAAA;AACA,qBAASpqB,KAAIwB,KAAS,GAAGxB,MAAK,GAAGA,MAAK;AACpC,uBAASD,KAAI,GAAGA,KAAIkB,IAAOlB,MAAK;AAC9B,sBAAMsqB,KAAcrqB,KAAIzE,KAAKsmB,WAAW5gB,QAAQ,IAAQ,IAAJlB,KAAQ;AAC5D,oBAAoC,MAAhC+c,GAAUQ,KAAK+M,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY3I,SAASvhB,IACrBoqB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJ,mBAAO,EACL3oB,aAAa,GACbmf,iBAAiB,EAAE7gB,GAAG,GAAGC,GAAG,EAAA,GAC5B0B,0BAA0B,EAAE3B,GAAG,GAAGC,GAAG,EAAA,GACrCsB,MAAM,EACJvB,GAAGmqB,GAAY1I,QAAQ0I,GAAY9oB,OAAO,GAC1CpB,GAAGkqB,GAAY3I,SAAS2I,GAAY7oB,MAAM,EAAA,GAE5CO,eAAe,EACb7B,GAAImqB,GAAY1I,QAAQ0I,GAAY9oB,OAAO,GAC3CpB,GAAIkqB,GAAY3I,SAAS2I,GAAY7oB,MAAM,EAAA,GAE7CF,QAAQ,EACNpB,GAAAA,CAAImqB,GAAY9oB,OAAOwmB,MAAYuC,MAAmBtC,KAAensB,KAAKoF,OAAOvF,KAAKwlB,QAAQ3I,kBAAkB7c,KAAKwlB,QAAQzI,mBAAmB,CAAA,IAAK,IACrJtY,GAAAA,CAAIkqB,GAAY7oB,MAAMumB,MAAYuC,MAAmBtC,KAA2C,MAA5BtsB,KAAKwlB,QAAQhQ,aAAmB,IAAIrV,KAAKsV,OAAOzV,KAAKwlB,QAAQ1I,mBAAmB9c,KAAKwlB,QAAQxI,oBAAoB,CAAA,IAAK,GAAA,EAAA;UAGhM;QAAA;AAj4BF,QAAA3e,GAAA,eAAA,GAkXUE,GAAA,CADP,EAAAsJ,SAAAA,GAAAA,EAAAA,WAAAA,gBAAAA,IAAAA;QAmhBH,MAAMqgB,EAAAA;UAKJ,IAAA,iBAAWV;AAA2B,mBAAOxnB,KAAK+uB,eAAe/uB,KAAKgG,OAAON,QAAQ1F,KAAKgG,OAAOC;UAAS;UAG1G,IAAA,SAAW2hB;AAA4C,mBAAO5nB,KAAKgvB;UAAS;UACrE,SAASC,IAAAA;AACdjvB,iBAAKgvB,QAAQrR,KAAKsR,EAAAA,GAClBjvB,KAAK+uB,eAAeE,GAAMlpB,KAAKvB,IAAIyqB,GAAMlpB,KAAKtB;UAChD;UAwBA,YACE+J,IACAzI,IACAmpB,IAAAA;AAEA,gBArCM,KAAAH,cAAsB,GAGb,KAAAC,UAA8B,CAAA,GAUxC,KAAA1nB,UAAU,GAYV,KAAA8f,aAAkC,EACvC5iB,GAAG,GACHC,GAAG,GACHwB,QAAQ,EAAA,GAEM,KAAAsoB,YAAmC,CAAA,GAO7CW;AACF,yBAAW9G,MAAK8G;AACdlvB,qBAAKgvB,QAAQrR,KAAAA,GAAQyK,GAAER,MAAAA,GACvB5nB,KAAK+uB,eAAe3G,GAAE2G;AAG1B/uB,iBAAKgG,SAASugB,EAAa/X,IAAUzI,IAAMA,EAAAA,GAI3C/F,KAAKugB,OAAM,GAAA,EAAAlgB,cAAaL,KAAKgG,OAAO6I,WAAW,MAAM,EAAE8J,OAAAA,KAAO,CAAA,CAAA;UAChE;UAEO,QAAApS;AACLvG,iBAAKugB,IAAIlH,UAAU,GAAG,GAAGrZ,KAAKgG,OAAON,OAAO1F,KAAKgG,OAAOC,MAAAA,GACxDjG,KAAKonB,WAAW5iB,IAAI,GACpBxE,KAAKonB,WAAW3iB,IAAI,GACpBzE,KAAKonB,WAAWnhB,SAAS,GACzBjG,KAAKuuB,UAAUnpB,SAAS,GACxBpF,KAAKsH;UACP;QAAA;AAQF,iBAAS2mB,EAAW1M,IAAsB5c,IAAYC,IAAYuqB,IAAAA;AAEhE,gBAAM3wB,KAAImG,GAAGkG,SAAS,IAChBM,KAAIxG,GAAGkG,SAAS,KAAK,KACrBO,KAAIzG,GAAGkG,SAAS,IAAI,KACpBukB,KAAMxqB,GAAGiG,SAAS,IAClBwkB,KAAMzqB,GAAGiG,SAAS,KAAK,KACvBykB,KAAM1qB,GAAGiG,SAAS,IAAI,KAQtB0kB,KAAYpvB,KAAKoF,OAAOpF,KAAKqvB,IAAIhxB,KAAI4wB,EAAAA,IAAOjvB,KAAKqvB,IAAIrkB,KAAIkkB,EAAAA,IAAOlvB,KAAKqvB,IAAIpkB,KAAIkkB,EAAAA,KAAQ,EAAA;AAG3F,cAAInB,KAAAA;AACJ,mBAASvoB,KAAS,GAAGA,KAAS2b,GAAUQ,KAAK3c,QAAQQ,MAAU;AAEzD2b,YAAAA,GAAUQ,KAAKnc,EAAAA,MAAYpH,MAC3B+iB,GAAUQ,KAAKnc,KAAS,CAAA,MAAOuF,MAC/BoW,GAAUQ,KAAKnc,KAAS,CAAA,MAAOwF,MAI7B+jB,MACChvB,KAAKqvB,IAAIjO,GAAUQ,KAAKnc,EAAAA,IAAUpH,EAAAA,IACnC2B,KAAKqvB,IAAIjO,GAAUQ,KAAKnc,KAAS,CAAA,IAAKuF,EAAAA,IACtChL,KAAKqvB,IAAIjO,GAAUQ,KAAKnc,KAAS,CAAA,IAAKwF,EAAAA,IAAMmkB,KANhDhO,GAAUQ,KAAKnc,KAAS,CAAA,IAAK,IAS3BuoB,KAAAA;AAKN,iBAAOA;QACT;AAWA,iBAAS5H,EAAa/X,IAAoB9I,IAAeO,IAAAA;AACvD,gBAAMD,KAASwI,GAASC,cAAc,QAAA;AAGtC,iBAFAzI,GAAON,QAAQA,IACfM,GAAOC,SAASA,IACTD;QACT;MAAA,GAAA,KAAA,CAAA5H,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAAA,GAAA,cAAAA,GAAA,OAAAA,GAAA,MAAAA,GAAA,MAAAA,GAAA,QAAAA,GAAA,WAAAA,GAAA,aAAA;ACpjCA,cAAAE,KAAAD,GAAA,GAAA;AAGA,YAAI8J,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK;AAUT,YAAiBknB,GAmBApkB,GAuEA0F,GAkGA2e,GAoCA7kB;AA8FjB,iBAAgB8kB,EAAYnP,IAAAA;AAC1B,gBAAMjiB,KAAIiiB,GAAEjI,SAAS,EAAA;AACrB,iBAAOha,GAAE6G,SAAS,IAAI,MAAM7G,KAAIA;QAClC;AAQA,iBAAgBqxB,EAAcC,IAAYC,IAAAA;AACxC,iBAAID,KAAKC,MACCA,KAAK,SAASD,KAAK,SAErBA,KAAK,SAASC,KAAK;QAC7B;AAtVa,QAAAzxB,GAAA4f,aAAqB,EAChClN,KAAK,aACLlG,MAAM,EAAA,GAMR,SAAiB4kB,IAAAA;AACC,UAAArxB,GAAA2xB,QAAhB,SAAsBvxB,IAAW2M,IAAWC,IAAWzM,IAAAA;AACrD,mBAAA,WAAIA,KACK,IAAIgxB,EAAYnxB,EAAAA,CAAAA,GAAKmxB,EAAYxkB,EAAAA,CAAAA,GAAKwkB,EAAYvkB,EAAAA,CAAAA,GAAKukB,EAAYhxB,EAAAA,CAAAA,KAErE,IAAIgxB,EAAYnxB,EAAAA,CAAAA,GAAKmxB,EAAYxkB,EAAAA,CAAAA,GAAKwkB,EAAYvkB,EAAAA,CAAAA;UAC3D,GAEgBhN,GAAA4xB,SAAhB,SAAuBxxB,IAAW2M,IAAWC,IAAWzM,KAAY,KAAA;AAIlE,oBAAQH,MAAK,KAAK2M,MAAK,KAAKC,MAAK,IAAIzM,QAAO;UAC9C;QACD,EAdgB8wB,MAAQpxB,GAAA,WAARoxB,IAAQ,CAAA,EAAA,GAmBzB,SAAiBrxB,IAAA;AAgDf,mBAAgB6xB,GAAQ5kB,IAAe4kB,IAAAA;AAGrC,mBAFA1nB,IAAKpI,KAAKsV,MAAgB,MAAVwa,EAAAA,GAAAA,CACf7nB,GAAIC,GAAIC,CAAAA,IAAMuC,EAAKqlB,WAAW7kB,GAAMR,IAAAA,GAC9B,EACLkG,KAAK0e,EAASM,MAAM3nB,GAAIC,GAAIC,GAAIC,CAAAA,GAChCsC,MAAM4kB,EAASO,OAAO5nB,GAAIC,GAAIC,GAAIC,CAAAA,EAAAA;UAEtC;AAtDgB,UAAAnK,GAAA+xB,QAAhB,SAAsBxrB,IAAYC,IAAAA;AAEhC,gBADA2D,KAAgB,MAAV3D,GAAGiG,QAAe,KACb,MAAPtC;AACF,qBAAO,EACLwI,KAAKnM,GAAGmM,KACRlG,MAAMjG,GAAGiG,KAAAA;AAGb,kBAAMukB,KAAOxqB,GAAGiG,QAAQ,KAAM,KACxBwkB,KAAOzqB,GAAGiG,QAAQ,KAAM,KACxBykB,KAAO1qB,GAAGiG,QAAQ,IAAK,KACvBulB,KAAOzrB,GAAGkG,QAAQ,KAAM,KACxBwlB,KAAO1rB,GAAGkG,QAAQ,KAAM,KACxBylB,KAAO3rB,GAAGkG,QAAQ,IAAK;AAM7B,mBALAzC,IAAKgoB,KAAMjwB,KAAKsV,OAAO2Z,KAAMgB,MAAO7nB,CAAAA,GACpCF,IAAKgoB,KAAMlwB,KAAKsV,OAAO4Z,KAAMgB,MAAO9nB,CAAAA,GACpCD,IAAKgoB,KAAMnwB,KAAKsV,OAAO6Z,KAAMgB,MAAO/nB,CAAAA,GAG7B,EAAEwI,KAFG0e,EAASM,MAAM3nB,GAAIC,GAAIC,CAAAA,GAErBuC,MADD4kB,EAASO,OAAO5nB,GAAIC,GAAIC,CAAAA,EAAAA;UAEvC,GAEgBlK,GAAAmyB,WAAhB,SAAyBllB,IAAAA;AACvB,mBAA+B,QAAV,MAAbA,GAAMR;UAChB,GAEgBzM,GAAA+rB,sBAAhB,SAAoCxlB,IAAYC,IAAY4rB,IAAAA;AAC1D,kBAAMlc,KAASzJ,EAAKsf,oBAAoBxlB,GAAGkG,MAAMjG,GAAGiG,MAAM2lB,EAAAA;AAC1D,gBAAKlc;AAGL,qBAAOzJ,EAAKqe,QACT5U,MAAU,KAAK,KACfA,MAAU,KAAK,KACfA,MAAU,IAAK,GAAA;UAEpB,GAEgBlW,GAAA+qB,SAAhB,SAAuB9d,IAAAA;AACrB,kBAAMolB,MAA0B,MAAbplB,GAAMR,UAAiB;AAE1C,mBAAA,CADCzC,GAAIC,GAAIC,CAAAA,IAAMuC,EAAKqlB,WAAWO,EAAAA,GACxB,EACL1f,KAAK0e,EAASM,MAAM3nB,GAAIC,GAAIC,CAAAA,GAC5BuC,MAAM4lB,GAAAA;UAEV,GAEgBryB,GAAA6xB,UAAO5xB,IASPD,GAAAsrB,kBAAhB,SAAgCre,IAAeqlB,IAAAA;AAE7C,mBADAnoB,IAAkB,MAAb8C,GAAMR,MACJolB,GAAQ5kB,IAAQ9C,IAAKmoB,KAAU,GAAA;UACxC,GAEgBtyB,GAAA6qB,aAAhB,SAA2B5d,IAAAA;AACzB,mBAAO,CAAEA,GAAMR,QAAQ,KAAM,KAAOQ,GAAMR,QAAQ,KAAM,KAAOQ,GAAMR,QAAQ,IAAK,GAAA;UACpF;QACD,EAjEgBQ,MAAKhN,GAAA,QAALgN,IAAK,CAAA,EAAA,GAuEtB,SAAiBjN,IAAA;AACf,cAAIuyB,IACAC;AACJ,cAAA,CAAKryB,GAAAsyB,QAAQ;AACX,kBAAM7qB,KAASwI,SAASC,cAAc,QAAA;AACtCzI,YAAAA,GAAON,QAAQ,GACfM,GAAOC,SAAS;AAChB,kBAAMsa,KAAMva,GAAO6I,WAAW,MAAM,EAClC6X,oBAAAA,KAAoB,CAAA;AAElBnG,YAAAA,OACFoQ,KAAOpQ,IACPoQ,GAAK9E,2BAA2B,QAChC+E,KAAeD,GAAKG,qBAAqB,GAAG,GAAG,GAAG,CAAA;UAAA;AAWtC,UAAA1yB,GAAA8qB,UAAhB,SAAwBnY,IAAAA;AAEtB,gBAAIA,GAAIggB,MAAM,gBAAA;AACZ,sBAAQhgB,GAAI3L,QAAAA;gBACV,KAAK;AAIH,yBAHAgD,IAAKsZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GACzC3oB,IAAKqZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GACzC1oB,IAAKoZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GAClCnmB,EAAKqe,QAAQ9gB,GAAIC,GAAIC,CAAAA;gBAE9B,KAAK;AAKH,yBAJAF,IAAKsZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GACzC3oB,IAAKqZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GACzC1oB,IAAKoZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GACzCzoB,IAAKmZ,SAAS3Q,GAAIqN,MAAM,GAAG,CAAA,EAAG4S,OAAO,CAAA,GAAI,EAAA,GAClCnmB,EAAKqe,QAAQ9gB,GAAIC,GAAIC,GAAIC,CAAAA;gBAElC,KAAK;AACH,yBAAO,EACLwI,KAAAA,IACAlG,OAAO6W,SAAS3Q,GAAIqN,MAAM,CAAA,GAAI,EAAA,KAAO,IAAI,SAAU,EAAA;gBAEvD,KAAK;AACH,yBAAO,EACLrN,KAAAA,IACAlG,MAAM6W,SAAS3Q,GAAIqN,MAAM,CAAA,GAAI,EAAA,MAAQ,EAAA;cAAA;AAM7C,kBAAM6S,KAAYlgB,GAAIggB,MAAM,oFAAA;AAC5B,gBAAIE;AAKF,qBAJA7oB,IAAKsZ,SAASuP,GAAU,CAAA,CAAA,GACxB5oB,IAAKqZ,SAASuP,GAAU,CAAA,CAAA,GACxB3oB,IAAKoZ,SAASuP,GAAU,CAAA,CAAA,GACxB1oB,IAAKpI,KAAKsV,MAAoE,OAAA,WAA7Dwb,GAAU,CAAA,IAAmB,IAAInP,WAAWmP,GAAU,CAAA,CAAA,EAAA,GAChEpmB,EAAKqe,QAAQ9gB,GAAIC,GAAIC,GAAIC,CAAAA;AAIlC,gBAAA,CAAKooB,MAAAA,CAASC;AACZ,oBAAM,IAAI9hB,MAAM,qCAAA;AAOlB,gBAFA6hB,GAAKrX,YAAYsX,IACjBD,GAAKrX,YAAYvI,IACa,YAAA,OAAnB4f,GAAKrX;AACd,oBAAM,IAAIxK,MAAM,qCAAA;AAOlB,gBAJA6hB,GAAKvX,SAAS,GAAG,GAAG,GAAG,CAAA,GAAA,CACtBhR,GAAIC,GAAIC,GAAIC,CAAAA,IAAMooB,GAAKzC,aAAa,GAAG,GAAG,GAAG,CAAA,EAAGnM,MAGtC,QAAPxZ;AACF,oBAAM,IAAIuG,MAAM,qCAAA;AAMlB,mBAAO,EACLjE,MAAM4kB,EAASO,OAAO5nB,GAAIC,GAAIC,GAAIC,CAAAA,GAClCwI,KAAAA,GAAAA;UAEJ;QACD,EA7FgBA,MAAG1S,GAAA,MAAH0S,IAAG,CAAA,EAAA,GAkGpB,SAAiB3S,IAAA;AAsBf,mBAAgB8yB,GAAmB1yB,IAAW2M,IAAWC,IAAAA;AACvD,kBAAM+lB,KAAK3yB,KAAI,KACT4yB,KAAKjmB,KAAI,KACTkmB,KAAKjmB,KAAI;AAIf,mBAAY,UAHD+lB,MAAM,UAAUA,KAAK,QAAQhxB,KAAKmxB,KAAKH,KAAK,SAAS,OAAO,GAAA,KAG7C,UAFfC,MAAM,UAAUA,KAAK,QAAQjxB,KAAKmxB,KAAKF,KAAK,SAAS,OAAO,GAAA,KAE/B,UAD7BC,MAAM,UAAUA,KAAK,QAAQlxB,KAAKmxB,KAAKD,KAAK,SAAS,OAAO,GAAA;UAEzE;AAvBgB,UAAAjzB,GAAAmzB,oBAAhB,SAAkC7B,IAAAA;AAChC,mBAAOwB,GACJxB,MAAO,KAAM,KACbA,MAAO,IAAM,KACA,MAAdtxB,EAAA;UACJ,GAUgBA,GAAA8yB,qBAAkB7yB;QASnC,EA/BgBqxB,MAAGrxB,GAAA,MAAHqxB,IAAG,CAAA,EAAA,GAoCpB,SAAiB7kB,IAAAA;AAyCf,mBAAgB2mB,GAAgBzH,IAAgBE,IAAgBuG,IAAAA;AAG9D,kBAAMJ,KAAOrG,MAAU,KAAM,KACvBsG,KAAOtG,MAAU,KAAM,KACvBuG,KAAOvG,MAAW,IAAK;AAC7B,gBAAIqF,KAAOnF,MAAU,KAAM,KACvBoF,KAAOpF,MAAU,KAAM,KACvBqF,KAAOrF,MAAW,IAAK,KACvBwH,KAAK7B,EAAcF,EAAIwB,mBAAmB9B,IAAKC,IAAKC,EAAAA,GAAMI,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAC/F,mBAAOmB,KAAKjB,OAAUpB,KAAM,KAAKC,KAAM,KAAKC,KAAM;AAEhDF,cAAAA,MAAOjvB,KAAK+V,IAAI,GAAG/V,KAAKoV,KAAW,MAAN6Z,EAAAA,CAAAA,GAC7BC,MAAOlvB,KAAK+V,IAAI,GAAG/V,KAAKoV,KAAW,MAAN8Z,EAAAA,CAAAA,GAC7BC,MAAOnvB,KAAK+V,IAAI,GAAG/V,KAAKoV,KAAW,MAAN+Z,EAAAA,CAAAA,GAC7BmC,KAAK7B,EAAcF,EAAIwB,mBAAmB9B,IAAKC,IAAKC,EAAAA,GAAMI,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAE7F,oBAAQlB,MAAO,KAAKC,MAAO,KAAKC,MAAO,IAAI,SAAU;UACvD;AAEA,mBAAgBoC,GAAkB3H,IAAgBE,IAAgBuG,IAAAA;AAGhE,kBAAMJ,KAAOrG,MAAU,KAAM,KACvBsG,KAAOtG,MAAU,KAAM,KACvBuG,KAAOvG,MAAW,IAAK;AAC7B,gBAAIqF,KAAOnF,MAAU,KAAM,KACvBoF,KAAOpF,MAAU,KAAM,KACvBqF,KAAOrF,MAAW,IAAK,KACvBwH,KAAK7B,EAAcF,EAAIwB,mBAAmB9B,IAAKC,IAAKC,EAAAA,GAAMI,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAC/F,mBAAOmB,KAAKjB,OAAUpB,KAAM,OAAQC,KAAM,OAAQC,KAAM;AAEtDF,cAAAA,KAAMjvB,KAAKC,IAAI,KAAMgvB,KAAMjvB,KAAKoV,KAAmB,OAAb,MAAM6Z,GAAAA,CAAAA,GAC5CC,KAAMlvB,KAAKC,IAAI,KAAMivB,KAAMlvB,KAAKoV,KAAmB,OAAb,MAAM8Z,GAAAA,CAAAA,GAC5CC,KAAMnvB,KAAKC,IAAI,KAAMkvB,KAAMnvB,KAAKoV,KAAmB,OAAb,MAAM+Z,GAAAA,CAAAA,GAC5CmC,KAAK7B,EAAcF,EAAIwB,mBAAmB9B,IAAKC,IAAKC,EAAAA,GAAMI,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAE7F,oBAAQlB,MAAO,KAAKC,MAAO,KAAKC,MAAO,IAAI,SAAU;UACvD;AAjEgB,UAAAlxB,GAAA+rB,sBAAhB,SAAoCJ,IAAgBE,IAAgBuG,IAAAA;AAClE,kBAAMmB,KAAMjC,EAAI6B,kBAAkBxH,MAAU,CAAA,GACtC6H,KAAMlC,EAAI6B,kBAAkBtH,MAAU,CAAA;AAE5C,gBADW2F,EAAc+B,IAAKC,EAAAA,IACrBpB,IAAO;AACd,kBAAIoB,KAAMD,IAAK;AACb,sBAAME,KAAUL,GAAgBzH,IAAQE,IAAQuG,EAAAA,GAC1CsB,KAAelC,EAAc+B,IAAKjC,EAAI6B,kBAAkBM,MAAW,CAAA,CAAA;AACzE,oBAAIC,KAAetB,IAAO;AACxB,wBAAMuB,KAAUL,GAAkB3H,IAAQE,IAAQuG,EAAAA;AAElD,yBAAOsB,KADclC,EAAc+B,IAAKjC,EAAI6B,kBAAkBQ,MAAW,CAAA,CAAA,IACpCF,KAAUE;gBAAAA;AAEjD,uBAAOF;cAAAA;AAET,oBAAMA,KAAUH,GAAkB3H,IAAQE,IAAQuG,EAAAA,GAC5CsB,KAAelC,EAAc+B,IAAKjC,EAAI6B,kBAAkBM,MAAW,CAAA,CAAA;AACzE,kBAAIC,KAAetB,IAAO;AACxB,sBAAMuB,KAAUP,GAAgBzH,IAAQE,IAAQuG,EAAAA;AAEhD,uBAAOsB,KADclC,EAAc+B,IAAKjC,EAAI6B,kBAAkBQ,MAAW,CAAA,CAAA,IACpCF,KAAUE;cAAAA;AAEjD,qBAAOF;YAAAA;UAGX,GAEgBzzB,GAAAozB,kBAAenzB,IAoBfD,GAAAszB,oBAAiBpzB,IAqBjBF,GAAA8xB,aAAhB,SAA2BjgB,IAAAA;AACzB,mBAAO,CAAEA,MAAS,KAAM,KAAOA,MAAS,KAAM,KAAOA,MAAS,IAAK,KAAc,MAARA,EAAAA;UAC3E,GAEgB7R,GAAA8qB,UAAhB,SAAwB1qB,IAAW2M,IAAWC,IAAWzM,IAAAA;AACvD,mBAAO,EACLoS,KAAK0e,EAASM,MAAMvxB,IAAG2M,IAAGC,IAAGzM,EAAAA,GAC7BkM,MAAM4kB,EAASO,OAAOxxB,IAAG2M,IAAGC,IAAGzM,EAAAA,EAAAA;UAEnC;QACD,EA5FgBkM,MAAIxM,GAAA,OAAJwM,IAAI,CAAA,EAAA,GA8FrBxM,GAAA,cAAA,GAWAA,GAAA,gBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,eAAAA,GAAA,eAAA,QCzUAA,GAAA,eAAA,MAAA;UAAA,cAAA;AACU,iBAAA2zB,aAAgC,CAAA,GAEhC,KAAAC,YAAAA;UA4CV;UA1CE,IAAA,QAAWjlB;AAmBT,mBAlBKhN,KAAKkyB,WACRlyB,KAAKkyB,SAAUC,CAAAA,QACbnyB,KAAKgyB,WAAWrU,KAAKwU,EAAAA,GACF,EACjB7hB,SAAS,MAAA;AACP,kBAAA,CAAKtQ,KAAKiyB;AACR,yBAAS3zB,KAAI,GAAGA,KAAI0B,KAAKgyB,WAAW5sB,QAAQ9G;AAC1C,sBAAI0B,KAAKgyB,WAAW1zB,EAAAA,MAAO6zB;AAEzB,2BAAA,KADAnyB,KAAKgyB,WAAWtU,OAAOpf,IAAG,CAAA;;YAAA,EAAA,KAUjC0B,KAAKkyB;UACd;UAEO,KAAKE,IAASC,IAAAA;AACnB,kBAAMxL,KAA2B,CAAA;AACjC,qBAASvoB,KAAI,GAAGA,KAAI0B,KAAKgyB,WAAW5sB,QAAQ9G;AAC1CuoB,cAAAA,GAAMlJ,KAAK3d,KAAKgyB,WAAW1zB,EAAAA,CAAAA;AAE7B,qBAASA,KAAI,GAAGA,KAAIuoB,GAAMzhB,QAAQ9G;AAChCuoB,cAAAA,GAAMvoB,EAAAA,EAAGg0B,KAAAA,QAAgBF,IAAMC,EAAAA;UAEnC;UAEO,UAAA/hB;AACLtQ,iBAAKuyB,eAAAA,GACLvyB,KAAKiyB,YAAAA;UACP;UAEO,iBAAAM;AACDvyB,iBAAKgyB,eACPhyB,KAAKgyB,WAAW5sB,SAAS;UAE7B;QAAA,GAGF/G,GAAA,eAAA,SAAgCm0B,IAAiBC,IAAAA;AAC/C,iBAAOD,GAAKp0B,CAAAA,OAAKq0B,GAAGnjB,KAAKlR,EAAAA,CAAAA;QAC3B;MAAA,GAAA,KAAA,CAAAA,IAAAC,OAAA;ACuBA,iBAAgBq0B,GAAaC,IAAAA;AAC3B,qBAAWtW,MAAKsW;AACdtW,YAAAA,GAAE/L,QAAAA;AAEJqiB,UAAAA,GAAYvtB,SAAS;QACvB;AAAA,eAAA,eAAA/G,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,4BAAAA,GAAA,eAAAA,GAAA,eAAAA,GAAA,oBAAAA,GAAA,aAAA,QAzFAA,GAAA,aAAA,MAAA;UAAA,cAAA;AACY,iBAAAu0B,eAA8B,CAAA,GAC9B,KAAAC,cAAAA;UAkCZ;UA7BS,UAAAviB;AACLtQ,iBAAK6yB,cAAAA;AACL,uBAAWxW,MAAKrc,KAAK4yB;AACnBvW,cAAAA,GAAE/L,QAAAA;AAEJtQ,iBAAK4yB,aAAaxtB,SAAS;UAC7B;UAOO,SAAgCiX,IAAAA;AAErC,mBADArc,KAAK4yB,aAAajV,KAAKtB,EAAAA,GAChBA;UACT;UAOO,WAAkCA,IAAAA;AACvC,kBAAM0B,KAAQ/d,KAAK4yB,aAAarV,QAAQlB,EAAAA;AAAAA,mBACpC0B,MACF/d,KAAK4yB,aAAalV,OAAOK,IAAO,CAAA;UAEpC;QAAA,GAGF1f,GAAA,oBAAA,MAAA;UAAA,cAAA;AAEU,iBAAAw0B,cAAAA;UAgCV;UA3BE,IAAA,QAAW5iB;AACT,mBAAOjQ,KAAK6yB,cAAAA,SAA0B7yB,KAAK8yB;UAC7C;UAKA,IAAA,MAAiB7iB,IAAAA;AAAAA,gBAAAA;AACXjQ,iBAAK6yB,eAAe5iB,OAAUjQ,KAAK8yB,WAG5B,UAAXz0B,KAAA2B,KAAK8yB,WAAAA,WAAMz0B,MAAAA,GAAEiS,QAAAA,GACbtQ,KAAK8yB,SAAS7iB;UAChB;UAKO,QAAA1J;AACLvG,iBAAKiQ,QAAAA;UACP;UAEO,UAAAK;AAAAA,gBAAAA;AACLtQ,iBAAK6yB,cAAAA,MACM,UAAXz0B,KAAA4B,KAAK8yB,WAAAA,WAAM10B,MAAAA,GAAEkS,QAAAA,GACbtQ,KAAK8yB,SAAAA;UACP;QAAA,GAMFz0B,GAAA,eAAA,SAA6BskB,IAAAA;AAC3B,iBAAO,EAAErS,SAASqS,GAAAA;QACpB,GAKAtkB,GAAA,eAAAC,IAUAD,GAAA,4BAAA,SAA0C4G,IAAAA;AACxC,iBAAO,EAAEqL,SAAS,MAAMoiB,GAAaztB,EAAAA,EAAAA;QACvC;MAAA,GAAA,KAAA,CAAA7G,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,YAAA;QCtGA,MAAa00B,GAAAA;UAAb,cAAA;AACU,iBAAAC,QAA8F,CAAC;UAgBzG;UAdS,IAAIC,IAAeC,IAAiBjjB,IAAAA;AACpCjQ,iBAAKgzB,MAAMC,EAAAA,MACdjzB,KAAKgzB,MAAMC,EAAAA,IAAS,CAAC,IAEvBjzB,KAAKgzB,MAAMC,EAAAA,EAA2BC,EAAAA,IAAUjjB;UAClD;UAEO,IAAIgjB,IAAeC,IAAAA;AACxB,mBAAOlzB,KAAKgzB,MAAMC,EAAAA,IAA4BjzB,KAAKgzB,MAAMC,EAAAA,EAA2BC,EAAAA,IAAAA;UACtF;UAEO,QAAA3sB;AACLvG,iBAAKgzB,QAAQ,CAAC;UAChB;QAAA;AAhBF,QAAA30B,GAAA,YAAAC,IAmBAD,GAAA,aAAA,MAAA;UAAA,cAAA;AACU,iBAAA20B,QAAwE,IAAID;UAgBtF;UAdS,IAAIE,IAAeC,IAAiBC,IAAeC,GAAiBnjB,GAAAA;AACpEjQ,iBAAKgzB,MAAM5e,IAAI6e,IAAOC,EAAAA,KACzBlzB,KAAKgzB,MAAM5rB,IAAI6rB,IAAOC,IAAQ,IAAIH,IAAAA,GAEpC/yB,KAAKgzB,MAAM5e,IAAI6e,IAAOC,EAAAA,EAAS9rB,IAAI+rB,IAAOC,GAAQnjB,CAAAA;UACpD;UAEO,IAAIgjB,IAAeC,IAAiBC,IAAeC,IAAAA;AAAAA,gBAAAA;AACxD,mBAAoC,UAA7B,IAAApzB,KAAKgzB,MAAM5e,IAAI6e,IAAOC,EAAAA,MAAAA,WAAO,IAAA,SAAA,EAAE9e,IAAI+e,IAAOC,EAAAA;UACnD;UAEO,QAAA7sB;AACLvG,iBAAKgzB,MAAMzsB,MAAAA;UACb;QAAA;MAAA,GAAA,KAAA,CAAAnI,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,UAAAA,GAAA,YAAAA,GAAA,WAAAA,GAAA,SAAAA,GAAA,QAAAA,GAAA,mBAAAA,GAAA,WAAAA,GAAA,eAAAA,GAAA,YAAAA,GAAA,SAAA,QCzBWA,GAAAwyB,SAA+B,eAAA,OAAdwC;AAC9B,cAAMC,KAAaj1B,GAAM,SAAI,SAASg1B,UAAUC,WAC1CC,KAAYl1B,GAAM,SAAI,SAASg1B,UAAUE;AAElC,QAAAl1B,GAAAwgB,YAAYyU,GAAUE,SAAS,SAAA,GAC/Bn1B,GAAAygB,eAAewU,GAAUE,SAAS,MAAA,GAClCn1B,GAAAo1B,WAAW,iCAAiCC,KAAKJ,EAAAA,GAC9Dj1B,GAAA,mBAAA,WAAA;AACE,cAAA,CAAKA,GAAAo1B;AACH,mBAAO;AAET,gBAAME,KAAeL,GAAUvC,MAAM,gBAAA;AACrC,iBAAqB,SAAjB4C,MAAyBA,GAAavuB,SAAS,IAC1C,IAEFsc,SAASiS,GAAa,CAAA,CAAA;QAC/B,GAKat1B,GAAAu1B,QAAQ,CAAC,aAAa,YAAY,UAAU,QAAA,EAAUJ,SAASD,EAAAA,GAC/Dl1B,GAAAw1B,SAAsB,WAAbN,IACTl1B,GAAAy1B,WAAwB,aAAbP,IACXl1B,GAAA01B,YAAY,CAAC,WAAW,SAAS,SAAS,OAAA,EAASP,SAASD,EAAAA,GAC5Dl1B,GAAA21B,UAAUT,GAAShW,QAAQ,OAAA,KAAY,GAEvClf,GAAA41B,aAAa,WAAWP,KAAKJ,EAAAA;MAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAA,OAAAA;AAAAA,eAAAA,eAAAA,IAAAA,cAAAA,EAAAA,OAAAA,KAAAA,CAAAA,GAAAA,GAAAA,oBAAAA,GAAAA,gBAAAA,GAAAA,oBAAAA;ACrC1C,cAAA/0B,KAAAD,GAAA,GAAA;QA2BA,MAAe41B,EAAAA;UAAf,cAAA;AACU,iBAAAC,SAAmC,CAAA,GAEnC,KAAAC,KAAK;UAkEf;UA7DS,QAAQC,IAAAA;AACbr0B,iBAAKm0B,OAAOxW,KAAK0W,EAAAA,GACjBr0B,KAAKs0B,OAAAA;UACP;UAEO,QAAAC;AACL,mBAAOv0B,KAAKo0B,KAAKp0B,KAAKm0B,OAAO/uB;AACtBpF,mBAAKm0B,OAAOn0B,KAAKo0B,EAAAA,EAAAA,KACpBp0B,KAAKo0B;AAGTp0B,iBAAKuG,MAAAA;UACP;UAEO,QAAAA;AACDvG,iBAAKw0B,kBACPx0B,KAAKy0B,gBAAgBz0B,KAAKw0B,aAAAA,GAC1Bx0B,KAAKw0B,gBAAAA,SAEPx0B,KAAKo0B,KAAK,GACVp0B,KAAKm0B,OAAO/uB,SAAS;UACvB;UAEQ,SAAAkvB;AACDt0B,iBAAKw0B,kBACRx0B,KAAKw0B,gBAAgBx0B,KAAK00B,iBAAiB10B,KAAK20B,SAASC,KAAK50B,IAAAA,CAAAA;UAElE;UAEQ,SAAS60B,IAAAA;AACf70B,iBAAKw0B,gBAAAA;AACL,gBAAIM,KAAe,GACfC,KAAc,GACdC,KAAwBH,GAASI,cAAAA,GACjCC,KAAoB;AACxB,mBAAOl1B,KAAKo0B,KAAKp0B,KAAKm0B,OAAO/uB,UAAQ;AAanC,kBAZA0vB,KAAetV,KAAKC,IAAAA,GACfzf,KAAKm0B,OAAOn0B,KAAKo0B,EAAAA,EAAAA,KACpBp0B,KAAKo0B,MAKPU,KAAe30B,KAAK+V,IAAI,GAAGsJ,KAAKC,IAAAA,IAAQqV,EAAAA,GACxCC,KAAc50B,KAAK+V,IAAI4e,IAAcC,EAAAA,GAGrCG,KAAoBL,GAASI,cAAAA,GACX,MAAdF,KAAoBG;AAOtB,uBAJIF,KAAwBF,KAAAA,OAC1B9lB,QAAQK,KAAK,4CAA4ClP,KAAKqvB,IAAIrvB,KAAKsV,MAAMuf,KAAwBF,EAAAA,CAAAA,CAAAA,IAAAA,GAAAA,KAEvG90B,KAAKs0B,OAAAA;AAGPU,cAAAA,KAAwBE;YAAAA;AAE1Bl1B,iBAAKuG,MAAAA;UACP;QAAA;QAQF,MAAa4uB,UAA0BjB,EAAAA;UAC3B,iBAAiBrQ,IAAAA;AACzB,mBAAOzU,WAAW,MAAMyU,GAAS7jB,KAAKo1B,gBAAgB,EAAA,CAAA,CAAA;UACxD;UAEU,gBAAgBC,IAAAA;AACxB9lB,yBAAa8lB,EAAAA;UACf;UAEQ,gBAAgBC,IAAAA;AACtB,kBAAM/jB,KAAMiO,KAAKC,IAAAA,IAAQ6V;AACzB,mBAAO,EACLL,eAAe,MAAM90B,KAAK+V,IAAI,GAAG3E,KAAMiO,KAAKC,IAAAA,CAAAA,EAAAA;UAEhD;QAAA;AAdF,QAAAphB,GAAA,oBAAA,GAoCaA,GAAAyoB,gBAAAA,CAAkBvoB,GAAAsyB,UAAU,yBAAyBjhB,SAnBlE,cAAoCskB,EAAAA;UACxB,iBAAiBrQ,IAAAA;AACzB,mBAAO0R,oBAAoB1R,EAAAA;UAC7B;UAEU,gBAAgBwR,IAAAA;AACxBG,+BAAmBH,EAAAA;UACrB;QAAA,IAYkGF,GAMpG92B,GAAA,oBAAA,MAAA;UAGE,cAAA2J;AACEhI,iBAAKy1B,SAAS,IAAIp3B,GAAAyoB;UACpB;UAEO,IAAIuN,IAAAA;AACTr0B,iBAAKy1B,OAAOlvB,MAAAA,GACZvG,KAAKy1B,OAAO1O,QAAQsN,EAAAA;UACtB;UAEO,QAAAE;AACLv0B,iBAAKy1B,OAAOlB,MAAAA;UACd;QAAA;MAAA,GAAA,KAAA,CAAAn2B,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAAA,GAAA,gBAAA;QC5JF,MAAasX,GAAAA;UAAb,cAAA;AAsBS,iBAAA/Q,KAAK,GACL,KAAAD,KAAK,GACL,KAAAwX,WAA2B,IAAIuZ;UAgGxC;UAvHS,OAAA,WAAkBzlB,IAAAA;AACvB,mBAAO,CACLA,OAAU,KAAuB,KACjCA,OAAU,IAAyB,KAC3B,MAARA,EAAAA;UAEJ;UAEO,OAAA,aAAoBA,IAAAA;AACzB,oBAAmB,MAAXA,GAAM,CAAA,MAAa,MAAmC,MAAXA,GAAM,CAAA,MAAa,IAAoC,MAAXA,GAAM,CAAA;UACvG;UAEO,QAAA0lB;AACL,kBAAMC,KAAS,IAAIjgB;AAInB,mBAHAigB,GAAOhxB,KAAK5E,KAAK4E,IACjBgxB,GAAOjxB,KAAK3E,KAAK2E,IACjBixB,GAAOzZ,WAAWnc,KAAKmc,SAASwZ,MAAAA,GACzBC;UACT;UAQO,YAAAhL;AAA4B,mBAAiB,WAAV5qB,KAAK4E;UAAsB;UAC9D,SAAAoV;AAA4B,mBAAiB,YAAVha,KAAK4E;UAAmB;UAC3D,cAAAomB;AACL,mBAAIhrB,KAAK61B,iBAAAA,KAAuD,MAAjC71B,KAAKmc,SAASoR,iBACpC,IAEQ,YAAVvtB,KAAK4E;UACd;UACO,UAAAkxB;AAA4B,mBAAiB,YAAV91B,KAAK4E;UAAoB;UAC5D,cAAA+lB;AAA4B,mBAAiB,aAAV3qB,KAAK4E;UAAwB;UAChE,WAAAqV;AAA4B,mBAAiB,WAAVja,KAAK2E;UAAqB;UAC7D,QAAAkmB;AAA4B,mBAAiB,YAAV7qB,KAAK2E;UAAkB;UAC1D,kBAAAumB;AAA4B,mBAAiB,aAAVlrB,KAAK4E;UAA4B;UACpE,cAAAmxB;AAA4B,mBAAiB,YAAV/1B,KAAK2E;UAAwB;UAChE,aAAAymB;AAA4B,mBAAiB,aAAVprB,KAAK2E;UAAuB;UAG/D,iBAAA2mB;AAA2B,mBAAiB,WAAVtrB,KAAK4E;UAAyB;UAChE,iBAAA4mB;AAA2B,mBAAiB,WAAVxrB,KAAK2E;UAAyB;UAChE,UAAAqxB;AAA2B,mBAA0C,aAAxB,WAAVh2B,KAAK4E;UAAgD;UACxF,UAAAqxB;AAA2B,mBAA0C,aAAxB,WAAVj2B,KAAK2E;UAAgD;UACxF,cAAAuxB;AAA2B,mBAA0C,aAAxB,WAAVl2B,KAAK4E,OAAqF,aAAxB,WAAV5E,KAAK4E;UAAiD;UACjJ,cAAAuxB;AAA2B,mBAA0C,aAAxB,WAAVn2B,KAAK2E,OAAqF,aAAxB,WAAV3E,KAAK2E;UAAiD;UACjJ,cAAAyxB;AAA2B,mBAA0C,MAAxB,WAAVp2B,KAAK4E;UAAgC;UACxE,cAAAyxB;AAA2B,mBAA0C,MAAxB,WAAVr2B,KAAK2E;UAAgC;UACxE,qBAAA2xB;AAAgC,mBAAmB,MAAZt2B,KAAK4E,MAAwB,MAAZ5E,KAAK2E;UAAU;UAGvE,aAAA0mB;AACL,oBAAkB,WAAVrrB,KAAK4E,IAAAA;cACX,KAAK;cACL,KAAK;AAAqB,uBAAiB,MAAV5E,KAAK4E;cACtC,KAAK;AAAqB,uBAAiB,WAAV5E,KAAK4E;cACtC;AAA0B,uBAAA;YAAQ;UAEtC;UACO,aAAA2mB;AACL,oBAAkB,WAAVvrB,KAAK2E,IAAAA;cACX,KAAK;cACL,KAAK;AAAqB,uBAAiB,MAAV3E,KAAK2E;cACtC,KAAK;AAAqB,uBAAiB,WAAV3E,KAAK2E;cACtC;AAA0B,uBAAA;YAAQ;UAEtC;UAGO,mBAAAkxB;AACL,mBAAiB,YAAV71B,KAAK2E;UACd;UACO,iBAAA4xB;AACDv2B,iBAAKmc,SAASgS,QAAAA,IAChBnuB,KAAK2E,MAAAA,aAEL3E,KAAK2E,MAAM;UAEf;UACO,oBAAAmoB;AACL,gBAAe,YAAV9sB,KAAK2E,MAAAA,CAA+B3E,KAAKmc,SAASqa;AACrD,sBAAuC,WAA/Bx2B,KAAKmc,SAASqa,gBAAAA;gBACpB,KAAK;gBACL,KAAK;AAAqB,yBAAsC,MAA/Bx2B,KAAKmc,SAASqa;gBAC/C,KAAK;AAAqB,yBAAsC,WAA/Bx2B,KAAKmc,SAASqa;gBAC/C;AAA0B,yBAAOx2B,KAAKqrB,WAAAA;cAAAA;AAG1C,mBAAOrrB,KAAKqrB,WAAAA;UACd;UACO,wBAAAoL;AACL,mBAAkB,YAAVz2B,KAAK2E,MAAAA,CAA+B3E,KAAKmc,SAASqa,iBACvB,WAA/Bx2B,KAAKmc,SAASqa,iBACdx2B,KAAKsrB,eAAAA;UACX;UACO,sBAAAuB;AACL,mBAAkB,YAAV7sB,KAAK2E,MAAAA,CAA+B3E,KAAKmc,SAASqa,iBACE,aAAxB,WAA/Bx2B,KAAKmc,SAASqa,kBACfx2B,KAAKg2B,QAAAA;UACX;UACO,0BAAAU;AACL,mBAAkB,YAAV12B,KAAK2E,MAAAA,CAA+B3E,KAAKmc,SAASqa,iBACE,aAAxB,WAA/Bx2B,KAAKmc,SAASqa,mBAC8C,aAAxB,WAA/Bx2B,KAAKmc,SAASqa,kBACpBx2B,KAAKk2B,YAAAA;UACX;UACO,0BAAAtJ;AACL,mBAAkB,YAAV5sB,KAAK2E,MAAAA,CAA+B3E,KAAKmc,SAASqa,iBACE,MAAxB,WAA/Bx2B,KAAKmc,SAASqa,kBACfx2B,KAAKo2B,YAAAA;UACX;UACO,oBAAAO;AACL,mBAAiB,YAAV32B,KAAK4E,KACG,YAAV5E,KAAK2E,KAA4B3E,KAAKmc,SAASoR,iBAAiB,IACjE;UACN;QAAA;AAvHF,QAAAlvB,GAAA,gBAAAC;QA+HA,MAAao3B,GAAAA;UAEX,IAAA,MAAW7wB;AACT,mBAAI7E,KAAK42B,SAAAA,aAEJ52B,KAAK62B,OACL72B,KAAKutB,kBAAkB,KAGrBvtB,KAAK62B;UACd;UACA,IAAA,IAAe5mB,IAAAA;AAAiBjQ,iBAAK62B,OAAO5mB;UAAO;UAEnD,IAAA,iBAAWsd;AAET,mBAAIvtB,KAAK42B,SACA,KAEW,YAAZ52B,KAAK62B,SAAoC;UACnD;UACA,IAAA,eAA0B5mB,IAAAA;AACxBjQ,iBAAK62B,QAAAA,YACL72B,KAAK62B,QAAS5mB,MAAS,KAAM;UAC/B;UAEA,IAAA,iBAAWumB;AACT,mBAAmB,WAAZx2B,KAAK62B;UACd;UACA,IAAA,eAA0B5mB,IAAAA;AACxBjQ,iBAAK62B,QAAAA,WACL72B,KAAK62B,QAAgB,WAAR5mB;UACf;UAGA,IAAA,QAAW6mB;AACT,mBAAO92B,KAAK42B;UACd;UACA,IAAA,MAAiB3mB,IAAAA;AACfjQ,iBAAK42B,SAAS3mB;UAChB;UAEA,YACEpL,KAAc,GACdiyB,KAAgB,GAAA;AA1CV,iBAAAD,OAAe,GAgCf,KAAAD,SAAiB,GAYvB52B,KAAK62B,OAAOhyB,IACZ7E,KAAK42B,SAASE;UAChB;UAEO,QAAAnB;AACL,mBAAO,IAAID,GAAc11B,KAAK62B,MAAM72B,KAAK42B,MAAAA;UAC3C;UAMO,UAAAzI;AACL,mBAA+B,MAAxBnuB,KAAKutB,kBAA0D,MAAhBvtB,KAAK42B;UAC7D;QAAA;AA3DF,QAAAv4B,GAAA,gBAAAE;MAAA,GAAA,KAAA,CAAAH,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,WAAA;ACjIA,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA;QAKA,MAAaoO,UAAiB,EAAAiJ,cAAAA;UAA9B,cAAA;AAAA,kBAAA,GAAA,SAAA,GAQS,KAAAE,UAAU,GACV,KAAAjR,KAAK,GACL,KAAAD,KAAK,GACL,KAAAwX,WAA2B,IAAI,EAAAuZ,iBAC/B,KAAA5f,eAAe;UAoExB;UA9ES,OAAA,aAAoB7F,IAAAA;AACzB,kBAAM8mB,KAAM,IAAIrqB;AAEhB,mBADAqqB,GAAIC,gBAAgB/mB,EAAAA,GACb8mB;UACT;UAQO,aAAA/gB;AACL,mBAAsB,UAAfhW,KAAK6V;UACd;UAEO,WAAAf;AACL,mBAAO9U,KAAK6V,WAAW;UACzB;UAEO,WAAAlB;AACL,mBAAmB,UAAf3U,KAAK6V,UACA7V,KAAK8V,eAEK,UAAf9V,KAAK6V,WACA,GAAAtX,GAAA04B,qBAAmC,UAAfj3B,KAAK6V,OAAAA,IAE3B;UACT;UAOO,UAAAjB;AACL,mBAAQ5U,KAAKgW,WAAAA,IACThW,KAAK8V,aAAakW,WAAWhsB,KAAK8V,aAAa1Q,SAAS,CAAA,IACzC,UAAfpF,KAAK6V;UACX;UAEO,gBAAgB5F,IAAAA;AACrBjQ,iBAAK4E,KAAKqL,GAAM,EAAAinB,oBAAAA,GAChBl3B,KAAK2E,KAAK;AACV,gBAAIwyB,KAAAA;AAEJ,gBAAIlnB,GAAM,EAAAmnB,oBAAAA,EAAsBhyB,SAAS;AACvC+xB,cAAAA,KAAAA;qBAE8C,MAAvClnB,GAAM,EAAAmnB,oBAAAA,EAAsBhyB,QAAc;AACjD,oBAAMV,KAAOuL,GAAM,EAAAmnB,oBAAAA,EAAsBpL,WAAW,CAAA;AAGpD,kBAAI,SAAUtnB,MAAQA,MAAQ,OAAQ;AACpC,sBAAMwuB,KAASjjB,GAAM,EAAAmnB,oBAAAA,EAAsBpL,WAAW,CAAA;AAClD,yBAAUkH,MAAUA,MAAU,QAChClzB,KAAK6V,UAA6B,QAAjBnR,KAAO,SAAkBwuB,KAAS,QAAS,QAAYjjB,GAAM,EAAAonB,qBAAAA,KAA0B,KAGxGF,KAAAA;cAAW;AAIbA,gBAAAA,KAAAA;YAAW;AAIbn3B,mBAAK6V,UAAU5F,GAAM,EAAAmnB,oBAAAA,EAAsBpL,WAAW,CAAA,IAAM/b,GAAM,EAAAonB,qBAAAA,KAA0B;AAE1FF,YAAAA,OACFn3B,KAAK8V,eAAe7F,GAAM,EAAAmnB,oBAAAA,GAC1Bp3B,KAAK6V,UAAU,UAA4B5F,GAAM,EAAAonB,qBAAAA,KAA0B;UAE/E;UAEO,gBAAAphB;AACL,mBAAO,CAACjW,KAAK4E,IAAI5E,KAAK2U,SAAAA,GAAY3U,KAAK8U,SAAAA,GAAY9U,KAAK4U,QAAAA,CAAAA;UAC1D;QAAA;AA/EF,QAAAvW,GAAA,WAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,uBAAAA,GAAA,iBAAAA,GAAA,kBAAAA,GAAA,iBAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,uBAAAA,GAAA,uBAAAA,GAAA,cAAAA,GAAA,eAAAA,GAAA,gBAAA,QCRaA,GAAA2oB,gBAAgB,GAChB3oB,GAAAi5B,eAAe,MAAaj5B,GAAA2oB,iBAAiB,GAC7C3oB,GAAA4oB,cAAc,GAEd5oB,GAAA64B,uBAAuB,GACvB74B,GAAA+4B,uBAAuB,GACvB/4B,GAAAg5B,wBAAwB,GACxBh5B,GAAAk5B,uBAAuB,GAOvBl5B,GAAA+W,iBAAiB,IACjB/W,GAAAm5B,kBAAkB,GAClBn5B,GAAA8G,iBAAiB,GAOjB9G,GAAAo5B,uBAAuB,KACvBp5B,GAAAq5B,wBAAwB,GACxBr5B,GAAAs5B,uBAAuB;MAAA,GAAA,KAAA,CAAAv5B,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,cAAAA,GAAA,gBAAAA,GAAA,gBAAAA,GAAA,sBAAA,QClBpCA,GAAA,sBAAA,SAAoCu5B,IAAAA;AAClC,iBAAIA,KAAY,SACdA,MAAa,OACNtN,OAAOC,aAAiC,SAAnBqN,MAAa,GAAA,IAAgBtN,OAAOC,aAAcqN,KAAY,OAAS,KAAA,KAE9FtN,OAAOC,aAAaqN,EAAAA;QAC7B,GAOAv5B,GAAA,gBAAA,SAA8B0jB,IAAmBzQ,KAAgB,GAAGC,KAAcwQ,GAAK3c,QAAAA;AACrF,cAAIkP,KAAS;AACb,mBAAShW,IAAIgT,IAAOhT,IAAIiT,IAAAA,EAAOjT,GAAG;AAChC,gBAAIkmB,KAAYzC,GAAKzjB,CAAAA;AACjBkmB,YAAAA,KAAY,SAMdA,MAAa,OACblQ,MAAUgW,OAAOC,aAAiC,SAAnB/F,MAAa,GAAA,IAAgB8F,OAAOC,aAAc/F,KAAY,OAAS,KAAA,KAEtGlQ,MAAUgW,OAAOC,aAAa/F,EAAAA;UAAAA;AAGlC,iBAAOlQ;QACT,GAMAjW,GAAA,gBAAA,MAAA;UAAA,cAAA;AACU,iBAAAw5B,WAAmB;UAkE7B;UA7DS,QAAAtxB;AACLvG,iBAAK63B,WAAW;UAClB;UAUO,OAAOC,IAAe7T,IAAAA;AAC3B,kBAAM7e,KAAS0yB,GAAM1yB;AAErB,gBAAA,CAAKA;AACH,qBAAO;AAGT,gBAAIW,KAAO,GACPgyB,IAAW;AAGf,gBAAI/3B,KAAK63B,UAAU;AACjB,oBAAM3E,KAAS4E,GAAM9L,WAAW+L,GAAAA;AAC5B,uBAAU7E,MAAUA,MAAU,QAChCjP,GAAOle,IAAAA,IAAqC,QAA1B/F,KAAK63B,WAAW,SAAkB3E,KAAS,QAAS,SAGtEjP,GAAOle,IAAAA,IAAU/F,KAAK63B,UACtB5T,GAAOle,IAAAA,IAAUmtB,KAEnBlzB,KAAK63B,WAAW;YAAA;AAGlB,qBAASv5B,IAAIy5B,GAAUz5B,IAAI8G,IAAAA,EAAU9G,GAAG;AACtC,oBAAMoG,KAAOozB,GAAM9L,WAAW1tB,CAAAA;AAE9B,kBAAI,SAAUoG,MAAQA,MAAQ,OAA9B;AACE,oBAAA,EAAMpG,KAAK8G;AAET,yBADApF,KAAK63B,WAAWnzB,IACTqB;AAET,sBAAMmtB,IAAS4E,GAAM9L,WAAW1tB,CAAAA;AAC5B,yBAAU40B,KAAUA,KAAU,QAChCjP,GAAOle,IAAAA,IAA4B,QAAjBrB,KAAO,SAAkBwuB,IAAS,QAAS,SAG7DjP,GAAOle,IAAAA,IAAUrB,IACjBuf,GAAOle,IAAAA,IAAUmtB;cAAAA;AAIR,0BAATxuB,OAIJuf,GAAOle,IAAAA,IAAUrB;YAAAA;AAEnB,mBAAOqB;UACT;QAAA,GAMF1H,GAAA,cAAA,MAAA;UAAA,cAAA;AACS,iBAAA25B,UAAsB,IAAI51B,WAAW,CAAA;UAgO9C;UA3NS,QAAAmE;AACLvG,iBAAKg4B,QAAQ1xB,KAAK,CAAA;UACpB;UAUO,OAAOwxB,IAAmB7T,IAAAA;AAC/B,kBAAM7e,KAAS0yB,GAAM1yB;AAErB,gBAAA,CAAKA;AACH,qBAAO;AAGT,gBACI6yB,IACAC,GACAC,GACAC,GAJAryB,IAAO,GAKPye,IAAY,GACZuT,IAAW;AAGf,gBAAI/3B,KAAKg4B,QAAQ,CAAA,GAAI;AACnB,kBAAIK,KAAAA,OACAC,KAAKt4B,KAAKg4B,QAAQ,CAAA;AACtBM,cAAAA,MAAyB,QAAV,MAALA,MAAwB,KAAyB,QAAV,MAALA,MAAwB,KAAO;AAC3E,kBACIC,IADAC,KAAM;AAEV,sBAAQD,KAA4B,KAAtBv4B,KAAKg4B,QAAAA,EAAUQ,EAAAA,MAAgBA,KAAM;AACjDF,gBAAAA,OAAO,GACPA,MAAMC;AAGR,oBAAMniB,KAAsC,QAAV,MAAlBpW,KAAKg4B,QAAQ,CAAA,KAAwB,IAAmC,QAAV,MAAlBh4B,KAAKg4B,QAAQ,CAAA,KAAwB,IAAI,GAC/FS,KAAUriB,KAAOoiB;AACvB,qBAAOT,IAAWU,MAAS;AACzB,oBAAIV,KAAY3yB;AACd,yBAAO;AAGT,oBADAmzB,KAAMT,GAAMC,GAAAA,GACS,QAAV,MAANQ,KAAsB;AAEzBR,uBACAM,KAAAA;AACA;gBAAA;AAGAr4B,qBAAKg4B,QAAQQ,IAAAA,IAASD,IACtBD,OAAO,GACPA,MAAY,KAANC;cAAAA;AAGLF,cAAAA,OAEU,MAATjiB,KACEkiB,KAAK,MAEPP,MAEA9T,GAAOle,GAAAA,IAAUuyB,KAED,MAATliB,KACLkiB,KAAK,QAAWA,MAAM,SAAUA,MAAM,SAAkB,UAAPA,OAGnDrU,GAAOle,GAAAA,IAAUuyB,MAGfA,KAAK,SAAYA,KAAK,YAGxBrU,GAAOle,GAAAA,IAAUuyB,MAIvBt4B,KAAKg4B,QAAQ1xB,KAAK,CAAA;YAAA;AAIpB,kBAAMoyB,IAAWtzB,KAAS;AAC1B,gBAAI9G,IAAIy5B;AACR,mBAAOz5B,IAAI8G,MAAQ;AAejB,qBAAA,EAAA,EAAO9G,IAAIo6B,MACiB,OAApBT,KAAQH,GAAMx5B,CAAAA,MACU,OAAxB45B,IAAQJ,GAAMx5B,IAAI,CAAA,MACM,OAAxB65B,IAAQL,GAAMx5B,IAAI,CAAA,MACM,OAAxB85B,IAAQN,GAAMx5B,IAAI,CAAA;AAExB2lB,gBAAAA,GAAOle,GAAAA,IAAUkyB,IACjBhU,GAAOle,GAAAA,IAAUmyB,GACjBjU,GAAOle,GAAAA,IAAUoyB,GACjBlU,GAAOle,GAAAA,IAAUqyB,GACjB95B,KAAK;AAOP,kBAHA25B,KAAQH,GAAMx5B,GAAAA,GAGV25B,KAAQ;AACVhU,gBAAAA,GAAOle,GAAAA,IAAUkyB;uBAGW,QAAV,MAARA,KAAwB;AAClC,oBAAI35B,KAAK8G;AAEP,yBADApF,KAAKg4B,QAAQ,CAAA,IAAKC,IACXlyB;AAGT,oBADAmyB,IAAQJ,GAAMx5B,GAAAA,GACS,QAAV,MAAR45B,IAAwB;AAE3B55B;AACA;gBAAA;AAGF,oBADAkmB,KAAqB,KAARyT,OAAiB,IAAa,KAARC,GAC/B1T,IAAY,KAAM;AAEpBlmB;AACA;gBAAA;AAEF2lB,gBAAAA,GAAOle,GAAAA,IAAUye;cAAAA,WAGW,QAAV,MAARyT,KAAwB;AAClC,oBAAI35B,KAAK8G;AAEP,yBADApF,KAAKg4B,QAAQ,CAAA,IAAKC,IACXlyB;AAGT,oBADAmyB,IAAQJ,GAAMx5B,GAAAA,GACS,QAAV,MAAR45B,IAAwB;AAE3B55B;AACA;gBAAA;AAEF,oBAAIA,KAAK8G;AAGP,yBAFApF,KAAKg4B,QAAQ,CAAA,IAAKC,IAClBj4B,KAAKg4B,QAAQ,CAAA,IAAKE,GACXnyB;AAGT,oBADAoyB,IAAQL,GAAMx5B,GAAAA,GACS,QAAV,MAAR65B,IAAwB;AAE3B75B;AACA;gBAAA;AAGF,oBADAkmB,KAAqB,KAARyT,OAAiB,MAAc,KAARC,MAAiB,IAAa,KAARC,GACtD3T,IAAY,QAAWA,KAAa,SAAUA,KAAa,SAAyB,UAAdA;AAExE;AAEFP,gBAAAA,GAAOle,GAAAA,IAAUye;cAAAA,WAGW,QAAV,MAARyT,KAAwB;AAClC,oBAAI35B,KAAK8G;AAEP,yBADApF,KAAKg4B,QAAQ,CAAA,IAAKC,IACXlyB;AAGT,oBADAmyB,IAAQJ,GAAMx5B,GAAAA,GACS,QAAV,MAAR45B,IAAwB;AAE3B55B;AACA;gBAAA;AAEF,oBAAIA,KAAK8G;AAGP,yBAFApF,KAAKg4B,QAAQ,CAAA,IAAKC,IAClBj4B,KAAKg4B,QAAQ,CAAA,IAAKE,GACXnyB;AAGT,oBADAoyB,IAAQL,GAAMx5B,GAAAA,GACS,QAAV,MAAR65B,IAAwB;AAE3B75B;AACA;gBAAA;AAEF,oBAAIA,KAAK8G;AAIP,yBAHApF,KAAKg4B,QAAQ,CAAA,IAAKC,IAClBj4B,KAAKg4B,QAAQ,CAAA,IAAKE,GAClBl4B,KAAKg4B,QAAQ,CAAA,IAAKG,GACXpyB;AAGT,oBADAqyB,IAAQN,GAAMx5B,GAAAA,GACS,QAAV,MAAR85B,IAAwB;AAE3B95B;AACA;gBAAA;AAGF,oBADAkmB,KAAqB,IAARyT,OAAiB,MAAc,KAARC,MAAiB,MAAc,KAARC,MAAiB,IAAa,KAARC,GAC7E5T,IAAY,SAAYA,IAAY;AAEtC;AAEFP,gBAAAA,GAAOle,GAAAA,IAAUye;cAAAA;YAAAA;AAKrB,mBAAOze;UACT;QAAA;MAAA,GAAA,KAAA,SAAA3H,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAC,IAAAC,KAAA,UAAA,QAAAC,KAAAD,KAAA,IAAAJ,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAAG,KAAA,QAAA,SAAAN,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAI,KAAAP,GAAA,SAAA,GAAAO,MAAA,GAAAA;AAAA,eAAAH,KAAAJ,GAAAO,EAAA,OAAAD,MAAAD,KAAA,IAAAD,GAAAE,EAAA,IAAAD,KAAA,IAAAD,GAAAH,IAAAC,IAAAI,EAAA,IAAAF,GAAAH,IAAAC,EAAA,MAAAI;AAAA,iBAAAD,KAAA,KAAAC,MAAA,OAAA,eAAAL,IAAAC,IAAAI,EAAA,GAAAA;QAAA,GAAA,IAAA,QAAA,KAAA,WAAA,SAAAN,IAAAC,IAAA;AAAA,iBAAA,SAAAC,IAAAC,IAAA;AAAA,YAAAF,GAAAC,IAAAC,IAAAH,EAAA;UAAA;QAAA;AAAA,eAAA,eAAAC,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,YAAAA,GAAA,iBAAAA,GAAA,aAAA;ACnVF,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA,GAgBMq6B,IAAwD,EAC5DC,OAAO,EAAAC,aAAaC,OACpBC,OAAO,EAAAF,aAAaG,OACpBC,MAAM,EAAAJ,aAAaK,MACnB7pB,MAAM,EAAAwpB,aAAaM,MACnBxiB,OAAO,EAAAkiB,aAAaO,OACpBC,KAAK,EAAAR,aAAaS,IAAAA;AAKb,YAiEHC,GAjESC,IAAUn7B,GAAA,aAAhB,cAAyB,EAAAgB,WAAAA;UAI9B,IAAA,WAAWo6B;AAA2B,mBAAOz5B,KAAK05B;UAAW;UAE7D,YACmBt7B,IAAA;AAEjBqB,kBAAAA,GAFkC,KAAAyM,kBAAAA,IAJ5B,KAAAwtB,YAA0B,EAAAb,aAAaS,KAO7Ct5B,KAAK25B,gBAAAA,GACL35B,KAAKc,SAASd,KAAKkM,gBAAgB0tB,uBAAuB,YAAY,MAAM55B,KAAK25B,gBAAAA,CAAAA,CAAAA,GAGjFJ,IAAcv5B;UAChB;UAEQ,kBAAA25B;AACN35B,iBAAK05B,YAAYf,EAAqB34B,KAAKkM,gBAAgB4F,WAAW2nB,QAAAA;UACxE;UAEQ,wBAAwBI,IAAAA;AAC9B,qBAASv7B,KAAI,GAAGA,KAAIu7B,GAAez0B,QAAQ9G;AACR,4BAAA,OAAtBu7B,GAAev7B,EAAAA,MACxBu7B,GAAev7B,EAAAA,IAAKu7B,GAAev7B,EAAAA,EAAAA;UAGzC;UAEQ,KAAK8X,IAAe0jB,IAAiBD,IAAAA;AAC3C75B,iBAAK+5B,wBAAwBF,EAAAA,GAC7BzjB,GAAKkc,KAAKtjB,UAAUhP,KAAKkM,gBAAgB2G,QAAQmnB,SAAS,KAjC3C,gBAiC8DF,IAAAA,GAAYD,EAAAA;UAC3F;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B75B,iBAAK05B,aAAa,EAAAb,aAAaC,SACjC94B,KAAKi6B,KAAyF,UAApF17B,KAAmC,UAAnCD,KAAA0B,KAAKkM,gBAAgB2G,QAAQmnB,WAAAA,WAAM17B,KAAA,SAAAA,GAAEs6B,MAAMhE,KAAK50B,KAAKkM,gBAAgB2G,QAAQmnB,MAAAA,MAAAA,WAAOz7B,KAAAA,KAAIyQ,QAAQC,KAAK6qB,IAASD,EAAAA;UAE5H;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B75B,iBAAK05B,aAAa,EAAAb,aAAaG,SACjCh5B,KAAKi6B,KAAyF,UAApF17B,KAAmC,UAAnCD,KAAA0B,KAAKkM,gBAAgB2G,QAAQmnB,WAAAA,WAAM17B,KAAA,SAAAA,GAAEy6B,MAAMnE,KAAK50B,KAAKkM,gBAAgB2G,QAAQmnB,MAAAA,MAAAA,WAAOz7B,KAAAA,KAAIyQ,QAAQC,KAAK6qB,IAASD,EAAAA;UAE5H;UAEO,KAAKC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC1B75B,iBAAK05B,aAAa,EAAAb,aAAaK,QACjCl5B,KAAKi6B,KAAwF,UAAnF17B,KAAmC,UAAnCD,KAAA0B,KAAKkM,gBAAgB2G,QAAQmnB,WAAAA,WAAM17B,KAAA,SAAAA,GAAE26B,KAAKrE,KAAK50B,KAAKkM,gBAAgB2G,QAAQmnB,MAAAA,MAAAA,WAAOz7B,KAAAA,KAAIyQ,QAAQiqB,MAAMa,IAASD,EAAAA;UAE5H;UAEO,KAAKC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC1B75B,iBAAK05B,aAAa,EAAAb,aAAaM,QACjCn5B,KAAKi6B,KAAwF,UAAnF17B,KAAmC,UAAnCD,KAAA0B,KAAKkM,gBAAgB2G,QAAQmnB,WAAAA,WAAM17B,KAAA,SAAAA,GAAE+Q,KAAKulB,KAAK50B,KAAKkM,gBAAgB2G,QAAQmnB,MAAAA,MAAAA,WAAOz7B,KAAAA,KAAIyQ,QAAQK,MAAMyqB,IAASD,EAAAA;UAE5H;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B75B,iBAAK05B,aAAa,EAAAb,aAAaO,SACjCp5B,KAAKi6B,KAAyF,UAApF17B,KAAmC,UAAnCD,KAAA0B,KAAKkM,gBAAgB2G,QAAQmnB,WAAAA,WAAM17B,KAAA,SAAAA,GAAEqY,MAAMie,KAAK50B,KAAKkM,gBAAgB2G,QAAQmnB,MAAAA,MAAAA,WAAOz7B,KAAAA,KAAIyQ,QAAQ2H,OAAOmjB,IAASD,EAAAA;UAE9H;QAAA;AAAA,QAAAx7B,GAAA,aA9DWm7B,IAAUj7B,GAAA,CAOlB,EAAA,GAAA,EAAA27B,eAAAA,CAAAA,GAPQV,CAAAA,GAkEbn7B,GAAA,iBAAA,SAA+B27B,IAAAA;AAC7BT,cAAcS;QAChB,GAKA37B,GAAA,YAAA,SAA0B87B,IAAcxR,IAAayR,IAAAA;AACnD,cAAgC,cAAA,OAArBA,GAAWnqB;AACpB,kBAAM,IAAInB,MAAM,eAAA;AAElB,gBACMurB,KAAKD,GAAWnqB;AACtBmqB,UAAAA,GAAgB,QAAI,YAAavX,IAAAA;AAE/B,gBAAI0W,EAAYE,aAAa,EAAAZ,aAAaC;AACxC,qBAAOuB,GAAGC,MAAMt6B,MAAM6iB,EAAAA;AAGxB0W,cAAYX,MAAM,iBAAiByB,GAAGE,IAAAA,IAAQ1X,GAAKhB,IAAIzjB,CAAAA,OAAKo8B,KAAKC,UAAUr8B,EAAAA,CAAAA,EAAI2uB,KAAK,IAAA,CAAA,GAAA;AACpF,kBAAMzY,KAAS+lB,GAAGC,MAAMt6B,MAAM6iB,EAAAA;AAE9B,mBADA0W,EAAYX,MAAM,iBAAiByB,GAAGE,IAAAA,WAAejmB,EAAAA,GAC9CA;UACT;QACF;MAAA,GAAA,KAAA,CAAAlW,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAAA,GAAA,yBAAAA,GAAA,kBAAA;AC9GA,cAAMq8B,KAAY,aACZC,KAAkB;AAEX,QAAAt8B,GAAAu8B,kBAAwD,oBAAIzZ,OAEzE9iB,GAAA,yBAAA,SAAuCw8B,IAAAA;AACrC,iBAAOA,GAAKF,EAAAA,KAAoB,CAAA;QAClC,GAEAt8B,GAAA,kBAAA,SAAmCuZ,IAAAA;AACjC,cAAIvZ,GAAAu8B,gBAAgBE,IAAIljB,EAAAA;AACtB,mBAAOvZ,GAAAu8B,gBAAgBxmB,IAAIwD,EAAAA;AAG7B,gBAAMmjB,IAAiB,SAAU9W,IAAkB0E,IAAa5K,GAAAA;AAC9D,gBAAyB,MAArBid,UAAU51B;AACZ,oBAAM,IAAI0J,MAAM,kEAAA;AAAA,aAYtB,SAAgC8I,IAAcqM,IAAkBlG,IAAAA;AACzDkG,cAAAA,GAAeyW,EAAAA,MAAezW,KAChCA,GAAe0W,EAAAA,EAAiBhd,KAAK,EAAE/F,IAAAA,IAAImG,OAAAA,GAAAA,CAAAA,KAE3CkG,GAAe0W,EAAAA,IAAmB,CAAC,EAAE/iB,IAAAA,IAAImG,OAAAA,GAAAA,CAAAA,GACzCkG,GAAeyW,EAAAA,IAAazW;YAEjC,EAhB2B8W,GAAW9W,IAAQlG,CAAAA;UAC5C;AAKA,iBAHAgd,EAAUxiB,WAAW,MAAMX,IAE3BvZ,GAAAu8B,gBAAgBxzB,IAAIwQ,IAAImjB,CAAAA,GACjBA;QACT;MAAA,GAAA,IAAA,CAAA38B,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,qBAAAA,GAAA,kBAAAA,GAAA,kBAAAA,GAAA,kBAAAA,GAAA,cAAAA,GAAA,eAAAA,GAAA,wBAAAA,GAAA,kBAAAA,GAAA,eAAAA,GAAA,oBAAAA,GAAA,iBAAA;AC/BA,cAAAE,KAAAD,GAAA,GAAA;AAuIA,YAAYu6B;AApIC,QAAAx6B,GAAA48B,kBAAiB,GAAA18B,GAAA28B,iBAAgC,eAAA,GAiBjD78B,GAAA88B,qBAAoB,GAAA58B,GAAA28B,iBAAmC,kBAAA,GAgCvD78B,GAAA+8B,gBAAe,GAAA78B,GAAA28B,iBAA8B,aAAA,GAsC7C78B,GAAAg9B,mBAAkB,GAAA98B,GAAA28B,iBAAiC,gBAAA,GAoCnD78B,GAAAi9B,yBAAwB,GAAA/8B,GAAA28B,iBAAuC,sBAAA,GAS5E,SAAYrC,IAAAA;AACV,UAAAz6B,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,OAAA,CAAA,IAAA,QACAA,GAAAA,GAAA,OAAA,CAAA,IAAA,QACAA,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,MAAA,CAAA,IAAA;QACD,EAPWy6B,MAAYx6B,GAAA,eAAZw6B,IAAY,CAAA,EAAA,GASXx6B,GAAAk9B,eAAc,GAAAh9B,GAAA28B,iBAA6B,YAAA,GAa3C78B,GAAA67B,mBAAkB,GAAA37B,GAAA28B,iBAAiC,gBAAA,GAqHnD78B,GAAAm9B,mBAAkB,GAAAj9B,GAAA28B,iBAAiC,gBAAA,GAgBnD78B,GAAAo9B,mBAAkB,GAAAl9B,GAAA28B,iBAAiC,gBAAA,GAwBnD78B,GAAAq9B,sBAAqB,GAAAn9B,GAAA28B,iBAAoC,mBAAA;MAAA,EAAA,GCjUlES,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC;AACH,iBAAOA,EAAa/9B;AAGrB,YAAIC,IAAS29B,EAAyBE,EAAAA,IAAY,EAGjD99B,SAAS,CAAC,EAAA;AAOX,eAHAg+B,EAAoBF,EAAAA,EAAUvJ,KAAKt0B,EAAOD,SAASC,GAAQA,EAAOD,SAAS69B,CAAAA,GAGpE59B,EAAOD;MACf;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAAK,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAA;ACfA,cAAAC,KAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA,GAGA,IAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA;QAEA,MAAa29B,UAAmB,EAAA38B,WAAAA;UAa9B,YACU48B,IAAAA;AAER,gBAAI,EAAAxI,aAAY,GAAA,EAAAyI,kBAAAA,IAAqB;AACnC,oBAAM,IAAIptB,MAAM,iDAAA;AAElBrP,kBAAAA,GALQ,KAAAw8B,yBAAAA,IAVO,KAAApvB,wBAAwB7M,KAAKc,SAAS,IAAIzC,GAAAyO,cAAAA,GAC3C,KAAAC,uBAAuB/M,KAAK6M,sBAAsBG,OACjD,KAAAC,2BAA2BjN,KAAKc,SAAS,IAAIzC,GAAAyO,cAAAA,GAC9C,KAAAI,0BAA0BlN,KAAKiN,yBAAyBD,OACvD,KAAAG,8BAA8BnN,KAAKc,SAAS,IAAIzC,GAAAyO,cAAAA,GACjD,KAAAM,6BAA6BpN,KAAKmN,4BAA4BH,OAC7D,KAAAO,iBAAiBvN,KAAKc,SAAS,IAAIzC,GAAAyO,cAAAA,GACpC,KAAAU,gBAAgBxN,KAAKuN,eAAeP;UASpD;UAEO,SAASxG,IAAAA;AACd,kBAAMoX,KAAQpX,GAAiBoH;AAC/B,gBAAA,CAAKpH,GAASmd;AAEZ,qBAAA,KADA3jB,KAAKc,SAAS8c,GAAKue,WAAW,MAAMn8B,KAAKo8B,SAAS51B,EAAAA,CAAAA,CAAAA;AAIpDxG,iBAAKV,YAAYkH;AACjB,kBAAM61B,KAA4Bze,GAAKye,aACjC7hB,KAAkCoD,GAAKpD,gBAEvC8hB,KAAa1e,IACb2e,IAAgCD,GAAWE,gBAC3CC,IAAkDH,GAAWzwB,yBAC7D6wB,IAAoCJ,GAAWxwB,kBAC/CyO,IAA0C+hB,GAAWvwB,qBACrD4wB,IAAwCL,GAAWrwB,oBACnD2wB,IAA0BN,GAAWO,aACrCpiB,IAA8B6hB,GAAW7zB;AAAAA,aAI/C,GAAA,EAAAq0B,gBAAeF,CAAAA,GAEf58B,KAAK+8B,YAAY/8B,KAAKc,SAAS,IAAI,EAAA8K,cACjCpF,IACAi2B,GACAC,GACAniB,GACA8hB,IACAM,GACAniB,IACAC,GACAza,KAAKi8B,sBAAAA,CAAAA,GAEPj8B,KAAKc,UAAS,GAAAzC,GAAA2T,cAAahS,KAAK+8B,UAAUvvB,eAAexN,KAAKuN,cAAAA,CAAAA,GAC9DvN,KAAKc,UAAS,GAAAzC,GAAA2T,cAAahS,KAAK+8B,UAAUhwB,sBAAsB/M,KAAK6M,qBAAAA,CAAAA,GACrE7M,KAAKc,UAAS,GAAAzC,GAAA2T,cAAahS,KAAK+8B,UAAU7vB,yBAAyBlN,KAAKiN,wBAAAA,CAAAA,GACxEjN,KAAKc,UAAS,GAAAzC,GAAA2T,cAAahS,KAAK+8B,UAAU3vB,4BAA4BpN,KAAKmN,2BAAAA,CAAAA,GAC3EovB,EAAcS,YAAYh9B,KAAK+8B,SAAAA,GAE/B/8B,KAAKc,UAAS,GAAA,EAAAC,cAAa,MAAA;AACzB,oBAAMw7B,KAAiCv8B,KAAKV,UAAkBsO,MAAM4uB;AACpED,cAAAA,GAAcS,YAAah9B,KAAKV,UAAkBsO,MAAMqvB,gBAAAA,CAAAA,GACxDV,GAAcl4B,aAAamC,GAAStB,MAAMsB,GAASE,IAAAA;YAAK,CAAA,CAAA;UAE5D;UAEA,IAAA,eAAW+J;AAAAA,gBAAAA;AACT,mBAAqB,UAAdrS,KAAA4B,KAAK+8B,cAAAA,WAAS3+B,KAAA,SAAAA,GAAEqS;UACzB;UAEO,oBAAA2B;AAAAA,gBAAAA;AACS,sBAAdhU,KAAA4B,KAAK+8B,cAAAA,WAAS3+B,MAAAA,GAAEgU,kBAAAA;UAClB;QAAA;AA5EF,QAAAhU,GAAA,aAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "e", "t", "i", "s", "r", "o", "n", "a", "INDICES_PER_CELL", "BYTES_PER_CELL", "Float32Array", "BYTES_PER_ELEMENT", "$glyph", "$i", "$leftCellPadding", "$clippedPixels", "Gly<PERSON><PERSON><PERSON><PERSON>", "Disposable", "_terminal", "_gl", "_dimensions", "super", "_activeBuffer", "_vertices", "count", "attributes", "attributesBuffers", "gl", "this", "TextureAtlas", "maxAtlasPages", "Math", "min", "throwIfFalsy", "getParameter", "MAX_TEXTURE_IMAGE_UNITS", "maxTextureSize", "MAX_TEXTURE_SIZE", "_program", "createProgram", "maxFragmentShaderTextureUnits", "textureConditionals", "register", "toDisposable", "deleteProgram", "_projectionLocation", "getUniformLocation", "_resolutionLocation", "_textureLocation", "_vertexArrayObject", "createVertexArray", "bindVertexArray", "unitQuadVertices", "unitQuadVerticesBuffer", "createBuffer", "deleteBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "enableVertexAttribArray", "vertexAttribPointer", "FLOAT", "unitQuadElementIndices", "Uint8Array", "elementIndicesBuffer", "ELEMENT_ARRAY_BUFFER", "_attributes<PERSON>uffer", "vertexAttribDivisor", "useProgram", "textureUnits", "Int32Array", "uniform1iv", "uniformMatrix4fv", "PROJECTION_MATRIX", "_atlasTextures", "glTexture", "GLTexture", "createTexture", "deleteTexture", "texture", "activeTexture", "TEXTURE0", "bindTexture", "TEXTURE_2D", "texParameteri", "TEXTURE_WRAP_S", "CLAMP_TO_EDGE", "TEXTURE_WRAP_T", "texImage2D", "RGBA", "UNSIGNED_BYTE", "enable", "BLEND", "blendFunc", "SRC_ALPHA", "ONE_MINUS_SRC_ALPHA", "handleResize", "beginFrame", "_atlas", "x", "y", "code", "bg", "fg", "ext", "chars", "lastBg", "_updateCell", "array", "cols", "NULL_CELL_CODE", "length", "getRasterizedGlyphCombinedChar", "getRasterizedGlyph", "floor", "device", "cell", "width", "char", "offset", "left", "top", "size", "canvas", "height", "texturePage", "texturePositionClipSpace", "pages", "sizeClipSpace", "fill", "clear", "terminal", "newCount", "rows", "viewport", "uniform2f", "renderModel", "active<PERSON>uffer", "bufferLength", "lineLengths", "si", "sub", "subarray", "set", "STREAM_DRAW", "version", "_bindAtlasPageTexture", "drawElementsInstanced", "TRIANGLE_STRIP", "atlas", "generateMipmap", "dimensions", "traceCall", "BYTES_PER_RECTANGLE", "Vertices", "constructor", "$rgba", "$x1", "$y1", "$r", "$g", "$b", "$a", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_themeService", "_verticesCursor", "_updateCachedColors", "colors", "onChangeColors", "_updateViewportRectangle", "renderBackgrounds", "_renderVertices", "renderCursor", "vertices", "DYNAMIC_DRAW", "_bgFloat", "_colorToFloat32Array", "background", "_cursorFloat", "cursor", "_addRectangleFloat", "model", "currentStartX", "currentBg", "currentFg", "currentInverse", "modelIndex", "inverse", "rectangleCount", "RENDER_MODEL_INDICIES_PER_CELL", "cells", "RENDER_MODEL_BG_OFFSET", "RENDER_MODEL_FG_OFFSET", "_updateRectangle", "style", "dpr", "cursor<PERSON><PERSON><PERSON>", "startX", "endX", "ansi", "rgba", "foreground", "expandFloat32Array", "_addRectangle", "x1", "y1", "g", "b", "color", "RENDER_MODEL_EXT_OFFSET", "COMBINED_CHAR_BIT_MASK", "Uint32Array", "selection", "createSelectionRenderModel", "indexCount", "Webgl<PERSON><PERSON><PERSON>", "_characterJoinerService", "_charSizeService", "_coreBrowserService", "_coreService", "_decorationService", "_optionsService", "preserveDrawingBuffer", "_cursorBlinkStateManager", "MutableDisposable", "_charAtlasDisposable", "_model", "RenderModel", "_workCell", "CellData", "_rectangle<PERSON><PERSON>er", "_glyph<PERSON><PERSON><PERSON>", "_onChangeTextureAtlas", "EventEmitter", "onChangeTextureAtlas", "event", "_onAddTextureAtlasCanvas", "onAddTextureAtlasCanvas", "_onRemoveTextureAtlasCanvas", "onRemoveTextureAtlasCanvas", "_onRequestRedraw", "onRequestRedraw", "_onContextLoss", "onContextLoss", "_handleColorChange", "_cellColorResolver", "CellColorResolver", "_core", "_renderLayers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "screenElement", "linkifier2", "createRenderDimensions", "_devicePixelRatio", "_updateDimensions", "_updateCursorBlink", "onOptionChange", "_handleOptionsChanged", "_canvas", "document", "createElement", "contextAttributes", "antialias", "depth", "getContext", "Error", "addDisposableDomListener", "console", "log", "preventDefault", "_contextRestorationTimeout", "setTimeout", "warn", "fire", "clearTimeout", "removeTerminalFromCache", "_initializeWebGLState", "_requestRedrawViewport", "observeDevicePixelDimensions", "window", "w", "h", "_setCanvasDevicePixelDimensions", "append<PERSON><PERSON><PERSON>", "value", "_isAttached", "body", "contains", "l", "dispose", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "textureAtlas", "_char<PERSON><PERSON>as", "_refreshCharAtlas", "_clearModel", "handleDevicePixelRatioChange", "resize", "css", "setDimensions", "handleCharSizeChanged", "handleBlur", "pause", "handleFocus", "resume", "start", "end", "columnSelectMode", "handleSelectionChanged", "update", "handleCursorMove", "restartBlinkAnimation", "acquireTextureAtlas", "rawOptions", "getDisposeArrayDisposable", "forwardEvent", "warmUp", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearTextureAtlas", "clearTexture", "reset", "handler", "joinerId", "handleGridChanged", "_updateModel", "render", "isCursorVisible", "options", "cursorBlink", "CursorBlinkStateManager", "_requestRedrawCursor", "row", "line", "joinedRanges", "isJoined", "lastCharX", "range", "j", "clamp", "cursorY", "buffer", "active", "baseY", "cursorX", "lastCursorX", "isCursorInitialized", "isCursorHidden", "modelUpdated", "ydisp", "lines", "get", "getJoinedCharacters", "result", "loadCell", "shift", "JoinedCellData", "translateToString", "getChars", "getCode", "resolve", "getWidth", "isFocused", "cursorStyle", "cursorInactiveStyle", "cursorAccent", "updateCell", "NULL_CELL_CHAR", "updateBackgrounds", "updateCursor", "ceil", "lineHeight", "round", "letterSpacing", "AttributeData", "firstCell", "content", "combinedData", "_width", "isCombined", "getAsCharData", "max", "createShader", "type", "source", "shader", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "error", "getShaderInfoLog", "deleteShader", "vertexSource", "fragmentSource", "program", "<PERSON><PERSON><PERSON><PERSON>", "VERTEX_SHADER", "FRAGMENT_SHADER", "linkProgram", "getProgramParameter", "LINK_STATUS", "getProgramInfoLog", "<PERSON><PERSON><PERSON><PERSON>", "newArray", "BaseRender<PERSON><PERSON>er", "_container", "id", "zIndex", "_alpha", "_deviceCharWidth", "_deviceCharHeight", "_device<PERSON>ell<PERSON>idth", "_deviceCellHeight", "_deviceCharLeft", "_deviceCharTop", "classList", "add", "toString", "_initCanvas", "remove", "_ctx", "alpha", "_clearAll", "startRow", "endRow", "oldCanvas", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "colorSet", "dim", "fillRect", "clearRect", "fillStyle", "font", "_getFont", "textBaseline", "TEXT_BASELINE", "_clipCell", "fillText", "beginPath", "rect", "clip", "isBold", "isItalic", "fontWeightBold", "fontWeight", "fontSize", "fontFamily", "container", "coreBrowserService", "optionsService", "themeService", "onShowLinkUnderline", "_handleShowLinkUnderline", "onHideLinkUnderline", "_handleHideLinkUnderline", "_state", "_clearCurrentLink", "_clearCells", "middleRowCount", "y2", "x2", "undefined", "INVERTED_DEFAULT_COLOR", "is256Color", "_fillBottomLineAtCells", "node", "addEventListener", "disposed", "removeEventListener", "$colors", "$fg", "$bg", "$hasFg", "$hasBg", "$isSelected", "_selectionRenderModel", "extended", "forEachDecorationAtCell", "d", "backgroundColorRGB", "foregroundColorRGB", "isCellSelected", "selectionBackgroundOpaque", "selectionInactiveBackgroundOpaque", "selectionForeground", "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceCellWidth", "deviceCellHeight", "deviceCharWidth", "deviceCharHeight", "devicePixelRatio", "newConfig", "generateConfig", "entry", "ownedByIndex", "ownedBy", "indexOf", "configEquals", "config", "splice", "push", "core", "newEntry", "unicodeService", "index", "clonedColors", "NULL_COLOR", "selectionBackgroundTransparent", "selectionInactiveBackgroundTransparent", "slice", "contrastCache", "halfContrastCache", "customGlyphs", "allowTransparency", "drawBoldTextInBrightColors", "minimumContrastRatio", "colorCode", "DIM_OPACITY", "isFirefox", "isLegacyEdge", "_renderCallback", "_restartInterval", "isPaused", "_blinkStartTimeout", "_blinkInterval", "clearInterval", "_animationFrame", "cancelAnimationFrame", "_animationTimeRestarted", "Date", "now", "requestAnimationFrame", "timeToStart", "BLINK_INTERVAL", "time", "setInterval", "blockElementDefinitions", "patternCharacterDefinitions", "boxDrawingDefinitions", "xp", "yp", "powerlineDefinitions", "rightPadding", "leftPadding", "ctx", "c", "xOffset", "yOffset", "blockElementDefinition", "charDefinition", "box", "xEighth", "yEighth", "patternDefinition", "patternSet", "cachedPatterns", "Map", "pattern", "tmpCanvas", "tmpCtx", "imageData", "ImageData", "startsWith", "parseInt", "substring", "split", "map", "parseFloat", "data", "putImageData", "createPattern", "boxDrawingDefinition", "strokeStyle", "instructions", "Object", "entries", "actualInstructions", "lineWidth", "Number", "instruction", "f", "svgToCanvasInstructionMap", "args", "<PERSON><PERSON><PERSON><PERSON>", "stroke", "closePath", "powerlineDefinition", "clipRegion", "Path2D", "cssLineWidth", "bezierCurveTo", "lineTo", "moveTo", "cellWidth", "cellHeight", "doClamp", "element", "parentWindow", "callback", "observer", "ResizeObserver", "find", "target", "disconnect", "devicePixelContentBoxSize", "inlineSize", "blockSize", "observe", "isPowerlineGlyph", "codepoint", "SelectionRenderModel", "hasSelection", "viewportStartRow", "viewportEndRow", "viewportCappedStartRow", "viewportCappedEndRow", "startCol", "endCol", "selectionStart", "selectionEnd", "viewportY", "NULL_RASTERIZED_GLYPH", "texturePosition", "_pages", "_document", "_config", "_unicodeService", "_didWarmUp", "_cacheMap", "FourKeyMap", "_cacheMapCombined", "_activePages", "_workBoundingBox", "bottom", "right", "_workAttributeData", "_textureSize", "_requestClearModel", "_createNewPage", "_tmpCanvas", "createCanvas", "TMP_CANVAS_GLYPH_PADDING", "_tmpCtx", "willReadFrequently", "page", "_doWarmUp", "queue", "IdleTaskQueue", "enqueue", "DEFAULT_COLOR", "DEFAULT_EXT", "rasterizedGlyph", "_drawTo<PERSON>ache", "currentRow", "pagesBySize", "filter", "sort", "percentageUsed", "sameSizeI", "mergingPages", "sortedMergingPagesIndexes", "glyphs", "mergedPageIndex", "mergedPage", "_mergePages", "_deletePage", "newPage", "AtlasPage", "mergedSize", "p", "drawImage", "pageIndex", "adjustingPage", "restrictToCellHeight", "_getFromCacheMap", "cacheMap", "key", "idx", "bgColorMode", "bgColor", "_getColorFromAnsiIndex", "arr", "toColorRGB", "toColor", "opaque", "fgColorMode", "fgColor", "bold", "excludeFromContrastRatioDemands", "minimumContrastColor", "_getMinimumContrastColor", "multiplyOpacity", "cache", "_getContrastCache", "adjustedColor", "getColor", "bgRgba", "_resolveBackgroundRgba", "fgRgba", "_resolveForegroundRgba", "ensureContrastRatio", "setColor", "codeOrChars", "String", "fromCharCode", "<PERSON><PERSON><PERSON><PERSON>", "allowedHeight", "save", "isInvisible", "isInverse", "isDim", "italic", "underline", "isUnderline", "strikethrough", "isStrikethrough", "overline", "isOverline", "getFgColor", "getFgColorMode", "getBgColor", "getBgColorMode", "temp", "temp2", "backgroundColor", "_getBackgroundColor", "globalCompositeOperation", "fontStyle", "powerlineGlyph", "charCodeAt", "restrictedPowerlineGlyph", "isRestrictedPowerlineGlyph", "foregroundColor", "_getForegroundColor", "padding", "customGlyph", "tryDrawCustomChar", "ch<PERSON><PERSON><PERSON>", "enableClearThresholdCheck", "wcwidth", "getStringCell<PERSON>th", "isUnderlineColorDefault", "isUnderlineColorRGB", "getUnderlineColor", "join", "xLeft", "yTop", "yMid", "yBot", "xChLeft", "xChRight", "xChMid", "underlineStyle", "y<PERSON>urlyBot", "yCurlyTop", "setLineDash", "restore", "metrics", "measureText", "actualBoundingBoxDescent", "strokeText", "isBeyondCellBounds", "clearColor", "getImageData", "isEmpty", "_findGlyphBoundingBox", "activePage", "activeRow", "fixedRows", "wasPageAndRowFound", "candidate<PERSON>age", "addGlyph", "boundingBox", "restrictedGlyph", "found", "alphaOffset", "_usedPixels", "_glyphs", "glyph", "sourcePages", "enableThresholdCheck", "fgR", "fgG", "fgB", "threshold", "abs", "channels", "rgb", "toPaddedHex", "contrastRatio", "l1", "l2", "to<PERSON>s", "toRgba", "opacity", "toChannels", "blend", "bgR", "bgG", "bgB", "isOpaque", "ratio", "rgbaColor", "factor", "$ctx", "$litmusColor", "isNode", "createLinearGradient", "match", "repeat", "rgbaMatch", "relativeLuminance2", "rs", "gs", "bs", "pow", "relativeLuminance", "reduceLuminance", "cr", "increaseLuminance", "bgL", "fgL", "resultA", "resultARatio", "resultB", "_listeners", "_disposed", "_event", "listener", "arg1", "arg2", "call", "clearListeners", "from", "to", "dispose<PERSON><PERSON><PERSON>", "disposables", "_disposables", "_isDisposed", "_value", "TwoKeyMap", "_data", "first", "second", "third", "fourth", "navigator", "userAgent", "platform", "includes", "<PERSON><PERSON><PERSON><PERSON>", "test", "majorVersion", "isMac", "isIpad", "isIphone", "isWindows", "isLinux", "isChromeOS", "TaskQueue", "_tasks", "_i", "task", "_start", "flush", "_idleCallback", "_cancelCallback", "_requestCallback", "_process", "bind", "deadline", "taskDuration", "longestTask", "lastDeadlineRemaining", "timeRemaining", "deadlineRemaining", "PriorityTaskQueue", "_createDeadline", "identifier", "duration", "requestIdleCallback", "cancelIdleCallback", "_queue", "ExtendedAttrs", "clone", "newObj", "hasExtendedAttrs", "isBlink", "isProtected", "isFgRGB", "isBgRGB", "isFgPalette", "isBgPalette", "isFgDefault", "isBgDefault", "isAttributeDefault", "updateExtended", "underlineColor", "getUnderlineColorMode", "isUnderlineColorPalette", "getUnderlineStyle", "_urlId", "_ext", "urlId", "obj", "setFromCharData", "stringFromCodePoint", "CHAR_DATA_ATTR_INDEX", "combined", "CHAR_DATA_CHAR_INDEX", "CHAR_DATA_WIDTH_INDEX", "DEFAULT_ATTR", "CHAR_DATA_CODE_INDEX", "NULL_CELL_WIDTH", "WHITESPACE_CELL_CHAR", "WHITESPACE_CELL_WIDTH", "WHITESPACE_CELL_CODE", "codePoint", "_interim", "input", "startPos", "interim", "byte1", "byte2", "byte3", "byte4", "discardInterim", "cp", "tmp", "pos", "missing", "fourStop", "optionsKeyToLogLevel", "trace", "LogLevelEnum", "TRACE", "debug", "DEBUG", "info", "INFO", "WARN", "ERROR", "off", "OFF", "<PERSON><PERSON><PERSON><PERSON>", "LogService", "logLevel", "_logLevel", "_updateLogLevel", "onSpecificOptionChange", "optionalParams", "message", "_evalLazyOptionalParams", "logger", "_log", "IOptionsService", "_target", "descriptor", "fn", "apply", "name", "JSON", "stringify", "DI_TARGET", "DI_DEPENDENCIES", "serviceRegistry", "ctor", "has", "decorator", "arguments", "IBufferService", "createDecorator", "ICoreMouseService", "ICoreService", "ICharsetService", "IInstantiationService", "ILogService", "IOscLinkService", "IUnicodeService", "IDecorationService", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "WebglAddon", "_preserveDrawingBuffer", "getSafariVersion", "onWillOpen", "activate", "coreService", "unsafeCore", "renderService", "_renderService", "characterJoinerService", "charSizeService", "decorationService", "logService", "_logService", "<PERSON><PERSON><PERSON><PERSON>og<PERSON>", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_create<PERSON><PERSON><PERSON>"]}