// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gmodel

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type Browser struct {
	URL           string `json:"url"`
	ScreenshotURL string `json:"screenshotUrl"`
}

type Flow struct {
	ID       uint       `json:"id"`
	Name     string     `json:"name"`
	Tasks    []*Task    `json:"tasks"`
	Terminal *Terminal  `json:"terminal"`
	Browser  *Browser   `json:"browser"`
	Status   FlowStatus `json:"status"`
	Model    *Model     `json:"model"`
}

type Log struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}

type Model struct {
	Provider string `json:"provider"`
	ID       string `json:"id"`
}

type Mutation struct {
}

type Query struct {
}

type Subscription struct {
}

type Task struct {
	ID        uint       `json:"id"`
	Message   string     `json:"message"`
	CreatedAt time.Time  `json:"createdAt"`
	Type      TaskType   `json:"type"`
	Status    TaskStatus `json:"status"`
	Args      string     `json:"args"`
	Results   string     `json:"results"`
}

type Terminal struct {
	ContainerName string `json:"containerName"`
	Connected     bool   `json:"connected"`
	Logs          []*Log `json:"logs"`
}

type FlowStatus string

const (
	FlowStatusInProgress FlowStatus = "inProgress"
	FlowStatusFinished   FlowStatus = "finished"
)

var AllFlowStatus = []FlowStatus{
	FlowStatusInProgress,
	FlowStatusFinished,
}

func (e FlowStatus) IsValid() bool {
	switch e {
	case FlowStatusInProgress, FlowStatusFinished:
		return true
	}
	return false
}

func (e FlowStatus) String() string {
	return string(e)
}

func (e *FlowStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FlowStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FlowStatus", str)
	}
	return nil
}

func (e FlowStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TaskStatus string

const (
	TaskStatusInProgress TaskStatus = "inProgress"
	TaskStatusFinished   TaskStatus = "finished"
	TaskStatusStopped    TaskStatus = "stopped"
	TaskStatusFailed     TaskStatus = "failed"
)

var AllTaskStatus = []TaskStatus{
	TaskStatusInProgress,
	TaskStatusFinished,
	TaskStatusStopped,
	TaskStatusFailed,
}

func (e TaskStatus) IsValid() bool {
	switch e {
	case TaskStatusInProgress, TaskStatusFinished, TaskStatusStopped, TaskStatusFailed:
		return true
	}
	return false
}

func (e TaskStatus) String() string {
	return string(e)
}

func (e *TaskStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskStatus", str)
	}
	return nil
}

func (e TaskStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TaskType string

const (
	TaskTypeInput    TaskType = "input"
	TaskTypeTerminal TaskType = "terminal"
	TaskTypeBrowser  TaskType = "browser"
	TaskTypeCode     TaskType = "code"
	TaskTypeAsk      TaskType = "ask"
	TaskTypeDone     TaskType = "done"
)

var AllTaskType = []TaskType{
	TaskTypeInput,
	TaskTypeTerminal,
	TaskTypeBrowser,
	TaskTypeCode,
	TaskTypeAsk,
	TaskTypeDone,
}

func (e TaskType) IsValid() bool {
	switch e {
	case TaskTypeInput, TaskTypeTerminal, TaskTypeBrowser, TaskTypeCode, TaskTypeAsk, TaskTypeDone:
		return true
	}
	return false
}

func (e TaskType) String() string {
	return string(e)
}

func (e *TaskType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskType", str)
	}
	return nil
}

func (e TaskType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
