{"version": 3, "sources": ["../../xterm-addon-canvas/lib/webpack:/CanvasAddon/webpack/universalModuleDefinition", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/BaseRenderLayer.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/CanvasRenderer.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/CursorRenderLayer.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/GridCache.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/LinkRenderLayer.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/SelectionRenderLayer.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/TextRenderLayer.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/CellColorResolver.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/CharAtlasCache.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/CharAtlasUtils.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/Constants.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/CursorBlinkStateManager.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/CustomGlyphs.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/DevicePixelObserver.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/RendererUtils.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/SelectionRenderModel.ts", "../../xterm-addon-canvas/lib/src/browser/renderer/shared/TextureAtlas.ts", "../../xterm-addon-canvas/lib/src/browser/services/CharacterJoinerService.ts", "../../xterm-addon-canvas/lib/src/common/Color.ts", "../../xterm-addon-canvas/lib/src/common/EventEmitter.ts", "../../xterm-addon-canvas/lib/src/common/Lifecycle.ts", "../../xterm-addon-canvas/lib/src/common/MultiKeyMap.ts", "../../xterm-addon-canvas/lib/src/common/Platform.ts", "../../xterm-addon-canvas/lib/src/common/TaskQueue.ts", "../../xterm-addon-canvas/lib/src/common/buffer/AttributeData.ts", "../../xterm-addon-canvas/lib/src/common/buffer/CellData.ts", "../../xterm-addon-canvas/lib/src/common/buffer/Constants.ts", "../../xterm-addon-canvas/lib/src/common/input/TextDecoder.ts", "../../xterm-addon-canvas/lib/src/common/services/LogService.ts", "../../xterm-addon-canvas/lib/src/common/services/ServiceRegistry.ts", "../../xterm-addon-canvas/lib/src/common/services/Services.ts", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/webpack/bootstrap", "../../xterm-addon-canvas/lib/webpack:/CanvasAddon/src/CanvasAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"CanvasAddon\"] = factory();\n\telse\n\t\troot[\"CanvasAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ReadonlyColorSet } from 'browser/Types';\nimport { CellColorResolver } from 'browser/renderer/shared/CellColorResolver';\nimport { acquireTextureAtlas } from 'browser/renderer/shared/CharAtlasCache';\nimport { TEXT_BASELINE } from 'browser/renderer/shared/Constants';\nimport { tryDrawCustomChar } from 'browser/renderer/shared/CustomGlyphs';\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { createSelectionRenderModel } from 'browser/renderer/shared/SelectionRenderModel';\nimport { IRasterizedGlyph, IRenderDimensions, ISelectionRenderModel, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { EventEmitter, forwardEvent } from 'common/EventEmitter';\nimport { Disposable, MutableDisposable, toDisposable } from 'common/Lifecycle';\nimport { isSafari } from 'common/Platform';\nimport { ICellData } from 'common/Types';\nimport { CellData } from 'common/buffer/CellData';\nimport { WHITESPACE_CELL_CODE } from 'common/buffer/Constants';\nimport { IBufferService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { IRenderLayer } from './Types';\n\nexport abstract class BaseRenderLayer extends Disposable implements IRenderLayer {\n  private _canvas: HTMLCanvasElement;\n  protected _ctx!: CanvasRenderingContext2D;\n  private _deviceCharWidth: number = 0;\n  private _deviceCharHeight: number = 0;\n  private _deviceCellWidth: number = 0;\n  private _deviceCellHeight: number = 0;\n  private _deviceCharLeft: number = 0;\n  private _deviceCharTop: number = 0;\n\n  protected _selectionModel: ISelectionRenderModel = createSelectionRenderModel();\n  private _cellColorResolver: CellColorResolver;\n  private _bitmapGenerator: (BitmapGenerator | undefined)[] = [];\n\n  protected _charAtlas!: ITextureAtlas;\n  protected _charAtlasDisposable = this.register(new MutableDisposable());\n\n  public get canvas(): HTMLCanvasElement { return this._canvas; }\n  public get cacheCanvas(): HTMLCanvasElement { return this._charAtlas?.pages[0].canvas!; }\n\n  private readonly _onAddTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private _container: HTMLElement,\n    id: string,\n    zIndex: number,\n    private _alpha: boolean,\n    protected readonly _themeService: IThemeService,\n    protected readonly _bufferService: IBufferService,\n    protected readonly _optionsService: IOptionsService,\n    protected readonly _decorationService: IDecorationService,\n    protected readonly _coreBrowserService: ICoreBrowserService\n  ) {\n    super();\n    this._cellColorResolver = new CellColorResolver(this._terminal, this._selectionModel, this._decorationService, this._coreBrowserService, this._themeService);\n    this._canvas = document.createElement('canvas');\n    this._canvas.classList.add(`xterm-${id}-layer`);\n    this._canvas.style.zIndex = zIndex.toString();\n    this._initCanvas();\n    this._container.appendChild(this._canvas);\n    this._refreshCharAtlas(this._themeService.colors);\n    this.register(this._themeService.onChangeColors(e => {\n      this._refreshCharAtlas(e);\n      this.reset();\n      // Trigger selection changed as it's handled separately to regular rendering\n      this.handleSelectionChanged(this._selectionModel.selectionStart, this._selectionModel.selectionEnd, this._selectionModel.columnSelectMode);\n    }));\n\n    this.register(toDisposable(() => {\n      this._canvas.remove();\n    }));\n  }\n\n  private _initCanvas(): void {\n    this._ctx = throwIfFalsy(this._canvas.getContext('2d', { alpha: this._alpha }));\n    // Draw the background if this is an opaque layer\n    if (!this._alpha) {\n      this._clearAll();\n    }\n  }\n\n  public handleBlur(): void {}\n  public handleFocus(): void {}\n  public handleCursorMove(): void {}\n  public handleGridChanged(startRow: number, endRow: number): void {}\n\n  public handleSelectionChanged(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean = false): void {\n    this._selectionModel.update(this._terminal, start, end, columnSelectMode);\n  }\n\n  protected _setTransparency(alpha: boolean): void {\n    // Do nothing when alpha doesn't change\n    if (alpha === this._alpha) {\n      return;\n    }\n\n    // Create new canvas and replace old one\n    const oldCanvas = this._canvas;\n    this._alpha = alpha;\n    // Cloning preserves properties\n    this._canvas = this._canvas.cloneNode() as HTMLCanvasElement;\n    this._initCanvas();\n    this._container.replaceChild(this._canvas, oldCanvas);\n\n    // Regenerate char atlas and force a full redraw\n    this._refreshCharAtlas(this._themeService.colors);\n    this.handleGridChanged(0, this._bufferService.rows - 1);\n  }\n\n  /**\n   * Refreshes the char atlas, aquiring a new one if necessary.\n   * @param colorSet The color set to use for the char atlas.\n   */\n  private _refreshCharAtlas(colorSet: ReadonlyColorSet): void {\n    if (this._deviceCharWidth <= 0 && this._deviceCharHeight <= 0) {\n      return;\n    }\n    this._charAtlas = acquireTextureAtlas(this._terminal, this._optionsService.rawOptions, colorSet, this._deviceCellWidth, this._deviceCellHeight, this._deviceCharWidth, this._deviceCharHeight, this._coreBrowserService.dpr);\n    this._charAtlasDisposable.value = forwardEvent(this._charAtlas.onAddTextureAtlasCanvas, this._onAddTextureAtlasCanvas);\n    this._charAtlas.warmUp();\n    for (let i = 0; i < this._charAtlas.pages.length; i++) {\n      this._bitmapGenerator[i] = new BitmapGenerator(this._charAtlas.pages[i].canvas);\n    }\n  }\n\n  public resize(dim: IRenderDimensions): void {\n    this._deviceCellWidth = dim.device.cell.width;\n    this._deviceCellHeight = dim.device.cell.height;\n    this._deviceCharWidth = dim.device.char.width;\n    this._deviceCharHeight = dim.device.char.height;\n    this._deviceCharLeft = dim.device.char.left;\n    this._deviceCharTop = dim.device.char.top;\n    this._canvas.width = dim.device.canvas.width;\n    this._canvas.height = dim.device.canvas.height;\n    this._canvas.style.width = `${dim.css.canvas.width}px`;\n    this._canvas.style.height = `${dim.css.canvas.height}px`;\n\n    // Draw the background if this is an opaque layer\n    if (!this._alpha) {\n      this._clearAll();\n    }\n\n    this._refreshCharAtlas(this._themeService.colors);\n  }\n\n  public abstract reset(): void;\n\n  public clearTextureAtlas(): void {\n    this._charAtlas?.clearTexture();\n  }\n\n  /**\n   * Fills 1+ cells completely. This uses the existing fillStyle on the context.\n   * @param x The column to start at.\n   * @param y The row to start at\n   * @param width The number of columns to fill.\n   * @param height The number of rows to fill.\n   */\n  protected _fillCells(x: number, y: number, width: number, height: number): void {\n    this._ctx.fillRect(\n      x * this._deviceCellWidth,\n      y * this._deviceCellHeight,\n      width * this._deviceCellWidth,\n      height * this._deviceCellHeight);\n  }\n\n  /**\n   * Fills a 1px line (2px on HDPI) at the middle of the cell. This uses the\n   * existing fillStyle on the context.\n   * @param x The column to fill.\n   * @param y The row to fill.\n   */\n  protected _fillMiddleLineAtCells(x: number, y: number, width: number = 1): void {\n    const cellOffset = Math.ceil(this._deviceCellHeight * 0.5);\n    this._ctx.fillRect(\n      x * this._deviceCellWidth,\n      (y + 1) * this._deviceCellHeight - cellOffset - this._coreBrowserService.dpr,\n      width * this._deviceCellWidth,\n      this._coreBrowserService.dpr);\n  }\n\n  /**\n   * Fills a 1px line (2px on HDPI) at the bottom of the cell. This uses the\n   * existing fillStyle on the context.\n   * @param x The column to fill.\n   * @param y The row to fill.\n   */\n  protected _fillBottomLineAtCells(x: number, y: number, width: number = 1, pixelOffset: number = 0): void {\n    this._ctx.fillRect(\n      x * this._deviceCellWidth,\n      (y + 1) * this._deviceCellHeight + pixelOffset - this._coreBrowserService.dpr - 1 /* Ensure it's drawn within the cell */,\n      width * this._deviceCellWidth,\n      this._coreBrowserService.dpr);\n  }\n\n  protected _curlyUnderlineAtCell(x: number, y: number, width: number = 1): void {\n    this._ctx.save();\n    this._ctx.beginPath();\n    this._ctx.strokeStyle = this._ctx.fillStyle;\n    const lineWidth = this._coreBrowserService.dpr;\n    this._ctx.lineWidth = lineWidth;\n    for (let xOffset = 0; xOffset < width; xOffset++) {\n      const xLeft = (x + xOffset) * this._deviceCellWidth;\n      const xMid = (x + xOffset + 0.5) * this._deviceCellWidth;\n      const xRight = (x + xOffset + 1) * this._deviceCellWidth;\n      const yMid = (y + 1) * this._deviceCellHeight - lineWidth - 1;\n      const yMidBot = yMid - lineWidth;\n      const yMidTop = yMid + lineWidth;\n      this._ctx.moveTo(xLeft, yMid);\n      this._ctx.bezierCurveTo(\n        xLeft, yMidBot,\n        xMid, yMidBot,\n        xMid, yMid\n      );\n      this._ctx.bezierCurveTo(\n        xMid, yMidTop,\n        xRight, yMidTop,\n        xRight, yMid\n      );\n    }\n    this._ctx.stroke();\n    this._ctx.restore();\n  }\n\n  protected _dottedUnderlineAtCell(x: number, y: number, width: number = 1): void {\n    this._ctx.save();\n    this._ctx.beginPath();\n    this._ctx.strokeStyle = this._ctx.fillStyle;\n    const lineWidth = this._coreBrowserService.dpr;\n    this._ctx.lineWidth = lineWidth;\n    this._ctx.setLineDash([lineWidth * 2, lineWidth]);\n    const xLeft = x * this._deviceCellWidth;\n    const yMid = (y + 1) * this._deviceCellHeight - lineWidth - 1;\n    this._ctx.moveTo(xLeft, yMid);\n    for (let xOffset = 0; xOffset < width; xOffset++) {\n      // const xLeft = x * this._deviceCellWidth;\n      const xRight = (x + width + xOffset) * this._deviceCellWidth;\n      this._ctx.lineTo(xRight, yMid);\n    }\n    this._ctx.stroke();\n    this._ctx.closePath();\n    this._ctx.restore();\n  }\n\n  protected _dashedUnderlineAtCell(x: number, y: number, width: number = 1): void {\n    this._ctx.save();\n    this._ctx.beginPath();\n    this._ctx.strokeStyle = this._ctx.fillStyle;\n    const lineWidth = this._coreBrowserService.dpr;\n    this._ctx.lineWidth = lineWidth;\n    this._ctx.setLineDash([lineWidth * 4, lineWidth * 3]);\n    const xLeft = x * this._deviceCellWidth;\n    const xRight = (x + width) * this._deviceCellWidth;\n    const yMid = (y + 1) * this._deviceCellHeight - lineWidth - 1;\n    this._ctx.moveTo(xLeft, yMid);\n    this._ctx.lineTo(xRight, yMid);\n    this._ctx.stroke();\n    this._ctx.closePath();\n    this._ctx.restore();\n  }\n\n  /**\n   * Fills a 1px line (2px on HDPI) at the left of the cell. This uses the\n   * existing fillStyle on the context.\n   * @param x The column to fill.\n   * @param y The row to fill.\n   */\n  protected _fillLeftLineAtCell(x: number, y: number, width: number): void {\n    this._ctx.fillRect(\n      x * this._deviceCellWidth,\n      y * this._deviceCellHeight,\n      this._coreBrowserService.dpr * width,\n      this._deviceCellHeight);\n  }\n\n  /**\n   * Strokes a 1px rectangle (2px on HDPI) around a cell. This uses the existing\n   * strokeStyle on the context.\n   * @param x The column to fill.\n   * @param y The row to fill.\n   */\n  protected _strokeRectAtCell(x: number, y: number, width: number, height: number): void {\n    const lineWidth = this._coreBrowserService.dpr;\n    this._ctx.lineWidth = lineWidth;\n    this._ctx.strokeRect(\n      x * this._deviceCellWidth + lineWidth / 2,\n      y * this._deviceCellHeight + (lineWidth / 2),\n      width * this._deviceCellWidth - lineWidth,\n      (height * this._deviceCellHeight) - lineWidth);\n  }\n\n  /**\n   * Clears the entire canvas.\n   */\n  protected _clearAll(): void {\n    if (this._alpha) {\n      this._ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n      this._ctx.fillRect(0, 0, this._canvas.width, this._canvas.height);\n    }\n  }\n\n  /**\n   * Clears 1+ cells completely.\n   * @param x The column to start at.\n   * @param y The row to start at.\n   * @param width The number of columns to clear.\n   * @param height The number of rows to clear.\n   */\n  protected _clearCells(x: number, y: number, width: number, height: number): void {\n    if (this._alpha) {\n      this._ctx.clearRect(\n        x * this._deviceCellWidth,\n        y * this._deviceCellHeight,\n        width * this._deviceCellWidth,\n        height * this._deviceCellHeight);\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n      this._ctx.fillRect(\n        x * this._deviceCellWidth,\n        y * this._deviceCellHeight,\n        width * this._deviceCellWidth,\n        height * this._deviceCellHeight);\n    }\n  }\n\n  /**\n   * Draws a truecolor character at the cell. The character will be clipped to\n   * ensure that it fits with the cell, including the cell to the right if it's\n   * a wide character. This uses the existing fillStyle on the context.\n   * @param cell The cell data for the character to draw.\n   * @param x The column to draw at.\n   * @param y The row to draw at.\n   */\n  protected _fillCharTrueColor(cell: CellData, x: number, y: number): void {\n    this._ctx.font = this._getFont(false, false);\n    this._ctx.textBaseline = TEXT_BASELINE;\n    this._clipRow(y);\n\n    // Draw custom characters if applicable\n    let drawSuccess = false;\n    if (this._optionsService.rawOptions.customGlyphs !== false) {\n      drawSuccess = tryDrawCustomChar(this._ctx, cell.getChars(), x * this._deviceCellWidth, y * this._deviceCellHeight, this._deviceCellWidth, this._deviceCellHeight, this._optionsService.rawOptions.fontSize, this._coreBrowserService.dpr);\n    }\n\n    // Draw the character\n    if (!drawSuccess) {\n      this._ctx.fillText(\n        cell.getChars(),\n        x * this._deviceCellWidth + this._deviceCharLeft,\n        y * this._deviceCellHeight + this._deviceCharTop + this._deviceCharHeight);\n    }\n  }\n\n  /**\n   * Draws one or more characters at a cell. If possible this will draw using\n   * the character atlas to reduce draw time.\n   */\n  protected _drawChars(cell: ICellData, x: number, y: number): void {\n    const chars = cell.getChars();\n    this._cellColorResolver.resolve(cell, x, this._bufferService.buffer.ydisp + y);\n\n    if (!this._charAtlas) {\n      return;\n    }\n\n    let glyph: IRasterizedGlyph;\n    if (chars && chars.length > 1) {\n      glyph = this._charAtlas.getRasterizedGlyphCombinedChar(chars, this._cellColorResolver.result.bg, this._cellColorResolver.result.fg, this._cellColorResolver.result.ext, true);\n    } else {\n      glyph = this._charAtlas.getRasterizedGlyph(cell.getCode() || WHITESPACE_CELL_CODE, this._cellColorResolver.result.bg, this._cellColorResolver.result.fg, this._cellColorResolver.result.ext, true);\n    }\n    if (!glyph.size.x || !glyph.size.y) {\n      return;\n    }\n    this._ctx.save();\n    this._clipRow(y);\n\n    // Draw the image, use the bitmap if it's available\n\n    // HACK: If the canvas doesn't match, delete the generator. It's not clear how this happens but\n    // something is wrong with either the lifecycle of _bitmapGenerator or the page canvases are\n    // swapped out unexpectedly\n    if (this._bitmapGenerator[glyph.texturePage] && this._charAtlas.pages[glyph.texturePage].canvas !== this._bitmapGenerator[glyph.texturePage]!.canvas) {\n      this._bitmapGenerator[glyph.texturePage]?.bitmap?.close();\n      delete this._bitmapGenerator[glyph.texturePage];\n    }\n\n    if (this._charAtlas.pages[glyph.texturePage].version !== this._bitmapGenerator[glyph.texturePage]?.version) {\n      if (!this._bitmapGenerator[glyph.texturePage]) {\n        this._bitmapGenerator[glyph.texturePage] = new BitmapGenerator(this._charAtlas.pages[glyph.texturePage].canvas);\n      }\n      this._bitmapGenerator[glyph.texturePage]!.refresh();\n      this._bitmapGenerator[glyph.texturePage]!.version = this._charAtlas.pages[glyph.texturePage].version;\n    }\n    this._ctx.drawImage(\n      this._bitmapGenerator[glyph.texturePage]?.bitmap || this._charAtlas!.pages[glyph.texturePage].canvas,\n      glyph.texturePosition.x,\n      glyph.texturePosition.y,\n      glyph.size.x,\n      glyph.size.y,\n      x * this._deviceCellWidth + this._deviceCharLeft - glyph.offset.x,\n      y * this._deviceCellHeight + this._deviceCharTop - glyph.offset.y,\n      glyph.size.x,\n      glyph.size.y\n    );\n    this._ctx.restore();\n  }\n\n  /**\n   * Clips a row to ensure no pixels will be drawn outside the cells in the row.\n   * @param y The row to clip.\n   */\n  private _clipRow(y: number): void {\n    this._ctx.beginPath();\n    this._ctx.rect(\n      0,\n      y * this._deviceCellHeight,\n      this._bufferService.cols * this._deviceCellWidth,\n      this._deviceCellHeight);\n    this._ctx.clip();\n  }\n\n  /**\n   * Gets the current font.\n   * @param isBold If we should use the bold fontWeight.\n   */\n  protected _getFont(isBold: boolean, isItalic: boolean): string {\n    const fontWeight = isBold ? this._optionsService.rawOptions.fontWeightBold : this._optionsService.rawOptions.fontWeight;\n    const fontStyle = isItalic ? 'italic' : '';\n\n    return `${fontStyle} ${fontWeight} ${this._optionsService.rawOptions.fontSize * this._coreBrowserService.dpr}px ${this._optionsService.rawOptions.fontFamily}`;\n  }\n}\n\n/**\n * The number of milliseconds to wait before generating the ImageBitmap, this is to debounce/batch\n * the operation as window.createImageBitmap is asynchronous.\n */\nconst GLYPH_BITMAP_COMMIT_DELAY = 100;\n\nconst enum BitmapGeneratorState {\n  IDLE = 0,\n  GENERATING = 1,\n  GENERATING_INVALID = 2\n}\n\nclass BitmapGenerator {\n  private _state: BitmapGeneratorState = BitmapGeneratorState.IDLE;\n  private _commitTimeout: number | undefined = undefined;\n  private _bitmap: ImageBitmap | undefined = undefined;\n  public get bitmap(): ImageBitmap | undefined { return this._bitmap; }\n  public version: number = -1;\n\n  constructor(public readonly canvas: HTMLCanvasElement) {\n  }\n\n  public refresh(): void {\n    // Clear the bitmap immediately as it's stale\n    this._bitmap?.close();\n    this._bitmap = undefined;\n    // Disable ImageBitmaps on Safari because of https://bugs.webkit.org/show_bug.cgi?id=149990\n    if (isSafari) {\n      return;\n    }\n    if (this._commitTimeout === undefined) {\n      this._commitTimeout = window.setTimeout(() => this._generate(), GLYPH_BITMAP_COMMIT_DELAY);\n    }\n    if (this._state === BitmapGeneratorState.GENERATING) {\n      this._state = BitmapGeneratorState.GENERATING_INVALID;\n    }\n  }\n\n  private _generate(): void {\n    if (this._state === BitmapGeneratorState.IDLE) {\n      this._bitmap?.close();\n      this._bitmap = undefined;\n      this._state = BitmapGeneratorState.GENERATING;\n      window.createImageBitmap(this.canvas).then(bitmap => {\n        if (this._state === BitmapGeneratorState.GENERATING_INVALID) {\n          this.refresh();\n        } else {\n          this._bitmap = bitmap;\n        }\n        this._state = BitmapGeneratorState.IDLE;\n      });\n      if (this._commitTimeout) {\n        this._commitTimeout = undefined;\n      }\n    }\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ILinkifier2 } from 'browser/Types';\nimport { removeTerminalFromCache } from 'browser/renderer/shared/CharAtlasCache';\nimport { observeDevicePixelDimensions } from 'browser/renderer/shared/DevicePixelObserver';\nimport { createRenderDimensions } from 'browser/renderer/shared/RendererUtils';\nimport { IRenderDimensions, IRenderer, IRequestRedrawEvent } from 'browser/renderer/shared/Types';\nimport { ICharSizeService, ICharacterJoinerService, ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { EventEmitter, forwardEvent } from 'common/EventEmitter';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { IBufferService, ICoreService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { CursorRenderLayer } from './CursorRenderLayer';\nimport { LinkRenderLayer } from './LinkRenderLayer';\nimport { SelectionRenderLayer } from './SelectionRenderLayer';\nimport { TextRenderLayer } from './TextRenderLayer';\nimport { IRenderLayer } from './Types';\n\nexport class CanvasRenderer extends Disposable implements IRenderer {\n  private _renderLayers: IRenderLayer[];\n  private _devicePixelRatio: number;\n\n  public dimensions: IRenderDimensions;\n\n  private readonly _onRequestRedraw = this.register(new EventEmitter<IRequestRedrawEvent>());\n  public readonly onRequestRedraw = this._onRequestRedraw.event;\n  private readonly _onChangeTextureAtlas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onChangeTextureAtlas = this._onChangeTextureAtlas.event;\n  private readonly _onAddTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _screenElement: HTMLElement,\n    linkifier2: ILinkifier2,\n    private readonly _bufferService: IBufferService,\n    private readonly _charSizeService: ICharSizeService,\n    private readonly _optionsService: IOptionsService,\n    characterJoinerService: ICharacterJoinerService,\n    coreService: ICoreService,\n    private readonly _coreBrowserService: ICoreBrowserService,\n    decorationService: IDecorationService,\n    private readonly _themeService: IThemeService\n  ) {\n    super();\n    const allowTransparency = this._optionsService.rawOptions.allowTransparency;\n    this._renderLayers = [\n      new TextRenderLayer(this._terminal, this._screenElement, 0, allowTransparency, this._bufferService, this._optionsService, characterJoinerService, decorationService, this._coreBrowserService, _themeService),\n      new SelectionRenderLayer(this._terminal, this._screenElement, 1, this._bufferService, this._coreBrowserService, decorationService, this._optionsService, _themeService),\n      new LinkRenderLayer(this._terminal, this._screenElement, 2, linkifier2, this._bufferService, this._optionsService, decorationService, this._coreBrowserService, _themeService),\n      new CursorRenderLayer(this._terminal, this._screenElement, 3, this._onRequestRedraw, this._bufferService, this._optionsService, coreService, this._coreBrowserService, decorationService, _themeService)\n    ];\n    for (const layer of this._renderLayers) {\n      forwardEvent(layer.onAddTextureAtlasCanvas, this._onAddTextureAtlasCanvas);\n    }\n    this.dimensions = createRenderDimensions();\n    this._devicePixelRatio = this._coreBrowserService.dpr;\n    this._updateDimensions();\n\n    this.register(observeDevicePixelDimensions(this._renderLayers[0].canvas, this._coreBrowserService.window, (w, h) => this._setCanvasDevicePixelDimensions(w, h)));\n    this.register(toDisposable(() => {\n      for (const l of this._renderLayers) {\n        l.dispose();\n      }\n      removeTerminalFromCache(this._terminal);\n    }));\n  }\n\n  public get textureAtlas(): HTMLCanvasElement | undefined {\n    return this._renderLayers[0].cacheCanvas;\n  }\n\n  public handleDevicePixelRatioChange(): void {\n    // If the device pixel ratio changed, the char atlas needs to be regenerated\n    // and the terminal needs to refreshed\n    if (this._devicePixelRatio !== this._coreBrowserService.dpr) {\n      this._devicePixelRatio = this._coreBrowserService.dpr;\n      this.handleResize(this._bufferService.cols, this._bufferService.rows);\n    }\n  }\n\n  public handleResize(cols: number, rows: number): void {\n    // Update character and canvas dimensions\n    this._updateDimensions();\n\n    // Resize all render layers\n    for (const l of this._renderLayers) {\n      l.resize(this.dimensions);\n    }\n\n    // Resize the screen\n    this._screenElement.style.width = `${this.dimensions.css.canvas.width}px`;\n    this._screenElement.style.height = `${this.dimensions.css.canvas.height}px`;\n  }\n\n  public handleCharSizeChanged(): void {\n    this.handleResize(this._bufferService.cols, this._bufferService.rows);\n  }\n\n  public handleBlur(): void {\n    this._runOperation(l => l.handleBlur());\n  }\n\n  public handleFocus(): void {\n    this._runOperation(l => l.handleFocus());\n  }\n\n  public handleSelectionChanged(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean = false): void {\n    this._runOperation(l => l.handleSelectionChanged(start, end, columnSelectMode));\n    // Selection foreground requires a full re-render\n    if (this._themeService.colors.selectionForeground) {\n      this._onRequestRedraw.fire({ start: 0, end: this._bufferService.rows - 1 });\n    }\n  }\n\n  public handleCursorMove(): void {\n    this._runOperation(l => l.handleCursorMove());\n  }\n\n  public clear(): void {\n    this._runOperation(l => l.reset());\n  }\n\n  private _runOperation(operation: (layer: IRenderLayer) => void): void {\n    for (const l of this._renderLayers) {\n      operation(l);\n    }\n  }\n\n  /**\n   * Performs the refresh loop callback, calling refresh only if a refresh is\n   * necessary before queueing up the next one.\n   */\n  public renderRows(start: number, end: number): void {\n    for (const l of this._renderLayers) {\n      l.handleGridChanged(start, end);\n    }\n  }\n\n  public clearTextureAtlas(): void {\n    for (const layer of this._renderLayers) {\n      layer.clearTextureAtlas();\n    }\n  }\n\n  /**\n   * Recalculates the character and canvas dimensions.\n   */\n  private _updateDimensions(): void {\n    if (!this._charSizeService.hasValidSize) {\n      return;\n    }\n\n    // See the WebGL renderer for an explanation of this section.\n    const dpr = this._coreBrowserService.dpr;\n    this.dimensions.device.char.width = Math.floor(this._charSizeService.width * dpr);\n    this.dimensions.device.char.height = Math.ceil(this._charSizeService.height * dpr);\n    this.dimensions.device.cell.height = Math.floor(this.dimensions.device.char.height * this._optionsService.rawOptions.lineHeight);\n    this.dimensions.device.char.top = this._optionsService.rawOptions.lineHeight === 1 ? 0 : Math.round((this.dimensions.device.cell.height - this.dimensions.device.char.height) / 2);\n    this.dimensions.device.cell.width = this.dimensions.device.char.width + Math.round(this._optionsService.rawOptions.letterSpacing);\n    this.dimensions.device.char.left = Math.floor(this._optionsService.rawOptions.letterSpacing / 2);\n    this.dimensions.device.canvas.height = this._bufferService.rows * this.dimensions.device.cell.height;\n    this.dimensions.device.canvas.width = this._bufferService.cols * this.dimensions.device.cell.width;\n    this.dimensions.css.canvas.height = Math.round(this.dimensions.device.canvas.height / dpr);\n    this.dimensions.css.canvas.width = Math.round(this.dimensions.device.canvas.width / dpr);\n    this.dimensions.css.cell.height = this.dimensions.css.canvas.height / this._bufferService.rows;\n    this.dimensions.css.cell.width = this.dimensions.css.canvas.width / this._bufferService.cols;\n  }\n\n  private _setCanvasDevicePixelDimensions(width: number, height: number): void {\n    this.dimensions.device.canvas.height = height;\n    this.dimensions.device.canvas.width = width;\n    // Resize all render layers\n    for (const l of this._renderLayers) {\n      l.resize(this.dimensions);\n    }\n    this._requestRedrawViewport();\n  }\n\n  private _requestRedrawViewport(): void {\n    this._onRequestRedraw.fire({ start: 0, end: this._bufferService.rows - 1 });\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { CursorBlinkStateManager } from 'browser/renderer/shared/CursorBlinkStateManager';\nimport { IRenderDimensions, IRequestRedrawEvent } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { IEventEmitter } from 'common/EventEmitter';\nimport { MutableDisposable } from 'common/Lifecycle';\nimport { isFirefox } from 'common/Platform';\nimport { ICellData } from 'common/Types';\nimport { CellData } from 'common/buffer/CellData';\nimport { IBufferService, ICoreService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { BaseRenderLayer } from './BaseRenderLayer';\n\ninterface ICursorState {\n  x: number;\n  y: number;\n  isFocused: boolean;\n  style: string;\n  width: number;\n}\n\nexport class CursorRenderLayer extends BaseRenderLayer {\n  private _state: ICursorState;\n  private _cursorRenderers: {[key: string]: (x: number, y: number, cell: ICellData) => void};\n  private _cursorBlinkStateManager: MutableDisposable<CursorBlinkStateManager> = this.register(new MutableDisposable());\n  private _cell: ICellData = new CellData();\n\n  constructor(\n    terminal: Terminal,\n    container: HTMLElement,\n    zIndex: number,\n    private readonly _onRequestRedraw: IEventEmitter<IRequestRedrawEvent>,\n    bufferService: IBufferService,\n    optionsService: IOptionsService,\n    private readonly _coreService: ICoreService,\n    coreBrowserService: ICoreBrowserService,\n    decorationService: IDecorationService,\n    themeService: IThemeService\n  ) {\n    super(terminal, container, 'cursor', zIndex, true, themeService, bufferService, optionsService, decorationService, coreBrowserService);\n    this._state = {\n      x: 0,\n      y: 0,\n      isFocused: false,\n      style: '',\n      width: 0\n    };\n    this._cursorRenderers = {\n      'bar': this._renderBarCursor.bind(this),\n      'block': this._renderBlockCursor.bind(this),\n      'underline': this._renderUnderlineCursor.bind(this),\n      'outline': this._renderOutlineCursor.bind(this)\n    };\n    this.register(optionsService.onOptionChange(() => this._handleOptionsChanged()));\n    this._handleOptionsChanged();\n  }\n\n  public resize(dim: IRenderDimensions): void {\n    super.resize(dim);\n    // Resizing the canvas discards the contents of the canvas so clear state\n    this._state = {\n      x: 0,\n      y: 0,\n      isFocused: false,\n      style: '',\n      width: 0\n    };\n  }\n\n  public reset(): void {\n    this._clearCursor();\n    this._cursorBlinkStateManager.value?.restartBlinkAnimation();\n    this._handleOptionsChanged();\n  }\n\n  public handleBlur(): void {\n    this._cursorBlinkStateManager.value?.pause();\n    this._onRequestRedraw.fire({ start: this._bufferService.buffer.y, end: this._bufferService.buffer.y });\n  }\n\n  public handleFocus(): void {\n    this._cursorBlinkStateManager.value?.resume();\n    this._onRequestRedraw.fire({ start: this._bufferService.buffer.y, end: this._bufferService.buffer.y });\n  }\n\n  private _handleOptionsChanged(): void {\n    if (this._optionsService.rawOptions.cursorBlink) {\n      if (!this._cursorBlinkStateManager.value) {\n        this._cursorBlinkStateManager.value = new CursorBlinkStateManager(() => this._render(true), this._coreBrowserService);\n      }\n    } else {\n      this._cursorBlinkStateManager.clear();\n    }\n    // Request a refresh from the terminal as management of rendering is being\n    // moved back to the terminal\n    this._onRequestRedraw.fire({ start: this._bufferService.buffer.y, end: this._bufferService.buffer.y });\n  }\n\n  public handleCursorMove(): void {\n    this._cursorBlinkStateManager.value?.restartBlinkAnimation();\n  }\n\n  public handleGridChanged(startRow: number, endRow: number): void {\n    if (!this._cursorBlinkStateManager.value || this._cursorBlinkStateManager.value.isPaused) {\n      this._render(false);\n    } else {\n      this._cursorBlinkStateManager.value.restartBlinkAnimation();\n    }\n  }\n\n  private _render(triggeredByAnimationFrame: boolean): void {\n    // Don't draw the cursor if it's hidden\n    if (!this._coreService.isCursorInitialized || this._coreService.isCursorHidden) {\n      this._clearCursor();\n      return;\n    }\n\n    const cursorY = this._bufferService.buffer.ybase + this._bufferService.buffer.y;\n    const viewportRelativeCursorY = cursorY - this._bufferService.buffer.ydisp;\n\n    // Don't draw the cursor if it's off-screen\n    if (viewportRelativeCursorY < 0 || viewportRelativeCursorY >= this._bufferService.rows) {\n      this._clearCursor();\n      return;\n    }\n\n    // in case cursor.x == cols adjust visual cursor to cols - 1\n    const cursorX = Math.min(this._bufferService.buffer.x, this._bufferService.cols - 1);\n    this._bufferService.buffer.lines.get(cursorY)!.loadCell(cursorX, this._cell);\n    if (this._cell.content === undefined) {\n      return;\n    }\n\n    if (!this._coreBrowserService.isFocused) {\n      this._clearCursor();\n      this._ctx.save();\n      this._ctx.fillStyle = this._themeService.colors.cursor.css;\n      const cursorStyle = this._optionsService.rawOptions.cursorStyle;\n      const cursorInactiveStyle = this._optionsService.rawOptions.cursorInactiveStyle;\n      if (cursorInactiveStyle && cursorInactiveStyle !== 'none') {\n        this._cursorRenderers[cursorInactiveStyle](cursorX, viewportRelativeCursorY, this._cell);\n      }\n      this._ctx.restore();\n      this._state.x = cursorX;\n      this._state.y = viewportRelativeCursorY;\n      this._state.isFocused = false;\n      this._state.style = cursorStyle;\n      this._state.width = this._cell.getWidth();\n      return;\n    }\n\n    // Don't draw the cursor if it's blinking\n    if (this._cursorBlinkStateManager.value && !this._cursorBlinkStateManager.value.isCursorVisible) {\n      this._clearCursor();\n      return;\n    }\n\n    if (this._state) {\n      // The cursor is already in the correct spot, don't redraw\n      if (this._state.x === cursorX &&\n          this._state.y === viewportRelativeCursorY &&\n          this._state.isFocused === this._coreBrowserService.isFocused &&\n          this._state.style === this._optionsService.rawOptions.cursorStyle &&\n          this._state.width === this._cell.getWidth()) {\n        return;\n      }\n      this._clearCursor();\n    }\n\n    this._ctx.save();\n    this._cursorRenderers[this._optionsService.rawOptions.cursorStyle || 'block'](cursorX, viewportRelativeCursorY, this._cell);\n    this._ctx.restore();\n\n    this._state.x = cursorX;\n    this._state.y = viewportRelativeCursorY;\n    this._state.isFocused = false;\n    this._state.style = this._optionsService.rawOptions.cursorStyle;\n    this._state.width = this._cell.getWidth();\n  }\n\n  private _clearCursor(): void {\n    if (this._state) {\n      // Avoid potential rounding errors when browser is Firefox (#4487) or device pixel ratio is\n      // less than 1\n      if (isFirefox || this._coreBrowserService.dpr < 1) {\n        this._clearAll();\n      } else {\n        this._clearCells(this._state.x, this._state.y, this._state.width, 1);\n      }\n      this._state = {\n        x: 0,\n        y: 0,\n        isFocused: false,\n        style: '',\n        width: 0\n      };\n    }\n  }\n\n  private _renderBarCursor(x: number, y: number, cell: ICellData): void {\n    this._ctx.save();\n    this._ctx.fillStyle = this._themeService.colors.cursor.css;\n    this._fillLeftLineAtCell(x, y, this._optionsService.rawOptions.cursorWidth);\n    this._ctx.restore();\n  }\n\n  private _renderBlockCursor(x: number, y: number, cell: ICellData): void {\n    this._ctx.save();\n    this._ctx.fillStyle = this._themeService.colors.cursor.css;\n    this._fillCells(x, y, cell.getWidth(), 1);\n    this._ctx.fillStyle = this._themeService.colors.cursorAccent.css;\n    this._fillCharTrueColor(cell, x, y);\n    this._ctx.restore();\n  }\n\n  private _renderUnderlineCursor(x: number, y: number, cell: ICellData): void {\n    this._ctx.save();\n    this._ctx.fillStyle = this._themeService.colors.cursor.css;\n    this._fillBottomLineAtCells(x, y);\n    this._ctx.restore();\n  }\n\n  private _renderOutlineCursor(x: number, y: number, cell: ICellData): void {\n    this._ctx.save();\n    this._ctx.strokeStyle = this._themeService.colors.cursor.css;\n    this._strokeRectAtCell(x, y, cell.getWidth(), 1);\n    this._ctx.restore();\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nexport class GridCache<T> {\n  public cache: (T | undefined)[][];\n\n  public constructor() {\n    this.cache = [];\n  }\n\n  public resize(width: number, height: number): void {\n    for (let x = 0; x < width; x++) {\n      if (this.cache.length <= x) {\n        this.cache.push([]);\n      }\n      for (let y = this.cache[x].length; y < height; y++) {\n        this.cache[x].push(undefined);\n      }\n      this.cache[x].length = height;\n    }\n    this.cache.length = width;\n  }\n\n  public clear(): void {\n    for (let x = 0; x < this.cache.length; x++) {\n      for (let y = 0; y < this.cache[x].length; y++) {\n        this.cache[x][y] = undefined;\n      }\n    }\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ILinkifier2, ILinkifierEvent } from 'browser/Types';\nimport { is256Color } from 'browser/renderer/shared/CharAtlasUtils';\nimport { INVERTED_DEFAULT_COLOR } from 'browser/renderer/shared/Constants';\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { IBufferService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { BaseRenderLayer } from './BaseRenderLayer';\n\nexport class LinkRenderLayer extends BaseRenderLayer {\n  private _state: ILinkifierEvent | undefined;\n\n  constructor(\n    terminal: Terminal,\n    container: HTMLElement,\n    zIndex: number,\n    linkifier2: ILinkifier2,\n    bufferService: IBufferService,\n    optionsService: IOptionsService,\n    decorationService: IDecorationService,\n    coreBrowserService: ICoreBrowserService,\n    themeService: IThemeService\n  ) {\n    super(terminal, container, 'link', zIndex, true, themeService, bufferService, optionsService, decorationService, coreBrowserService);\n\n    this.register(linkifier2.onShowLinkUnderline(e => this._handleShowLinkUnderline(e)));\n    this.register(linkifier2.onHideLinkUnderline(e => this._handleHideLinkUnderline(e)));\n  }\n\n  public resize(dim: IRenderDimensions): void {\n    super.resize(dim);\n    // Resizing the canvas discards the contents of the canvas so clear state\n    this._state = undefined;\n  }\n\n  public reset(): void {\n    this._clearCurrentLink();\n  }\n\n  private _clearCurrentLink(): void {\n    if (this._state) {\n      this._clearCells(this._state.x1, this._state.y1, this._state.cols - this._state.x1, 1);\n      const middleRowCount = this._state.y2 - this._state.y1 - 1;\n      if (middleRowCount > 0) {\n        this._clearCells(0, this._state.y1 + 1, this._state.cols, middleRowCount);\n      }\n      this._clearCells(0, this._state.y2, this._state.x2, 1);\n      this._state = undefined;\n    }\n  }\n\n  private _handleShowLinkUnderline(e: ILinkifierEvent): void {\n    if (e.fg === INVERTED_DEFAULT_COLOR) {\n      this._ctx.fillStyle = this._themeService.colors.background.css;\n    } else if (e.fg && is256Color(e.fg)) {\n      // 256 color support\n      this._ctx.fillStyle = this._themeService.colors.ansi[e.fg].css;\n    } else {\n      this._ctx.fillStyle = this._themeService.colors.foreground.css;\n    }\n\n    if (e.y1 === e.y2) {\n      // Single line link\n      this._fillBottomLineAtCells(e.x1, e.y1, e.x2 - e.x1);\n    } else {\n      // Multi-line link\n      this._fillBottomLineAtCells(e.x1, e.y1, e.cols - e.x1);\n      for (let y = e.y1 + 1; y < e.y2; y++) {\n        this._fillBottomLineAtCells(0, y, e.cols);\n      }\n      this._fillBottomLineAtCells(0, e.y2, e.x2);\n    }\n    this._state = e;\n  }\n\n  private _handleHideLinkUnderline(e: ILinkifierEvent): void {\n    this._clearCurrentLink();\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\nimport { BaseRenderLayer } from './BaseRenderLayer';\nimport { IBufferService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { Terminal } from 'xterm';\n\ninterface ISelectionState {\n  start?: [number, number];\n  end?: [number, number];\n  columnSelectMode?: boolean;\n  ydisp?: number;\n}\n\nexport class SelectionRenderLayer extends BaseRenderLayer {\n  private _state!: ISelectionState;\n\n  constructor(\n    terminal: Terminal,\n    container: HTMLElement,\n    zIndex: number,\n    bufferService: IBufferService,\n    coreBrowserService: ICoreBrowserService,\n    decorationService: IDecorationService,\n    optionsService: IOptionsService,\n    themeService: IThemeService\n  ) {\n    super(terminal, container, 'selection', zIndex, true, themeService, bufferService, optionsService, decorationService, coreBrowserService);\n    this._clearState();\n  }\n\n  private _clearState(): void {\n    this._state = {\n      start: undefined,\n      end: undefined,\n      columnSelectMode: undefined,\n      ydisp: undefined\n    };\n  }\n\n  public resize(dim: IRenderDimensions): void {\n    super.resize(dim);\n    // On resize use the base render layer's cached selection values since resize clears _state\n    // inside reset.\n    if (this._selectionModel.selectionStart && this._selectionModel.selectionEnd) {\n      this._clearState();\n      this._redrawSelection(this._selectionModel.selectionStart, this._selectionModel.selectionEnd, this._selectionModel.columnSelectMode);\n    }\n  }\n\n  public reset(): void {\n    if (this._state.start && this._state.end) {\n      this._clearState();\n      this._clearAll();\n    }\n  }\n\n  public handleBlur(): void {\n    this.reset();\n    this._redrawSelection(this._selectionModel.selectionStart, this._selectionModel.selectionEnd, this._selectionModel.columnSelectMode);\n  }\n\n  public handleFocus(): void {\n    this.reset();\n    this._redrawSelection(this._selectionModel.selectionStart, this._selectionModel.selectionEnd, this._selectionModel.columnSelectMode);\n  }\n\n  public handleSelectionChanged(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean): void {\n    super.handleSelectionChanged(start, end, columnSelectMode);\n    this._redrawSelection(start, end, columnSelectMode);\n  }\n\n  private _redrawSelection(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean): void {\n    // Selection has not changed\n    if (!this._didStateChange(start, end, columnSelectMode, this._bufferService.buffer.ydisp)) {\n      return;\n    }\n\n    // Remove all selections\n    this._clearAll();\n\n    // Selection does not exist\n    if (!start || !end) {\n      this._clearState();\n      return;\n    }\n\n    // Translate from buffer position to viewport position\n    const viewportStartRow = start[1] - this._bufferService.buffer.ydisp;\n    const viewportEndRow = end[1] - this._bufferService.buffer.ydisp;\n    const viewportCappedStartRow = Math.max(viewportStartRow, 0);\n    const viewportCappedEndRow = Math.min(viewportEndRow, this._bufferService.rows - 1);\n\n    // No need to draw the selection\n    if (viewportCappedStartRow >= this._bufferService.rows || viewportCappedEndRow < 0) {\n      this._state.ydisp = this._bufferService.buffer.ydisp;\n      return;\n    }\n\n    this._ctx.fillStyle = (this._coreBrowserService.isFocused\n      ? this._themeService.colors.selectionBackgroundTransparent\n      : this._themeService.colors.selectionInactiveBackgroundTransparent).css;\n\n    if (columnSelectMode) {\n      const startCol = start[0];\n      const width = end[0] - startCol;\n      const height = viewportCappedEndRow - viewportCappedStartRow + 1;\n      this._fillCells(startCol, viewportCappedStartRow, width, height);\n    } else {\n      // Draw first row\n      const startCol = viewportStartRow === viewportCappedStartRow ? start[0] : 0;\n      const startRowEndCol = viewportCappedStartRow === viewportEndRow ? end[0] : this._bufferService.cols;\n      this._fillCells(startCol, viewportCappedStartRow, startRowEndCol - startCol, 1);\n\n      // Draw middle rows\n      const middleRowsCount = Math.max(viewportCappedEndRow - viewportCappedStartRow - 1, 0);\n      this._fillCells(0, viewportCappedStartRow + 1, this._bufferService.cols, middleRowsCount);\n\n      // Draw final row\n      if (viewportCappedStartRow !== viewportCappedEndRow) {\n        // Only draw viewportEndRow if it's not the same as viewportStartRow\n        const endCol = viewportEndRow === viewportCappedEndRow ? end[0] : this._bufferService.cols;\n        this._fillCells(0, viewportCappedEndRow, endCol, 1);\n      }\n    }\n\n    // Save state for next render\n    this._state.start = [start[0], start[1]];\n    this._state.end = [end[0], end[1]];\n    this._state.columnSelectMode = columnSelectMode;\n    this._state.ydisp = this._bufferService.buffer.ydisp;\n  }\n\n  private _didStateChange(start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean, ydisp: number): boolean {\n    return !this._areCoordinatesEqual(start, this._state.start) ||\n      !this._areCoordinatesEqual(end, this._state.end) ||\n      columnSelectMode !== this._state.columnSelectMode ||\n      ydisp !== this._state.ydisp;\n  }\n\n  private _areCoordinatesEqual(coord1: [number, number] | undefined, coord2: [number, number] | undefined): boolean {\n    if (!coord1 || !coord2) {\n      return false;\n    }\n\n    return coord1[0] === coord2[0] && coord1[1] === coord2[1];\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\nimport { JoinedCellData } from 'browser/services/CharacterJoinerService';\nimport { ICharacterJoinerService, ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { CharData, ICellData } from 'common/Types';\nimport { AttributeData } from 'common/buffer/AttributeData';\nimport { CellData } from 'common/buffer/CellData';\nimport { Content, NULL_CELL_CODE } from 'common/buffer/Constants';\nimport { IBufferService, IDecorationService, IOptionsService } from 'common/services/Services';\nimport { Terminal } from 'xterm';\nimport { BaseRenderLayer } from './BaseRenderLayer';\nimport { GridCache } from './GridCache';\n\n/**\n * This CharData looks like a null character, which will forc a clear and render\n * when the character changes (a regular space ' ' character may not as it's\n * drawn state is a cleared cell).\n */\n// const OVERLAP_OWNED_CHAR_DATA: CharData = [null, '', 0, -1];\n\nexport class TextRenderLayer extends BaseRenderLayer {\n  private _state: GridCache<CharData>;\n  private _characterWidth: number = 0;\n  private _characterFont: string = '';\n  private _characterOverlapCache: { [key: string]: boolean } = {};\n  private _workCell = new CellData();\n\n  constructor(\n    terminal: Terminal,\n    container: HTMLElement,\n    zIndex: number,\n    alpha: boolean,\n    bufferService: IBufferService,\n    optionsService: IOptionsService,\n    private readonly _characterJoinerService: ICharacterJoinerService,\n    decorationService: IDecorationService,\n    coreBrowserService: ICoreBrowserService,\n    themeService: IThemeService\n  ) {\n    super(terminal, container, 'text', zIndex, alpha, themeService, bufferService, optionsService, decorationService, coreBrowserService);\n    this._state = new GridCache<CharData>();\n    this.register(optionsService.onSpecificOptionChange('allowTransparency', value => this._setTransparency(value)));\n  }\n\n  public resize(dim: IRenderDimensions): void {\n    super.resize(dim);\n\n    // Clear the character width cache if the font or width has changed\n    const terminalFont = this._getFont(false, false);\n    if (this._characterWidth !== dim.device.char.width || this._characterFont !== terminalFont) {\n      this._characterWidth = dim.device.char.width;\n      this._characterFont = terminalFont;\n      this._characterOverlapCache = {};\n    }\n    // Resizing the canvas discards the contents of the canvas so clear state\n    this._state.clear();\n    this._state.resize(this._bufferService.cols, this._bufferService.rows);\n  }\n\n  public reset(): void {\n    this._state.clear();\n    this._clearAll();\n  }\n\n  private _forEachCell(\n    firstRow: number,\n    lastRow: number,\n    callback: (\n      cell: ICellData,\n      x: number,\n      y: number\n    ) => void\n  ): void {\n    for (let y = firstRow; y <= lastRow; y++) {\n      const row = y + this._bufferService.buffer.ydisp;\n      const line = this._bufferService.buffer.lines.get(row);\n      const joinedRanges = this._characterJoinerService.getJoinedCharacters(row);\n      for (let x = 0; x < this._bufferService.cols; x++) {\n        line!.loadCell(x, this._workCell);\n        let cell = this._workCell;\n\n        // If true, indicates that the current character(s) to draw were joined.\n        let isJoined = false;\n        let lastCharX = x;\n\n        // The character to the left is a wide character, drawing is owned by\n        // the char at x-1\n        if (cell.getWidth() === 0) {\n          continue;\n        }\n\n        // exit early for NULL and SP\n        // NOTE: commented out due to #4120 (needs a more clever patch to keep things performant)\n        // const code = cell.getCode();\n        // if (code === 0 || code === 32) {\n        //  continue;\n        // }\n\n        // Process any joined character ranges as needed. Because of how the\n        // ranges are produced, we know that they are valid for the characters\n        // and attributes of our input.\n        if (joinedRanges.length > 0 && x === joinedRanges[0][0]) {\n          isJoined = true;\n          const range = joinedRanges.shift()!;\n\n          // We already know the exact start and end column of the joined range,\n          // so we get the string and width representing it directly\n          cell = new JoinedCellData(\n            this._workCell,\n            line!.translateToString(true, range[0], range[1]),\n            range[1] - range[0]\n          );\n\n          // Skip over the cells occupied by this range in the loop\n          lastCharX = range[1] - 1;\n        }\n\n        // If the character is an overlapping char and the character to the\n        // right is a space, take ownership of the cell to the right. We skip\n        // this check for joined characters because their rendering likely won't\n        // yield the same result as rendering the last character individually.\n        if (!isJoined && this._isOverlapping(cell)) {\n          // If the character is overlapping, we want to force a re-render on every\n          // frame. This is specifically to work around the case where two\n          // overlaping chars `a` and `b` are adjacent, the cursor is moved to b and a\n          // space is added. Without this, the first half of `b` would never\n          // get removed, and `a` would not re-render because it thinks it's\n          // already in the correct state.\n          // this._state.cache[x][y] = OVERLAP_OWNED_CHAR_DATA;\n          if (lastCharX < line!.length - 1 && line!.getCodePoint(lastCharX + 1) === NULL_CELL_CODE) {\n            // patch width to 2\n            cell.content &= ~Content.WIDTH_MASK;\n            cell.content |= 2 << Content.WIDTH_SHIFT;\n            // this._clearChar(x + 1, y);\n            // The overlapping char's char data will force a clear and render when the\n            // overlapping char is no longer to the left of the character and also when\n            // the space changes to another character.\n            // this._state.cache[x + 1][y] = OVERLAP_OWNED_CHAR_DATA;\n          }\n        }\n\n        callback(\n          cell,\n          x,\n          y\n        );\n\n        x = lastCharX;\n      }\n    }\n  }\n\n  /**\n   * Draws the background for a specified range of columns. Tries to batch adjacent cells of the\n   * same color together to reduce draw calls.\n   */\n  private _drawBackground(firstRow: number, lastRow: number): void {\n    const ctx = this._ctx;\n    const cols = this._bufferService.cols;\n    let startX: number = 0;\n    let startY: number = 0;\n    let prevFillStyle: string | null = null;\n\n    ctx.save();\n\n    this._forEachCell(firstRow, lastRow, (cell, x, y) => {\n      // libvte and xterm both draw the background (but not foreground) of invisible characters,\n      // so we should too.\n      let nextFillStyle = null; // null represents default background color\n\n      if (cell.isInverse()) {\n        if (cell.isFgDefault()) {\n          nextFillStyle = this._themeService.colors.foreground.css;\n        } else if (cell.isFgRGB()) {\n          nextFillStyle = `rgb(${AttributeData.toColorRGB(cell.getFgColor()).join(',')})`;\n        } else {\n          nextFillStyle = this._themeService.colors.ansi[cell.getFgColor()].css;\n        }\n      } else if (cell.isBgRGB()) {\n        nextFillStyle = `rgb(${AttributeData.toColorRGB(cell.getBgColor()).join(',')})`;\n      } else if (cell.isBgPalette()) {\n        nextFillStyle = this._themeService.colors.ansi[cell.getBgColor()].css;\n      }\n\n      // Get any decoration foreground/background overrides, this must be fetched before the early\n      // exist but applied after inverse\n      let isTop = false;\n      this._decorationService.forEachDecorationAtCell(x, this._bufferService.buffer.ydisp + y, undefined, d => {\n        if (d.options.layer !== 'top' && isTop) {\n          return;\n        }\n        if (d.backgroundColorRGB) {\n          nextFillStyle = d.backgroundColorRGB.css;\n        }\n        isTop = d.options.layer === 'top';\n      });\n\n      if (prevFillStyle === null) {\n        // This is either the first iteration, or the default background was set. Either way, we\n        // don't need to draw anything.\n        startX = x;\n        startY = y;\n      }\n\n      if (y !== startY) {\n        // our row changed, draw the previous row\n        ctx.fillStyle = prevFillStyle || '';\n        this._fillCells(startX, startY, cols - startX, 1);\n        startX = x;\n        startY = y;\n      } else if (prevFillStyle !== nextFillStyle) {\n        // our color changed, draw the previous characters in this row\n        ctx.fillStyle = prevFillStyle || '';\n        this._fillCells(startX, startY, x - startX, 1);\n        startX = x;\n        startY = y;\n      }\n\n      prevFillStyle = nextFillStyle;\n    });\n\n    // flush the last color we encountered\n    if (prevFillStyle !== null) {\n      ctx.fillStyle = prevFillStyle;\n      this._fillCells(startX, startY, cols - startX, 1);\n    }\n\n    ctx.restore();\n  }\n\n  private _drawForeground(firstRow: number, lastRow: number): void {\n    this._forEachCell(firstRow, lastRow, (cell, x, y) => this._drawChars(cell, x, y));\n  }\n\n  public handleGridChanged(firstRow: number, lastRow: number): void {\n    // Resize has not been called yet\n    if (this._state.cache.length === 0) {\n      return;\n    }\n\n    if (this._charAtlas) {\n      this._charAtlas.beginFrame();\n    }\n\n    this._clearCells(0, firstRow, this._bufferService.cols, lastRow - firstRow + 1);\n    this._drawBackground(firstRow, lastRow);\n    this._drawForeground(firstRow, lastRow);\n  }\n\n  /**\n   * Whether a character is overlapping to the next cell.\n   */\n  private _isOverlapping(cell: ICellData): boolean {\n    // Only single cell characters can be overlapping, rendering issues can\n    // occur without this check\n    if (cell.getWidth() !== 1) {\n      return false;\n    }\n\n    // We assume that any ascii character will not overlap\n    if (cell.getCode() < 256) {\n      return false;\n    }\n\n    const chars = cell.getChars();\n\n    // Deliver from cache if available\n    if (this._characterOverlapCache.hasOwnProperty(chars)) {\n      return this._characterOverlapCache[chars];\n    }\n\n    // Setup the font\n    this._ctx.save();\n    this._ctx.font = this._characterFont;\n\n    // Measure the width of the character, but Math.floor it\n    // because that is what the renderer does when it calculates\n    // the character dimensions we are comparing against\n    const overlaps = Math.floor(this._ctx.measureText(chars).width) > this._characterWidth;\n\n    // Restore the original context\n    this._ctx.restore();\n\n    // Cache and return\n    this._characterOverlapCache[chars] = overlaps;\n    return overlaps;\n  }\n}\n", "import { ISelectionRenderModel } from 'browser/renderer/shared/Types';\nimport { ICoreBrowserService, IThemeService } from 'browser/services/Services';\nimport { ReadonlyColorSet } from 'browser/Types';\nimport { Attributes, BgFlags, FgFlags } from 'common/buffer/Constants';\nimport { IDecorationService } from 'common/services/Services';\nimport { ICellData } from 'common/Types';\nimport { Terminal } from 'xterm';\n\n// Work variables to avoid garbage collection\nlet $fg = 0;\nlet $bg = 0;\nlet $hasFg = false;\nlet $hasBg = false;\nlet $isSelected = false;\nlet $colors: ReadonlyColorSet | undefined;\n\nexport class CellColorResolver {\n  /**\n   * The shared result of the {@link resolve} call. This is only safe to use immediately after as\n   * any other calls will share object.\n   */\n  public readonly result: { fg: number, bg: number, ext: number } = {\n    fg: 0,\n    bg: 0,\n    ext: 0\n  };\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _selectionRenderModel: ISelectionRenderModel,\n    private readonly _decorationService: IDecorationService,\n    private readonly _coreBrowserService: ICoreBrowserService,\n    private readonly _themeService: IThemeService\n  ) {\n  }\n\n  /**\n   * Resolves colors for the cell, putting the result into the shared {@link result}. This resolves\n   * overrides, inverse and selection for the cell which can then be used to feed into the renderer.\n   */\n  public resolve(cell: ICellData, x: number, y: number): void {\n    this.result.bg = cell.bg;\n    this.result.fg = cell.fg;\n    this.result.ext = cell.bg & BgFlags.HAS_EXTENDED ? cell.extended.ext : 0;\n    // Get any foreground/background overrides, this happens on the model to avoid spreading\n    // override logic throughout the different sub-renderers\n\n    // Reset overrides work variables\n    $bg = 0;\n    $fg = 0;\n    $hasBg = false;\n    $hasFg = false;\n    $isSelected = false;\n    $colors = this._themeService.colors;\n\n    // Apply decorations on the bottom layer\n    this._decorationService.forEachDecorationAtCell(x, y, 'bottom', d => {\n      if (d.backgroundColorRGB) {\n        $bg = d.backgroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasBg = true;\n      }\n      if (d.foregroundColorRGB) {\n        $fg = d.foregroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    });\n\n    // Apply the selection color if needed\n    $isSelected = this._selectionRenderModel.isCellSelected(this._terminal, x, y);\n    if ($isSelected) {\n      $bg = (this._coreBrowserService.isFocused ? $colors.selectionBackgroundOpaque : $colors.selectionInactiveBackgroundOpaque).rgba >> 8 & 0xFFFFFF;\n      $hasBg = true;\n      if ($colors.selectionForeground) {\n        $fg = $colors.selectionForeground.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    }\n\n    // Apply decorations on the top layer\n    this._decorationService.forEachDecorationAtCell(x, y, 'top', d => {\n      if (d.backgroundColorRGB) {\n        $bg = d.backgroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasBg = true;\n      }\n      if (d.foregroundColorRGB) {\n        $fg = d.foregroundColorRGB.rgba >> 8 & 0xFFFFFF;\n        $hasFg = true;\n      }\n    });\n\n    // Convert any overrides from rgba to the fg/bg packed format. This resolves the inverse flag\n    // ahead of time in order to use the correct cache key\n    if ($hasBg) {\n      if ($isSelected) {\n        // Non-RGB attributes from model + force non-dim + override + force RGB color mode\n        $bg = (cell.bg & ~Attributes.RGB_MASK & ~BgFlags.DIM) | $bg | Attributes.CM_RGB;\n      } else {\n        // Non-RGB attributes from model + override + force RGB color mode\n        $bg = (cell.bg & ~Attributes.RGB_MASK) | $bg | Attributes.CM_RGB;\n      }\n    }\n    if ($hasFg) {\n      // Non-RGB attributes from model + force disable inverse + override + force RGB color mode\n      $fg = (cell.fg & ~Attributes.RGB_MASK & ~FgFlags.INVERSE) | $fg | Attributes.CM_RGB;\n    }\n\n    // Handle case where inverse was specified by only one of bg override or fg override was set,\n    // resolving the other inverse color and setting the inverse flag if needed.\n    if (this.result.fg & FgFlags.INVERSE) {\n      if ($hasBg && !$hasFg) {\n        // Resolve bg color type (default color has a different meaning in fg vs bg)\n        if ((this.result.bg & Attributes.CM_MASK) === Attributes.CM_DEFAULT) {\n          $fg = (this.result.fg & ~(Attributes.RGB_MASK | FgFlags.INVERSE | Attributes.CM_MASK)) | (($colors.background.rgba >> 8 & 0xFFFFFF) & Attributes.RGB_MASK) | Attributes.CM_RGB;\n        } else {\n          $fg = (this.result.fg & ~(Attributes.RGB_MASK | FgFlags.INVERSE | Attributes.CM_MASK)) | this.result.bg & (Attributes.RGB_MASK | Attributes.CM_MASK);\n        }\n        $hasFg = true;\n      }\n      if (!$hasBg && $hasFg) {\n        // Resolve bg color type (default color has a different meaning in fg vs bg)\n        if ((this.result.fg & Attributes.CM_MASK) === Attributes.CM_DEFAULT) {\n          $bg = (this.result.bg & ~(Attributes.RGB_MASK | Attributes.CM_MASK)) | (($colors.foreground.rgba >> 8 & 0xFFFFFF) & Attributes.RGB_MASK) | Attributes.CM_RGB;\n        } else {\n          $bg = (this.result.bg & ~(Attributes.RGB_MASK | Attributes.CM_MASK)) | this.result.fg & (Attributes.RGB_MASK | Attributes.CM_MASK);\n        }\n        $hasBg = true;\n      }\n    }\n\n    // Release object\n    $colors = undefined;\n\n    // Use the override if it exists\n    this.result.bg = $hasBg ? $bg : this.result.bg;\n    this.result.fg = $hasFg ? $fg : this.result.fg;\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { TextureAtlas } from 'browser/renderer/shared/TextureAtlas';\nimport { ITerminalOptions, Terminal } from 'xterm';\nimport { ITerminal, ReadonlyColorSet } from 'browser/Types';\nimport { ICharAtlasConfig, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { generateConfig, configEquals } from 'browser/renderer/shared/CharAtlasUtils';\n\ninterface ITextureAtlasCacheEntry {\n  atlas: ITextureAtlas;\n  config: ICharAtlasConfig;\n  // N.B. This implementation potentially holds onto copies of the terminal forever, so\n  // this may cause memory leaks.\n  ownedBy: Terminal[];\n}\n\nconst charAtlasCache: ITextureAtlasCacheEntry[] = [];\n\n/**\n * Acquires a char atlas, either generating a new one or returning an existing\n * one that is in use by another terminal.\n */\nexport function acquireTextureAtlas(\n  terminal: Terminal,\n  options: Required<ITerminalOptions>,\n  colors: ReadonlyColorSet,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  deviceCharWidth: number,\n  deviceCharHeight: number,\n  devicePixelRatio: number\n): ITextureAtlas {\n  const newConfig = generateConfig(deviceCellWidth, deviceCellHeight, deviceCharWidth, deviceCharHeight, options, colors, devicePixelRatio);\n\n  // Check to see if the terminal already owns this config\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const entry = charAtlasCache[i];\n    const ownedByIndex = entry.ownedBy.indexOf(terminal);\n    if (ownedByIndex >= 0) {\n      if (configEquals(entry.config, newConfig)) {\n        return entry.atlas;\n      }\n      // The configs differ, release the terminal from the entry\n      if (entry.ownedBy.length === 1) {\n        entry.atlas.dispose();\n        charAtlasCache.splice(i, 1);\n      } else {\n        entry.ownedBy.splice(ownedByIndex, 1);\n      }\n      break;\n    }\n  }\n\n  // Try match a char atlas from the cache\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const entry = charAtlasCache[i];\n    if (configEquals(entry.config, newConfig)) {\n      // Add the terminal to the cache entry and return\n      entry.ownedBy.push(terminal);\n      return entry.atlas;\n    }\n  }\n\n  const core: ITerminal = (terminal as any)._core;\n  const newEntry: ITextureAtlasCacheEntry = {\n    atlas: new TextureAtlas(document, newConfig, core.unicodeService),\n    config: newConfig,\n    ownedBy: [terminal]\n  };\n  charAtlasCache.push(newEntry);\n  return newEntry.atlas;\n}\n\n/**\n * Removes a terminal reference from the cache, allowing its memory to be freed.\n * @param terminal The terminal to remove.\n */\nexport function removeTerminalFromCache(terminal: Terminal): void {\n  for (let i = 0; i < charAtlasCache.length; i++) {\n    const index = charAtlasCache[i].ownedBy.indexOf(terminal);\n    if (index !== -1) {\n      if (charAtlasCache[i].ownedBy.length === 1) {\n        // Remove the cache entry if it's the only terminal\n        charAtlasCache[i].atlas.dispose();\n        charAtlasCache.splice(i, 1);\n      } else {\n        // Remove the reference from the cache entry\n        charAtlasCache[i].ownedBy.splice(index, 1);\n      }\n      break;\n    }\n  }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICharAtlasConfig } from './Types';\nimport { Attributes } from 'common/buffer/Constants';\nimport { ITerminalOptions } from 'xterm';\nimport { IColorSet, ReadonlyColorSet } from 'browser/Types';\nimport { NULL_COLOR } from 'common/Color';\n\nexport function generateConfig(deviceCellWidth: number, deviceCellHeight: number, deviceCharWidth: number, deviceCharHeight: number, options: Required<ITerminalOptions>, colors: ReadonlyColorSet, devicePixelRatio: number): ICharAtlasConfig {\n  // null out some fields that don't matter\n  const clonedColors: IColorSet = {\n    foreground: colors.foreground,\n    background: colors.background,\n    cursor: NULL_COLOR,\n    cursorAccent: NULL_COLOR,\n    selectionForeground: NULL_COLOR,\n    selectionBackgroundTransparent: NULL_COLOR,\n    selectionBackgroundOpaque: NULL_COLOR,\n    selectionInactiveBackgroundTransparent: NULL_COLOR,\n    selectionInactiveBackgroundOpaque: NULL_COLOR,\n    // For the static char atlas, we only use the first 16 colors, but we need all 256 for the\n    // dynamic character atlas.\n    ansi: colors.ansi.slice(),\n    contrastCache: colors.contrastCache,\n    halfContrastCache: colors.halfContrastCache\n  };\n  return {\n    customGlyphs: options.customGlyphs,\n    devicePixelRatio,\n    letterSpacing: options.letterSpacing,\n    lineHeight: options.lineHeight,\n    deviceCellWidth: deviceCellWidth,\n    deviceCellHeight: deviceCellHeight,\n    deviceCharWidth: deviceCharWidth,\n    deviceCharHeight: deviceCharHeight,\n    fontFamily: options.fontFamily,\n    fontSize: options.fontSize,\n    fontWeight: options.fontWeight,\n    fontWeightBold: options.fontWeightBold,\n    allowTransparency: options.allowTransparency,\n    drawBoldTextInBrightColors: options.drawBoldTextInBrightColors,\n    minimumContrastRatio: options.minimumContrastRatio,\n    colors: clonedColors\n  };\n}\n\nexport function configEquals(a: ICharAtlasConfig, b: ICharAtlasConfig): boolean {\n  for (let i = 0; i < a.colors.ansi.length; i++) {\n    if (a.colors.ansi[i].rgba !== b.colors.ansi[i].rgba) {\n      return false;\n    }\n  }\n  return a.devicePixelRatio === b.devicePixelRatio &&\n      a.customGlyphs === b.customGlyphs &&\n      a.lineHeight === b.lineHeight &&\n      a.letterSpacing === b.letterSpacing &&\n      a.fontFamily === b.fontFamily &&\n      a.fontSize === b.fontSize &&\n      a.fontWeight === b.fontWeight &&\n      a.fontWeightBold === b.fontWeightBold &&\n      a.allowTransparency === b.allowTransparency &&\n      a.deviceCharWidth === b.deviceCharWidth &&\n      a.deviceCharHeight === b.deviceCharHeight &&\n      a.drawBoldTextInBrightColors === b.drawBoldTextInBrightColors &&\n      a.minimumContrastRatio === b.minimumContrastRatio &&\n      a.colors.foreground.rgba === b.colors.foreground.rgba &&\n      a.colors.background.rgba === b.colors.background.rgba;\n}\n\nexport function is256Color(colorCode: number): boolean {\n  return (colorCode & Attributes.CM_MASK) === Attributes.CM_P16 || (colorCode & Attributes.CM_MASK) === Attributes.CM_P256;\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isFirefox, isLegacyEdge } from 'common/Platform';\n\nexport const INVERTED_DEFAULT_COLOR = 257;\n\nexport const DIM_OPACITY = 0.5;\n// The text baseline is set conditionally by browser. Using 'ideographic' for Firefox or Legacy Edge\n// would result in truncated text (Issue 3353). Using 'bottom' for Chrome would result in slightly\n// unaligned Powerline fonts (PR 3356#issuecomment-850928179).\nexport const TEXT_BASELINE: CanvasTextBaseline = isFirefox || isLegacyEdge ? 'bottom' : 'ideographic';\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICoreBrowserService } from 'browser/services/Services';\n\n/**\n * The time between cursor blinks.\n */\nconst BLINK_INTERVAL = 600;\n\nexport class CursorBlinkStateManager {\n  public isCursorVisible: boolean;\n\n  private _animationFrame: number | undefined;\n  private _blinkStartTimeout: number | undefined;\n  private _blinkInterval: number | undefined;\n\n  /**\n   * The time at which the animation frame was restarted, this is used on the\n   * next render to restart the timers so they don't need to restart the timers\n   * multiple times over a short period.\n   */\n  private _animationTimeRestarted: number | undefined;\n\n  constructor(\n    private _renderCallback: () => void,\n    private _coreBrowserService: ICoreBrowserService\n  ) {\n    this.isCursorVisible = true;\n    if (this._coreBrowserService.isFocused) {\n      this._restartInterval();\n    }\n  }\n\n  public get isPaused(): boolean { return !(this._blinkStartTimeout || this._blinkInterval); }\n\n  public dispose(): void {\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n    if (this._blinkStartTimeout) {\n      this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout);\n      this._blinkStartTimeout = undefined;\n    }\n    if (this._animationFrame) {\n      this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame);\n      this._animationFrame = undefined;\n    }\n  }\n\n  public restartBlinkAnimation(): void {\n    if (this.isPaused) {\n      return;\n    }\n    // Save a timestamp so that the restart can be done on the next interval\n    this._animationTimeRestarted = Date.now();\n    // Force a cursor render to ensure it's visible and in the correct position\n    this.isCursorVisible = true;\n    if (!this._animationFrame) {\n      this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n        this._renderCallback();\n        this._animationFrame = undefined;\n      });\n    }\n  }\n\n  private _restartInterval(timeToStart: number = BLINK_INTERVAL): void {\n    // Clear any existing interval\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n\n    // Setup the initial timeout which will hide the cursor, this is done before\n    // the regular interval is setup in order to support restarting the blink\n    // animation in a lightweight way (without thrashing clearInterval and\n    // setInterval).\n    this._blinkStartTimeout = this._coreBrowserService.window.setTimeout(() => {\n      // Check if another animation restart was requested while this was being\n      // started\n      if (this._animationTimeRestarted) {\n        const time = BLINK_INTERVAL - (Date.now() - this._animationTimeRestarted);\n        this._animationTimeRestarted = undefined;\n        if (time > 0) {\n          this._restartInterval(time);\n          return;\n        }\n      }\n\n      // Hide the cursor\n      this.isCursorVisible = false;\n      this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n        this._renderCallback();\n        this._animationFrame = undefined;\n      });\n\n      // Setup the blink interval\n      this._blinkInterval = this._coreBrowserService.window.setInterval(() => {\n        // Adjust the animation time if it was restarted\n        if (this._animationTimeRestarted) {\n          // calc time diff\n          // Make restart interval do a setTimeout initially?\n          const time = BLINK_INTERVAL - (Date.now() - this._animationTimeRestarted);\n          this._animationTimeRestarted = undefined;\n          this._restartInterval(time);\n          return;\n        }\n\n        // Invert visibility and render\n        this.isCursorVisible = !this.isCursorVisible;\n        this._animationFrame = this._coreBrowserService.window.requestAnimationFrame(() => {\n          this._renderCallback();\n          this._animationFrame = undefined;\n        });\n      }, BLINK_INTERVAL);\n    }, timeToStart);\n  }\n\n  public pause(): void {\n    this.isCursorVisible = true;\n    if (this._blinkInterval) {\n      this._coreBrowserService.window.clearInterval(this._blinkInterval);\n      this._blinkInterval = undefined;\n    }\n    if (this._blinkStartTimeout) {\n      this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout);\n      this._blinkStartTimeout = undefined;\n    }\n    if (this._animationFrame) {\n      this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame);\n      this._animationFrame = undefined;\n    }\n  }\n\n  public resume(): void {\n    // Clear out any existing timers just in case\n    this.pause();\n\n    this._animationTimeRestarted = undefined;\n    this._restartInterval();\n    this.restartBlinkAnimation();\n  }\n}\n", "/**\n * Copyright (c) 2021 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\n\ninterface IBlockVector {\n  x: number;\n  y: number;\n  w: number;\n  h: number;\n}\n\nexport const blockElementDefinitions: { [index: string]: IBlockVector[] | undefined } = {\n  // Block elements (0x2580-0x2590)\n  '▀': [{ x: 0, y: 0, w: 8, h: 4 }], // UPPER HALF BLOCK\n  '▁': [{ x: 0, y: 7, w: 8, h: 1 }], // LOWER ONE EIGHTH BLOCK\n  '▂': [{ x: 0, y: 6, w: 8, h: 2 }], // LOWER ONE QUARTER BLOCK\n  '▃': [{ x: 0, y: 5, w: 8, h: 3 }], // LOWER THREE EIGHTHS BLOCK\n  '▄': [{ x: 0, y: 4, w: 8, h: 4 }], // LOWER HALF BLOCK\n  '▅': [{ x: 0, y: 3, w: 8, h: 5 }], // LOWER FIVE EIGHTHS BLOCK\n  '▆': [{ x: 0, y: 2, w: 8, h: 6 }], // LOWER THREE QUARTERS BLOCK\n  '▇': [{ x: 0, y: 1, w: 8, h: 7 }], // LOWER SEVEN EIGHTHS BLOCK\n  '█': [{ x: 0, y: 0, w: 8, h: 8 }], // FULL BLOCK\n  '▉': [{ x: 0, y: 0, w: 7, h: 8 }], // LEFT SEVEN EIGHTHS BLOCK\n  '▊': [{ x: 0, y: 0, w: 6, h: 8 }], // LEFT THREE QUARTERS BLOCK\n  '▋': [{ x: 0, y: 0, w: 5, h: 8 }], // LEFT FIVE EIGHTHS BLOCK\n  '▌': [{ x: 0, y: 0, w: 4, h: 8 }], // LEFT HALF BLOCK\n  '▍': [{ x: 0, y: 0, w: 3, h: 8 }], // LEFT THREE EIGHTHS BLOCK\n  '▎': [{ x: 0, y: 0, w: 2, h: 8 }], // LEFT ONE QUARTER BLOCK\n  '▏': [{ x: 0, y: 0, w: 1, h: 8 }], // LEFT ONE EIGHTH BLOCK\n  '▐': [{ x: 4, y: 0, w: 4, h: 8 }], // RIGHT HALF BLOCK\n\n  // Block elements (0x2594-0x2595)\n  '▔': [{ x: 0, y: 0, w: 8, h: 1 }], // UPPER ONE EIGHTH BLOCK\n  '▕': [{ x: 7, y: 0, w: 1, h: 8 }], // RIGHT ONE EIGHTH BLOCK\n\n  // Terminal graphic characters (0x2596-0x259F)\n  '▖': [{ x: 0, y: 4, w: 4, h: 4 }],                             // QUADRANT LOWER LEFT\n  '▗': [{ x: 4, y: 4, w: 4, h: 4 }],                             // QUADRANT LOWER RIGHT\n  '▘': [{ x: 0, y: 0, w: 4, h: 4 }],                             // QUADRANT UPPER LEFT\n  '▙': [{ x: 0, y: 0, w: 4, h: 8 }, { x: 0, y: 4, w: 8, h: 4 }], // QUADRANT UPPER LEFT AND LOWER LEFT AND LOWER RIGHT\n  '▚': [{ x: 0, y: 0, w: 4, h: 4 }, { x: 4, y: 4, w: 4, h: 4 }], // QUADRANT UPPER LEFT AND LOWER RIGHT\n  '▛': [{ x: 0, y: 0, w: 4, h: 8 }, { x: 4, y: 0, w: 4, h: 4 }], // QUADRANT UPPER LEFT AND UPPER RIGHT AND LOWER LEFT\n  '▜': [{ x: 0, y: 0, w: 8, h: 4 }, { x: 4, y: 0, w: 4, h: 8 }], // QUADRANT UPPER LEFT AND UPPER RIGHT AND LOWER RIGHT\n  '▝': [{ x: 4, y: 0, w: 4, h: 4 }],                             // QUADRANT UPPER RIGHT\n  '▞': [{ x: 4, y: 0, w: 4, h: 4 }, { x: 0, y: 4, w: 4, h: 4 }], // QUADRANT UPPER RIGHT AND LOWER LEFT\n  '▟': [{ x: 4, y: 0, w: 4, h: 8 }, { x: 0, y: 4, w: 8, h: 4 }], // QUADRANT UPPER RIGHT AND LOWER LEFT AND LOWER RIGHT\n\n  // VERTICAL ONE EIGHTH BLOCK-2 through VERTICAL ONE EIGHTH BLOCK-7\n  '\\u{1FB70}': [{ x: 1, y: 0, w: 1, h: 8 }],\n  '\\u{1FB71}': [{ x: 2, y: 0, w: 1, h: 8 }],\n  '\\u{1FB72}': [{ x: 3, y: 0, w: 1, h: 8 }],\n  '\\u{1FB73}': [{ x: 4, y: 0, w: 1, h: 8 }],\n  '\\u{1FB74}': [{ x: 5, y: 0, w: 1, h: 8 }],\n  '\\u{1FB75}': [{ x: 6, y: 0, w: 1, h: 8 }],\n\n  // HORIZONTAL ONE EIGHTH BLOCK-2 through HORIZONTAL ONE EIGHTH BLOCK-7\n  '\\u{1FB76}': [{ x: 0, y: 1, w: 8, h: 1 }],\n  '\\u{1FB77}': [{ x: 0, y: 2, w: 8, h: 1 }],\n  '\\u{1FB78}': [{ x: 0, y: 3, w: 8, h: 1 }],\n  '\\u{1FB79}': [{ x: 0, y: 4, w: 8, h: 1 }],\n  '\\u{1FB7A}': [{ x: 0, y: 5, w: 8, h: 1 }],\n  '\\u{1FB7B}': [{ x: 0, y: 6, w: 8, h: 1 }],\n\n  // LEFT AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB7C}': [{ x: 0, y: 0, w: 1, h: 8 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // LEFT AND UPPER ONE EIGHTH BLOCK\n  '\\u{1FB7D}': [{ x: 0, y: 0, w: 1, h: 8 }, { x: 0, y: 0, w: 8, h: 1 }],\n  // RIGHT AND UPPER ONE EIGHTH BLOCK\n  '\\u{1FB7E}': [{ x: 7, y: 0, w: 1, h: 8 }, { x: 0, y: 0, w: 8, h: 1 }],\n  // RIGHT AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB7F}': [{ x: 7, y: 0, w: 1, h: 8 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // UPPER AND LOWER ONE EIGHTH BLOCK\n  '\\u{1FB80}': [{ x: 0, y: 0, w: 8, h: 1 }, { x: 0, y: 7, w: 8, h: 1 }],\n  // HORIZONTAL ONE EIGHTH BLOCK-1358\n  '\\u{1FB81}': [{ x: 0, y: 0, w: 8, h: 1 }, { x: 0, y: 2, w: 8, h: 1 }, { x: 0, y: 4, w: 8, h: 1 }, { x: 0, y: 7, w: 8, h: 1 }],\n\n  // UPPER ONE QUARTER BLOCK\n  '\\u{1FB82}': [{ x: 0, y: 0, w: 8, h: 2 }],\n  // UPPER THREE EIGHTHS BLOCK\n  '\\u{1FB83}': [{ x: 0, y: 0, w: 8, h: 3 }],\n  // UPPER FIVE EIGHTHS BLOCK\n  '\\u{1FB84}': [{ x: 0, y: 0, w: 8, h: 5 }],\n  // UPPER THREE QUARTERS BLOCK\n  '\\u{1FB85}': [{ x: 0, y: 0, w: 8, h: 6 }],\n  // UPPER SEVEN EIGHTHS BLOCK\n  '\\u{1FB86}': [{ x: 0, y: 0, w: 8, h: 7 }],\n\n  // RIGHT ONE QUARTER BLOCK\n  '\\u{1FB87}': [{ x: 6, y: 0, w: 2, h: 8 }],\n  // RIGHT THREE EIGHTHS B0OCK\n  '\\u{1FB88}': [{ x: 5, y: 0, w: 3, h: 8 }],\n  // RIGHT FIVE EIGHTHS BL0CK\n  '\\u{1FB89}': [{ x: 3, y: 0, w: 5, h: 8 }],\n  // RIGHT THREE QUARTERS 0LOCK\n  '\\u{1FB8A}': [{ x: 2, y: 0, w: 6, h: 8 }],\n  // RIGHT SEVEN EIGHTHS B0OCK\n  '\\u{1FB8B}': [{ x: 1, y: 0, w: 7, h: 8 }],\n\n  // CHECKER BOARD FILL\n  '\\u{1FB95}': [\n    { x: 0, y: 0, w: 2, h: 2 }, { x: 4, y: 0, w: 2, h: 2 },\n    { x: 2, y: 2, w: 2, h: 2 }, { x: 6, y: 2, w: 2, h: 2 },\n    { x: 0, y: 4, w: 2, h: 2 }, { x: 4, y: 4, w: 2, h: 2 },\n    { x: 2, y: 6, w: 2, h: 2 }, { x: 6, y: 6, w: 2, h: 2 }\n  ],\n  // INVERSE CHECKER BOARD FILL\n  '\\u{1FB96}': [\n    { x: 2, y: 0, w: 2, h: 2 }, { x: 6, y: 0, w: 2, h: 2 },\n    { x: 0, y: 2, w: 2, h: 2 }, { x: 4, y: 2, w: 2, h: 2 },\n    { x: 2, y: 4, w: 2, h: 2 }, { x: 6, y: 4, w: 2, h: 2 },\n    { x: 0, y: 6, w: 2, h: 2 }, { x: 4, y: 6, w: 2, h: 2 }\n  ],\n  // HEAVY HORIZONTAL FILL (upper middle and lower one quarter block)\n  '\\u{1FB97}': [{ x: 0, y: 2, w: 8, h: 2 }, { x: 0, y: 6, w: 8, h: 2 }]\n};\n\ntype PatternDefinition = number[][];\n\n/**\n * Defines the repeating pattern used by special characters, the pattern is made up of a 2d array of\n * pixel values to be filled (1) or not filled (0).\n */\nconst patternCharacterDefinitions: { [key: string]: PatternDefinition | undefined } = {\n  // Shade characters (0x2591-0x2593)\n  '░': [ // LIGHT SHADE (25%)\n    [1, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 1, 0],\n    [0, 0, 0, 0]\n  ],\n  '▒': [ // MEDIUM SHADE (50%)\n    [1, 0],\n    [0, 0],\n    [0, 1],\n    [0, 0]\n  ],\n  '▓': [ // DARK SHADE (75%)\n    [0, 1],\n    [1, 1],\n    [1, 0],\n    [1, 1]\n  ]\n};\n\nconst enum Shapes {\n  /** │ */ TOP_TO_BOTTOM = 'M.5,0 L.5,1',\n  /** ─ */ LEFT_TO_RIGHT = 'M0,.5 L1,.5',\n\n  /** └ */ TOP_TO_RIGHT = 'M.5,0 L.5,.5 L1,.5',\n  /** ┘ */ TOP_TO_LEFT = 'M.5,0 L.5,.5 L0,.5',\n  /** ┐ */ LEFT_TO_BOTTOM = 'M0,.5 L.5,.5 L.5,1',\n  /** ┌ */ RIGHT_TO_BOTTOM = 'M0.5,1 L.5,.5 L1,.5',\n\n  /** ╵ */ MIDDLE_TO_TOP = 'M.5,.5 L.5,0',\n  /** ╴ */ MIDDLE_TO_LEFT = 'M.5,.5 L0,.5',\n  /** ╶ */ MIDDLE_TO_RIGHT = 'M.5,.5 L1,.5',\n  /** ╷ */ MIDDLE_TO_BOTTOM = 'M.5,.5 L.5,1',\n\n  /** ┴ */ T_TOP = 'M0,.5 L1,.5 M.5,.5 L.5,0',\n  /** ┤ */ T_LEFT = 'M.5,0 L.5,1 M.5,.5 L0,.5',\n  /** ├ */ T_RIGHT = 'M.5,0 L.5,1 M.5,.5 L1,.5',\n  /** ┬ */ T_BOTTOM = 'M0,.5 L1,.5 M.5,.5 L.5,1',\n\n  /** ┼ */ CROSS = 'M0,.5 L1,.5 M.5,0 L.5,1',\n\n  /** ╌ */ TWO_DASHES_HORIZONTAL = 'M.1,.5 L.4,.5 M.6,.5 L.9,.5', // .2 empty, .3 filled\n  /** ┄ */ THREE_DASHES_HORIZONTAL = 'M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5', // .1333 empty, .2 filled\n  /** ┉ */ FOUR_DASHES_HORIZONTAL = 'M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5', // .1 empty, .15 filled\n  /** ╎ */ TWO_DASHES_VERTICAL = 'M.5,.1 L.5,.4 M.5,.6 L.5,.9',\n  /** ┆ */ THREE_DASHES_VERTICAL = 'M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333',\n  /** ┊ */ FOUR_DASHES_VERTICAL = 'M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95',\n}\n\nconst enum Style {\n  NORMAL = 1,\n  BOLD = 3\n}\n\n/**\n * @param xp The percentage of 15% of the x axis.\n * @param yp The percentage of 15% of the x axis on the y axis.\n */\ntype DrawFunctionDefinition = (xp: number, yp: number) => string;\n\n/**\n * This contains the definitions of all box drawing characters in the format of SVG paths (ie. the\n * svg d attribute).\n */\nexport const boxDrawingDefinitions: { [character: string]: { [fontWeight: number]: string | DrawFunctionDefinition } | undefined } = {\n  // Uniform normal and bold\n  '─': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT },\n  '━': { [Style.BOLD]:   Shapes.LEFT_TO_RIGHT },\n  '│': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM },\n  '┃': { [Style.BOLD]:   Shapes.TOP_TO_BOTTOM },\n  '┌': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM },\n  '┏': { [Style.BOLD]:   Shapes.RIGHT_TO_BOTTOM },\n  '┐': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM },\n  '┓': { [Style.BOLD]:   Shapes.LEFT_TO_BOTTOM },\n  '└': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT },\n  '┗': { [Style.BOLD]:   Shapes.TOP_TO_RIGHT },\n  '┘': { [Style.NORMAL]: Shapes.TOP_TO_LEFT },\n  '┛': { [Style.BOLD]:   Shapes.TOP_TO_LEFT },\n  '├': { [Style.NORMAL]: Shapes.T_RIGHT },\n  '┣': { [Style.BOLD]:   Shapes.T_RIGHT },\n  '┤': { [Style.NORMAL]: Shapes.T_LEFT },\n  '┫': { [Style.BOLD]:   Shapes.T_LEFT },\n  '┬': { [Style.NORMAL]: Shapes.T_BOTTOM },\n  '┳': { [Style.BOLD]:   Shapes.T_BOTTOM },\n  '┴': { [Style.NORMAL]: Shapes.T_TOP },\n  '┻': { [Style.BOLD]:   Shapes.T_TOP },\n  '┼': { [Style.NORMAL]: Shapes.CROSS },\n  '╋': { [Style.BOLD]:   Shapes.CROSS },\n  '╴': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT },\n  '╸': { [Style.BOLD]:   Shapes.MIDDLE_TO_LEFT },\n  '╵': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP },\n  '╹': { [Style.BOLD]:   Shapes.MIDDLE_TO_TOP },\n  '╶': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT },\n  '╺': { [Style.BOLD]:   Shapes.MIDDLE_TO_RIGHT },\n  '╷': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM },\n  '╻': { [Style.BOLD]:   Shapes.MIDDLE_TO_BOTTOM },\n\n  // Double border\n  '═': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '║': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╒': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 - yp} L1,${.5 - yp} M.5,${.5 + yp} L1,${.5 + yp}` },\n  '╓': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},1 L${.5 - xp},.5 L1,.5 M${.5 + xp},.5 L${.5 + xp},1` },\n  '╔': { [Style.NORMAL]: (xp, yp) => `M1,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1` },\n  '╕': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L.5,${.5 - yp} L.5,1 M0,${.5 + yp} L.5,${.5 + yp}` },\n  '╖': { [Style.NORMAL]: (xp, yp) => `M${.5 + xp},1 L${.5 + xp},.5 L0,.5 M${.5 - xp},.5 L${.5 - xp},1` },\n  '╗': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M0,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},1` },\n  '╘': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 + yp} L1,${.5 + yp} M.5,${.5 - yp} L1,${.5 - yp}` },\n  '╙': { [Style.NORMAL]: (xp, yp) => `M1,.5 L${.5 - xp},.5 L${.5 - xp},0 M${.5 + xp},.5 L${.5 + xp},0` },\n  '╚': { [Style.NORMAL]: (xp, yp) => `M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0 M1,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},0` },\n  '╛': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L.5,${.5 + yp} L.5,0 M0,${.5 - yp} L.5,${.5 - yp}` },\n  '╜': { [Style.NORMAL]: (xp, yp) => `M0,.5 L${.5 + xp},.5 L${.5 + xp},0 M${.5 - xp},.5 L${.5 - xp},0` },\n  '╝': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M0,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},0` },\n  '╞': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M.5,${.5 - yp} L1,${.5 - yp} M.5,${.5 + yp} L1,${.5 + yp}` },\n  '╟': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1 M${.5 + xp},.5 L1,.5` },\n  '╠': { [Style.NORMAL]: (xp, yp) => `M${.5 - xp},0 L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n  '╡': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M0,${.5 - yp} L.5,${.5 - yp} M0,${.5 + yp} L.5,${.5 + yp}` },\n  '╢': { [Style.NORMAL]: (xp, yp) => `M0,.5 L${.5 - xp},.5 M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╣': { [Style.NORMAL]: (xp, yp) => `M${.5 + xp},0 L${.5 + xp},1 M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0` },\n  '╤': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp} M.5,${.5 + yp} L.5,1` },\n  '╥': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},.5 L${.5 - xp},1 M${.5 + xp},.5 L${.5 + xp},1` },\n  '╦': { [Style.NORMAL]: (xp, yp) => `M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1` },\n  '╧': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - yp} M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '╨': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},.5 L${.5 - xp},0 M${.5 + xp},.5 L${.5 + xp},0` },\n  '╩': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L1,${.5 + yp} M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n  '╪': { [Style.NORMAL]: (xp, yp) => `${Shapes.TOP_TO_BOTTOM} M0,${.5 - yp} L1,${.5 - yp} M0,${.5 + yp} L1,${.5 + yp}` },\n  '╫': { [Style.NORMAL]: (xp, yp) => `${Shapes.LEFT_TO_RIGHT} M${.5 - xp},0 L${.5 - xp},1 M${.5 + xp},0 L${.5 + xp},1` },\n  '╬': { [Style.NORMAL]: (xp, yp) => `M0,${.5 + yp} L${.5 - xp},${.5 + yp} L${.5 - xp},1 M1,${.5 + yp} L${.5 + xp},${.5 + yp} L${.5 + xp},1 M0,${.5 - yp} L${.5 - xp},${.5 - yp} L${.5 - xp},0 M1,${.5 - yp} L${.5 + xp},${.5 - yp} L${.5 + xp},0` },\n\n  // Diagonal\n  '╱': { [Style.NORMAL]: 'M1,0 L0,1' },\n  '╲': { [Style.NORMAL]: 'M0,0 L1,1' },\n  '╳': { [Style.NORMAL]: 'M1,0 L0,1 M0,0 L1,1' },\n\n  // Mixed weight\n  '╼': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '╽': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '╾': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '╿': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┍': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┎': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┑': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┒': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┕': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┖': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┙': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┚': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┝': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┞': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┟': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┠': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '┡': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '┢': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '┥': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┦': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┧': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┨': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '┩': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '┪': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '┭': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┮': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┯': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '┰': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '┱': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '┲': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '┵': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┶': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┷': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '┸': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '┹': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '┺': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '┽': { [Style.NORMAL]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_RIGHT}`,  [Style.BOLD]: Shapes.MIDDLE_TO_LEFT },\n  '┾': { [Style.NORMAL]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_LEFT}`,   [Style.BOLD]: Shapes.MIDDLE_TO_RIGHT },\n  '┿': { [Style.NORMAL]: Shapes.TOP_TO_BOTTOM,                                 [Style.BOLD]: Shapes.LEFT_TO_RIGHT },\n  '╀': { [Style.NORMAL]: `${Shapes.LEFT_TO_RIGHT} ${Shapes.MIDDLE_TO_BOTTOM}`, [Style.BOLD]: Shapes.MIDDLE_TO_TOP },\n  '╁': { [Style.NORMAL]: `${Shapes.MIDDLE_TO_TOP} ${Shapes.LEFT_TO_RIGHT}`,    [Style.BOLD]: Shapes.MIDDLE_TO_BOTTOM },\n  '╂': { [Style.NORMAL]: Shapes.LEFT_TO_RIGHT,                                 [Style.BOLD]: Shapes.TOP_TO_BOTTOM },\n  '╃': { [Style.NORMAL]: Shapes.RIGHT_TO_BOTTOM,                               [Style.BOLD]: Shapes.TOP_TO_LEFT },\n  '╄': { [Style.NORMAL]: Shapes.LEFT_TO_BOTTOM,                                [Style.BOLD]: Shapes.TOP_TO_RIGHT },\n  '╅': { [Style.NORMAL]: Shapes.TOP_TO_RIGHT,                                  [Style.BOLD]: Shapes.LEFT_TO_BOTTOM },\n  '╆': { [Style.NORMAL]: Shapes.TOP_TO_LEFT,                                   [Style.BOLD]: Shapes.RIGHT_TO_BOTTOM },\n  '╇': { [Style.NORMAL]: Shapes.MIDDLE_TO_BOTTOM,                              [Style.BOLD]: `${Shapes.MIDDLE_TO_TOP} ${Shapes.LEFT_TO_RIGHT}` },\n  '╈': { [Style.NORMAL]: Shapes.MIDDLE_TO_TOP,                                 [Style.BOLD]: `${Shapes.LEFT_TO_RIGHT} ${Shapes.MIDDLE_TO_BOTTOM}` },\n  '╉': { [Style.NORMAL]: Shapes.MIDDLE_TO_RIGHT,                               [Style.BOLD]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_LEFT}` },\n  '╊': { [Style.NORMAL]: Shapes.MIDDLE_TO_LEFT,                                [Style.BOLD]: `${Shapes.TOP_TO_BOTTOM} ${Shapes.MIDDLE_TO_RIGHT}` },\n\n  // Dashed\n  '╌': { [Style.NORMAL]: Shapes.TWO_DASHES_HORIZONTAL },\n  '╍': { [Style.BOLD]:   Shapes.TWO_DASHES_HORIZONTAL },\n  '┄': { [Style.NORMAL]: Shapes.THREE_DASHES_HORIZONTAL },\n  '┅': { [Style.BOLD]:   Shapes.THREE_DASHES_HORIZONTAL },\n  '┈': { [Style.NORMAL]: Shapes.FOUR_DASHES_HORIZONTAL },\n  '┉': { [Style.BOLD]:   Shapes.FOUR_DASHES_HORIZONTAL },\n  '╎': { [Style.NORMAL]: Shapes.TWO_DASHES_VERTICAL },\n  '╏': { [Style.BOLD]:   Shapes.TWO_DASHES_VERTICAL },\n  '┆': { [Style.NORMAL]: Shapes.THREE_DASHES_VERTICAL  },\n  '┇': { [Style.BOLD]:   Shapes.THREE_DASHES_VERTICAL },\n  '┊': { [Style.NORMAL]: Shapes.FOUR_DASHES_VERTICAL },\n  '┋': { [Style.BOLD]:   Shapes.FOUR_DASHES_VERTICAL },\n\n  // Curved\n  '╭': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 + (yp / .15 * .5)} C.5,${.5 + (yp / .15 * .5)},.5,.5,1,.5` },\n  '╮': { [Style.NORMAL]: (xp, yp) => `M.5,1 L.5,${.5 + (yp / .15 * .5)} C.5,${.5 + (yp / .15 * .5)},.5,.5,0,.5` },\n  '╯': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - (yp / .15 * .5)} C.5,${.5 - (yp / .15 * .5)},.5,.5,0,.5` },\n  '╰': { [Style.NORMAL]: (xp, yp) => `M.5,0 L.5,${.5 - (yp / .15 * .5)} C.5,${.5 - (yp / .15 * .5)},.5,.5,1,.5` }\n};\n\ninterface IVectorShape {\n  d: string;\n  type: VectorType;\n  leftPadding?: number;\n  rightPadding?: number;\n}\n\nconst enum VectorType {\n  FILL,\n  STROKE\n}\n\n/**\n * This contains the definitions of the primarily used box drawing characters as vector shapes. The\n * reason these characters are defined specially is to avoid common problems if a user's font has\n * not been patched with powerline characters and also to get pixel perfect rendering as rendering\n * issues can occur around AA/SPAA.\n *\n * The line variants draw beyond the cell and get clipped to ensure the end of the line is not\n * visible.\n *\n * Original symbols defined in https://github.com/powerline/fontpatcher\n */\nexport const powerlineDefinitions: { [index: string]: IVectorShape } = {\n  // Right triangle solid\n  '\\u{E0B0}': { d: 'M0,0 L1,.5 L0,1', type: VectorType.FILL, rightPadding: 2 },\n  // Right triangle line\n  '\\u{E0B1}': { d: 'M-1,-.5 L1,.5 L-1,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Left triangle solid\n  '\\u{E0B2}': { d: 'M1,0 L0,.5 L1,1', type: VectorType.FILL, leftPadding: 2 },\n  // Left triangle line\n  '\\u{E0B3}': { d: 'M2,-.5 L0,.5 L2,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Right semi-circle solid\n  '\\u{E0B4}': { d: 'M0,0 L0,1 C0.552,1,1,0.776,1,.5 C1,0.224,0.552,0,0,0', type: VectorType.FILL, rightPadding: 1 },\n  // Right semi-circle line\n  '\\u{E0B5}': { d: 'M.2,1 C.422,1,.8,.826,.78,.5 C.8,.174,0.422,0,.2,0', type: VectorType.STROKE, rightPadding: 1 },\n  // Left semi-circle solid\n  '\\u{E0B6}': { d: 'M1,0 L1,1 C0.448,1,0,0.776,0,.5 C0,0.224,0.448,0,1,0', type: VectorType.FILL, leftPadding: 1 },\n  // Left semi-circle line\n  '\\u{E0B7}': { d: 'M.8,1 C0.578,1,0.2,.826,.22,.5 C0.2,0.174,0.578,0,0.8,0', type: VectorType.STROKE, leftPadding: 1 },\n  // Lower left triangle\n  '\\u{E0B8}': { d: 'M-.5,-.5 L1.5,1.5 L-.5,1.5', type: VectorType.FILL },\n  // Backslash separator\n  '\\u{E0B9}': { d: 'M-.5,-.5 L1.5,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Lower right triangle\n  '\\u{E0BA}': { d: 'M1.5,-.5 L-.5,1.5 L1.5,1.5', type: VectorType.FILL },\n  // Upper left triangle\n  '\\u{E0BC}': { d: 'M1.5,-.5 L-.5,1.5 L-.5,-.5', type: VectorType.FILL },\n  // Forward slash separator\n  '\\u{E0BD}': { d: 'M1.5,-.5 L-.5,1.5', type: VectorType.STROKE, leftPadding: 1, rightPadding: 1 },\n  // Upper right triangle\n  '\\u{E0BE}': { d: 'M-.5,-.5 L1.5,1.5 L1.5,-.5', type: VectorType.FILL }\n};\n// Forward slash separator redundant\npowerlineDefinitions['\\u{E0BB}'] = powerlineDefinitions['\\u{E0BD}'];\n// Backslash separator redundant\npowerlineDefinitions['\\u{E0BF}'] = powerlineDefinitions['\\u{E0B9}'];\n\n/**\n * Try drawing a custom block element or box drawing character, returning whether it was\n * successfully drawn.\n */\nexport function tryDrawCustomChar(\n  ctx: CanvasRenderingContext2D,\n  c: string,\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  fontSize: number,\n  devicePixelRatio: number\n): boolean {\n  const blockElementDefinition = blockElementDefinitions[c];\n  if (blockElementDefinition) {\n    drawBlockElementChar(ctx, blockElementDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n    return true;\n  }\n\n  const patternDefinition = patternCharacterDefinitions[c];\n  if (patternDefinition) {\n    drawPatternChar(ctx, patternDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n    return true;\n  }\n\n  const boxDrawingDefinition = boxDrawingDefinitions[c];\n  if (boxDrawingDefinition) {\n    drawBoxDrawingChar(ctx, boxDrawingDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight, devicePixelRatio);\n    return true;\n  }\n\n  const powerlineDefinition = powerlineDefinitions[c];\n  if (powerlineDefinition) {\n    drawPowerlineChar(ctx, powerlineDefinition, xOffset, yOffset, deviceCellWidth, deviceCellHeight, fontSize, devicePixelRatio);\n    return true;\n  }\n\n  return false;\n}\n\nfunction drawBlockElementChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: IBlockVector[],\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number\n): void {\n  for (let i = 0; i < charDefinition.length; i++) {\n    const box = charDefinition[i];\n    const xEighth = deviceCellWidth / 8;\n    const yEighth = deviceCellHeight / 8;\n    ctx.fillRect(\n      xOffset + box.x * xEighth,\n      yOffset + box.y * yEighth,\n      box.w * xEighth,\n      box.h * yEighth\n    );\n  }\n}\n\nconst cachedPatterns: Map<PatternDefinition, Map</* fillStyle */string, CanvasPattern>> = new Map();\n\nfunction drawPatternChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: number[][],\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number\n): void {\n  let patternSet = cachedPatterns.get(charDefinition);\n  if (!patternSet) {\n    patternSet = new Map();\n    cachedPatterns.set(charDefinition, patternSet);\n  }\n  const fillStyle = ctx.fillStyle;\n  if (typeof fillStyle !== 'string') {\n    throw new Error(`Unexpected fillStyle type \"${fillStyle}\"`);\n  }\n  let pattern = patternSet.get(fillStyle);\n  if (!pattern) {\n    const width = charDefinition[0].length;\n    const height = charDefinition.length;\n    const tmpCanvas = document.createElement('canvas');\n    tmpCanvas.width = width;\n    tmpCanvas.height = height;\n    const tmpCtx = throwIfFalsy(tmpCanvas.getContext('2d'));\n    const imageData = new ImageData(width, height);\n\n    // Extract rgba from fillStyle\n    let r: number;\n    let g: number;\n    let b: number;\n    let a: number;\n    if (fillStyle.startsWith('#')) {\n      r = parseInt(fillStyle.slice(1, 3), 16);\n      g = parseInt(fillStyle.slice(3, 5), 16);\n      b = parseInt(fillStyle.slice(5, 7), 16);\n      a = fillStyle.length > 7 && parseInt(fillStyle.slice(7, 9), 16) || 1;\n    } else if (fillStyle.startsWith('rgba')) {\n      ([r, g, b, a] = fillStyle.substring(5, fillStyle.length - 1).split(',').map(e => parseFloat(e)));\n    } else {\n      throw new Error(`Unexpected fillStyle color format \"${fillStyle}\" when drawing pattern glyph`);\n    }\n\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        imageData.data[(y * width + x) * 4    ] = r;\n        imageData.data[(y * width + x) * 4 + 1] = g;\n        imageData.data[(y * width + x) * 4 + 2] = b;\n        imageData.data[(y * width + x) * 4 + 3] = charDefinition[y][x] * (a * 255);\n      }\n    }\n    tmpCtx.putImageData(imageData, 0, 0);\n    pattern = throwIfFalsy(ctx.createPattern(tmpCanvas, null));\n    patternSet.set(fillStyle, pattern);\n  }\n  ctx.fillStyle = pattern;\n  ctx.fillRect(xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n}\n\n/**\n * Draws the following box drawing characters by mapping a subset of SVG d attribute instructions to\n * canvas draw calls.\n *\n * Box styles:       ┎┰┒┍┯┑╓╥╖╒╤╕ ┏┳┓┌┲┓┌┬┐┏┱┐\n * ┌─┬─┐ ┏━┳━┓ ╔═╦═╗ ┠╂┨┝┿┥╟╫╢╞╪╡ ┡╇┩├╊┫┢╈┪┣╉┤\n * │ │ │ ┃ ┃ ┃ ║ ║ ║ ┖┸┚┕┷┙╙╨╜╘╧╛ └┴┘└┺┛┗┻┛┗┹┘\n * ├─┼─┤ ┣━╋━┫ ╠═╬═╣ ┏┱┐┌┲┓┌┬┐┌┬┐ ┏┳┓┌┮┓┌┬┐┏┭┐\n * │ │ │ ┃ ┃ ┃ ║ ║ ║ ┡╃┤├╄┩├╆┪┢╅┤ ┞╀┦├┾┫┟╁┧┣┽┤\n * └─┴─┘ ┗━┻━┛ ╚═╩═╝ └┴┘└┴┘└┺┛┗┹┘ └┴┘└┶┛┗┻┛┗┵┘\n *\n * Other:\n * ╭─╮ ╲ ╱ ╷╻╎╏┆┇┊┋ ╺╾╴ ╌╌╌ ┄┄┄ ┈┈┈\n * │ │  ╳  ╽╿╎╏┆┇┊┋ ╶╼╸ ╍╍╍ ┅┅┅ ┉┉┉\n * ╰─╯ ╱ ╲ ╹╵╎╏┆┇┊┋\n *\n * All box drawing characters:\n * ─ ━ │ ┃ ┄ ┅ ┆ ┇ ┈ ┉ ┊ ┋ ┌ ┍ ┎ ┏\n * ┐ ┑ ┒ ┓ └ ┕ ┖ ┗ ┘ ┙ ┚ ┛ ├ ┝ ┞ ┟\n * ┠ ┡ ┢ ┣ ┤ ┥ ┦ ┧ ┨ ┩ ┪ ┫ ┬ ┭ ┮ ┯\n * ┰ ┱ ┲ ┳ ┴ ┵ ┶ ┷ ┸ ┹ ┺ ┻ ┼ ┽ ┾ ┿\n * ╀ ╁ ╂ ╃ ╄ ╅ ╆ ╇ ╈ ╉ ╊ ╋ ╌ ╍ ╎ ╏\n * ═ ║ ╒ ╓ ╔ ╕ ╖ ╗ ╘ ╙ ╚ ╛ ╜ ╝ ╞ ╟\n * ╠ ╡ ╢ ╣ ╤ ╥ ╦ ╧ ╨ ╩ ╪ ╫ ╬ ╭ ╮ ╯\n * ╰ ╱ ╲ ╳ ╴ ╵ ╶ ╷ ╸ ╹ ╺ ╻ ╼ ╽ ╾ ╿\n *\n * ---\n *\n * Box drawing alignment tests:                                          █\n *                                                                       ▉\n *   ╔══╦══╗  ┌──┬──┐  ╭──┬──╮  ╭──┬──╮  ┏━━┳━━┓  ┎┒┏┑   ╷  ╻ ┏┯┓ ┌┰┐    ▊ ╱╲╱╲╳╳╳\n *   ║┌─╨─┐║  │╔═╧═╗│  │╒═╪═╕│  │╓─╁─╖│  ┃┌─╂─┐┃  ┗╃╄┙  ╶┼╴╺╋╸┠┼┨ ┝╋┥    ▋ ╲╱╲╱╳╳╳\n *   ║│╲ ╱│║  │║   ║│  ││ │ ││  │║ ┃ ║│  ┃│ ╿ │┃  ┍╅╆┓   ╵  ╹ ┗┷┛ └┸┘    ▌ ╱╲╱╲╳╳╳\n *   ╠╡ ╳ ╞╣  ├╢   ╟┤  ├┼─┼─┼┤  ├╫─╂─╫┤  ┣┿╾┼╼┿┫  ┕┛┖┚     ┌┄┄┐ ╎ ┏┅┅┓ ┋ ▍ ╲╱╲╱╳╳╳\n *   ║│╱ ╲│║  │║   ║│  ││ │ ││  │║ ┃ ║│  ┃│ ╽ │┃  ░░▒▒▓▓██ ┊  ┆ ╎ ╏  ┇ ┋ ▎\n *   ║└─╥─┘║  │╚═╤═╝│  │╘═╪═╛│  │╙─╀─╜│  ┃└─╂─┘┃  ░░▒▒▓▓██ ┊  ┆ ╎ ╏  ┇ ┋ ▏\n *   ╚══╩══╝  └──┴──┘  ╰──┴──╯  ╰──┴──╯  ┗━━┻━━┛           └╌╌┘ ╎ ┗╍╍┛ ┋  ▁▂▃▄▅▆▇█\n *\n * Source: https://www.w3.org/2001/06/utf-8-test/UTF-8-demo.html\n */\nfunction drawBoxDrawingChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: { [fontWeight: number]: string | ((xp: number, yp: number) => string) },\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  devicePixelRatio: number\n): void {\n  ctx.strokeStyle = ctx.fillStyle;\n  for (const [fontWeight, instructions] of Object.entries(charDefinition)) {\n    ctx.beginPath();\n    ctx.lineWidth = devicePixelRatio * Number.parseInt(fontWeight);\n    let actualInstructions: string;\n    if (typeof instructions === 'function') {\n      const xp = .15;\n      const yp = .15 / deviceCellHeight * deviceCellWidth;\n      actualInstructions = instructions(xp, yp);\n    } else {\n      actualInstructions = instructions;\n    }\n    for (const instruction of actualInstructions.split(' ')) {\n      const type = instruction[0];\n      const f = svgToCanvasInstructionMap[type];\n      if (!f) {\n        console.error(`Could not find drawing instructions for \"${type}\"`);\n        continue;\n      }\n      const args: string[] = instruction.substring(1).split(',');\n      if (!args[0] || !args[1]) {\n        continue;\n      }\n      f(ctx, translateArgs(args, deviceCellWidth, deviceCellHeight, xOffset, yOffset, true, devicePixelRatio));\n    }\n    ctx.stroke();\n    ctx.closePath();\n  }\n}\n\nfunction drawPowerlineChar(\n  ctx: CanvasRenderingContext2D,\n  charDefinition: IVectorShape,\n  xOffset: number,\n  yOffset: number,\n  deviceCellWidth: number,\n  deviceCellHeight: number,\n  fontSize: number,\n  devicePixelRatio: number\n): void {\n  // Clip the cell to make sure drawing doesn't occur beyond bounds\n  const clipRegion = new Path2D();\n  clipRegion.rect(xOffset, yOffset, deviceCellWidth, deviceCellHeight);\n  ctx.clip(clipRegion);\n\n  ctx.beginPath();\n  // Scale the stroke with DPR and font size\n  const cssLineWidth = fontSize / 12;\n  ctx.lineWidth = devicePixelRatio * cssLineWidth;\n  for (const instruction of charDefinition.d.split(' ')) {\n    const type = instruction[0];\n    const f = svgToCanvasInstructionMap[type];\n    if (!f) {\n      console.error(`Could not find drawing instructions for \"${type}\"`);\n      continue;\n    }\n    const args: string[] = instruction.substring(1).split(',');\n    if (!args[0] || !args[1]) {\n      continue;\n    }\n    f(ctx, translateArgs(\n      args,\n      deviceCellWidth,\n      deviceCellHeight,\n      xOffset,\n      yOffset,\n      false,\n      devicePixelRatio,\n      (charDefinition.leftPadding ?? 0) * (cssLineWidth / 2),\n      (charDefinition.rightPadding ?? 0) * (cssLineWidth / 2)\n    ));\n  }\n  if (charDefinition.type === VectorType.STROKE) {\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.stroke();\n  } else {\n    ctx.fill();\n  }\n  ctx.closePath();\n}\n\nfunction clamp(value: number, max: number, min: number = 0): number {\n  return Math.max(Math.min(value, max), min);\n}\n\nconst svgToCanvasInstructionMap: { [index: string]: any } = {\n  'C': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.bezierCurveTo(args[0], args[1], args[2], args[3], args[4], args[5]),\n  'L': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.lineTo(args[0], args[1]),\n  'M': (ctx: CanvasRenderingContext2D, args: number[]) => ctx.moveTo(args[0], args[1])\n};\n\nfunction translateArgs(args: string[], cellWidth: number, cellHeight: number, xOffset: number, yOffset: number, doClamp: boolean, devicePixelRatio: number, leftPadding: number = 0, rightPadding: number = 0): number[] {\n  const result = args.map(e => parseFloat(e) || parseInt(e));\n\n  if (result.length < 2) {\n    throw new Error('Too few arguments for instruction');\n  }\n\n  for (let x = 0; x < result.length; x += 2) {\n    // Translate from 0-1 to 0-cellWidth\n    result[x] *= cellWidth - (leftPadding * devicePixelRatio) - (rightPadding * devicePixelRatio);\n    // Ensure coordinate doesn't escape cell bounds and round to the nearest 0.5 to ensure a crisp\n    // line at 100% devicePixelRatio\n    if (doClamp && result[x] !== 0) {\n      result[x] = clamp(Math.round(result[x] + 0.5) - 0.5, cellWidth, 0);\n    }\n    // Apply the cell's offset (ie. x*cellWidth)\n    result[x] += xOffset + (leftPadding * devicePixelRatio);\n  }\n\n  for (let y = 1; y < result.length; y += 2) {\n    // Translate from 0-1 to 0-cellHeight\n    result[y] *= cellHeight;\n    // Ensure coordinate doesn't escape cell bounds and round to the nearest 0.5 to ensure a crisp\n    // line at 100% devicePixelRatio\n    if (doClamp && result[y] !== 0) {\n      result[y] = clamp(Math.round(result[y] + 0.5) - 0.5, cellHeight, 0);\n    }\n    // Apply the cell's offset (ie. x*cellHeight)\n    result[y] += yOffset;\n  }\n\n  return result;\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { toDisposable } from 'common/Lifecycle';\nimport { IDisposable } from 'common/Types';\n\nexport function observeDevicePixelDimensions(element: HTMLElement, parentWindow: Window & typeof globalThis, callback: (deviceWidth: number, deviceHeight: number) => void): IDisposable {\n  // Observe any resizes to the element and extract the actual pixel size of the element if the\n  // devicePixelContentBoxSize API is supported. This allows correcting rounding errors when\n  // converting between CSS pixels and device pixels which causes blurry rendering when device\n  // pixel ratio is not a round number.\n  let observer: ResizeObserver | undefined = new parentWindow.ResizeObserver((entries) => {\n    const entry = entries.find((entry) => entry.target === element);\n    if (!entry) {\n      return;\n    }\n\n    // Disconnect if devicePixelContentBoxSize isn't supported by the browser\n    if (!('devicePixelContentBoxSize' in entry)) {\n      observer?.disconnect();\n      observer = undefined;\n      return;\n    }\n\n    // Fire the callback, ignore events where the dimensions are 0x0 as the canvas is likely hidden\n    const width = entry.devicePixelContentBoxSize[0].inlineSize;\n    const height = entry.devicePixelContentBoxSize[0].blockSize;\n    if (width > 0 && height > 0) {\n      callback(width, height);\n    }\n  });\n  try {\n    observer.observe(element, { box: ['device-pixel-content-box'] } as any);\n  } catch {\n    observer.disconnect();\n    observer = undefined;\n  }\n  return toDisposable(() => observer?.disconnect());\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDimensions, IRenderDimensions } from 'browser/renderer/shared/Types';\n\nexport function throwIfFalsy<T>(value: T | undefined | null): T {\n  if (!value) {\n    throw new Error('value must not be falsy');\n  }\n  return value;\n}\n\nexport function isPowerlineGlyph(codepoint: number): boolean {\n  // Only return true for Powerline symbols which require\n  // different padding and should be excluded from minimum contrast\n  // ratio standards\n  return 0xE0A4 <= codepoint && codepoint <= 0xE0D6;\n}\n\nexport function isRestrictedPowerlineGlyph(codepoint: number): boolean {\n  return 0xE0B0 <= codepoint && codepoint <= 0xE0B7;\n}\n\nfunction isBoxOrBlockGlyph(codepoint: number): boolean {\n  return 0x2500 <= codepoint && codepoint <= 0x259F;\n}\n\nexport function excludeFromContrastRatioDemands(codepoint: number): boolean {\n  return isPowerlineGlyph(codepoint) || isBoxOrBlockGlyph(codepoint);\n}\n\nexport function createRenderDimensions(): IRenderDimensions {\n  return {\n    css: {\n      canvas: createDimension(),\n      cell: createDimension()\n    },\n    device: {\n      canvas: createDimension(),\n      cell: createDimension(),\n      char: {\n        width: 0,\n        height: 0,\n        left: 0,\n        top: 0\n      }\n    }\n  };\n}\n\nfunction createDimension(): IDimensions {\n  return {\n    width: 0,\n    height: 0\n  };\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ISelectionRenderModel } from 'browser/renderer/shared/Types';\nimport { Terminal } from 'xterm';\n\nclass SelectionRenderModel implements ISelectionRenderModel {\n  public hasSelection!: boolean;\n  public columnSelectMode!: boolean;\n  public viewportStartRow!: number;\n  public viewportEndRow!: number;\n  public viewportCappedStartRow!: number;\n  public viewportCappedEndRow!: number;\n  public startCol!: number;\n  public endCol!: number;\n  public selectionStart: [number, number] | undefined;\n  public selectionEnd: [number, number] | undefined;\n\n  constructor() {\n    this.clear();\n  }\n\n  public clear(): void {\n    this.hasSelection = false;\n    this.columnSelectMode = false;\n    this.viewportStartRow = 0;\n    this.viewportEndRow = 0;\n    this.viewportCappedStartRow = 0;\n    this.viewportCappedEndRow = 0;\n    this.startCol = 0;\n    this.endCol = 0;\n    this.selectionStart = undefined;\n    this.selectionEnd = undefined;\n  }\n\n  public update(terminal: Terminal, start: [number, number] | undefined, end: [number, number] | undefined, columnSelectMode: boolean = false): void {\n    this.selectionStart = start;\n    this.selectionEnd = end;\n    // Selection does not exist\n    if (!start || !end || (start[0] === end[0] && start[1] === end[1])) {\n      this.clear();\n      return;\n    }\n\n    // Translate from buffer position to viewport position\n    const viewportStartRow = start[1] - terminal.buffer.active.viewportY;\n    const viewportEndRow = end[1] - terminal.buffer.active.viewportY;\n    const viewportCappedStartRow = Math.max(viewportStartRow, 0);\n    const viewportCappedEndRow = Math.min(viewportEndRow, terminal.rows - 1);\n\n    // No need to draw the selection\n    if (viewportCappedStartRow >= terminal.rows || viewportCappedEndRow < 0) {\n      this.clear();\n      return;\n    }\n\n    this.hasSelection = true;\n    this.columnSelectMode = columnSelectMode;\n    this.viewportStartRow = viewportStartRow;\n    this.viewportEndRow = viewportEndRow;\n    this.viewportCappedStartRow = viewportCappedStartRow;\n    this.viewportCappedEndRow = viewportCappedEndRow;\n    this.startCol = start[0];\n    this.endCol = end[0];\n  }\n\n  public isCellSelected(terminal: Terminal, x: number, y: number): boolean {\n    if (!this.hasSelection) {\n      return false;\n    }\n    y -= terminal.buffer.active.viewportY;\n    if (this.columnSelectMode) {\n      if (this.startCol <= this.endCol) {\n        return x >= this.startCol && y >= this.viewportCappedStartRow &&\n          x < this.endCol && y <= this.viewportCappedEndRow;\n      }\n      return x < this.startCol && y >= this.viewportCappedStartRow &&\n        x >= this.endCol && y <= this.viewportCappedEndRow;\n    }\n    return (y > this.viewportStartRow && y < this.viewportEndRow) ||\n      (this.viewportStartRow === this.viewportEndRow && y === this.viewportStartRow && x >= this.startCol && x < this.endCol) ||\n      (this.viewportStartRow < this.viewportEndRow && y === this.viewportEndRow && x < this.endCol) ||\n      (this.viewportStartRow < this.viewportEndRow && y === this.viewportStartRow && x >= this.startCol);\n  }\n}\n\nexport function createSelectionRenderModel(): ISelectionRenderModel {\n  return new SelectionRenderModel();\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IColorContrastCache } from 'browser/Types';\nimport { DIM_OPACITY, TEXT_BASELINE } from 'browser/renderer/shared/Constants';\nimport { tryDrawCustom<PERSON>har } from 'browser/renderer/shared/CustomGlyphs';\nimport { excludeFromContrastRatioDemands, isPowerlineGlyph, isRestrictedPowerlineGlyph, throwIfFalsy } from 'browser/renderer/shared/RendererUtils';\nimport { IBoundingBox, ICharAtlasConfig, IRasterizedGlyph, ITextureAtlas } from 'browser/renderer/shared/Types';\nimport { NULL_COLOR, color, rgba } from 'common/Color';\nimport { EventEmitter } from 'common/EventEmitter';\nimport { FourKeyMap } from 'common/MultiKeyMap';\nimport { IdleTaskQueue } from 'common/TaskQueue';\nimport { IColor } from 'common/Types';\nimport { AttributeData } from 'common/buffer/AttributeData';\nimport { Attributes, DEFAULT_COLOR, DEFAULT_EXT, UnderlineStyle } from 'common/buffer/Constants';\nimport { traceCall } from 'common/services/LogService';\nimport { IUnicodeService } from 'common/services/Services';\n\n/**\n * A shared object which is used to draw nothing for a particular cell.\n */\nconst NULL_RASTERIZED_GLYPH: IRasterizedGlyph = {\n  texturePage: 0,\n  texturePosition: { x: 0, y: 0 },\n  texturePositionClipSpace: { x: 0, y: 0 },\n  offset: { x: 0, y: 0 },\n  size: { x: 0, y: 0 },\n  sizeClipSpace: { x: 0, y: 0 }\n};\n\nconst TMP_CANVAS_GLYPH_PADDING = 2;\n\nconst enum Constants {\n  /**\n   * The amount of pixel padding to allow in each row. Setting this to zero would make the atlas\n   * page pack as tightly as possible, but more pages would end up being created as a result.\n   */\n  ROW_PIXEL_THRESHOLD = 2,\n  /**\n   * The maximum texture size regardless of what the actual hardware maximum turns out to be. This\n   * is enforced to ensure uploading the texture still finishes in a reasonable amount of time. A\n   * 4096 squared image takes up 16MB of GPU memory.\n   */\n  FORCED_MAX_TEXTURE_SIZE = 4096\n}\n\ninterface ICharAtlasActiveRow {\n  x: number;\n  y: number;\n  height: number;\n}\n\n// Work variables to avoid garbage collection\nlet $glyph = undefined;\n\nexport class TextureAtlas implements ITextureAtlas {\n  private _didWarmUp: boolean = false;\n\n  private _cacheMap: FourKeyMap<number, number, number, number, IRasterizedGlyph> = new FourKeyMap();\n  private _cacheMapCombined: FourKeyMap<string, number, number, number, IRasterizedGlyph> = new FourKeyMap();\n\n  // The texture that the atlas is drawn to\n  private _pages: AtlasPage[] = [];\n  public get pages(): { canvas: HTMLCanvasElement, version: number }[] { return this._pages; }\n\n  // The set of atlas pages that can be written to\n  private _activePages: AtlasPage[] = [];\n\n  private _tmpCanvas: HTMLCanvasElement;\n  // A temporary context that glyphs are drawn to before being transfered to the atlas.\n  private _tmpCtx: CanvasRenderingContext2D;\n\n  private _workBoundingBox: IBoundingBox = { top: 0, left: 0, bottom: 0, right: 0 };\n  private _workAttributeData: AttributeData = new AttributeData();\n\n  private _textureSize: number = 512;\n\n  public static maxAtlasPages: number | undefined;\n  public static maxTextureSize: number | undefined;\n\n  private readonly _onAddTextureAtlasCanvas = new EventEmitter<HTMLCanvasElement>();\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n  private readonly _onRemoveTextureAtlasCanvas = new EventEmitter<HTMLCanvasElement>();\n  public readonly onRemoveTextureAtlasCanvas = this._onRemoveTextureAtlasCanvas.event;\n\n  constructor(\n    private readonly _document: Document,\n    private readonly _config: ICharAtlasConfig,\n    private readonly _unicodeService: IUnicodeService\n  ) {\n    this._createNewPage();\n    this._tmpCanvas = createCanvas(\n      _document,\n      this._config.deviceCellWidth * 4 + TMP_CANVAS_GLYPH_PADDING * 2,\n      this._config.deviceCellHeight + TMP_CANVAS_GLYPH_PADDING * 2\n    );\n    this._tmpCtx = throwIfFalsy(this._tmpCanvas.getContext('2d', {\n      alpha: this._config.allowTransparency,\n      willReadFrequently: true\n    }));\n  }\n\n  public dispose(): void {\n    for (const page of this.pages) {\n      page.canvas.remove();\n    }\n    this._onAddTextureAtlasCanvas.dispose();\n  }\n\n  public warmUp(): void {\n    if (!this._didWarmUp) {\n      this._doWarmUp();\n      this._didWarmUp = true;\n    }\n  }\n\n  private _doWarmUp(): void {\n    // Pre-fill with ASCII 33-126, this is not urgent and done in idle callbacks\n    const queue = new IdleTaskQueue();\n    for (let i = 33; i < 126; i++) {\n      queue.enqueue(() => {\n        if (!this._cacheMap.get(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT)) {\n          const rasterizedGlyph = this._drawToCache(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT);\n          this._cacheMap.set(i, DEFAULT_COLOR, DEFAULT_COLOR, DEFAULT_EXT, rasterizedGlyph);\n        }\n      });\n    }\n  }\n\n  private _requestClearModel = false;\n  public beginFrame(): boolean {\n    return this._requestClearModel;\n  }\n\n  public clearTexture(): void {\n    if (this._pages[0].currentRow.x === 0 && this._pages[0].currentRow.y === 0) {\n      return;\n    }\n    for (const page of this._pages) {\n      page.clear();\n    }\n    this._cacheMap.clear();\n    this._cacheMapCombined.clear();\n    this._didWarmUp = false;\n  }\n\n  private _createNewPage(): AtlasPage {\n    // Try merge the set of the 4 most used pages of the largest size. This is is deferred to a\n    // microtask to ensure it does not interrupt textures that will be rendered in the current\n    // animation frame which would result in blank rendered areas. This is actually not that\n    // expensive relative to drawing the glyphs, so there is no need to wait for an idle callback.\n    if (TextureAtlas.maxAtlasPages && this._pages.length >= Math.max(4, TextureAtlas.maxAtlasPages)) {\n      // Find the set of the largest 4 images, below the maximum size, with the highest\n      // percentages used\n      const pagesBySize = this._pages.filter(e => {\n        return e.canvas.width * 2 <= (TextureAtlas.maxTextureSize || Constants.FORCED_MAX_TEXTURE_SIZE);\n      }).sort((a, b) => {\n        if (b.canvas.width !== a.canvas.width) {\n          return b.canvas.width - a.canvas.width;\n        }\n        return b.percentageUsed - a.percentageUsed;\n      });\n      let sameSizeI = -1;\n      let size = 0;\n      for (let i = 0; i < pagesBySize.length; i++) {\n        if (pagesBySize[i].canvas.width !== size) {\n          sameSizeI = i;\n          size = pagesBySize[i].canvas.width;\n        } else if (i - sameSizeI === 3) {\n          break;\n        }\n      }\n\n      // Gather details of the merge\n      const mergingPages = pagesBySize.slice(sameSizeI, sameSizeI + 4);\n      const sortedMergingPagesIndexes = mergingPages.map(e => e.glyphs[0].texturePage).sort((a, b) => a > b ? 1 : -1);\n      const mergedPageIndex = this.pages.length - mergingPages.length;\n\n      // Merge into the new page\n      const mergedPage = this._mergePages(mergingPages, mergedPageIndex);\n      mergedPage.version++;\n\n      // Delete the pages, shifting glyph texture pages as needed\n      for (let i = sortedMergingPagesIndexes.length - 1; i >= 0; i--) {\n        this._deletePage(sortedMergingPagesIndexes[i]);\n      }\n\n      // Add the new merged page to the end\n      this.pages.push(mergedPage);\n\n      // Request the model to be cleared to refresh all texture pages.\n      this._requestClearModel = true;\n      this._onAddTextureAtlasCanvas.fire(mergedPage.canvas);\n    }\n\n    // All new atlas pages are created small as they are highly dynamic\n    const newPage = new AtlasPage(this._document, this._textureSize);\n    this._pages.push(newPage);\n    this._activePages.push(newPage);\n    this._onAddTextureAtlasCanvas.fire(newPage.canvas);\n    return newPage;\n  }\n\n  private _mergePages(mergingPages: AtlasPage[], mergedPageIndex: number): AtlasPage {\n    const mergedSize = mergingPages[0].canvas.width * 2;\n    const mergedPage = new AtlasPage(this._document, mergedSize, mergingPages);\n    for (const [i, p] of mergingPages.entries()) {\n      const xOffset = i * p.canvas.width % mergedSize;\n      const yOffset = Math.floor(i / 2) * p.canvas.height;\n      mergedPage.ctx.drawImage(p.canvas, xOffset, yOffset);\n      for (const g of p.glyphs) {\n        g.texturePage = mergedPageIndex;\n        g.sizeClipSpace.x = g.size.x / mergedSize;\n        g.sizeClipSpace.y = g.size.y / mergedSize;\n        g.texturePosition.x += xOffset;\n        g.texturePosition.y += yOffset;\n        g.texturePositionClipSpace.x = g.texturePosition.x / mergedSize;\n        g.texturePositionClipSpace.y = g.texturePosition.y / mergedSize;\n      }\n\n      this._onRemoveTextureAtlasCanvas.fire(p.canvas);\n\n      // Remove the merging page from active pages if it was there\n      const index = this._activePages.indexOf(p);\n      if (index !== -1) {\n        this._activePages.splice(index, 1);\n      }\n    }\n    return mergedPage;\n  }\n\n  private _deletePage(pageIndex: number): void {\n    this._pages.splice(pageIndex, 1);\n    for (let j = pageIndex; j < this._pages.length; j++) {\n      const adjustingPage = this._pages[j];\n      for (const g of adjustingPage.glyphs) {\n        g.texturePage--;\n      }\n      adjustingPage.version++;\n    }\n  }\n\n  public getRasterizedGlyphCombinedChar(chars: string, bg: number, fg: number, ext: number, restrictToCellHeight: boolean): IRasterizedGlyph {\n    return this._getFromCacheMap(this._cacheMapCombined, chars, bg, fg, ext, restrictToCellHeight);\n  }\n\n  public getRasterizedGlyph(code: number, bg: number, fg: number, ext: number, restrictToCellHeight: boolean): IRasterizedGlyph {\n    return this._getFromCacheMap(this._cacheMap, code, bg, fg, ext, restrictToCellHeight);\n  }\n\n  /**\n   * Gets the glyphs texture coords, drawing the texture if it's not already\n   */\n  private _getFromCacheMap(\n    cacheMap: FourKeyMap<string | number, number, number, number, IRasterizedGlyph>,\n    key: string | number,\n    bg: number,\n    fg: number,\n    ext: number,\n    restrictToCellHeight: boolean = false\n  ): IRasterizedGlyph {\n    $glyph = cacheMap.get(key, bg, fg, ext);\n    if (!$glyph) {\n      $glyph = this._drawToCache(key, bg, fg, ext, restrictToCellHeight);\n      cacheMap.set(key, bg, fg, ext, $glyph);\n    }\n    return $glyph;\n  }\n\n  private _getColorFromAnsiIndex(idx: number): IColor {\n    if (idx >= this._config.colors.ansi.length) {\n      throw new Error('No color found for idx ' + idx);\n    }\n    return this._config.colors.ansi[idx];\n  }\n\n  private _getBackgroundColor(bgColorMode: number, bgColor: number, inverse: boolean, dim: boolean): IColor {\n    if (this._config.allowTransparency) {\n      // The background color might have some transparency, so we need to render it as fully\n      // transparent in the atlas. Otherwise we'd end up drawing the transparent background twice\n      // around the anti-aliased edges of the glyph, and it would look too dark.\n      return NULL_COLOR;\n    }\n\n    let result: IColor;\n    switch (bgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        result = this._getColorFromAnsiIndex(bgColor);\n        break;\n      case Attributes.CM_RGB:\n        const arr = AttributeData.toColorRGB(bgColor);\n        // TODO: This object creation is slow\n        result = rgba.toColor(arr[0], arr[1], arr[2]);\n        break;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          result = color.opaque(this._config.colors.foreground);\n        } else {\n          result = this._config.colors.background;\n        }\n        break;\n    }\n\n    return result;\n  }\n\n  private _getForegroundColor(bg: number, bgColorMode: number, bgColor: number, fg: number, fgColorMode: number, fgColor: number, inverse: boolean, dim: boolean, bold: boolean, excludeFromContrastRatioDemands: boolean): IColor {\n    const minimumContrastColor = this._getMinimumContrastColor(bg, bgColorMode, bgColor, fg, fgColorMode, fgColor, false, bold, dim, excludeFromContrastRatioDemands);\n    if (minimumContrastColor) {\n      return minimumContrastColor;\n    }\n\n    let result: IColor;\n    switch (fgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        if (this._config.drawBoldTextInBrightColors && bold && fgColor < 8) {\n          fgColor += 8;\n        }\n        result = this._getColorFromAnsiIndex(fgColor);\n        break;\n      case Attributes.CM_RGB:\n        const arr = AttributeData.toColorRGB(fgColor);\n        result = rgba.toColor(arr[0], arr[1], arr[2]);\n        break;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          result = this._config.colors.background;\n        } else {\n          result = this._config.colors.foreground;\n        }\n    }\n\n    // Always use an opaque color regardless of allowTransparency\n    if (this._config.allowTransparency) {\n      result = color.opaque(result);\n    }\n\n    // Apply dim to the color, opacity is fine to use for the foreground color\n    if (dim) {\n      result = color.multiplyOpacity(result, DIM_OPACITY);\n    }\n\n    return result;\n  }\n\n  private _resolveBackgroundRgba(bgColorMode: number, bgColor: number, inverse: boolean): number {\n    switch (bgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        return this._getColorFromAnsiIndex(bgColor).rgba;\n      case Attributes.CM_RGB:\n        return bgColor << 8;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          return this._config.colors.foreground.rgba;\n        }\n        return this._config.colors.background.rgba;\n    }\n  }\n\n  private _resolveForegroundRgba(fgColorMode: number, fgColor: number, inverse: boolean, bold: boolean): number {\n    switch (fgColorMode) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:\n        if (this._config.drawBoldTextInBrightColors && bold && fgColor < 8) {\n          fgColor += 8;\n        }\n        return this._getColorFromAnsiIndex(fgColor).rgba;\n      case Attributes.CM_RGB:\n        return fgColor << 8;\n      case Attributes.CM_DEFAULT:\n      default:\n        if (inverse) {\n          return this._config.colors.background.rgba;\n        }\n        return this._config.colors.foreground.rgba;\n    }\n  }\n\n  private _getMinimumContrastColor(bg: number, bgColorMode: number, bgColor: number, fg: number, fgColorMode: number, fgColor: number, inverse: boolean, bold: boolean, dim: boolean, excludeFromContrastRatioDemands: boolean): IColor | undefined {\n    if (this._config.minimumContrastRatio === 1 || excludeFromContrastRatioDemands) {\n      return undefined;\n    }\n\n    // Try get from cache first\n    const cache = this._getContrastCache(dim);\n    const adjustedColor = cache.getColor(bg, fg);\n    if (adjustedColor !== undefined) {\n      return adjustedColor || undefined;\n    }\n\n    const bgRgba = this._resolveBackgroundRgba(bgColorMode, bgColor, inverse);\n    const fgRgba = this._resolveForegroundRgba(fgColorMode, fgColor, inverse, bold);\n    // Dim cells only require half the contrast, otherwise they wouldn't be distinguishable from\n    // non-dim cells\n    const result = rgba.ensureContrastRatio(bgRgba, fgRgba, this._config.minimumContrastRatio / (dim ? 2 : 1));\n\n    if (!result) {\n      cache.setColor(bg, fg, null);\n      return undefined;\n    }\n\n    const color = rgba.toColor(\n      (result >> 24) & 0xFF,\n      (result >> 16) & 0xFF,\n      (result >> 8) & 0xFF\n    );\n    cache.setColor(bg, fg, color);\n\n    return color;\n  }\n\n  private _getContrastCache(dim: boolean): IColorContrastCache {\n    if (dim) {\n      return this._config.colors.halfContrastCache;\n    }\n    return this._config.colors.contrastCache;\n  }\n\n  @traceCall\n  private _drawToCache(codeOrChars: number | string, bg: number, fg: number, ext: number, restrictToCellHeight: boolean = false): IRasterizedGlyph {\n    const chars = typeof codeOrChars === 'number' ? String.fromCharCode(codeOrChars) : codeOrChars;\n\n    // Uncomment for debugging\n    // console.log(`draw to cache \"${chars}\"`, bg, fg, ext);\n\n    // Allow 1 cell width per character, with a minimum of 2 (CJK), plus some padding. This is used\n    // to draw the glyph to the canvas as well as to restrict the bounding box search to ensure\n    // giant ligatures (eg. =====>) don't impact overall performance.\n    const allowedWidth = Math.min(this._config.deviceCellWidth * Math.max(chars.length, 2) + TMP_CANVAS_GLYPH_PADDING * 2, this._textureSize);\n    if (this._tmpCanvas.width < allowedWidth) {\n      this._tmpCanvas.width = allowedWidth;\n    }\n    // Include line height when drawing glyphs\n    const allowedHeight = Math.min(this._config.deviceCellHeight + TMP_CANVAS_GLYPH_PADDING * 4, this._textureSize);\n    if (this._tmpCanvas.height < allowedHeight) {\n      this._tmpCanvas.height = allowedHeight;\n    }\n    this._tmpCtx.save();\n\n    this._workAttributeData.fg = fg;\n    this._workAttributeData.bg = bg;\n    this._workAttributeData.extended.ext = ext;\n\n    const invisible = !!this._workAttributeData.isInvisible();\n    if (invisible) {\n      return NULL_RASTERIZED_GLYPH;\n    }\n\n    const bold = !!this._workAttributeData.isBold();\n    const inverse = !!this._workAttributeData.isInverse();\n    const dim = !!this._workAttributeData.isDim();\n    const italic = !!this._workAttributeData.isItalic();\n    const underline = !!this._workAttributeData.isUnderline();\n    const strikethrough = !!this._workAttributeData.isStrikethrough();\n    const overline = !!this._workAttributeData.isOverline();\n    let fgColor = this._workAttributeData.getFgColor();\n    let fgColorMode = this._workAttributeData.getFgColorMode();\n    let bgColor = this._workAttributeData.getBgColor();\n    let bgColorMode = this._workAttributeData.getBgColorMode();\n    if (inverse) {\n      const temp = fgColor;\n      fgColor = bgColor;\n      bgColor = temp;\n      const temp2 = fgColorMode;\n      fgColorMode = bgColorMode;\n      bgColorMode = temp2;\n    }\n\n    // draw the background\n    const backgroundColor = this._getBackgroundColor(bgColorMode, bgColor, inverse, dim);\n    // Use a 'copy' composite operation to clear any existing glyph out of _tmpCtxWithAlpha,\n    // regardless of transparency in backgroundColor\n    this._tmpCtx.globalCompositeOperation = 'copy';\n    this._tmpCtx.fillStyle = backgroundColor.css;\n    this._tmpCtx.fillRect(0, 0, this._tmpCanvas.width, this._tmpCanvas.height);\n    this._tmpCtx.globalCompositeOperation = 'source-over';\n\n    // draw the foreground/glyph\n    const fontWeight = bold ? this._config.fontWeightBold : this._config.fontWeight;\n    const fontStyle = italic ? 'italic' : '';\n    this._tmpCtx.font =\n      `${fontStyle} ${fontWeight} ${this._config.fontSize * this._config.devicePixelRatio}px ${this._config.fontFamily}`;\n    this._tmpCtx.textBaseline = TEXT_BASELINE;\n\n    const powerlineGlyph = chars.length === 1 && isPowerlineGlyph(chars.charCodeAt(0));\n    const restrictedPowerlineGlyph = chars.length === 1 && isRestrictedPowerlineGlyph(chars.charCodeAt(0));\n    const foregroundColor = this._getForegroundColor(bg, bgColorMode, bgColor, fg, fgColorMode, fgColor, inverse, dim, bold, excludeFromContrastRatioDemands(chars.charCodeAt(0)));\n    this._tmpCtx.fillStyle = foregroundColor.css;\n\n    // For powerline glyphs left/top padding is excluded (https://github.com/microsoft/vscode/issues/120129)\n    const padding = restrictedPowerlineGlyph ? 0 : TMP_CANVAS_GLYPH_PADDING * 2;\n\n    // Draw custom characters if applicable\n    let customGlyph = false;\n    if (this._config.customGlyphs !== false) {\n      customGlyph = tryDrawCustomChar(this._tmpCtx, chars, padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight, this._config.fontSize, this._config.devicePixelRatio);\n    }\n\n    // Whether to clear pixels based on a threshold difference between the glyph color and the\n    // background color. This should be disabled when the glyph contains multiple colors such as\n    // underline colors to prevent important colors could get cleared.\n    let enableClearThresholdCheck = !powerlineGlyph;\n\n    let chWidth: number;\n    if (typeof codeOrChars === 'number') {\n      chWidth = this._unicodeService.wcwidth(codeOrChars);\n    } else {\n      chWidth = this._unicodeService.getStringCellWidth(codeOrChars);\n    }\n\n    // Draw underline\n    if (underline) {\n      this._tmpCtx.save();\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 15));\n      // When the line width is odd, draw at a 0.5 position\n      const yOffset = lineWidth % 2 === 1 ? 0.5 : 0;\n      this._tmpCtx.lineWidth = lineWidth;\n\n      // Underline color\n      if (this._workAttributeData.isUnderlineColorDefault()) {\n        this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      } else if (this._workAttributeData.isUnderlineColorRGB()) {\n        enableClearThresholdCheck = false;\n        this._tmpCtx.strokeStyle = `rgb(${AttributeData.toColorRGB(this._workAttributeData.getUnderlineColor()).join(',')})`;\n      } else {\n        enableClearThresholdCheck = false;\n        let fg = this._workAttributeData.getUnderlineColor();\n        if (this._config.drawBoldTextInBrightColors && this._workAttributeData.isBold() && fg < 8) {\n          fg += 8;\n        }\n        this._tmpCtx.strokeStyle = this._getColorFromAnsiIndex(fg).css;\n      }\n\n      // Underline style/stroke\n      this._tmpCtx.beginPath();\n      const xLeft = padding;\n      const yTop = Math.ceil(padding + this._config.deviceCharHeight) - yOffset - (restrictToCellHeight ? lineWidth * 2 : 0);\n      const yMid = yTop + lineWidth;\n      const yBot = yTop + lineWidth * 2;\n\n      for (let i = 0; i < chWidth; i++) {\n        this._tmpCtx.save();\n        const xChLeft = xLeft + i * this._config.deviceCellWidth;\n        const xChRight = xLeft + (i + 1) * this._config.deviceCellWidth;\n        const xChMid = xChLeft + this._config.deviceCellWidth / 2;\n        switch (this._workAttributeData.extended.underlineStyle) {\n          case UnderlineStyle.DOUBLE:\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            this._tmpCtx.moveTo(xChLeft, yBot);\n            this._tmpCtx.lineTo(xChRight, yBot);\n            break;\n          case UnderlineStyle.CURLY:\n            // Choose the bezier top and bottom based on the device pixel ratio, the curly line is\n            // made taller when the line width is  as otherwise it's not very clear otherwise.\n            const yCurlyBot = lineWidth <= 1 ? yBot : Math.ceil(padding + this._config.deviceCharHeight - lineWidth / 2) - yOffset;\n            const yCurlyTop = lineWidth <= 1 ? yTop : Math.ceil(padding + this._config.deviceCharHeight + lineWidth / 2) - yOffset;\n            // Clip the left and right edges of the underline such that it can be drawn just outside\n            // the edge of the cell to ensure a continuous stroke when there are multiple underlined\n            // glyphs adjacent to one another.\n            const clipRegion = new Path2D();\n            clipRegion.rect(xChLeft, yTop, this._config.deviceCellWidth, yBot - yTop);\n            this._tmpCtx.clip(clipRegion);\n            // Start 1/2 cell before and end 1/2 cells after to ensure a smooth curve with other\n            // cells\n            this._tmpCtx.moveTo(xChLeft - this._config.deviceCellWidth / 2, yMid);\n            this._tmpCtx.bezierCurveTo(\n              xChLeft - this._config.deviceCellWidth / 2, yCurlyTop,\n              xChLeft, yCurlyTop,\n              xChLeft, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChLeft, yCurlyBot,\n              xChMid, yCurlyBot,\n              xChMid, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChMid, yCurlyTop,\n              xChRight, yCurlyTop,\n              xChRight, yMid\n            );\n            this._tmpCtx.bezierCurveTo(\n              xChRight, yCurlyBot,\n              xChRight + this._config.deviceCellWidth / 2, yCurlyBot,\n              xChRight + this._config.deviceCellWidth / 2, yMid\n            );\n            break;\n          case UnderlineStyle.DOTTED:\n            this._tmpCtx.setLineDash([Math.round(lineWidth), Math.round(lineWidth)]);\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n          case UnderlineStyle.DASHED:\n            this._tmpCtx.setLineDash([this._config.devicePixelRatio * 4, this._config.devicePixelRatio * 3]);\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n          case UnderlineStyle.SINGLE:\n          default:\n            this._tmpCtx.moveTo(xChLeft, yTop);\n            this._tmpCtx.lineTo(xChRight, yTop);\n            break;\n        }\n        this._tmpCtx.stroke();\n        this._tmpCtx.restore();\n      }\n      this._tmpCtx.restore();\n\n      // Draw stroke in the background color for non custom characters in order to give an outline\n      // between the text and the underline. Only do this when font size is >= 12 as the underline\n      // looks odd when the font size is too small\n      if (!customGlyph && this._config.fontSize >= 12) {\n        // This only works when transparency is disabled because it's not clear how to clear stroked\n        // text\n        if (!this._config.allowTransparency && chars !== ' ') {\n          // Measure the text, only draw the stroke if there is a descent beyond an alphabetic text\n          // baseline\n          this._tmpCtx.save();\n          this._tmpCtx.textBaseline = 'alphabetic';\n          const metrics = this._tmpCtx.measureText(chars);\n          this._tmpCtx.restore();\n          if ('actualBoundingBoxDescent' in metrics && metrics.actualBoundingBoxDescent > 0) {\n            // This translates to 1/2 the line width in either direction\n            this._tmpCtx.save();\n            // Clip the region to only draw in valid pixels near the underline to avoid a slight\n            // outline around the whole glyph, as well as additional pixels in the glyph at the top\n            // which would increase GPU memory demands\n            const clipRegion = new Path2D();\n            clipRegion.rect(xLeft, yTop - Math.ceil(lineWidth / 2), this._config.deviceCellWidth * chWidth, yBot - yTop + Math.ceil(lineWidth / 2));\n            this._tmpCtx.clip(clipRegion);\n            this._tmpCtx.lineWidth = this._config.devicePixelRatio * 3;\n            this._tmpCtx.strokeStyle = backgroundColor.css;\n            this._tmpCtx.strokeText(chars, padding, padding + this._config.deviceCharHeight);\n            this._tmpCtx.restore();\n          }\n        }\n      }\n    }\n\n    // Overline\n    if (overline) {\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 15));\n      const yOffset = lineWidth % 2 === 1 ? 0.5 : 0;\n      this._tmpCtx.lineWidth = lineWidth;\n      this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      this._tmpCtx.beginPath();\n      this._tmpCtx.moveTo(padding, padding + yOffset);\n      this._tmpCtx.lineTo(padding + this._config.deviceCharWidth * chWidth, padding + yOffset);\n      this._tmpCtx.stroke();\n    }\n\n    // Draw the character\n    if (!customGlyph) {\n      this._tmpCtx.fillText(chars, padding, padding + this._config.deviceCharHeight);\n    }\n\n    // If this character is underscore and beyond the cell bounds, shift it up until it is visible\n    // even on the bottom row, try for a maximum of 5 pixels.\n    if (chars === '_' && !this._config.allowTransparency) {\n      let isBeyondCellBounds = clearColor(this._tmpCtx.getImageData(padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight), backgroundColor, foregroundColor, enableClearThresholdCheck);\n      if (isBeyondCellBounds) {\n        for (let offset = 1; offset <= 5; offset++) {\n          this._tmpCtx.save();\n          this._tmpCtx.fillStyle = backgroundColor.css;\n          this._tmpCtx.fillRect(0, 0, this._tmpCanvas.width, this._tmpCanvas.height);\n          this._tmpCtx.restore();\n          this._tmpCtx.fillText(chars, padding, padding + this._config.deviceCharHeight - offset);\n          isBeyondCellBounds = clearColor(this._tmpCtx.getImageData(padding, padding, this._config.deviceCellWidth, this._config.deviceCellHeight), backgroundColor, foregroundColor, enableClearThresholdCheck);\n          if (!isBeyondCellBounds) {\n            break;\n          }\n        }\n      }\n    }\n\n    // Draw strokethrough\n    if (strikethrough) {\n      const lineWidth = Math.max(1, Math.floor(this._config.fontSize * this._config.devicePixelRatio / 10));\n      const yOffset = this._tmpCtx.lineWidth % 2 === 1 ? 0.5 : 0; // When the width is odd, draw at 0.5 position\n      this._tmpCtx.lineWidth = lineWidth;\n      this._tmpCtx.strokeStyle = this._tmpCtx.fillStyle;\n      this._tmpCtx.beginPath();\n      this._tmpCtx.moveTo(padding, padding + Math.floor(this._config.deviceCharHeight / 2) - yOffset);\n      this._tmpCtx.lineTo(padding + this._config.deviceCharWidth * chWidth, padding + Math.floor(this._config.deviceCharHeight / 2) - yOffset);\n      this._tmpCtx.stroke();\n    }\n\n    this._tmpCtx.restore();\n\n    // clear the background from the character to avoid issues with drawing over the previous\n    // character if it extends past it's bounds\n    const imageData = this._tmpCtx.getImageData(\n      0, 0, this._tmpCanvas.width, this._tmpCanvas.height\n    );\n\n    // Clear out the background color and determine if the glyph is empty.\n    let isEmpty: boolean;\n    if (!this._config.allowTransparency) {\n      isEmpty = clearColor(imageData, backgroundColor, foregroundColor, enableClearThresholdCheck);\n    } else {\n      isEmpty = checkCompletelyTransparent(imageData);\n    }\n\n    // Handle empty glyphs\n    if (isEmpty) {\n      return NULL_RASTERIZED_GLYPH;\n    }\n\n    const rasterizedGlyph = this._findGlyphBoundingBox(imageData, this._workBoundingBox, allowedWidth, restrictedPowerlineGlyph, customGlyph, padding);\n\n    // Find the best atlas row to use\n    let activePage: AtlasPage;\n    let activeRow: ICharAtlasActiveRow;\n    while (true) {\n      // If there are no active pages (the last smallest 4 were merged), create a new one\n      if (this._activePages.length === 0) {\n        const newPage = this._createNewPage();\n        activePage = newPage;\n        activeRow = newPage.currentRow;\n        activeRow.height = rasterizedGlyph.size.y;\n        break;\n      }\n\n      // Get the best current row from all active pages\n      activePage = this._activePages[this._activePages.length - 1];\n      activeRow = activePage.currentRow;\n      for (const p of this._activePages) {\n        if (rasterizedGlyph.size.y <= p.currentRow.height) {\n          activePage = p;\n          activeRow = p.currentRow;\n        }\n      }\n\n      // TODO: This algorithm could be simplified:\n      // - Search for the page with ROW_PIXEL_THRESHOLD in mind\n      // - Keep track of current/fixed rows in a Map\n\n      // Replace the best current row with a fixed row if there is one at least as good as the\n      // current row. Search in reverse to prioritize filling in older pages.\n      for (let i = this._activePages.length - 1; i >= 0; i--) {\n        for (const row of this._activePages[i].fixedRows) {\n          if (row.height <= activeRow.height && rasterizedGlyph.size.y <= row.height) {\n            activePage = this._activePages[i];\n            activeRow = row;\n          }\n        }\n      }\n\n      // Create a new page if too much vertical space would be wasted or there is not enough room\n      // left in the page. The previous active row will become fixed in the process as it now has a\n      // fixed height\n      if (activeRow.y + rasterizedGlyph.size.y >= activePage.canvas.height || activeRow.height > rasterizedGlyph.size.y + Constants.ROW_PIXEL_THRESHOLD) {\n        // Create the new fixed height row, creating a new page if there isn't enough room on the\n        // current page\n        let wasPageAndRowFound = false;\n        if (activePage.currentRow.y + activePage.currentRow.height + rasterizedGlyph.size.y >= activePage.canvas.height) {\n          // Find the first page with room to create the new row on\n          let candidatePage: AtlasPage | undefined;\n          for (const p of this._activePages) {\n            if (p.currentRow.y + p.currentRow.height + rasterizedGlyph.size.y < p.canvas.height) {\n              candidatePage = p;\n              break;\n            }\n          }\n          if (candidatePage) {\n            activePage = candidatePage;\n          } else {\n            // Before creating a new atlas page that would trigger a page merge, check if the\n            // current active row is sufficient when ignoring the ROW_PIXEL_THRESHOLD. This will\n            // improve texture utilization by using the available space before the page is merged\n            // and becomes static.\n            if (\n              TextureAtlas.maxAtlasPages &&\n              this._pages.length >= TextureAtlas.maxAtlasPages &&\n              activeRow.y + rasterizedGlyph.size.y <= activePage.canvas.height &&\n              activeRow.height >= rasterizedGlyph.size.y &&\n              activeRow.x + rasterizedGlyph.size.x <= activePage.canvas.width\n            ) {\n              // activePage and activeRow is already valid\n              wasPageAndRowFound = true;\n            } else {\n              // Create a new page if there is no room\n              const newPage = this._createNewPage();\n              activePage = newPage;\n              activeRow = newPage.currentRow;\n              activeRow.height = rasterizedGlyph.size.y;\n              wasPageAndRowFound = true;\n            }\n          }\n        }\n        if (!wasPageAndRowFound) {\n          // Fix the current row as the new row is being added below\n          if (activePage.currentRow.height > 0) {\n            activePage.fixedRows.push(activePage.currentRow);\n          }\n          activeRow = {\n            x: 0,\n            y: activePage.currentRow.y + activePage.currentRow.height,\n            height: rasterizedGlyph.size.y\n          };\n          activePage.fixedRows.push(activeRow);\n\n          // Create the new current row below the new fixed height row\n          activePage.currentRow = {\n            x: 0,\n            y: activeRow.y + activeRow.height,\n            height: 0\n          };\n        }\n        // TODO: Remove pages from _activePages when all rows are filled\n      }\n\n      // Exit the loop if there is enough room in the row\n      if (activeRow.x + rasterizedGlyph.size.x <= activePage.canvas.width) {\n        break;\n      }\n\n      // If there is not enough room in the current row, finish it and try again\n      if (activeRow === activePage.currentRow) {\n        activeRow.x = 0;\n        activeRow.y += activeRow.height;\n        activeRow.height = 0;\n      } else {\n        activePage.fixedRows.splice(activePage.fixedRows.indexOf(activeRow), 1);\n      }\n    }\n\n    // Record texture position\n    rasterizedGlyph.texturePage = this._pages.indexOf(activePage);\n    rasterizedGlyph.texturePosition.x = activeRow.x;\n    rasterizedGlyph.texturePosition.y = activeRow.y;\n    rasterizedGlyph.texturePositionClipSpace.x = activeRow.x / activePage.canvas.width;\n    rasterizedGlyph.texturePositionClipSpace.y = activeRow.y / activePage.canvas.height;\n\n    // Fix the clipspace position as pages may be of differing size\n    rasterizedGlyph.sizeClipSpace.x /= activePage.canvas.width;\n    rasterizedGlyph.sizeClipSpace.y /= activePage.canvas.height;\n\n    // Update atlas current row, for fixed rows the glyph height will never be larger than the row\n    // height\n    activeRow.height = Math.max(activeRow.height, rasterizedGlyph.size.y);\n    activeRow.x += rasterizedGlyph.size.x;\n\n    // putImageData doesn't do any blending, so it will overwrite any existing cache entry for us\n    activePage.ctx.putImageData(\n      imageData,\n      rasterizedGlyph.texturePosition.x - this._workBoundingBox.left,\n      rasterizedGlyph.texturePosition.y - this._workBoundingBox.top,\n      this._workBoundingBox.left,\n      this._workBoundingBox.top,\n      rasterizedGlyph.size.x,\n      rasterizedGlyph.size.y\n    );\n    activePage.addGlyph(rasterizedGlyph);\n    activePage.version++;\n\n    return rasterizedGlyph;\n  }\n\n  /**\n   * Given an ImageData object, find the bounding box of the non-transparent\n   * portion of the texture and return an IRasterizedGlyph with these\n   * dimensions.\n   * @param imageData The image data to read.\n   * @param boundingBox An IBoundingBox to put the clipped bounding box values.\n   */\n  private _findGlyphBoundingBox(imageData: ImageData, boundingBox: IBoundingBox, allowedWidth: number, restrictedGlyph: boolean, customGlyph: boolean, padding: number): IRasterizedGlyph {\n    boundingBox.top = 0;\n    const height = restrictedGlyph ? this._config.deviceCellHeight : this._tmpCanvas.height;\n    const width = restrictedGlyph ? this._config.deviceCellWidth : allowedWidth;\n    let found = false;\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.top = y;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.left = 0;\n    found = false;\n    for (let x = 0; x < padding + width; x++) {\n      for (let y = 0; y < height; y++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.left = x;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.right = width;\n    found = false;\n    for (let x = padding + width - 1; x >= padding; x--) {\n      for (let y = 0; y < height; y++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.right = x;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    boundingBox.bottom = height;\n    found = false;\n    for (let y = height - 1; y >= 0; y--) {\n      for (let x = 0; x < width; x++) {\n        const alphaOffset = y * this._tmpCanvas.width * 4 + x * 4 + 3;\n        if (imageData.data[alphaOffset] !== 0) {\n          boundingBox.bottom = y;\n          found = true;\n          break;\n        }\n      }\n      if (found) {\n        break;\n      }\n    }\n    return {\n      texturePage: 0,\n      texturePosition: { x: 0, y: 0 },\n      texturePositionClipSpace: { x: 0, y: 0 },\n      size: {\n        x: boundingBox.right - boundingBox.left + 1,\n        y: boundingBox.bottom - boundingBox.top + 1\n      },\n      sizeClipSpace: {\n        x: (boundingBox.right - boundingBox.left + 1),\n        y: (boundingBox.bottom - boundingBox.top + 1)\n      },\n      offset: {\n        x: -boundingBox.left + padding + ((restrictedGlyph || customGlyph) ? Math.floor((this._config.deviceCellWidth - this._config.deviceCharWidth) / 2) : 0),\n        y: -boundingBox.top + padding + ((restrictedGlyph || customGlyph) ? this._config.lineHeight === 1 ? 0 : Math.round((this._config.deviceCellHeight - this._config.deviceCharHeight) / 2) : 0)\n      }\n    };\n  }\n}\n\nclass AtlasPage {\n  public readonly canvas: HTMLCanvasElement;\n  public readonly ctx: CanvasRenderingContext2D;\n\n  private _usedPixels: number = 0;\n  public get percentageUsed(): number { return this._usedPixels / (this.canvas.width * this.canvas.height); }\n\n  private readonly _glyphs: IRasterizedGlyph[] = [];\n  public get glyphs(): ReadonlyArray<IRasterizedGlyph> { return this._glyphs; }\n  public addGlyph(glyph: IRasterizedGlyph): void {\n    this._glyphs.push(glyph);\n    this._usedPixels += glyph.size.x * glyph.size.y;\n  }\n\n  /**\n   * Used to check whether the canvas of the atlas page has changed.\n   */\n  public version = 0;\n\n  // Texture atlas current positioning data. The texture packing strategy used is to fill from\n  // left-to-right and top-to-bottom. When the glyph being written is less than half of the current\n  // row's height, the following happens:\n  //\n  // - The current row becomes the fixed height row A\n  // - A new fixed height row B the exact size of the glyph is created below the current row\n  // - A new dynamic height current row is created below B\n  //\n  // This strategy does a good job preventing space being wasted for very short glyphs such as\n  // underscores, hyphens etc. or those with underlines rendered.\n  public currentRow: ICharAtlasActiveRow = {\n    x: 0,\n    y: 0,\n    height: 0\n  };\n  public readonly fixedRows: ICharAtlasActiveRow[] = [];\n\n  constructor(\n    document: Document,\n    size: number,\n    sourcePages?: AtlasPage[]\n  ) {\n    if (sourcePages) {\n      for (const p of sourcePages) {\n        this._glyphs.push(...p.glyphs);\n        this._usedPixels += p._usedPixels;\n      }\n    }\n    this.canvas = createCanvas(document, size, size);\n    // The canvas needs alpha because we use clearColor to convert the background color to alpha.\n    // It might also contain some characters with transparent backgrounds if allowTransparency is\n    // set.\n    this.ctx = throwIfFalsy(this.canvas.getContext('2d', { alpha: true }));\n  }\n\n  public clear(): void {\n    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    this.currentRow.x = 0;\n    this.currentRow.y = 0;\n    this.currentRow.height = 0;\n    this.fixedRows.length = 0;\n    this.version++;\n  }\n}\n\n/**\n * Makes a particular rgb color and colors that are nearly the same in an ImageData completely\n * transparent.\n * @returns True if the result is \"empty\", meaning all pixels are fully transparent.\n */\nfunction clearColor(imageData: ImageData, bg: IColor, fg: IColor, enableThresholdCheck: boolean): boolean {\n  // Get color channels\n  const r = bg.rgba >>> 24;\n  const g = bg.rgba >>> 16 & 0xFF;\n  const b = bg.rgba >>> 8 & 0xFF;\n  const fgR = fg.rgba >>> 24;\n  const fgG = fg.rgba >>> 16 & 0xFF;\n  const fgB = fg.rgba >>> 8 & 0xFF;\n\n  // Calculate a threshold that when below a color will be treated as transpart when the sum of\n  // channel value differs. This helps improve rendering when glyphs overlap with others. This\n  // threshold is calculated relative to the difference between the background and foreground to\n  // ensure important details of the glyph are always shown, even when the contrast ratio is low.\n  // The number 12 is largely arbitrary to ensure the pixels that escape the cell in the test case\n  // were covered (fg=#8ae234, bg=#c4a000).\n  const threshold = Math.floor((Math.abs(r - fgR) + Math.abs(g - fgG) + Math.abs(b - fgB)) / 12);\n\n  // Set alpha channel of relevent pixels to 0\n  let isEmpty = true;\n  for (let offset = 0; offset < imageData.data.length; offset += 4) {\n    // Check exact match\n    if (imageData.data[offset] === r &&\n        imageData.data[offset + 1] === g &&\n        imageData.data[offset + 2] === b) {\n      imageData.data[offset + 3] = 0;\n    } else {\n      // Check the threshold based difference\n      if (enableThresholdCheck &&\n          (Math.abs(imageData.data[offset] - r) +\n          Math.abs(imageData.data[offset + 1] - g) +\n          Math.abs(imageData.data[offset + 2] - b)) < threshold) {\n        imageData.data[offset + 3] = 0;\n      } else {\n        isEmpty = false;\n      }\n    }\n  }\n\n  return isEmpty;\n}\n\nfunction checkCompletelyTransparent(imageData: ImageData): boolean {\n  for (let offset = 0; offset < imageData.data.length; offset += 4) {\n    if (imageData.data[offset + 3] > 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction createCanvas(document: Document, width: number, height: number): HTMLCanvasElement {\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IBufferLine, ICellData, CharData } from 'common/Types';\nimport { ICharacterJoiner } from 'browser/Types';\nimport { AttributeData } from 'common/buffer/AttributeData';\nimport { WHITESPACE_CELL_CHAR, Content } from 'common/buffer/Constants';\nimport { CellData } from 'common/buffer/CellData';\nimport { IBufferService } from 'common/services/Services';\nimport { ICharacterJoinerService } from 'browser/services/Services';\n\nexport class JoinedCellData extends AttributeData implements ICellData {\n  private _width: number;\n  // .content carries no meaning for joined CellData, simply nullify it\n  // thus we have to overload all other .content accessors\n  public content: number = 0;\n  public fg: number;\n  public bg: number;\n  public combinedData: string = '';\n\n  constructor(firstCell: ICellData, chars: string, width: number) {\n    super();\n    this.fg = firstCell.fg;\n    this.bg = firstCell.bg;\n    this.combinedData = chars;\n    this._width = width;\n  }\n\n  public isCombined(): number {\n    // always mark joined cell data as combined\n    return Content.IS_COMBINED_MASK;\n  }\n\n  public getWidth(): number {\n    return this._width;\n  }\n\n  public getChars(): string {\n    return this.combinedData;\n  }\n\n  public getCode(): number {\n    // code always gets the highest possible fake codepoint (read as -1)\n    // this is needed as code is used by caches as identifier\n    return 0x1FFFFF;\n  }\n\n  public setFromCharData(value: CharData): void {\n    throw new Error('not implemented');\n  }\n\n  public getAsCharData(): CharData {\n    return [this.fg, this.getChars(), this.getWidth(), this.getCode()];\n  }\n}\n\nexport class CharacterJoinerService implements ICharacterJoinerService {\n  public serviceBrand: undefined;\n\n  private _characterJoiners: ICharacterJoiner[] = [];\n  private _nextCharacterJoinerId: number = 0;\n  private _workCell: CellData = new CellData();\n\n  constructor(\n    @IBufferService private _bufferService: IBufferService\n  ) { }\n\n  public register(handler: (text: string) => [number, number][]): number {\n    const joiner: ICharacterJoiner = {\n      id: this._nextCharacterJoinerId++,\n      handler\n    };\n\n    this._characterJoiners.push(joiner);\n    return joiner.id;\n  }\n\n  public deregister(joinerId: number): boolean {\n    for (let i = 0; i < this._characterJoiners.length; i++) {\n      if (this._characterJoiners[i].id === joinerId) {\n        this._characterJoiners.splice(i, 1);\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  public getJoinedCharacters(row: number): [number, number][] {\n    if (this._characterJoiners.length === 0) {\n      return [];\n    }\n\n    const line = this._bufferService.buffer.lines.get(row);\n    if (!line || line.length === 0) {\n      return [];\n    }\n\n    const ranges: [number, number][] = [];\n    const lineStr = line.translateToString(true);\n\n    // Because some cells can be represented by multiple javascript characters,\n    // we track the cell and the string indexes separately. This allows us to\n    // translate the string ranges we get from the joiners back into cell ranges\n    // for use when rendering\n    let rangeStartColumn = 0;\n    let currentStringIndex = 0;\n    let rangeStartStringIndex = 0;\n    let rangeAttrFG = line.getFg(0);\n    let rangeAttrBG = line.getBg(0);\n\n    for (let x = 0; x < line.getTrimmedLength(); x++) {\n      line.loadCell(x, this._workCell);\n\n      if (this._workCell.getWidth() === 0) {\n        // If this character is of width 0, skip it.\n        continue;\n      }\n\n      // End of range\n      if (this._workCell.fg !== rangeAttrFG || this._workCell.bg !== rangeAttrBG) {\n        // If we ended up with a sequence of more than one character,\n        // look for ranges to join.\n        if (x - rangeStartColumn > 1) {\n          const joinedRanges = this._getJoinedRanges(\n            lineStr,\n            rangeStartStringIndex,\n            currentStringIndex,\n            line,\n            rangeStartColumn\n          );\n          for (let i = 0; i < joinedRanges.length; i++) {\n            ranges.push(joinedRanges[i]);\n          }\n        }\n\n        // Reset our markers for a new range.\n        rangeStartColumn = x;\n        rangeStartStringIndex = currentStringIndex;\n        rangeAttrFG = this._workCell.fg;\n        rangeAttrBG = this._workCell.bg;\n      }\n\n      currentStringIndex += this._workCell.getChars().length || WHITESPACE_CELL_CHAR.length;\n    }\n\n    // Process any trailing ranges.\n    if (this._bufferService.cols - rangeStartColumn > 1) {\n      const joinedRanges = this._getJoinedRanges(\n        lineStr,\n        rangeStartStringIndex,\n        currentStringIndex,\n        line,\n        rangeStartColumn\n      );\n      for (let i = 0; i < joinedRanges.length; i++) {\n        ranges.push(joinedRanges[i]);\n      }\n    }\n\n    return ranges;\n  }\n\n  /**\n   * Given a segment of a line of text, find all ranges of text that should be\n   * joined in a single rendering unit. Ranges are internally converted to\n   * column ranges, rather than string ranges.\n   * @param line String representation of the full line of text\n   * @param startIndex Start position of the range to search in the string (inclusive)\n   * @param endIndex End position of the range to search in the string (exclusive)\n   */\n  private _getJoinedRanges(line: string, startIndex: number, endIndex: number, lineData: IBufferLine, startCol: number): [number, number][] {\n    const text = line.substring(startIndex, endIndex);\n    // At this point we already know that there is at least one joiner so\n    // we can just pull its value and assign it directly rather than\n    // merging it into an empty array, which incurs unnecessary writes.\n    let allJoinedRanges: [number, number][] = [];\n    try {\n      allJoinedRanges = this._characterJoiners[0].handler(text);\n    } catch (error) {\n      console.error(error);\n    }\n    for (let i = 1; i < this._characterJoiners.length; i++) {\n      // We merge any overlapping ranges across the different joiners\n      try {\n        const joinerRanges = this._characterJoiners[i].handler(text);\n        for (let j = 0; j < joinerRanges.length; j++) {\n          CharacterJoinerService._mergeRanges(allJoinedRanges, joinerRanges[j]);\n        }\n      } catch (error) {\n        console.error(error);\n      }\n    }\n    this._stringRangesToCellRanges(allJoinedRanges, lineData, startCol);\n    return allJoinedRanges;\n  }\n\n  /**\n   * Modifies the provided ranges in-place to adjust for variations between\n   * string length and cell width so that the range represents a cell range,\n   * rather than the string range the joiner provides.\n   * @param ranges String ranges containing start (inclusive) and end (exclusive) index\n   * @param line Cell data for the relevant line in the terminal\n   * @param startCol Offset within the line to start from\n   */\n  private _stringRangesToCellRanges(ranges: [number, number][], line: IBufferLine, startCol: number): void {\n    let currentRangeIndex = 0;\n    let currentRangeStarted = false;\n    let currentStringIndex = 0;\n    let currentRange = ranges[currentRangeIndex];\n\n    // If we got through all of the ranges, stop searching\n    if (!currentRange) {\n      return;\n    }\n\n    for (let x = startCol; x < this._bufferService.cols; x++) {\n      const width = line.getWidth(x);\n      const length = line.getString(x).length || WHITESPACE_CELL_CHAR.length;\n\n      // We skip zero-width characters when creating the string to join the text\n      // so we do the same here\n      if (width === 0) {\n        continue;\n      }\n\n      // Adjust the start of the range\n      if (!currentRangeStarted && currentRange[0] <= currentStringIndex) {\n        currentRange[0] = x;\n        currentRangeStarted = true;\n      }\n\n      // Adjust the end of the range\n      if (currentRange[1] <= currentStringIndex) {\n        currentRange[1] = x;\n\n        // We're finished with this range, so we move to the next one\n        currentRange = ranges[++currentRangeIndex];\n\n        // If there are no more ranges left, stop searching\n        if (!currentRange) {\n          break;\n        }\n\n        // Ranges can be on adjacent characters. Because the end index of the\n        // ranges are exclusive, this means that the index for the start of a\n        // range can be the same as the end index of the previous range. To\n        // account for the start of the next range, we check here just in case.\n        if (currentRange[0] <= currentStringIndex) {\n          currentRange[0] = x;\n          currentRangeStarted = true;\n        } else {\n          currentRangeStarted = false;\n        }\n      }\n\n      // Adjust the string index based on the character length to line up with\n      // the column adjustment\n      currentStringIndex += length;\n    }\n\n    // If there is still a range left at the end, it must extend all the way to\n    // the end of the line.\n    if (currentRange) {\n      currentRange[1] = this._bufferService.cols;\n    }\n  }\n\n  /**\n   * Merges the range defined by the provided start and end into the list of\n   * existing ranges. The merge is done in place on the existing range for\n   * performance and is also returned.\n   * @param ranges Existing range list\n   * @param newRange Tuple of two numbers representing the new range to merge in.\n   * @returns The ranges input with the new range merged in place\n   */\n  private static _mergeRanges(ranges: [number, number][], newRange: [number, number]): [number, number][] {\n    let inRange = false;\n    for (let i = 0; i < ranges.length; i++) {\n      const range = ranges[i];\n      if (!inRange) {\n        if (newRange[1] <= range[0]) {\n          // Case 1: New range is before the search range\n          ranges.splice(i, 0, newRange);\n          return ranges;\n        }\n\n        if (newRange[1] <= range[1]) {\n          // Case 2: New range is either wholly contained within the\n          // search range or overlaps with the front of it\n          range[0] = Math.min(newRange[0], range[0]);\n          return ranges;\n        }\n\n        if (newRange[0] < range[1]) {\n          // Case 3: New range either wholly contains the search range\n          // or overlaps with the end of it\n          range[0] = Math.min(newRange[0], range[0]);\n          inRange = true;\n        }\n\n        // Case 4: New range starts after the search range\n        continue;\n      } else {\n        if (newRange[1] <= range[0]) {\n          // Case 5: New range extends from previous range but doesn't\n          // reach the current one\n          ranges[i - 1][1] = newRange[1];\n          return ranges;\n        }\n\n        if (newRange[1] <= range[1]) {\n          // Case 6: New range extends from prvious range into the\n          // current range\n          ranges[i - 1][1] = Math.max(newRange[1], range[1]);\n          ranges.splice(i, 1);\n          return ranges;\n        }\n\n        // Case 7: New range extends from previous range past the\n        // end of the current range\n        ranges.splice(i, 1);\n        i--;\n      }\n    }\n\n    if (inRange) {\n      // Case 8: New range extends past the last existing range\n      ranges[ranges.length - 1][1] = newRange[1];\n    } else {\n      // Case 9: New range starts after the last existing range\n      ranges.push(newRange);\n    }\n\n    return ranges;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isNode } from 'common/Platform';\nimport { IColor, IColorRGB } from 'common/Types';\n\nlet $r = 0;\nlet $g = 0;\nlet $b = 0;\nlet $a = 0;\n\nexport const NULL_COLOR: IColor = {\n  css: '#00000000',\n  rgba: 0\n};\n\n/**\n * Helper functions where the source type is \"channels\" (individual color channels as numbers).\n */\nexport namespace channels {\n  export function toCss(r: number, g: number, b: number, a?: number): string {\n    if (a !== undefined) {\n      return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}${toPaddedHex(a)}`;\n    }\n    return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}`;\n  }\n\n  export function toRgba(r: number, g: number, b: number, a: number = 0xFF): number {\n    // Note: The aggregated number is RGBA32 (BE), thus needs to be converted to ABGR32\n    // on LE systems, before it can be used for direct 32-bit buffer writes.\n    // >>> 0 forces an unsigned int\n    return (r << 24 | g << 16 | b << 8 | a) >>> 0;\n  }\n}\n\n/**\n * Helper functions where the source type is `IColor`.\n */\nexport namespace color {\n  export function blend(bg: IColor, fg: IColor): IColor {\n    $a = (fg.rgba & 0xFF) / 255;\n    if ($a === 1) {\n      return {\n        css: fg.css,\n        rgba: fg.rgba\n      };\n    }\n    const fgR = (fg.rgba >> 24) & 0xFF;\n    const fgG = (fg.rgba >> 16) & 0xFF;\n    const fgB = (fg.rgba >> 8) & 0xFF;\n    const bgR = (bg.rgba >> 24) & 0xFF;\n    const bgG = (bg.rgba >> 16) & 0xFF;\n    const bgB = (bg.rgba >> 8) & 0xFF;\n    $r = bgR + Math.round((fgR - bgR) * $a);\n    $g = bgG + Math.round((fgG - bgG) * $a);\n    $b = bgB + Math.round((fgB - bgB) * $a);\n    const css = channels.toCss($r, $g, $b);\n    const rgba = channels.toRgba($r, $g, $b);\n    return { css, rgba };\n  }\n\n  export function isOpaque(color: IColor): boolean {\n    return (color.rgba & 0xFF) === 0xFF;\n  }\n\n  export function ensureContrastRatio(bg: IColor, fg: IColor, ratio: number): IColor | undefined {\n    const result = rgba.ensureContrastRatio(bg.rgba, fg.rgba, ratio);\n    if (!result) {\n      return undefined;\n    }\n    return rgba.toColor(\n      (result >> 24 & 0xFF),\n      (result >> 16 & 0xFF),\n      (result >> 8  & 0xFF)\n    );\n  }\n\n  export function opaque(color: IColor): IColor {\n    const rgbaColor = (color.rgba | 0xFF) >>> 0;\n    [$r, $g, $b] = rgba.toChannels(rgbaColor);\n    return {\n      css: channels.toCss($r, $g, $b),\n      rgba: rgbaColor\n    };\n  }\n\n  export function opacity(color: IColor, opacity: number): IColor {\n    $a = Math.round(opacity * 0xFF);\n    [$r, $g, $b] = rgba.toChannels(color.rgba);\n    return {\n      css: channels.toCss($r, $g, $b, $a),\n      rgba: channels.toRgba($r, $g, $b, $a)\n    };\n  }\n\n  export function multiplyOpacity(color: IColor, factor: number): IColor {\n    $a = color.rgba & 0xFF;\n    return opacity(color, ($a * factor) / 0xFF);\n  }\n\n  export function toColorRGB(color: IColor): IColorRGB {\n    return [(color.rgba >> 24) & 0xFF, (color.rgba >> 16) & 0xFF, (color.rgba >> 8) & 0xFF];\n  }\n}\n\n/**\n * Helper functions where the source type is \"css\" (string: '#rgb', '#rgba', '#rrggbb',\n * '#rrggbbaa').\n */\nexport namespace css {\n  let $ctx: CanvasRenderingContext2D | undefined;\n  let $litmusColor: CanvasGradient | undefined;\n  if (!isNode) {\n    const canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    const ctx = canvas.getContext('2d', {\n      willReadFrequently: true\n    });\n    if (ctx) {\n      $ctx = ctx;\n      $ctx.globalCompositeOperation = 'copy';\n      $litmusColor = $ctx.createLinearGradient(0, 0, 1, 1);\n    }\n  }\n\n  /**\n   * Converts a css string to an IColor, this should handle all valid CSS color strings and will\n   * throw if it's invalid. The ideal format to use is `#rrggbb[aa]` as it's the fastest to parse.\n   *\n   * Only `#rgb[a]`, `#rrggbb[aa]`, `rgb()` and `rgba()` formats are supported when run in a Node\n   * environment.\n   */\n  export function toColor(css: string): IColor {\n    // Formats: #rgb[a] and #rrggbb[aa]\n    if (css.match(/#[\\da-f]{3,8}/i)) {\n      switch (css.length) {\n        case 4: { // #rgb\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          return rgba.toColor($r, $g, $b);\n        }\n        case 5: { // #rgba\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          $a = parseInt(css.slice(4, 5).repeat(2), 16);\n          return rgba.toColor($r, $g, $b, $a);\n        }\n        case 7: // #rrggbb\n          return {\n            css,\n            rgba: (parseInt(css.slice(1), 16) << 8 | 0xFF) >>> 0\n          };\n        case 9: // #rrggbbaa\n          return {\n            css,\n            rgba: parseInt(css.slice(1), 16) >>> 0\n          };\n      }\n    }\n\n    // Formats: rgb() or rgba()\n    const rgbaMatch = css.match(/rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*(,\\s*(0|1|\\d?\\.(\\d+))\\s*)?\\)/);\n    if (rgbaMatch) {\n      $r = parseInt(rgbaMatch[1]);\n      $g = parseInt(rgbaMatch[2]);\n      $b = parseInt(rgbaMatch[3]);\n      $a = Math.round((rgbaMatch[5] === undefined ? 1 : parseFloat(rgbaMatch[5])) * 0xFF);\n      return rgba.toColor($r, $g, $b, $a);\n    }\n\n    // Validate the context is available for canvas-based color parsing\n    if (!$ctx || !$litmusColor) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Validate the color using canvas fillStyle\n    // See https://html.spec.whatwg.org/multipage/canvas.html#fill-and-stroke-styles\n    $ctx.fillStyle = $litmusColor;\n    $ctx.fillStyle = css;\n    if (typeof $ctx.fillStyle !== 'string') {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    $ctx.fillRect(0, 0, 1, 1);\n    [$r, $g, $b, $a] = $ctx.getImageData(0, 0, 1, 1).data;\n\n    // Validate the color is non-transparent as color hue gets lost when drawn to the canvas\n    if ($a !== 0xFF) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Extract the color from the canvas' fillStyle property which exposes the color value in rgba()\n    // format\n    // See https://html.spec.whatwg.org/multipage/canvas.html#serialisation-of-a-color\n    return {\n      rgba: channels.toRgba($r, $g, $b, $a),\n      css\n    };\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgb\" (number: 0xrrggbb).\n */\nexport namespace rgb {\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param rgb The color to use.\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance(rgb: number): number {\n    return relativeLuminance2(\n      (rgb >> 16) & 0xFF,\n      (rgb >> 8 ) & 0xFF,\n      (rgb      ) & 0xFF);\n  }\n\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param r The red channel (0x00 to 0xFF).\n   * @param g The green channel (0x00 to 0xFF).\n   * @param b The blue channel (0x00 to 0xFF).\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance2(r: number, g: number, b: number): number {\n    const rs = r / 255;\n    const gs = g / 255;\n    const bs = b / 255;\n    const rr = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);\n    const rg = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);\n    const rb = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);\n    return rr * 0.2126 + rg * 0.7152 + rb * 0.0722;\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgba\" (number: 0xrrggbbaa).\n */\nexport namespace rgba {\n  /**\n   * Given a foreground color and a background color, either increase or reduce the luminance of the\n   * foreground color until the specified contrast ratio is met. If pure white or black is hit\n   * without the contrast ratio being met, go the other direction using the background color as the\n   * foreground color and take either the first or second result depending on which has the higher\n   * contrast ratio.\n   *\n   * `undefined` will be returned if the contrast ratio is already met.\n   *\n   * @param bgRgba The background color in rgba format.\n   * @param fgRgba The foreground color in rgba format.\n   * @param ratio The contrast ratio to achieve.\n   */\n  export function ensureContrastRatio(bgRgba: number, fgRgba: number, ratio: number): number | undefined {\n    const bgL = rgb.relativeLuminance(bgRgba >> 8);\n    const fgL = rgb.relativeLuminance(fgRgba >> 8);\n    const cr = contrastRatio(bgL, fgL);\n    if (cr < ratio) {\n      if (fgL < bgL) {\n        const resultA = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n        if (resultARatio < ratio) {\n          const resultB = increaseLuminance(bgRgba, fgRgba, ratio);\n          const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n          return resultARatio > resultBRatio ? resultA : resultB;\n        }\n        return resultA;\n      }\n      const resultA = increaseLuminance(bgRgba, fgRgba, ratio);\n      const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n      if (resultARatio < ratio) {\n        const resultB = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n        return resultARatio > resultBRatio ? resultA : resultB;\n      }\n      return resultA;\n    }\n    return undefined;\n  }\n\n  export function reduceLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to reducing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR > 0 || fgG > 0 || fgB > 0)) {\n      // Reduce by 10% until the ratio is hit\n      fgR -= Math.max(0, Math.ceil(fgR * 0.1));\n      fgG -= Math.max(0, Math.ceil(fgG * 0.1));\n      fgB -= Math.max(0, Math.ceil(fgB * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  export function increaseLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to increasing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR < 0xFF || fgG < 0xFF || fgB < 0xFF)) {\n      // Increase by 10% until the ratio is hit\n      fgR = Math.min(0xFF, fgR + Math.ceil((255 - fgR) * 0.1));\n      fgG = Math.min(0xFF, fgG + Math.ceil((255 - fgG) * 0.1));\n      fgB = Math.min(0xFF, fgB + Math.ceil((255 - fgB) * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  // FIXME: Move this to channels NS?\n  export function toChannels(value: number): [number, number, number, number] {\n    return [(value >> 24) & 0xFF, (value >> 16) & 0xFF, (value >> 8) & 0xFF, value & 0xFF];\n  }\n\n  export function toColor(r: number, g: number, b: number, a?: number): IColor {\n    return {\n      css: channels.toCss(r, g, b, a),\n      rgba: channels.toRgba(r, g, b, a)\n    };\n  }\n}\n\nexport function toPaddedHex(c: number): string {\n  const s = c.toString(16);\n  return s.length < 2 ? '0' + s : s;\n}\n\n/**\n * Gets the contrast ratio between two relative luminance values.\n * @param l1 The first relative luminance.\n * @param l2 The first relative luminance.\n * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef\n */\nexport function contrastRatio(l1: number, l2: number): number {\n  if (l1 < l2) {\n    return (l2 + 0.05) / (l1 + 0.05);\n  }\n  return (l1 + 0.05) / (l2 + 0.05);\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\ninterface IListener<T, U = void> {\n  (arg1: T, arg2: U): void;\n}\n\nexport interface IEvent<T, U = void> {\n  (listener: (arg1: T, arg2: U) => any): IDisposable;\n}\n\nexport interface IEventEmitter<T, U = void> {\n  event: IEvent<T, U>;\n  fire(arg1: T, arg2: U): void;\n  dispose(): void;\n}\n\nexport class EventEmitter<T, U = void> implements IEventEmitter<T, U> {\n  private _listeners: IListener<T, U>[] = [];\n  private _event?: IEvent<T, U>;\n  private _disposed: boolean = false;\n\n  public get event(): IEvent<T, U> {\n    if (!this._event) {\n      this._event = (listener: (arg1: T, arg2: U) => any) => {\n        this._listeners.push(listener);\n        const disposable = {\n          dispose: () => {\n            if (!this._disposed) {\n              for (let i = 0; i < this._listeners.length; i++) {\n                if (this._listeners[i] === listener) {\n                  this._listeners.splice(i, 1);\n                  return;\n                }\n              }\n            }\n          }\n        };\n        return disposable;\n      };\n    }\n    return this._event;\n  }\n\n  public fire(arg1: T, arg2: U): void {\n    const queue: IListener<T, U>[] = [];\n    for (let i = 0; i < this._listeners.length; i++) {\n      queue.push(this._listeners[i]);\n    }\n    for (let i = 0; i < queue.length; i++) {\n      queue[i].call(undefined, arg1, arg2);\n    }\n  }\n\n  public dispose(): void {\n    this.clearListeners();\n    this._disposed = true;\n  }\n\n  public clearListeners(): void {\n    if (this._listeners) {\n      this._listeners.length = 0;\n    }\n  }\n}\n\nexport function forwardEvent<T>(from: IEvent<T>, to: IEventEmitter<T>): IDisposable {\n  return from(e => to.fire(e));\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\n/**\n * A base class that can be extended to provide convenience methods for managing the lifecycle of an\n * object and its components.\n */\nexport abstract class Disposable implements IDisposable {\n  protected _disposables: IDisposable[] = [];\n  protected _isDisposed: boolean = false;\n\n  /**\n   * Disposes the object, triggering the `dispose` method on all registered IDisposables.\n   */\n  public dispose(): void {\n    this._isDisposed = true;\n    for (const d of this._disposables) {\n      d.dispose();\n    }\n    this._disposables.length = 0;\n  }\n\n  /**\n   * Registers a disposable object.\n   * @param d The disposable to register.\n   * @returns The disposable.\n   */\n  public register<T extends IDisposable>(d: T): T {\n    this._disposables.push(d);\n    return d;\n  }\n\n  /**\n   * Unregisters a disposable object if it has been registered, if not do\n   * nothing.\n   * @param d The disposable to unregister.\n   */\n  public unregister<T extends IDisposable>(d: T): void {\n    const index = this._disposables.indexOf(d);\n    if (index !== -1) {\n      this._disposables.splice(index, 1);\n    }\n  }\n}\n\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n  private _value?: T;\n  private _isDisposed = false;\n\n  /**\n   * Gets the value if it exists.\n   */\n  public get value(): T | undefined {\n    return this._isDisposed ? undefined : this._value;\n  }\n\n  /**\n   * Sets the value, disposing of the old value if it exists.\n   */\n  public set value(value: T | undefined) {\n    if (this._isDisposed || value === this._value) {\n      return;\n    }\n    this._value?.dispose();\n    this._value = value;\n  }\n\n  /**\n   * Resets the stored value and disposes of the previously stored value.\n   */\n  public clear(): void {\n    this.value = undefined;\n  }\n\n  public dispose(): void {\n    this._isDisposed = true;\n    this._value?.dispose();\n    this._value = undefined;\n  }\n}\n\n/**\n * Wrap a function in a disposable.\n */\nexport function toDisposable(f: () => void): IDisposable {\n  return { dispose: f };\n}\n\n/**\n * Dispose of all disposables in an array and set its length to 0.\n */\nexport function disposeArray(disposables: IDisposable[]): void {\n  for (const d of disposables) {\n    d.dispose();\n  }\n  disposables.length = 0;\n}\n\n/**\n * Creates a disposable that will dispose of an array of disposables when disposed.\n */\nexport function getDisposeArrayDisposable(array: IDisposable[]): IDisposable {\n  return { dispose: () => disposeArray(array) };\n}\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nexport class TwoKeyMap<TFirst extends string | number, TSecond extends string | number, TValue> {\n  private _data: { [bg: string | number]: { [fg: string | number]: TValue | undefined } | undefined } = {};\n\n  public set(first: TFirst, second: TSecond, value: TValue): void {\n    if (!this._data[first]) {\n      this._data[first] = {};\n    }\n    this._data[first as string | number]![second] = value;\n  }\n\n  public get(first: TFirst, second: TSecond): TValue | undefined {\n    return this._data[first as string | number] ? this._data[first as string | number]![second] : undefined;\n  }\n\n  public clear(): void {\n    this._data = {};\n  }\n}\n\nexport class FourKeyMap<TFirst extends string | number, TSecond extends string | number, TThird extends string | number, TFourth extends string | number, TValue> {\n  private _data: TwoKeyMap<TFirst, TSecond, TwoKeyMap<TThird, TFourth, TValue>> = new TwoKeyMap();\n\n  public set(first: TFirst, second: TSecond, third: TThird, fourth: TFourth, value: TValue): void {\n    if (!this._data.get(first, second)) {\n      this._data.set(first, second, new TwoKeyMap());\n    }\n    this._data.get(first, second)!.set(third, fourth, value);\n  }\n\n  public get(first: TFirst, second: TSecond, third: TThird, fourth: TFourth): TValue | undefined {\n    return this._data.get(first, second)?.get(third, fourth);\n  }\n\n  public clear(): void {\n    this._data.clear();\n  }\n}\n", "/**\n * Copyright (c) 2016 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\ninterface INavigator {\n  userAgent: string;\n  language: string;\n  platform: string;\n}\n\n// We're declaring a navigator global here as we expect it in all runtimes (node and browser), but\n// we want this module to live in common.\ndeclare const navigator: INavigator;\n\nexport const isNode = (typeof navigator === 'undefined') ? true : false;\nconst userAgent = (isNode) ? 'node' : navigator.userAgent;\nconst platform = (isNode) ? 'node' : navigator.platform;\n\nexport const isFirefox = userAgent.includes('Firefox');\nexport const isLegacyEdge = userAgent.includes('Edge');\nexport const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);\nexport function getSafariVersion(): number {\n  if (!isSafari) {\n    return 0;\n  }\n  const majorVersion = userAgent.match(/Version\\/(\\d+)/);\n  if (majorVersion === null || majorVersion.length < 2) {\n    return 0;\n  }\n  return parseInt(majorVersion[1]);\n}\n\n// Find the users platform. We use this to interpret the meta key\n// and ISO third level shifts.\n// http://stackoverflow.com/q/19877924/577598\nexport const isMac = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'].includes(platform);\nexport const isIpad = platform === 'iPad';\nexport const isIphone = platform === 'iPhone';\nexport const isWindows = ['Windows', 'Win16', 'Win32', 'WinCE'].includes(platform);\nexport const isLinux = platform.indexOf('Linux') >= 0;\n// Note that when this is true, isLinux will also be true.\nexport const isChromeOS = /\\bCrOS\\b/.test(userAgent);\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { isNode } from 'common/Platform';\n\ninterface ITaskQueue {\n  /**\n   * Adds a task to the queue which will run in a future idle callback.\n   * To avoid perceivable stalls on the mainthread, tasks with heavy workload\n   * should split their work into smaller pieces and return `true` to get\n   * called again until the work is done (on falsy return value).\n   */\n  enqueue(task: () => boolean | void): void;\n\n  /**\n   * Flushes the queue, running all remaining tasks synchronously.\n   */\n  flush(): void;\n\n  /**\n   * Clears any remaining tasks from the queue, these will not be run.\n   */\n  clear(): void;\n}\n\ninterface ITaskDeadline {\n  timeRemaining(): number;\n}\ntype CallbackWithDeadline = (deadline: ITaskDeadline) => void;\n\nabstract class TaskQueue implements ITaskQueue {\n  private _tasks: (() => boolean | void)[] = [];\n  private _idleCallback?: number;\n  private _i = 0;\n\n  protected abstract _requestCallback(callback: CallbackWithDeadline): number;\n  protected abstract _cancelCallback(identifier: number): void;\n\n  public enqueue(task: () => boolean | void): void {\n    this._tasks.push(task);\n    this._start();\n  }\n\n  public flush(): void {\n    while (this._i < this._tasks.length) {\n      if (!this._tasks[this._i]()) {\n        this._i++;\n      }\n    }\n    this.clear();\n  }\n\n  public clear(): void {\n    if (this._idleCallback) {\n      this._cancelCallback(this._idleCallback);\n      this._idleCallback = undefined;\n    }\n    this._i = 0;\n    this._tasks.length = 0;\n  }\n\n  private _start(): void {\n    if (!this._idleCallback) {\n      this._idleCallback = this._requestCallback(this._process.bind(this));\n    }\n  }\n\n  private _process(deadline: ITaskDeadline): void {\n    this._idleCallback = undefined;\n    let taskDuration = 0;\n    let longestTask = 0;\n    let lastDeadlineRemaining = deadline.timeRemaining();\n    let deadlineRemaining = 0;\n    while (this._i < this._tasks.length) {\n      taskDuration = Date.now();\n      if (!this._tasks[this._i]()) {\n        this._i++;\n      }\n      // other than performance.now, Date.now might not be stable (changes on wall clock changes),\n      // this is not an issue here as a clock change during a short running task is very unlikely\n      // in case it still happened and leads to negative duration, simply assume 1 msec\n      taskDuration = Math.max(1, Date.now() - taskDuration);\n      longestTask = Math.max(taskDuration, longestTask);\n      // Guess the following task will take a similar time to the longest task in this batch, allow\n      // additional room to try avoid exceeding the deadline\n      deadlineRemaining = deadline.timeRemaining();\n      if (longestTask * 1.5 > deadlineRemaining) {\n        // Warn when the time exceeding the deadline is over 20ms, if this happens in practice the\n        // task should be split into sub-tasks to ensure the UI remains responsive.\n        if (lastDeadlineRemaining - taskDuration < -20) {\n          console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(lastDeadlineRemaining - taskDuration))}ms`);\n        }\n        this._start();\n        return;\n      }\n      lastDeadlineRemaining = deadlineRemaining;\n    }\n    this.clear();\n  }\n}\n\n/**\n * A queue of that runs tasks over several tasks via setTimeout, trying to maintain above 60 frames\n * per second. The tasks will run in the order they are enqueued, but they will run some time later,\n * and care should be taken to ensure they're non-urgent and will not introduce race conditions.\n */\nexport class PriorityTaskQueue extends TaskQueue {\n  protected _requestCallback(callback: CallbackWithDeadline): number {\n    return setTimeout(() => callback(this._createDeadline(16)));\n  }\n\n  protected _cancelCallback(identifier: number): void {\n    clearTimeout(identifier);\n  }\n\n  private _createDeadline(duration: number): ITaskDeadline {\n    const end = Date.now() + duration;\n    return {\n      timeRemaining: () => Math.max(0, end - Date.now())\n    };\n  }\n}\n\nclass IdleTaskQueueInternal extends TaskQueue {\n  protected _requestCallback(callback: IdleRequestCallback): number {\n    return requestIdleCallback(callback);\n  }\n\n  protected _cancelCallback(identifier: number): void {\n    cancelIdleCallback(identifier);\n  }\n}\n\n/**\n * A queue of that runs tasks over several idle callbacks, trying to respect the idle callback's\n * deadline given by the environment. The tasks will run in the order they are enqueued, but they\n * will run some time later, and care should be taken to ensure they're non-urgent and will not\n * introduce race conditions.\n *\n * This reverts to a {@link PriorityTaskQueue} if the environment does not support idle callbacks.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const IdleTaskQueue = (!isNode && 'requestIdleCallback' in window) ? IdleTaskQueueInternal : PriorityTaskQueue;\n\n/**\n * An object that tracks a single debounced task that will run on the next idle frame. When called\n * multiple times, only the last set task will run.\n */\nexport class DebouncedIdleTask {\n  private _queue: ITaskQueue;\n\n  constructor() {\n    this._queue = new IdleTaskQueue();\n  }\n\n  public set(task: () => boolean | void): void {\n    this._queue.clear();\n    this._queue.enqueue(task);\n  }\n\n  public flush(): void {\n    this._queue.flush();\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IAttributeData, IColorRGB, IExtendedAttrs } from 'common/Types';\nimport { Attributes, FgFlags, BgFlags, UnderlineStyle, ExtFlags } from 'common/buffer/Constants';\n\nexport class AttributeData implements IAttributeData {\n  public static toColorRGB(value: number): IColorRGB {\n    return [\n      value >>> Attributes.RED_SHIFT & 255,\n      value >>> Attributes.GREEN_SHIFT & 255,\n      value & 255\n    ];\n  }\n\n  public static fromColorRGB(value: IColorRGB): number {\n    return (value[0] & 255) << Attributes.RED_SHIFT | (value[1] & 255) << Attributes.GREEN_SHIFT | value[2] & 255;\n  }\n\n  public clone(): IAttributeData {\n    const newObj = new AttributeData();\n    newObj.fg = this.fg;\n    newObj.bg = this.bg;\n    newObj.extended = this.extended.clone();\n    return newObj;\n  }\n\n  // data\n  public fg = 0;\n  public bg = 0;\n  public extended: IExtendedAttrs = new ExtendedAttrs();\n\n  // flags\n  public isInverse(): number       { return this.fg & FgFlags.INVERSE; }\n  public isBold(): number          { return this.fg & FgFlags.BOLD; }\n  public isUnderline(): number     {\n    if (this.hasExtendedAttrs() && this.extended.underlineStyle !== UnderlineStyle.NONE) {\n      return 1;\n    }\n    return this.fg & FgFlags.UNDERLINE;\n  }\n  public isBlink(): number         { return this.fg & FgFlags.BLINK; }\n  public isInvisible(): number     { return this.fg & FgFlags.INVISIBLE; }\n  public isItalic(): number        { return this.bg & BgFlags.ITALIC; }\n  public isDim(): number           { return this.bg & BgFlags.DIM; }\n  public isStrikethrough(): number { return this.fg & FgFlags.STRIKETHROUGH; }\n  public isProtected(): number     { return this.bg & BgFlags.PROTECTED; }\n  public isOverline(): number      { return this.bg & BgFlags.OVERLINE; }\n\n  // color modes\n  public getFgColorMode(): number { return this.fg & Attributes.CM_MASK; }\n  public getBgColorMode(): number { return this.bg & Attributes.CM_MASK; }\n  public isFgRGB(): boolean       { return (this.fg & Attributes.CM_MASK) === Attributes.CM_RGB; }\n  public isBgRGB(): boolean       { return (this.bg & Attributes.CM_MASK) === Attributes.CM_RGB; }\n  public isFgPalette(): boolean   { return (this.fg & Attributes.CM_MASK) === Attributes.CM_P16 || (this.fg & Attributes.CM_MASK) === Attributes.CM_P256; }\n  public isBgPalette(): boolean   { return (this.bg & Attributes.CM_MASK) === Attributes.CM_P16 || (this.bg & Attributes.CM_MASK) === Attributes.CM_P256; }\n  public isFgDefault(): boolean   { return (this.fg & Attributes.CM_MASK) === 0; }\n  public isBgDefault(): boolean   { return (this.bg & Attributes.CM_MASK) === 0; }\n  public isAttributeDefault(): boolean { return this.fg === 0 && this.bg === 0; }\n\n  // colors\n  public getFgColor(): number {\n    switch (this.fg & Attributes.CM_MASK) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:  return this.fg & Attributes.PCOLOR_MASK;\n      case Attributes.CM_RGB:   return this.fg & Attributes.RGB_MASK;\n      default:                  return -1;  // CM_DEFAULT defaults to -1\n    }\n  }\n  public getBgColor(): number {\n    switch (this.bg & Attributes.CM_MASK) {\n      case Attributes.CM_P16:\n      case Attributes.CM_P256:  return this.bg & Attributes.PCOLOR_MASK;\n      case Attributes.CM_RGB:   return this.bg & Attributes.RGB_MASK;\n      default:                  return -1;  // CM_DEFAULT defaults to -1\n    }\n  }\n\n  // extended attrs\n  public hasExtendedAttrs(): number {\n    return this.bg & BgFlags.HAS_EXTENDED;\n  }\n  public updateExtended(): void {\n    if (this.extended.isEmpty()) {\n      this.bg &= ~BgFlags.HAS_EXTENDED;\n    } else {\n      this.bg |= BgFlags.HAS_EXTENDED;\n    }\n  }\n  public getUnderlineColor(): number {\n    if ((this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor) {\n      switch (this.extended.underlineColor & Attributes.CM_MASK) {\n        case Attributes.CM_P16:\n        case Attributes.CM_P256:  return this.extended.underlineColor & Attributes.PCOLOR_MASK;\n        case Attributes.CM_RGB:   return this.extended.underlineColor & Attributes.RGB_MASK;\n        default:                  return this.getFgColor();\n      }\n    }\n    return this.getFgColor();\n  }\n  public getUnderlineColorMode(): number {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? this.extended.underlineColor & Attributes.CM_MASK\n      : this.getFgColorMode();\n  }\n  public isUnderlineColorRGB(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_RGB\n      : this.isFgRGB();\n  }\n  public isUnderlineColorPalette(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_P16\n          || (this.extended.underlineColor & Attributes.CM_MASK) === Attributes.CM_P256\n      : this.isFgPalette();\n  }\n  public isUnderlineColorDefault(): boolean {\n    return (this.bg & BgFlags.HAS_EXTENDED) && ~this.extended.underlineColor\n      ? (this.extended.underlineColor & Attributes.CM_MASK) === 0\n      : this.isFgDefault();\n  }\n  public getUnderlineStyle(): UnderlineStyle {\n    return this.fg & FgFlags.UNDERLINE\n      ? (this.bg & BgFlags.HAS_EXTENDED ? this.extended.underlineStyle : UnderlineStyle.SINGLE)\n      : UnderlineStyle.NONE;\n  }\n}\n\n\n/**\n * Extended attributes for a cell.\n * Holds information about different underline styles and color.\n */\nexport class ExtendedAttrs implements IExtendedAttrs {\n  private _ext: number = 0;\n  public get ext(): number {\n    if (this._urlId) {\n      return (\n        (this._ext & ~ExtFlags.UNDERLINE_STYLE) |\n        (this.underlineStyle << 26)\n      );\n    }\n    return this._ext;\n  }\n  public set ext(value: number) { this._ext = value; }\n\n  public get underlineStyle(): UnderlineStyle {\n    // Always return the URL style if it has one\n    if (this._urlId) {\n      return UnderlineStyle.DASHED;\n    }\n    return (this._ext & ExtFlags.UNDERLINE_STYLE) >> 26;\n  }\n  public set underlineStyle(value: UnderlineStyle) {\n    this._ext &= ~ExtFlags.UNDERLINE_STYLE;\n    this._ext |= (value << 26) & ExtFlags.UNDERLINE_STYLE;\n  }\n\n  public get underlineColor(): number {\n    return this._ext & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n  public set underlineColor(value: number) {\n    this._ext &= ~(Attributes.CM_MASK | Attributes.RGB_MASK);\n    this._ext |= value & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n\n  private _urlId: number = 0;\n  public get urlId(): number {\n    return this._urlId;\n  }\n  public set urlId(value: number) {\n    this._urlId = value;\n  }\n\n  constructor(\n    ext: number = 0,\n    urlId: number = 0\n  ) {\n    this._ext = ext;\n    this._urlId = urlId;\n  }\n\n  public clone(): IExtendedAttrs {\n    return new ExtendedAttrs(this._ext, this._urlId);\n  }\n\n  /**\n   * Convenient method to indicate whether the object holds no additional information,\n   * that needs to be persistant in the buffer.\n   */\n  public isEmpty(): boolean {\n    return this.underlineStyle === UnderlineStyle.NONE && this._urlId === 0;\n  }\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { CharData, ICellData, IExtendedAttrs } from 'common/Types';\nimport { stringFromCodePoint } from 'common/input/TextDecoder';\nimport { CHAR_DATA_CHAR_INDEX, CHAR_DATA_WIDTH_INDEX, CHAR_DATA_ATTR_INDEX, Content } from 'common/buffer/Constants';\nimport { AttributeData, ExtendedAttrs } from 'common/buffer/AttributeData';\n\n/**\n * CellData - represents a single Cell in the terminal buffer.\n */\nexport class CellData extends AttributeData implements ICellData {\n  /** Helper to create CellData from CharData. */\n  public static fromCharData(value: CharData): CellData {\n    const obj = new CellData();\n    obj.setFromCharData(value);\n    return obj;\n  }\n  /** Primitives from terminal buffer. */\n  public content = 0;\n  public fg = 0;\n  public bg = 0;\n  public extended: IExtendedAttrs = new ExtendedAttrs();\n  public combinedData = '';\n  /** Whether cell contains a combined string. */\n  public isCombined(): number {\n    return this.content & Content.IS_COMBINED_MASK;\n  }\n  /** Width of the cell. */\n  public getWidth(): number {\n    return this.content >> Content.WIDTH_SHIFT;\n  }\n  /** JS string of the content. */\n  public getChars(): string {\n    if (this.content & Content.IS_COMBINED_MASK) {\n      return this.combinedData;\n    }\n    if (this.content & Content.CODEPOINT_MASK) {\n      return stringFromCodePoint(this.content & Content.CODEPOINT_MASK);\n    }\n    return '';\n  }\n  /**\n   * Codepoint of cell\n   * Note this returns the UTF32 codepoint of single chars,\n   * if content is a combined string it returns the codepoint\n   * of the last char in string to be in line with code in CharData.\n   */\n  public getCode(): number {\n    return (this.isCombined())\n      ? this.combinedData.charCodeAt(this.combinedData.length - 1)\n      : this.content & Content.CODEPOINT_MASK;\n  }\n  /** Set data from CharData */\n  public setFromCharData(value: CharData): void {\n    this.fg = value[CHAR_DATA_ATTR_INDEX];\n    this.bg = 0;\n    let combined = false;\n    // surrogates and combined strings need special treatment\n    if (value[CHAR_DATA_CHAR_INDEX].length > 2) {\n      combined = true;\n    }\n    else if (value[CHAR_DATA_CHAR_INDEX].length === 2) {\n      const code = value[CHAR_DATA_CHAR_INDEX].charCodeAt(0);\n      // if the 2-char string is a surrogate create single codepoint\n      // everything else is combined\n      if (0xD800 <= code && code <= 0xDBFF) {\n        const second = value[CHAR_DATA_CHAR_INDEX].charCodeAt(1);\n        if (0xDC00 <= second && second <= 0xDFFF) {\n          this.content = ((code - 0xD800) * 0x400 + second - 0xDC00 + 0x10000) | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n        }\n        else {\n          combined = true;\n        }\n      }\n      else {\n        combined = true;\n      }\n    }\n    else {\n      this.content = value[CHAR_DATA_CHAR_INDEX].charCodeAt(0) | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n    }\n    if (combined) {\n      this.combinedData = value[CHAR_DATA_CHAR_INDEX];\n      this.content = Content.IS_COMBINED_MASK | (value[CHAR_DATA_WIDTH_INDEX] << Content.WIDTH_SHIFT);\n    }\n  }\n  /** Get data as CharData. */\n  public getAsCharData(): CharData {\n    return [this.fg, this.getChars(), this.getWidth(), this.getCode()];\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nexport const DEFAULT_COLOR = 0;\nexport const DEFAULT_ATTR = (0 << 18) | (DEFAULT_COLOR << 9) | (256 << 0);\nexport const DEFAULT_EXT = 0;\n\nexport const CHAR_DATA_ATTR_INDEX = 0;\nexport const CHAR_DATA_CHAR_INDEX = 1;\nexport const CHAR_DATA_WIDTH_INDEX = 2;\nexport const CHAR_DATA_CODE_INDEX = 3;\n\n/**\n * Null cell - a real empty cell (containing nothing).\n * Note that code should always be 0 for a null cell as\n * several test condition of the buffer line rely on this.\n */\nexport const NULL_CELL_CHAR = '';\nexport const NULL_CELL_WIDTH = 1;\nexport const NULL_CELL_CODE = 0;\n\n/**\n * Whitespace cell.\n * This is meant as a replacement for empty cells when needed\n * during rendering lines to preserve correct aligment.\n */\nexport const WHITESPACE_CELL_CHAR = ' ';\nexport const WHITESPACE_CELL_WIDTH = 1;\nexport const WHITESPACE_CELL_CODE = 32;\n\n/**\n * Bitmasks for accessing data in `content`.\n */\nexport const enum Content {\n  /**\n   * bit 1..21    codepoint, max allowed in UTF32 is 0x10FFFF (21 bits taken)\n   *              read:   `codepoint = content & Content.codepointMask;`\n   *              write:  `content |= codepoint & Content.codepointMask;`\n   *                      shortcut if precondition `codepoint <= 0x10FFFF` is met:\n   *                      `content |= codepoint;`\n   */\n  CODEPOINT_MASK = 0x1FFFFF,\n\n  /**\n   * bit 22       flag indication whether a cell contains combined content\n   *              read:   `isCombined = content & Content.isCombined;`\n   *              set:    `content |= Content.isCombined;`\n   *              clear:  `content &= ~Content.isCombined;`\n   */\n  IS_COMBINED_MASK = 0x200000,  // 1 << 21\n\n  /**\n   * bit 1..22    mask to check whether a cell contains any string data\n   *              we need to check for codepoint and isCombined bits to see\n   *              whether a cell contains anything\n   *              read:   `isEmpty = !(content & Content.hasContent)`\n   */\n  HAS_CONTENT_MASK = 0x3FFFFF,\n\n  /**\n   * bit 23..24   wcwidth value of cell, takes 2 bits (ranges from 0..2)\n   *              read:   `width = (content & Content.widthMask) >> Content.widthShift;`\n   *                      `hasWidth = content & Content.widthMask;`\n   *                      as long as wcwidth is highest value in `content`:\n   *                      `width = content >> Content.widthShift;`\n   *              write:  `content |= (width << Content.widthShift) & Content.widthMask;`\n   *                      shortcut if precondition `0 <= width <= 3` is met:\n   *                      `content |= width << Content.widthShift;`\n   */\n  WIDTH_MASK = 0xC00000,   // 3 << 22\n  WIDTH_SHIFT = 22\n}\n\nexport const enum Attributes {\n  /**\n   * bit 1..8     blue in RGB, color in P256 and P16\n   */\n  BLUE_MASK = 0xFF,\n  BLUE_SHIFT = 0,\n  PCOLOR_MASK = 0xFF,\n  PCOLOR_SHIFT = 0,\n\n  /**\n   * bit 9..16    green in RGB\n   */\n  GREEN_MASK = 0xFF00,\n  GREEN_SHIFT = 8,\n\n  /**\n   * bit 17..24   red in RGB\n   */\n  RED_MASK = 0xFF0000,\n  RED_SHIFT = 16,\n\n  /**\n   * bit 25..26   color mode: DEFAULT (0) | P16 (1) | P256 (2) | RGB (3)\n   */\n  CM_MASK = 0x3000000,\n  CM_DEFAULT = 0,\n  CM_P16 = 0x1000000,\n  CM_P256 = 0x2000000,\n  CM_RGB = 0x3000000,\n\n  /**\n   * bit 1..24  RGB room\n   */\n  RGB_MASK = 0xFFFFFF\n}\n\nexport const enum FgFlags {\n  /**\n   * bit 27..32\n   */\n  INVERSE = 0x4000000,\n  BOLD = 0x8000000,\n  UNDERLINE = 0x10000000,\n  BLINK = 0x20000000,\n  INVISIBLE = 0x40000000,\n  STRIKETHROUGH = 0x80000000,\n}\n\nexport const enum BgFlags {\n  /**\n   * bit 27..32 (upper 2 unused)\n   */\n  ITALIC = 0x4000000,\n  DIM = 0x8000000,\n  HAS_EXTENDED = 0x10000000,\n  PROTECTED = 0x20000000,\n  OVERLINE = 0x40000000\n}\n\nexport const enum ExtFlags {\n  /**\n   * bit 27..32 (upper 3 unused)\n   */\n  UNDERLINE_STYLE = 0x1C000000\n}\n\nexport const enum UnderlineStyle {\n  NONE = 0,\n  SINGLE = 1,\n  DOUBLE = 2,\n  CURLY = 3,\n  DOTTED = 4,\n  DASHED = 5\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\n/**\n * Polyfill - Convert UTF32 codepoint into JS string.\n * Note: The built-in String.fromCodePoint happens to be much slower\n *       due to additional sanity checks. We can avoid them since\n *       we always operate on legal UTF32 (granted by the input decoders)\n *       and use this faster version instead.\n */\nexport function stringFromCodePoint(codePoint: number): string {\n  if (codePoint > 0xFFFF) {\n    codePoint -= 0x10000;\n    return String.fromCharCode((codePoint >> 10) + 0xD800) + String.fromCharCode((codePoint % 0x400) + 0xDC00);\n  }\n  return String.fromCharCode(codePoint);\n}\n\n/**\n * Convert UTF32 char codes into JS string.\n * Basically the same as `stringFromCodePoint` but for multiple codepoints\n * in a loop (which is a lot faster).\n */\nexport function utf32ToString(data: Uint32Array, start: number = 0, end: number = data.length): string {\n  let result = '';\n  for (let i = start; i < end; ++i) {\n    let codepoint = data[i];\n    if (codepoint > 0xFFFF) {\n      // JS strings are encoded as UTF16, thus a non BMP codepoint gets converted into a surrogate\n      // pair conversion rules:\n      //  - subtract 0x10000 from code point, leaving a 20 bit number\n      //  - add high 10 bits to 0xD800  --> first surrogate\n      //  - add low 10 bits to 0xDC00   --> second surrogate\n      codepoint -= 0x10000;\n      result += String.fromCharCode((codepoint >> 10) + 0xD800) + String.fromCharCode((codepoint % 0x400) + 0xDC00);\n    } else {\n      result += String.fromCharCode(codepoint);\n    }\n  }\n  return result;\n}\n\n/**\n * StringToUtf32 - decodes UTF16 sequences into UTF32 codepoints.\n * To keep the decoder in line with JS strings it handles single surrogates as UCS2.\n */\nexport class StringToUtf32 {\n  private _interim: number = 0;\n\n  /**\n   * Clears interim and resets decoder to clean state.\n   */\n  public clear(): void {\n    this._interim = 0;\n  }\n\n  /**\n   * Decode JS string to UTF32 codepoints.\n   * The methods assumes stream input and will store partly transmitted\n   * surrogate pairs and decode them with the next data chunk.\n   * Note: The method does no bound checks for target, therefore make sure\n   * the provided input data does not exceed the size of `target`.\n   * Returns the number of written codepoints in `target`.\n   */\n  public decode(input: string, target: Uint32Array): number {\n    const length = input.length;\n\n    if (!length) {\n      return 0;\n    }\n\n    let size = 0;\n    let startPos = 0;\n\n    // handle leftover surrogate high\n    if (this._interim) {\n      const second = input.charCodeAt(startPos++);\n      if (0xDC00 <= second && second <= 0xDFFF) {\n        target[size++] = (this._interim - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n      } else {\n        // illegal codepoint (USC2 handling)\n        target[size++] = this._interim;\n        target[size++] = second;\n      }\n      this._interim = 0;\n    }\n\n    for (let i = startPos; i < length; ++i) {\n      const code = input.charCodeAt(i);\n      // surrogate pair first\n      if (0xD800 <= code && code <= 0xDBFF) {\n        if (++i >= length) {\n          this._interim = code;\n          return size;\n        }\n        const second = input.charCodeAt(i);\n        if (0xDC00 <= second && second <= 0xDFFF) {\n          target[size++] = (code - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n        } else {\n          // illegal codepoint (USC2 handling)\n          target[size++] = code;\n          target[size++] = second;\n        }\n        continue;\n      }\n      if (code === 0xFEFF) {\n        // BOM\n        continue;\n      }\n      target[size++] = code;\n    }\n    return size;\n  }\n}\n\n/**\n * Utf8Decoder - decodes UTF8 byte sequences into UTF32 codepoints.\n */\nexport class Utf8ToUtf32 {\n  public interim: Uint8Array = new Uint8Array(3);\n\n  /**\n   * Clears interim bytes and resets decoder to clean state.\n   */\n  public clear(): void {\n    this.interim.fill(0);\n  }\n\n  /**\n   * Decodes UTF8 byte sequences in `input` to UTF32 codepoints in `target`.\n   * The methods assumes stream input and will store partly transmitted bytes\n   * and decode them with the next data chunk.\n   * Note: The method does no bound checks for target, therefore make sure\n   * the provided data chunk does not exceed the size of `target`.\n   * Returns the number of written codepoints in `target`.\n   */\n  public decode(input: Uint8Array, target: Uint32Array): number {\n    const length = input.length;\n\n    if (!length) {\n      return 0;\n    }\n\n    let size = 0;\n    let byte1: number;\n    let byte2: number;\n    let byte3: number;\n    let byte4: number;\n    let codepoint = 0;\n    let startPos = 0;\n\n    // handle leftover bytes\n    if (this.interim[0]) {\n      let discardInterim = false;\n      let cp = this.interim[0];\n      cp &= ((((cp & 0xE0) === 0xC0)) ? 0x1F : (((cp & 0xF0) === 0xE0)) ? 0x0F : 0x07);\n      let pos = 0;\n      let tmp: number;\n      while ((tmp = this.interim[++pos] & 0x3F) && pos < 4) {\n        cp <<= 6;\n        cp |= tmp;\n      }\n      // missing bytes - read ahead from input\n      const type = (((this.interim[0] & 0xE0) === 0xC0)) ? 2 : (((this.interim[0] & 0xF0) === 0xE0)) ? 3 : 4;\n      const missing = type - pos;\n      while (startPos < missing) {\n        if (startPos >= length) {\n          return 0;\n        }\n        tmp = input[startPos++];\n        if ((tmp & 0xC0) !== 0x80) {\n          // wrong continuation, discard interim bytes completely\n          startPos--;\n          discardInterim = true;\n          break;\n        } else {\n          // need to save so we can continue short inputs in next call\n          this.interim[pos++] = tmp;\n          cp <<= 6;\n          cp |= tmp & 0x3F;\n        }\n      }\n      if (!discardInterim) {\n        // final test is type dependent\n        if (type === 2) {\n          if (cp < 0x80) {\n            // wrong starter byte\n            startPos--;\n          } else {\n            target[size++] = cp;\n          }\n        } else if (type === 3) {\n          if (cp < 0x0800 || (cp >= 0xD800 && cp <= 0xDFFF) || cp === 0xFEFF) {\n            // illegal codepoint or BOM\n          } else {\n            target[size++] = cp;\n          }\n        } else {\n          if (cp < 0x010000 || cp > 0x10FFFF) {\n            // illegal codepoint\n          } else {\n            target[size++] = cp;\n          }\n        }\n      }\n      this.interim.fill(0);\n    }\n\n    // loop through input\n    const fourStop = length - 4;\n    let i = startPos;\n    while (i < length) {\n      /**\n       * ASCII shortcut with loop unrolled to 4 consecutive ASCII chars.\n       * This is a compromise between speed gain for ASCII\n       * and penalty for non ASCII:\n       * For best ASCII performance the char should be stored directly into target,\n       * but even a single attempt to write to target and compare afterwards\n       * penalizes non ASCII really bad (-50%), thus we load the char into byteX first,\n       * which reduces ASCII performance by ~15%.\n       * This trial for ASCII reduces non ASCII performance by ~10% which seems acceptible\n       * compared to the gains.\n       * Note that this optimization only takes place for 4 consecutive ASCII chars,\n       * for any shorter it bails out. Worst case - all 4 bytes being read but\n       * thrown away due to the last being a non ASCII char (-10% performance).\n       */\n      while (i < fourStop\n        && !((byte1 = input[i]) & 0x80)\n        && !((byte2 = input[i + 1]) & 0x80)\n        && !((byte3 = input[i + 2]) & 0x80)\n        && !((byte4 = input[i + 3]) & 0x80))\n      {\n        target[size++] = byte1;\n        target[size++] = byte2;\n        target[size++] = byte3;\n        target[size++] = byte4;\n        i += 4;\n      }\n\n      // reread byte1\n      byte1 = input[i++];\n\n      // 1 byte\n      if (byte1 < 0x80) {\n        target[size++] = byte1;\n\n        // 2 bytes\n      } else if ((byte1 & 0xE0) === 0xC0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x1F) << 6 | (byte2 & 0x3F);\n        if (codepoint < 0x80) {\n          // wrong starter byte\n          i--;\n          continue;\n        }\n        target[size++] = codepoint;\n\n        // 3 bytes\n      } else if ((byte1 & 0xF0) === 0xE0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          return size;\n        }\n        byte3 = input[i++];\n        if ((byte3 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x0F) << 12 | (byte2 & 0x3F) << 6 | (byte3 & 0x3F);\n        if (codepoint < 0x0800 || (codepoint >= 0xD800 && codepoint <= 0xDFFF) || codepoint === 0xFEFF) {\n          // illegal codepoint or BOM, no i-- here\n          continue;\n        }\n        target[size++] = codepoint;\n\n        // 4 bytes\n      } else if ((byte1 & 0xF8) === 0xF0) {\n        if (i >= length) {\n          this.interim[0] = byte1;\n          return size;\n        }\n        byte2 = input[i++];\n        if ((byte2 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          return size;\n        }\n        byte3 = input[i++];\n        if ((byte3 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        if (i >= length) {\n          this.interim[0] = byte1;\n          this.interim[1] = byte2;\n          this.interim[2] = byte3;\n          return size;\n        }\n        byte4 = input[i++];\n        if ((byte4 & 0xC0) !== 0x80) {\n          // wrong continuation\n          i--;\n          continue;\n        }\n        codepoint = (byte1 & 0x07) << 18 | (byte2 & 0x3F) << 12 | (byte3 & 0x3F) << 6 | (byte4 & 0x3F);\n        if (codepoint < 0x010000 || codepoint > 0x10FFFF) {\n          // illegal codepoint, no i-- here\n          continue;\n        }\n        target[size++] = codepoint;\n      } else {\n        // illegal byte, just skip\n      }\n    }\n    return size;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Disposable } from 'common/Lifecycle';\nimport { ILogService, IOptionsService, LogLevelEnum } from 'common/services/Services';\n\ntype LogType = (message?: any, ...optionalParams: any[]) => void;\n\ninterface IConsole {\n  log: LogType;\n  error: LogType;\n  info: LogType;\n  trace: LogType;\n  warn: LogType;\n}\n\n// console is available on both node.js and browser contexts but the common\n// module doesn't depend on them so we need to explicitly declare it.\ndeclare const console: IConsole;\n\nconst optionsKeyToLogLevel: { [key: string]: LogLevelEnum } = {\n  trace: LogLevelEnum.TRACE,\n  debug: LogLevelEnum.DEBUG,\n  info: LogLevelEnum.INFO,\n  warn: LogLevelEnum.WARN,\n  error: LogLevelEnum.ERROR,\n  off: LogLevelEnum.OFF\n};\n\nconst LOG_PREFIX = 'xterm.js: ';\n\nexport class LogService extends Disposable implements ILogService {\n  public serviceBrand: any;\n\n  private _logLevel: LogLevelEnum = LogLevelEnum.OFF;\n  public get logLevel(): LogLevelEnum { return this._logLevel; }\n\n  constructor(\n    @IOptionsService private readonly _optionsService: IOptionsService\n  ) {\n    super();\n    this._updateLogLevel();\n    this.register(this._optionsService.onSpecificOptionChange('logLevel', () => this._updateLogLevel()));\n\n    // For trace logging, assume the latest created log service is valid\n    traceLogger = this;\n  }\n\n  private _updateLogLevel(): void {\n    this._logLevel = optionsKeyToLogLevel[this._optionsService.rawOptions.logLevel];\n  }\n\n  private _evalLazyOptionalParams(optionalParams: any[]): void {\n    for (let i = 0; i < optionalParams.length; i++) {\n      if (typeof optionalParams[i] === 'function') {\n        optionalParams[i] = optionalParams[i]();\n      }\n    }\n  }\n\n  private _log(type: LogType, message: string, optionalParams: any[]): void {\n    this._evalLazyOptionalParams(optionalParams);\n    type.call(console, (this._optionsService.options.logger ? '' : LOG_PREFIX) + message, ...optionalParams);\n  }\n\n  public trace(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.TRACE) {\n      this._log(this._optionsService.options.logger?.trace.bind(this._optionsService.options.logger) ?? console.log, message, optionalParams);\n    }\n  }\n\n  public debug(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.DEBUG) {\n      this._log(this._optionsService.options.logger?.debug.bind(this._optionsService.options.logger) ?? console.log, message, optionalParams);\n    }\n  }\n\n  public info(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.INFO) {\n      this._log(this._optionsService.options.logger?.info.bind(this._optionsService.options.logger) ?? console.info, message, optionalParams);\n    }\n  }\n\n  public warn(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.WARN) {\n      this._log(this._optionsService.options.logger?.warn.bind(this._optionsService.options.logger) ?? console.warn, message, optionalParams);\n    }\n  }\n\n  public error(message: string, ...optionalParams: any[]): void {\n    if (this._logLevel <= LogLevelEnum.ERROR) {\n      this._log(this._optionsService.options.logger?.error.bind(this._optionsService.options.logger) ?? console.error, message, optionalParams);\n    }\n  }\n}\n\nlet traceLogger: ILogService;\nexport function setTraceLogger(logger: ILogService): void {\n  traceLogger = logger;\n}\n\n/**\n * A decorator that can be used to automatically log trace calls to the decorated function.\n */\nexport function traceCall(_target: any, key: string, descriptor: any): any {\n  if (typeof descriptor.value !== 'function') {\n    throw new Error('not supported');\n  }\n  const fnKey = 'value';\n  const fn = descriptor.value;\n  descriptor[fnKey] = function (...args: any[]) {\n    // Early exit\n    if (traceLogger.logLevel !== LogLevelEnum.TRACE) {\n      return fn.apply(this, args);\n    }\n\n    traceLogger.trace(`GlyphRenderer#${fn.name}(${args.map(e => JSON.stringify(e)).join(', ')})`);\n    const result = fn.apply(this, args);\n    traceLogger.trace(`GlyphRenderer#${fn.name} return`, result);\n    return result;\n  };\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n *\n * This was heavily inspired from microsoft/vscode's dependency injection system (MIT).\n */\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IServiceIdentifier } from 'common/services/Services';\n\nconst DI_TARGET = 'di$target';\nconst DI_DEPENDENCIES = 'di$dependencies';\n\nexport const serviceRegistry: Map<string, IServiceIdentifier<any>> = new Map();\n\nexport function getServiceDependencies(ctor: any): { id: IServiceIdentifier<any>, index: number, optional: boolean }[] {\n  return ctor[DI_DEPENDENCIES] || [];\n}\n\nexport function createDecorator<T>(id: string): IServiceIdentifier<T> {\n  if (serviceRegistry.has(id)) {\n    return serviceRegistry.get(id)!;\n  }\n\n  const decorator: any = function (target: Function, key: string, index: number): any {\n    if (arguments.length !== 3) {\n      throw new Error('@IServiceName-decorator can only be used to decorate a parameter');\n    }\n\n    storeServiceDependency(decorator, target, index);\n  };\n\n  decorator.toString = () => id;\n\n  serviceRegistry.set(id, decorator);\n  return decorator;\n}\n\nfunction storeServiceDependency(id: Function, target: Function, index: number): void {\n  if ((target as any)[DI_TARGET] === target) {\n    (target as any)[DI_DEPENDENCIES].push({ id, index });\n  } else {\n    (target as any)[DI_DEPENDENCIES] = [{ id, index }];\n    (target as any)[DI_TARGET] = target;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IEvent, IEventEmitter } from 'common/EventEmitter';\nimport { IBuffer, IBufferSet } from 'common/buffer/Types';\nimport { IDecPrivateModes, ICoreMouseEvent, CoreMouseEncoding, ICoreMouseProtocol, CoreMouseEventType, ICharset, IWindowOptions, IModes, IAttributeData, ScrollSource, IDisposable, IColor, CursorStyle, CursorInactiveStyle, IOscLinkData } from 'common/Types';\nimport { createDecorator } from 'common/services/ServiceRegistry';\nimport { IDecorationOptions, IDecoration, ILinkHandler, IWindowsPty, ILogger } from 'xterm';\n\nexport const IBufferService = createDecorator<IBufferService>('BufferService');\nexport interface IBufferService {\n  serviceBrand: undefined;\n\n  readonly cols: number;\n  readonly rows: number;\n  readonly buffer: IBuffer;\n  readonly buffers: IBufferSet;\n  isUserScrolling: boolean;\n  onResize: IEvent<{ cols: number, rows: number }>;\n  onScroll: IEvent<number>;\n  scroll(eraseAttr: IAttributeData, isWrapped?: boolean): void;\n  scrollLines(disp: number, suppressScrollEvent?: boolean, source?: ScrollSource): void;\n  resize(cols: number, rows: number): void;\n  reset(): void;\n}\n\nexport const ICoreMouseService = createDecorator<ICoreMouseService>('CoreMouseService');\nexport interface ICoreMouseService {\n  activeProtocol: string;\n  activeEncoding: string;\n  areMouseEventsActive: boolean;\n  addProtocol(name: string, protocol: ICoreMouseProtocol): void;\n  addEncoding(name: string, encoding: CoreMouseEncoding): void;\n  reset(): void;\n\n  /**\n   * Triggers a mouse event to be sent.\n   *\n   * Returns true if the event passed all protocol restrictions and a report\n   * was sent, otherwise false. The return value may be used to decide whether\n   * the default event action in the bowser component should be omitted.\n   *\n   * Note: The method will change values of the given event object\n   * to fullfill protocol and encoding restrictions.\n   */\n  triggerMouseEvent(event: ICoreMouseEvent): boolean;\n\n  /**\n   * Event to announce changes in mouse tracking.\n   */\n  onProtocolChange: IEvent<CoreMouseEventType>;\n\n  /**\n   * Human readable version of mouse events.\n   */\n  explainEvents(events: CoreMouseEventType): { [event: string]: boolean };\n}\n\nexport const ICoreService = createDecorator<ICoreService>('CoreService');\nexport interface ICoreService {\n  serviceBrand: undefined;\n\n  /**\n   * Initially the cursor will not be visible until the first time the terminal\n   * is focused.\n   */\n  isCursorInitialized: boolean;\n  isCursorHidden: boolean;\n\n  readonly modes: IModes;\n  readonly decPrivateModes: IDecPrivateModes;\n\n  readonly onData: IEvent<string>;\n  readonly onUserInput: IEvent<void>;\n  readonly onBinary: IEvent<string>;\n  readonly onRequestScrollToBottom: IEvent<void>;\n\n  reset(): void;\n\n  /**\n   * Triggers the onData event in the public API.\n   * @param data The data that is being emitted.\n   * @param wasUserInput Whether the data originated from the user (as opposed to\n   * resulting from parsing incoming data). When true this will also:\n   * - Scroll to the bottom of the buffer if option scrollOnUserInput is true.\n   * - Fire the `onUserInput` event (so selection can be cleared).\n   */\n  triggerDataEvent(data: string, wasUserInput?: boolean): void;\n\n  /**\n   * Triggers the onBinary event in the public API.\n   * @param data The data that is being emitted.\n   */\n  triggerBinaryEvent(data: string): void;\n}\n\nexport const ICharsetService = createDecorator<ICharsetService>('CharsetService');\nexport interface ICharsetService {\n  serviceBrand: undefined;\n\n  charset: ICharset | undefined;\n  readonly glevel: number;\n\n  reset(): void;\n\n  /**\n   * Set the G level of the terminal.\n   * @param g\n   */\n  setgLevel(g: number): void;\n\n  /**\n   * Set the charset for the given G level of the terminal.\n   * @param g\n   * @param charset\n   */\n  setgCharset(g: number, charset: ICharset | undefined): void;\n}\n\nexport interface IServiceIdentifier<T> {\n  (...args: any[]): void;\n  type: T;\n}\n\nexport interface IBrandedService {\n  serviceBrand: undefined;\n}\n\ntype GetLeadingNonServiceArgs<TArgs extends any[]> = TArgs extends [] ? []\n  : TArgs extends [...infer TFirst, infer TLast] ? TLast extends IBrandedService ? GetLeadingNonServiceArgs<TFirst> : TArgs\n    : never;\n\nexport const IInstantiationService = createDecorator<IInstantiationService>('InstantiationService');\nexport interface IInstantiationService {\n  serviceBrand: undefined;\n\n  setService<T>(id: IServiceIdentifier<T>, instance: T): void;\n  getService<T>(id: IServiceIdentifier<T>): T | undefined;\n  createInstance<Ctor extends new (...args: any[]) => any, R extends InstanceType<Ctor>>(t: Ctor, ...args: GetLeadingNonServiceArgs<ConstructorParameters<Ctor>>): R;\n}\n\nexport enum LogLevelEnum {\n  TRACE = 0,\n  DEBUG = 1,\n  INFO = 2,\n  WARN = 3,\n  ERROR = 4,\n  OFF = 5\n}\n\nexport const ILogService = createDecorator<ILogService>('LogService');\nexport interface ILogService {\n  serviceBrand: undefined;\n\n  readonly logLevel: LogLevelEnum;\n\n  trace(message: any, ...optionalParams: any[]): void;\n  debug(message: any, ...optionalParams: any[]): void;\n  info(message: any, ...optionalParams: any[]): void;\n  warn(message: any, ...optionalParams: any[]): void;\n  error(message: any, ...optionalParams: any[]): void;\n}\n\nexport const IOptionsService = createDecorator<IOptionsService>('OptionsService');\nexport interface IOptionsService {\n  serviceBrand: undefined;\n\n  /**\n   * Read only access to the raw options object, this is an internal-only fast path for accessing\n   * single options without any validation as we trust TypeScript to enforce correct usage\n   * internally.\n   */\n  readonly rawOptions: Required<ITerminalOptions>;\n\n  /**\n   * Options as exposed through the public API, this property uses getters and setters with\n   * validation which makes it safer but slower. {@link rawOptions} should be used for pretty much\n   * all internal usage for performance reasons.\n   */\n  readonly options: Required<ITerminalOptions>;\n\n  /**\n   * Adds an event listener for when any option changes.\n   */\n  readonly onOptionChange: IEvent<keyof ITerminalOptions>;\n\n  /**\n   * Adds an event listener for when a specific option changes, this is a convenience method that is\n   * preferred over {@link onOptionChange} when only a single option is being listened to.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  onSpecificOptionChange<T extends keyof ITerminalOptions>(key: T, listener: (arg1: Required<ITerminalOptions>[T]) => any): IDisposable;\n\n  /**\n   * Adds an event listener for when a set of specific options change, this is a convenience method\n   * that is preferred over {@link onOptionChange} when multiple options are being listened to and\n   * handled the same way.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  onMultipleOptionChange(keys: (keyof ITerminalOptions)[], listener: () => any): IDisposable;\n}\n\nexport type FontWeight = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900' | number;\nexport type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'off';\n\nexport interface ITerminalOptions {\n  allowProposedApi?: boolean;\n  allowTransparency?: boolean;\n  altClickMovesCursor?: boolean;\n  cols?: number;\n  convertEol?: boolean;\n  cursorBlink?: boolean;\n  cursorStyle?: CursorStyle;\n  cursorWidth?: number;\n  cursorInactiveStyle?: CursorInactiveStyle;\n  customGlyphs?: boolean;\n  disableStdin?: boolean;\n  drawBoldTextInBrightColors?: boolean;\n  fastScrollModifier?: 'none' | 'alt' | 'ctrl' | 'shift';\n  fastScrollSensitivity?: number;\n  fontSize?: number;\n  fontFamily?: string;\n  fontWeight?: FontWeight;\n  fontWeightBold?: FontWeight;\n  ignoreBracketedPasteMode?: boolean;\n  letterSpacing?: number;\n  lineHeight?: number;\n  linkHandler?: ILinkHandler | null;\n  logLevel?: LogLevel;\n  logger?: ILogger | null;\n  macOptionIsMeta?: boolean;\n  macOptionClickForcesSelection?: boolean;\n  minimumContrastRatio?: number;\n  rightClickSelectsWord?: boolean;\n  rows?: number;\n  screenReaderMode?: boolean;\n  scrollback?: number;\n  scrollOnUserInput?: boolean;\n  scrollSensitivity?: number;\n  smoothScrollDuration?: number;\n  tabStopWidth?: number;\n  theme?: ITheme;\n  windowsMode?: boolean;\n  windowsPty?: IWindowsPty;\n  windowOptions?: IWindowOptions;\n  wordSeparator?: string;\n  overviewRulerWidth?: number;\n\n  [key: string]: any;\n  cancelEvents: boolean;\n  termName: string;\n}\n\nexport interface ITheme {\n  foreground?: string;\n  background?: string;\n  cursor?: string;\n  cursorAccent?: string;\n  selectionForeground?: string;\n  selectionBackground?: string;\n  selectionInactiveBackground?: string;\n  black?: string;\n  red?: string;\n  green?: string;\n  yellow?: string;\n  blue?: string;\n  magenta?: string;\n  cyan?: string;\n  white?: string;\n  brightBlack?: string;\n  brightRed?: string;\n  brightGreen?: string;\n  brightYellow?: string;\n  brightBlue?: string;\n  brightMagenta?: string;\n  brightCyan?: string;\n  brightWhite?: string;\n  extendedAnsi?: string[];\n}\n\nexport const IOscLinkService = createDecorator<IOscLinkService>('OscLinkService');\nexport interface IOscLinkService {\n  serviceBrand: undefined;\n  /**\n   * Registers a link to the service, returning the link ID. The link data is managed by this\n   * service and will be freed when this current cursor position is trimmed off the buffer.\n   */\n  registerLink(linkData: IOscLinkData): number;\n  /**\n   * Adds a line to a link if needed.\n   */\n  addLineToLink(linkId: number, y: number): void;\n  /** Get the link data associated with a link ID. */\n  getLinkData(linkId: number): IOscLinkData | undefined;\n}\n\nexport const IUnicodeService = createDecorator<IUnicodeService>('UnicodeService');\nexport interface IUnicodeService {\n  serviceBrand: undefined;\n  /** Register an Unicode version provider. */\n  register(provider: IUnicodeVersionProvider): void;\n  /** Registered Unicode versions. */\n  readonly versions: string[];\n  /** Currently active version. */\n  activeVersion: string;\n  /** Event triggered, when activate version changed. */\n  readonly onChange: IEvent<string>;\n\n  /**\n   * Unicode version dependent\n   */\n  wcwidth(codepoint: number): number;\n  getStringCellWidth(s: string): number;\n}\n\nexport interface IUnicodeVersionProvider {\n  readonly version: string;\n  wcwidth(ucs: number): 0 | 1 | 2;\n}\n\nexport const IDecorationService = createDecorator<IDecorationService>('DecorationService');\nexport interface IDecorationService extends IDisposable {\n  serviceBrand: undefined;\n  readonly decorations: IterableIterator<IInternalDecoration>;\n  readonly onDecorationRegistered: IEvent<IInternalDecoration>;\n  readonly onDecorationRemoved: IEvent<IInternalDecoration>;\n  registerDecoration(decorationOptions: IDecorationOptions): IDecoration | undefined;\n  reset(): void;\n  /**\n   * Trigger a callback over the decoration at a cell (in no particular order). This uses a callback\n   * instead of an iterator as it's typically used in hot code paths.\n   */\n  forEachDecorationAtCell(x: number, line: number, layer: 'bottom' | 'top' | undefined, callback: (decoration: IInternalDecoration) => void): void;\n}\nexport interface IInternalDecoration extends IDecoration {\n  readonly options: IDecorationOptions;\n  readonly backgroundColorRGB: IColor | undefined;\n  readonly foregroundColorRGB: IColor | undefined;\n  readonly onRenderEmitter: IEventEmitter<HTMLElement>;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2022 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ICharacterJoinerService, ICharSizeService, ICoreBrowserService, IRenderService, IThemeService } from 'browser/services/Services';\nimport { ITerminal } from 'browser/Types';\nimport { EventEmitter, forwardEvent } from 'common/EventEmitter';\nimport { Disposable, toDisposable } from 'common/Lifecycle';\nimport { setTraceLogger } from 'common/services/LogService';\nimport { IBufferService, IDecorationService, ILogService } from 'common/services/Services';\nimport { ITerminalAddon, Terminal } from 'xterm';\nimport { CanvasRenderer } from './CanvasRenderer';\n\nexport class CanvasAddon extends Disposable implements ITerminalAddon {\n  private _terminal?: Terminal;\n  private _renderer?: CanvasRenderer;\n\n  private readonly _onChangeTextureAtlas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onChangeTextureAtlas = this._onChangeTextureAtlas.event;\n  private readonly _onAddTextureAtlasCanvas = this.register(new EventEmitter<HTMLCanvasElement>());\n  public readonly onAddTextureAtlasCanvas = this._onAddTextureAtlasCanvas.event;\n\n  public get textureAtlas(): HTMLCanvasElement | undefined {\n    return this._renderer?.textureAtlas;\n  }\n\n  public activate(terminal: Terminal): void {\n    const core = (terminal as any)._core as ITerminal;\n    if (!terminal.element) {\n      this.register(core.onWillOpen(() => this.activate(terminal)));\n      return;\n    }\n\n    this._terminal = terminal;\n    const coreService = core.coreService;\n    const optionsService = core.optionsService;\n    const screenElement = core.screenElement!;\n    const linkifier = core.linkifier2;\n\n    const unsafeCore = core as any;\n    const bufferService: IBufferService = unsafeCore._bufferService;\n    const renderService: IRenderService = unsafeCore._renderService;\n    const characterJoinerService: ICharacterJoinerService = unsafeCore._characterJoinerService;\n    const charSizeService: ICharSizeService = unsafeCore._charSizeService;\n    const coreBrowserService: ICoreBrowserService = unsafeCore._coreBrowserService;\n    const decorationService: IDecorationService = unsafeCore._decorationService;\n    const logService: ILogService = unsafeCore._logService;\n    const themeService: IThemeService = unsafeCore._themeService;\n\n    // Set trace logger just in case it hasn't been yet which could happen when the addon is\n    // bundled separately to the core module\n    setTraceLogger(logService);\n\n    this._renderer = new CanvasRenderer(terminal, screenElement, linkifier, bufferService, charSizeService, optionsService, characterJoinerService, coreService, coreBrowserService, decorationService, themeService);\n    this.register(forwardEvent(this._renderer.onChangeTextureAtlas, this._onChangeTextureAtlas));\n    this.register(forwardEvent(this._renderer.onAddTextureAtlasCanvas, this._onAddTextureAtlasCanvas));\n    renderService.setRenderer(this._renderer);\n    renderService.handleResize(bufferService.cols, bufferService.rows);\n\n    this.register(toDisposable(() => {\n      renderService.setRenderer((this._terminal as any)._core._createRenderer());\n      renderService.handleResize(terminal.cols, terminal.rows);\n      this._renderer?.dispose();\n      this._renderer = undefined;\n    }));\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAqB,cAAID,EAAAA,IAEzBD,EAAkB,cAAIC,EAAAA;IACvB,EAAEK,MAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,EAAA,KAAA,CAAAC,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAA;ACHT,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA;QAKA,MAAsBE,UAAwB,EAAAC,WAAAA;UAiB5C,IAAA,SAAWC;AAA8B,mBAAOC,KAAKC;UAAS;UAC9D,IAAA,cAAWC;AAAW,gBAAAT;AAAwB,mBAAsB,UAAfA,KAAAO,KAAKG,eAAAA,WAAUV,KAAA,SAAAA,GAAEW,MAAM,CAAA,EAAGL;UAAS;UAKxF,YACmBM,IACTC,IACRC,IACAC,IACQC,IACWC,IACAC,IACAC,IACAC,IACAC,IAAAA;AAEnBC,kBAAAA,GAXiB,KAAAV,YAAAA,IACT,KAAAC,aAAAA,IAGA,KAAAG,SAAAA,IACW,KAAAC,gBAAAA,IACA,KAAAC,iBAAAA,IACA,KAAAC,kBAAAA,IACA,KAAAC,qBAAAA,IACA,KAAAC,sBAAAA,IA9Bb,KAAAE,mBAA2B,GAC3B,KAAAC,oBAA4B,GAC5B,KAAAC,mBAA2B,GAC3B,KAAAC,oBAA4B,GAC5B,KAAAC,kBAA0B,GAC1B,KAAAC,iBAAyB,GAEvB,KAAAC,mBAAyC,GAAA,EAAAC,4BAAAA,GAE3C,KAAAC,mBAAoD,CAAA,GAGlD,KAAAC,uBAAuBzB,KAAK0B,SAAS,IAAI,EAAAC,mBAAAA,GAKlC,KAAAC,2BAA2B5B,KAAK0B,SAAS,IAAI,EAAAG,cAAAA,GAC9C,KAAAC,0BAA0B9B,KAAK4B,yBAAyBG,OAetE/B,KAAKgC,qBAAqB,IAAIpC,GAAAqC,kBAAkBjC,KAAKK,WAAWL,KAAKsB,iBAAiBtB,KAAKa,oBAAoBb,KAAKc,qBAAqBd,KAAKU,aAAAA,GAC9IV,KAAKC,UAAUiC,SAASC,cAAc,QAAA,GACtCnC,KAAKC,QAAQmC,UAAUC,IAAI,SAAS9B,EAAAA,QAAAA,GACpCP,KAAKC,QAAQqC,MAAM9B,SAASA,GAAO+B,SAAAA,GACnCvC,KAAKwC,YAAAA,GACLxC,KAAKM,WAAWmC,YAAYzC,KAAKC,OAAAA,GACjCD,KAAK0C,kBAAkB1C,KAAKU,cAAciC,MAAAA,GAC1C3C,KAAK0B,SAAS1B,KAAKU,cAAckC,eAAenD,CAAAA,OAAAA;AAC9CO,mBAAK0C,kBAAkBjD,EAAAA,GACvBO,KAAK6C,MAAAA,GAEL7C,KAAK8C,uBAAuB9C,KAAKsB,gBAAgByB,gBAAgB/C,KAAKsB,gBAAgB0B,cAAchD,KAAKsB,gBAAgB2B,gBAAAA;YAAiB,CAAA,CAAA,GAG5IjD,KAAK0B,UAAS,GAAA,EAAAwB,cAAa,MAAA;AACzBlD,mBAAKC,QAAQkD,OAAAA;YAAQ,CAAA,CAAA;UAEzB;UAEQ,cAAAX;AACNxC,iBAAKoD,QAAO,GAAA,EAAAC,cAAarD,KAAKC,QAAQqD,WAAW,MAAM,EAAEC,OAAOvD,KAAKS,OAAAA,CAAAA,CAAAA,GAEhET,KAAKS,UACRT,KAAKwD,UAAAA;UAET;UAEO,aAAAC;UAAoB;UACpB,cAAAC;UAAqB;UACrB,mBAAAC;UAA0B;UAC1B,kBAAkBC,IAAkBC,IAAAA;UAAuB;UAE3D,uBAAuBC,IAAqCC,IAAmCd,KAAAA,OAA4B;AAChIjD,iBAAKsB,gBAAgB0C,OAAOhE,KAAKK,WAAWyD,IAAOC,IAAKd,EAAAA;UAC1D;UAEU,iBAAiBM,IAAAA;AAEzB,gBAAIA,OAAUvD,KAAKS;AACjB;AAIF,kBAAMwD,KAAYjE,KAAKC;AACvBD,iBAAKS,SAAS8C,IAEdvD,KAAKC,UAAUD,KAAKC,QAAQiE,UAAAA,GAC5BlE,KAAKwC,YAAAA,GACLxC,KAAKM,WAAW6D,aAAanE,KAAKC,SAASgE,EAAAA,GAG3CjE,KAAK0C,kBAAkB1C,KAAKU,cAAciC,MAAAA,GAC1C3C,KAAKoE,kBAAkB,GAAGpE,KAAKW,eAAe0D,OAAO,CAAA;UACvD;UAMQ,kBAAkBC,IAAAA;AACxB,gBAAA,EAAItE,KAAKgB,oBAAoB,KAAKhB,KAAKiB,qBAAqB,IAA5D;AAGAjB,mBAAKG,cAAa,GAAA,EAAAoE,qBAAoBvE,KAAKK,WAAWL,KAAKY,gBAAgB4D,YAAYF,IAAUtE,KAAKkB,kBAAkBlB,KAAKmB,mBAAmBnB,KAAKgB,kBAAkBhB,KAAKiB,mBAAmBjB,KAAKc,oBAAoB2D,GAAAA,GACxNzE,KAAKyB,qBAAqBiD,SAAQ,GAAA,EAAAC,cAAa3E,KAAKG,WAAW2B,yBAAyB9B,KAAK4B,wBAAAA,GAC7F5B,KAAKG,WAAWyE,OAAAA;AAChB,uBAASjF,KAAI,GAAGA,KAAIK,KAAKG,WAAWC,MAAMyE,QAAQlF;AAChDK,qBAAKwB,iBAAiB7B,EAAAA,IAAK,IAAImF,EAAgB9E,KAAKG,WAAWC,MAAMT,EAAAA,EAAGI,MAAAA;YAAAA;UAE5E;UAEO,OAAOgF,IAAAA;AACZ/E,iBAAKkB,mBAAmB6D,GAAIC,OAAOC,KAAKC,OACxClF,KAAKmB,oBAAoB4D,GAAIC,OAAOC,KAAKE,QACzCnF,KAAKgB,mBAAmB+D,GAAIC,OAAOI,KAAKF,OACxClF,KAAKiB,oBAAoB8D,GAAIC,OAAOI,KAAKD,QACzCnF,KAAKoB,kBAAkB2D,GAAIC,OAAOI,KAAKC,MACvCrF,KAAKqB,iBAAiB0D,GAAIC,OAAOI,KAAKE,KACtCtF,KAAKC,QAAQiF,QAAQH,GAAIC,OAAOjF,OAAOmF,OACvClF,KAAKC,QAAQkF,SAASJ,GAAIC,OAAOjF,OAAOoF,QACxCnF,KAAKC,QAAQqC,MAAM4C,QAAQ,GAAGH,GAAIQ,IAAIxF,OAAOmF,KAAAA,MAC7ClF,KAAKC,QAAQqC,MAAM6C,SAAS,GAAGJ,GAAIQ,IAAIxF,OAAOoF,MAAAA,MAGzCnF,KAAKS,UACRT,KAAKwD,UAAAA,GAGPxD,KAAK0C,kBAAkB1C,KAAKU,cAAciC,MAAAA;UAC5C;UAIO,oBAAA6C;AAAAA,gBAAAA;AACU,sBAAf/F,KAAAO,KAAKG,eAAAA,WAAUV,MAAAA,GAAEgG,aAAAA;UACnB;UASU,WAAWC,IAAWC,IAAWT,IAAeC,IAAAA;AACxDnF,iBAAKoD,KAAKwC,SACRF,KAAI1F,KAAKkB,kBACTyE,KAAI3F,KAAKmB,mBACT+D,KAAQlF,KAAKkB,kBACbiE,KAASnF,KAAKmB,iBAAAA;UAClB;UAQU,uBAAuBuE,IAAWC,IAAWT,KAAgB,GAAA;AACrE,kBAAMW,KAAaC,KAAKC,KAA8B,MAAzB/F,KAAKmB,iBAAAA;AAClCnB,iBAAKoD,KAAKwC,SACRF,KAAI1F,KAAKkB,mBACRyE,KAAI,KAAK3F,KAAKmB,oBAAoB0E,KAAa7F,KAAKc,oBAAoB2D,KACzES,KAAQlF,KAAKkB,kBACblB,KAAKc,oBAAoB2D,GAAAA;UAC7B;UAQU,uBAAuBiB,IAAWC,IAAWT,KAAgB,GAAGc,KAAsB,GAAA;AAC9FhG,iBAAKoD,KAAKwC,SACRF,KAAI1F,KAAKkB,mBACRyE,KAAI,KAAK3F,KAAKmB,oBAAoB6E,KAAchG,KAAKc,oBAAoB2D,MAAM,GAChFS,KAAQlF,KAAKkB,kBACblB,KAAKc,oBAAoB2D,GAAAA;UAC7B;UAEU,sBAAsBiB,IAAWC,IAAWT,KAAgB,GAAA;AACpElF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAK8C,UAAAA,GACVlG,KAAKoD,KAAK+C,cAAcnG,KAAKoD,KAAKgD;AAClC,kBAAMC,KAAYrG,KAAKc,oBAAoB2D;AAC3CzE,iBAAKoD,KAAKiD,YAAYA;AACtB,qBAASC,KAAU,GAAGA,KAAUpB,IAAOoB,MAAW;AAChD,oBAAMC,MAASb,KAAIY,MAAWtG,KAAKkB,kBAC7BsF,MAAQd,KAAIY,KAAU,OAAOtG,KAAKkB,kBAClCuF,MAAUf,KAAIY,KAAU,KAAKtG,KAAKkB,kBAClCwF,MAAQf,KAAI,KAAK3F,KAAKmB,oBAAoBkF,KAAY,GACtDM,KAAUD,KAAOL,IACjBO,KAAUF,KAAOL;AACvBrG,mBAAKoD,KAAKyD,OAAON,IAAOG,EAAAA,GACxB1G,KAAKoD,KAAK0D,cACRP,IAAOI,IACPH,IAAMG,IACNH,IAAME,EAAAA,GAER1G,KAAKoD,KAAK0D,cACRN,IAAMI,IACNH,IAAQG,IACRH,IAAQC,EAAAA;YAAAA;AAGZ1G,iBAAKoD,KAAK2D,OAAAA,GACV/G,KAAKoD,KAAK4D,QAAAA;UACZ;UAEU,uBAAuBtB,IAAWC,IAAWT,KAAgB,GAAA;AACrElF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAK8C,UAAAA,GACVlG,KAAKoD,KAAK+C,cAAcnG,KAAKoD,KAAKgD;AAClC,kBAAMC,KAAYrG,KAAKc,oBAAoB2D;AAC3CzE,iBAAKoD,KAAKiD,YAAYA,IACtBrG,KAAKoD,KAAK6D,YAAY,CAAa,IAAZZ,IAAeA,EAAAA,CAAAA;AACtC,kBAAME,KAAQb,KAAI1F,KAAKkB,kBACjBwF,MAAQf,KAAI,KAAK3F,KAAKmB,oBAAoBkF,KAAY;AAC5DrG,iBAAKoD,KAAKyD,OAAON,IAAOG,EAAAA;AACxB,qBAASJ,KAAU,GAAGA,KAAUpB,IAAOoB,MAAW;AAEhD,oBAAMG,MAAUf,KAAIR,KAAQoB,MAAWtG,KAAKkB;AAC5ClB,mBAAKoD,KAAK8D,OAAOT,IAAQC,EAAAA;YAAAA;AAE3B1G,iBAAKoD,KAAK2D,OAAAA,GACV/G,KAAKoD,KAAK+D,UAAAA,GACVnH,KAAKoD,KAAK4D,QAAAA;UACZ;UAEU,uBAAuBtB,IAAWC,IAAWT,KAAgB,GAAA;AACrElF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAK8C,UAAAA,GACVlG,KAAKoD,KAAK+C,cAAcnG,KAAKoD,KAAKgD;AAClC,kBAAMC,KAAYrG,KAAKc,oBAAoB2D;AAC3CzE,iBAAKoD,KAAKiD,YAAYA,IACtBrG,KAAKoD,KAAK6D,YAAY,CAAa,IAAZZ,IAA2B,IAAZA,EAAAA,CAAAA;AACtC,kBAAME,KAAQb,KAAI1F,KAAKkB,kBACjBuF,MAAUf,KAAIR,MAASlF,KAAKkB,kBAC5BwF,MAAQf,KAAI,KAAK3F,KAAKmB,oBAAoBkF,KAAY;AAC5DrG,iBAAKoD,KAAKyD,OAAON,IAAOG,EAAAA,GACxB1G,KAAKoD,KAAK8D,OAAOT,IAAQC,EAAAA,GACzB1G,KAAKoD,KAAK2D,OAAAA,GACV/G,KAAKoD,KAAK+D,UAAAA,GACVnH,KAAKoD,KAAK4D,QAAAA;UACZ;UAQU,oBAAoBtB,IAAWC,IAAWT,IAAAA;AAClDlF,iBAAKoD,KAAKwC,SACRF,KAAI1F,KAAKkB,kBACTyE,KAAI3F,KAAKmB,mBACTnB,KAAKc,oBAAoB2D,MAAMS,IAC/BlF,KAAKmB,iBAAAA;UACT;UAQU,kBAAkBuE,IAAWC,IAAWT,IAAeC,IAAAA;AAC/D,kBAAMkB,KAAYrG,KAAKc,oBAAoB2D;AAC3CzE,iBAAKoD,KAAKiD,YAAYA,IACtBrG,KAAKoD,KAAKgE,WACR1B,KAAI1F,KAAKkB,mBAAmBmF,KAAY,GACxCV,KAAI3F,KAAKmB,oBAAqBkF,KAAY,GAC1CnB,KAAQlF,KAAKkB,mBAAmBmF,IAC/BlB,KAASnF,KAAKmB,oBAAqBkF,EAAAA;UACxC;UAKU,YAAA7C;AACJxD,iBAAKS,SACPT,KAAKoD,KAAKiE,UAAU,GAAG,GAAGrH,KAAKC,QAAQiF,OAAOlF,KAAKC,QAAQkF,MAAAA,KAE3DnF,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAO2E,WAAW/B,KAC3DvF,KAAKoD,KAAKwC,SAAS,GAAG,GAAG5F,KAAKC,QAAQiF,OAAOlF,KAAKC,QAAQkF,MAAAA;UAE9D;UASU,YAAYO,IAAWC,IAAWT,IAAeC,IAAAA;AACrDnF,iBAAKS,SACPT,KAAKoD,KAAKiE,UACR3B,KAAI1F,KAAKkB,kBACTyE,KAAI3F,KAAKmB,mBACT+D,KAAQlF,KAAKkB,kBACbiE,KAASnF,KAAKmB,iBAAAA,KAEhBnB,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAO2E,WAAW/B,KAC3DvF,KAAKoD,KAAKwC,SACRF,KAAI1F,KAAKkB,kBACTyE,KAAI3F,KAAKmB,mBACT+D,KAAQlF,KAAKkB,kBACbiE,KAASnF,KAAKmB,iBAAAA;UAEpB;UAUU,mBAAmB8D,IAAgBS,IAAWC,IAAAA;AACtD3F,iBAAKoD,KAAKmE,OAAOvH,KAAKwH,SAAAA,OAAS,KAAO,GACtCxH,KAAKoD,KAAKqE,eAAe,EAAAC,eACzB1H,KAAK2H,SAAShC,EAAAA;AAGd,gBAAIiC,KAAAA;AAAc,sBACd5H,KAAKY,gBAAgB4D,WAAWqD,iBAClCD,MAAc,GAAA,EAAAE,mBAAkB9H,KAAKoD,MAAM6B,GAAK8C,SAAAA,GAAYrC,KAAI1F,KAAKkB,kBAAkByE,KAAI3F,KAAKmB,mBAAmBnB,KAAKkB,kBAAkBlB,KAAKmB,mBAAmBnB,KAAKY,gBAAgB4D,WAAWwD,UAAUhI,KAAKc,oBAAoB2D,GAAAA,IAIlOmD,MACH5H,KAAKoD,KAAK6E,SACRhD,GAAK8C,SAAAA,GACLrC,KAAI1F,KAAKkB,mBAAmBlB,KAAKoB,iBACjCuE,KAAI3F,KAAKmB,oBAAoBnB,KAAKqB,iBAAiBrB,KAAKiB,iBAAAA;UAE9D;UAMU,WAAWgE,IAAiBS,IAAWC,IAAAA;AAAAA,gBAAAA,IAAAA,IAAAA,IAAAA;AAC/C,kBAAMuC,KAAQjD,GAAK8C,SAAAA;AAGnB,gBAFA/H,KAAKgC,mBAAmBmG,QAAQlD,IAAMS,IAAG1F,KAAKW,eAAeyH,OAAOC,QAAQ1C,EAAAA,GAAAA,CAEvE3F,KAAKG;AACR;AAGF,gBAAImI;AAEFA,YAAAA,KADEJ,MAASA,GAAMrD,SAAS,IAClB7E,KAAKG,WAAWoI,+BAA+BL,IAAOlI,KAAKgC,mBAAmBwG,OAAOC,IAAIzI,KAAKgC,mBAAmBwG,OAAOE,IAAI1I,KAAKgC,mBAAmBwG,OAAOG,KAAAA,IAAK,IAEhK3I,KAAKG,WAAWyI,mBAAmB3D,GAAK4D,QAAAA,KAAa,EAAAC,sBAAsB9I,KAAKgC,mBAAmBwG,OAAOC,IAAIzI,KAAKgC,mBAAmBwG,OAAOE,IAAI1I,KAAKgC,mBAAmBwG,OAAOG,KAAAA,IAAK,GAE1LL,GAAMS,KAAKrD,KAAM4C,GAAMS,KAAKpD,MAGjC3F,KAAKoD,KAAK6C,KAAAA,GACVjG,KAAK2H,SAAShC,EAAAA,GAOV3F,KAAKwB,iBAAiB8G,GAAMU,WAAAA,KAAgBhJ,KAAKG,WAAWC,MAAMkI,GAAMU,WAAAA,EAAajJ,WAAWC,KAAKwB,iBAAiB8G,GAAMU,WAAAA,EAAcjJ,WAC5F,UAAhDkJ,KAAwC,UAAxCrJ,KAAAI,KAAKwB,iBAAiB8G,GAAMU,WAAAA,MAAAA,WAAYpJ,KAAA,SAAAA,GAAEsJ,WAAAA,WAAMD,MAAAA,GAAEE,MAAAA,GAAAA,OAC3CnJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,IAGjChJ,KAAKG,WAAWC,MAAMkI,GAAMU,WAAAA,EAAaI,aAAoD,UAAxCC,KAAArJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,MAAAA,WAAYK,KAAA,SAAAA,GAAED,aAC5FpJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,MAC/BhJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,IAAe,IAAIlE,EAAgB9E,KAAKG,WAAWC,MAAMkI,GAAMU,WAAAA,EAAajJ,MAAAA,IAE1GC,KAAKwB,iBAAiB8G,GAAMU,WAAAA,EAAcM,QAAAA,GAC1CtJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,EAAcI,UAAUpJ,KAAKG,WAAWC,MAAMkI,GAAMU,WAAAA,EAAaI,UAE/FpJ,KAAKoD,KAAKmG,WACgC,UAAxCC,KAAAxJ,KAAKwB,iBAAiB8G,GAAMU,WAAAA,MAAAA,WAAYQ,KAAA,SAAAA,GAAEN,WAAUlJ,KAAKG,WAAYC,MAAMkI,GAAMU,WAAAA,EAAajJ,QAC9FuI,GAAMmB,gBAAgB/D,GACtB4C,GAAMmB,gBAAgB9D,GACtB2C,GAAMS,KAAKrD,GACX4C,GAAMS,KAAKpD,GACXD,KAAI1F,KAAKkB,mBAAmBlB,KAAKoB,kBAAkBkH,GAAMoB,OAAOhE,GAChEC,KAAI3F,KAAKmB,oBAAoBnB,KAAKqB,iBAAiBiH,GAAMoB,OAAO/D,GAChE2C,GAAMS,KAAKrD,GACX4C,GAAMS,KAAKpD,CAAAA,GAEb3F,KAAKoD,KAAK4D,QAAAA;UACZ;UAMQ,SAASrB,IAAAA;AACf3F,iBAAKoD,KAAK8C,UAAAA,GACVlG,KAAKoD,KAAKuG,KACR,GACAhE,KAAI3F,KAAKmB,mBACTnB,KAAKW,eAAeiJ,OAAO5J,KAAKkB,kBAChClB,KAAKmB,iBAAAA,GACPnB,KAAKoD,KAAKyG,KAAAA;UACZ;UAMU,SAASC,IAAiBC,IAAAA;AAIlC,mBAAO,GAFWA,KAAW,WAAW,EAAA,IADrBD,KAAS9J,KAAKY,gBAAgB4D,WAAWwF,iBAAiBhK,KAAKY,gBAAgB4D,WAAWyF,UAAAA,IAGxEjK,KAAKY,gBAAgB4D,WAAWwD,WAAWhI,KAAKc,oBAAoB2D,GAAAA,MAASzE,KAAKY,gBAAgB4D,WAAW0F,UAAAA;UACpJ;QAAA;AA/ZF,QAAAxK,GAAA,kBAAA;QA8aA,MAAMoF,EAAAA;UAIJ,IAAA,SAAWoE;AAAoC,mBAAOlJ,KAAKmK;UAAS;UAGpE,YAA4BpK,IAAAA;AAAA,iBAAAA,SAAAA,IANpB,KAAAqK,SAAM,GACN,KAAAC,iBAAAA,QACA,KAAAF,UAAAA,QAED,KAAAf,UAAAA;UAGP;UAEO,UAAAE;AAAAA,gBAAAA;AAEO,sBAAZ7J,KAAAO,KAAKmK,YAAAA,WAAO1K,MAAAA,GAAE0J,MAAAA,GACdnJ,KAAKmK,UAAAA,QAED,EAAAG,aAAAA,WAGAtK,KAAKqK,mBACPrK,KAAKqK,iBAAiBE,OAAOC,WAAW,MAAMxK,KAAKyK,UAAAA,GA3BvB,GAAA,IA6BV,MAAhBzK,KAAKoK,WACPpK,KAAKoK,SAAS;UAElB;UAEQ,YAAAK;AAAAA,gBAAAA;AACc,kBAAhBzK,KAAKoK,WACK,UAAZ3K,KAAAO,KAAKmK,YAAAA,WAAO1K,MAAAA,GAAE0J,MAAAA,GACdnJ,KAAKmK,UAAAA,QACLnK,KAAKoK,SAAS,GACdG,OAAOG,kBAAkB1K,KAAKD,MAAAA,EAAQ4K,KAAKzB,CAAAA,OAAAA;AACrB,oBAAhBlJ,KAAKoK,SACPpK,KAAKsJ,QAAAA,IAELtJ,KAAKmK,UAAUjB,IAEjBlJ,KAAKoK,SAAS;YAAyB,CAAA,GAErCpK,KAAKqK,mBACPrK,KAAKqK,iBAAAA;UAGX;QAAA;MAAA,GAAA,KAAA,CAAA5K,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,iBAAA;AC3eF,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA;QAGA,MAAaiL,UAAuB,EAAA9K,WAAAA;UAalC,YACmBO,IACAwK,IACjBC,IACiBnK,IACAoK,GACAnK,GACjBoK,GACAC,GACiBnK,GACjBoK,GACiBxK,GAAAA;AAEjBK,kBAAAA,GAZiB,KAAAV,YAAAA,IACA,KAAAwK,iBAAAA,IAEA,KAAAlK,iBAAAA,IACA,KAAAoK,mBAAAA,GACA,KAAAnK,kBAAAA,GAGA,KAAAE,sBAAAA,GAEA,KAAAJ,gBAAAA,GAlBF,KAAAyK,mBAAmBnL,KAAK0B,SAAS,IAAI,EAAAG,cAAAA,GACtC,KAAAuJ,kBAAkBpL,KAAKmL,iBAAiBpJ,OACvC,KAAAsJ,wBAAwBrL,KAAK0B,SAAS,IAAI,EAAAG,cAAAA,GAC3C,KAAAyJ,uBAAuBtL,KAAKqL,sBAAsBtJ,OACjD,KAAAH,2BAA2B5B,KAAK0B,SAAS,IAAI,EAAAG,cAAAA,GAC9C,KAAAC,0BAA0B9B,KAAK4B,yBAAyBG;AAgBtE,kBAAMwJ,IAAoBvL,KAAKY,gBAAgB4D,WAAW+G;AAC1DvL,iBAAKwL,gBAAgB,CACnB,IAAI,EAAAC,gBAAgBzL,KAAKK,WAAWL,KAAK6K,gBAAgB,GAAGU,GAAmBvL,KAAKW,gBAAgBX,KAAKY,iBAAiBoK,GAAwBE,GAAmBlL,KAAKc,qBAAqBJ,CAAAA,GAC/L,IAAI,EAAAgL,qBAAqB1L,KAAKK,WAAWL,KAAK6K,gBAAgB,GAAG7K,KAAKW,gBAAgBX,KAAKc,qBAAqBoK,GAAmBlL,KAAKY,iBAAiBF,CAAAA,GACzJ,IAAI,EAAAiL,gBAAgB3L,KAAKK,WAAWL,KAAK6K,gBAAgB,GAAGC,IAAY9K,KAAKW,gBAAgBX,KAAKY,iBAAiBsK,GAAmBlL,KAAKc,qBAAqBJ,CAAAA,GAChK,IAAI,EAAAkL,kBAAkB5L,KAAKK,WAAWL,KAAK6K,gBAAgB,GAAG7K,KAAKmL,kBAAkBnL,KAAKW,gBAAgBX,KAAKY,iBAAiBqK,GAAajL,KAAKc,qBAAqBoK,GAAmBxK,CAAAA,CAAAA;AAE5L,uBAAWmL,MAAS7L,KAAKwL;AAAAA,eACvB,GAAA,EAAA7G,cAAakH,GAAM/J,yBAAyB9B,KAAK4B,wBAAAA;AAEnD5B,iBAAK8L,cAAa,GAAA,EAAAC,wBAAAA,GAClB/L,KAAKgM,oBAAoBhM,KAAKc,oBAAoB2D,KAClDzE,KAAKiM,kBAAAA,GAELjM,KAAK0B,UAAS,GAAA,EAAAwK,8BAA6BlM,KAAKwL,cAAc,CAAA,EAAGzL,QAAQC,KAAKc,oBAAoByJ,QAAQ,CAAC4B,IAAGC,OAAMpM,KAAKqM,gCAAgCF,IAAGC,EAAAA,CAAAA,CAAAA,GAC5JpM,KAAK0B,UAAS,GAAA,EAAAwB,cAAa,MAAA;AACzB,yBAAWoJ,MAAKtM,KAAKwL;AACnBc,gBAAAA,GAAEC,QAAAA;AAAAA,eAEJ,GAAA3M,GAAA4M,yBAAwBxM,KAAKK,SAAAA;YAAU,CAAA,CAAA;UAE3C;UAEA,IAAA,eAAWoM;AACT,mBAAOzM,KAAKwL,cAAc,CAAA,EAAGtL;UAC/B;UAEO,+BAAAwM;AAGD1M,iBAAKgM,sBAAsBhM,KAAKc,oBAAoB2D,QACtDzE,KAAKgM,oBAAoBhM,KAAKc,oBAAoB2D,KAClDzE,KAAK2M,aAAa3M,KAAKW,eAAeiJ,MAAM5J,KAAKW,eAAe0D,IAAAA;UAEpE;UAEO,aAAauF,IAAcvF,IAAAA;AAEhCrE,iBAAKiM,kBAAAA;AAGL,uBAAWK,MAAKtM,KAAKwL;AACnBc,cAAAA,GAAEM,OAAO5M,KAAK8L,UAAAA;AAIhB9L,iBAAK6K,eAAevI,MAAM4C,QAAQ,GAAGlF,KAAK8L,WAAWvG,IAAIxF,OAAOmF,KAAAA,MAChElF,KAAK6K,eAAevI,MAAM6C,SAAS,GAAGnF,KAAK8L,WAAWvG,IAAIxF,OAAOoF,MAAAA;UACnE;UAEO,wBAAA0H;AACL7M,iBAAK2M,aAAa3M,KAAKW,eAAeiJ,MAAM5J,KAAKW,eAAe0D,IAAAA;UAClE;UAEO,aAAAZ;AACLzD,iBAAK8M,cAAcR,CAAAA,OAAKA,GAAE7I,WAAAA,CAAAA;UAC5B;UAEO,cAAAC;AACL1D,iBAAK8M,cAAcR,CAAAA,OAAKA,GAAE5I,YAAAA,CAAAA;UAC5B;UAEO,uBAAuBI,IAAqCC,IAAmCd,KAAAA,OAA4B;AAChIjD,iBAAK8M,cAAcR,CAAAA,OAAKA,GAAExJ,uBAAuBgB,IAAOC,IAAKd,EAAAA,CAAAA,GAEzDjD,KAAKU,cAAciC,OAAOoK,uBAC5B/M,KAAKmL,iBAAiB6B,KAAK,EAAElJ,OAAO,GAAGC,KAAK/D,KAAKW,eAAe0D,OAAO,EAAA,CAAA;UAE3E;UAEO,mBAAAV;AACL3D,iBAAK8M,cAAcR,CAAAA,OAAKA,GAAE3I,iBAAAA,CAAAA;UAC5B;UAEO,QAAAsJ;AACLjN,iBAAK8M,cAAcR,CAAAA,OAAKA,GAAEzJ,MAAAA,CAAAA;UAC5B;UAEQ,cAAcqK,IAAAA;AACpB,uBAAWZ,MAAKtM,KAAKwL;AACnB0B,cAAAA,GAAUZ,EAAAA;UAEd;UAMO,WAAWxI,IAAeC,IAAAA;AAC/B,uBAAWuI,MAAKtM,KAAKwL;AACnBc,cAAAA,GAAElI,kBAAkBN,IAAOC,EAAAA;UAE/B;UAEO,oBAAAyB;AACL,uBAAWqG,MAAS7L,KAAKwL;AACvBK,cAAAA,GAAMrG,kBAAAA;UAEV;UAKQ,oBAAAyG;AACN,gBAAA,CAAKjM,KAAK+K,iBAAiBoC;AACzB;AAIF,kBAAM1I,KAAMzE,KAAKc,oBAAoB2D;AACrCzE,iBAAK8L,WAAW9G,OAAOI,KAAKF,QAAQY,KAAKsH,MAAMpN,KAAK+K,iBAAiB7F,QAAQT,EAAAA,GAC7EzE,KAAK8L,WAAW9G,OAAOI,KAAKD,SAASW,KAAKC,KAAK/F,KAAK+K,iBAAiB5F,SAASV,EAAAA,GAC9EzE,KAAK8L,WAAW9G,OAAOC,KAAKE,SAASW,KAAKsH,MAAMpN,KAAK8L,WAAW9G,OAAOI,KAAKD,SAASnF,KAAKY,gBAAgB4D,WAAW6I,UAAAA,GACrHrN,KAAK8L,WAAW9G,OAAOI,KAAKE,MAAqD,MAA/CtF,KAAKY,gBAAgB4D,WAAW6I,aAAmB,IAAIvH,KAAKwH,OAAOtN,KAAK8L,WAAW9G,OAAOC,KAAKE,SAASnF,KAAK8L,WAAW9G,OAAOI,KAAKD,UAAU,CAAA,GAChLnF,KAAK8L,WAAW9G,OAAOC,KAAKC,QAAQlF,KAAK8L,WAAW9G,OAAOI,KAAKF,QAAQY,KAAKwH,MAAMtN,KAAKY,gBAAgB4D,WAAW+I,aAAAA,GACnHvN,KAAK8L,WAAW9G,OAAOI,KAAKC,OAAOS,KAAKsH,MAAMpN,KAAKY,gBAAgB4D,WAAW+I,gBAAgB,CAAA,GAC9FvN,KAAK8L,WAAW9G,OAAOjF,OAAOoF,SAASnF,KAAKW,eAAe0D,OAAOrE,KAAK8L,WAAW9G,OAAOC,KAAKE,QAC9FnF,KAAK8L,WAAW9G,OAAOjF,OAAOmF,QAAQlF,KAAKW,eAAeiJ,OAAO5J,KAAK8L,WAAW9G,OAAOC,KAAKC,OAC7FlF,KAAK8L,WAAWvG,IAAIxF,OAAOoF,SAASW,KAAKwH,MAAMtN,KAAK8L,WAAW9G,OAAOjF,OAAOoF,SAASV,EAAAA,GACtFzE,KAAK8L,WAAWvG,IAAIxF,OAAOmF,QAAQY,KAAKwH,MAAMtN,KAAK8L,WAAW9G,OAAOjF,OAAOmF,QAAQT,EAAAA,GACpFzE,KAAK8L,WAAWvG,IAAIN,KAAKE,SAASnF,KAAK8L,WAAWvG,IAAIxF,OAAOoF,SAASnF,KAAKW,eAAe0D,MAC1FrE,KAAK8L,WAAWvG,IAAIN,KAAKC,QAAQlF,KAAK8L,WAAWvG,IAAIxF,OAAOmF,QAAQlF,KAAKW,eAAeiJ;UAC1F;UAEQ,gCAAgC1E,IAAeC,IAAAA;AACrDnF,iBAAK8L,WAAW9G,OAAOjF,OAAOoF,SAASA,IACvCnF,KAAK8L,WAAW9G,OAAOjF,OAAOmF,QAAQA;AAEtC,uBAAWoH,MAAKtM,KAAKwL;AACnBc,cAAAA,GAAEM,OAAO5M,KAAK8L,UAAAA;AAEhB9L,iBAAKwN,uBAAAA;UACP;UAEQ,yBAAAA;AACNxN,iBAAKmL,iBAAiB6B,KAAK,EAAElJ,OAAO,GAAGC,KAAK/D,KAAKW,eAAe0D,OAAO,EAAA,CAAA;UACzE;QAAA;AAnKF,QAAA3E,GAAA,iBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,oBAAA;AChBA,cAAAE,KAAAD,GAAA,GAAA,GAIA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA;QAUA,MAAaiM,UAA0B,EAAA/L,gBAAAA;UAMrC,YACE4N,IACAC,IACAlN,IACiB2K,IACjBwC,IACAC,IACiBC,IACjBC,GACA5C,GACA6C,GAAAA;AAEAhN,kBAAM0M,IAAUC,IAAW,UAAUlN,IAAAA,MAAcuN,GAAcJ,IAAeC,IAAgB1C,GAAmB4C,CAAAA,GARlG,KAAA3C,mBAAAA,IAGA,KAAA0C,eAAAA,IAVX,KAAAG,2BAAuEhO,KAAK0B,SAAS,IAAI,EAAAC,mBAAAA,GACzF,KAAAsM,QAAmB,IAAI,EAAAC,YAe7BlO,KAAKoK,SAAS,EACZ1E,GAAG,GACHC,GAAG,GACHwI,WAAAA,OACA7L,OAAO,IACP4C,OAAO,EAAA,GAETlF,KAAKoO,mBAAmB,EACtB,KAAOpO,KAAKqO,iBAAiBC,KAAKtO,IAAAA,GAClC,OAASA,KAAKuO,mBAAmBD,KAAKtO,IAAAA,GACtC,WAAaA,KAAKwO,uBAAuBF,KAAKtO,IAAAA,GAC9C,SAAWA,KAAKyO,qBAAqBH,KAAKtO,IAAAA,EAAAA,GAE5CA,KAAK0B,SAASkM,GAAec,eAAe,MAAM1O,KAAK2O,sBAAAA,CAAAA,CAAAA,GACvD3O,KAAK2O,sBAAAA;UACP;UAEO,OAAO5J,IAAAA;AACZhE,kBAAM6L,OAAO7H,EAAAA,GAEb/E,KAAKoK,SAAS,EACZ1E,GAAG,GACHC,GAAG,GACHwI,WAAAA,OACA7L,OAAO,IACP4C,OAAO,EAAA;UAEX;UAEO,QAAArC;AAAAA,gBAAAA;AACL7C,iBAAK4O,aAAAA,GAC8B,UAAnCnP,KAAAO,KAAKgO,yBAAyBtJ,UAAAA,WAAKjF,MAAAA,GAAEoP,sBAAAA,GACrC7O,KAAK2O,sBAAAA;UACP;UAEO,aAAAlL;AAAAA,gBAAAA;AAC8B,sBAAnChE,KAAAO,KAAKgO,yBAAyBtJ,UAAAA,WAAKjF,MAAAA,GAAEqP,MAAAA,GACrC9O,KAAKmL,iBAAiB6B,KAAK,EAAElJ,OAAO9D,KAAKW,eAAeyH,OAAOzC,GAAG5B,KAAK/D,KAAKW,eAAeyH,OAAOzC,EAAAA,CAAAA;UACpG;UAEO,cAAAjC;AAAAA,gBAAAA;AAC8B,sBAAnCjE,KAAAO,KAAKgO,yBAAyBtJ,UAAAA,WAAKjF,MAAAA,GAAEsP,OAAAA,GACrC/O,KAAKmL,iBAAiB6B,KAAK,EAAElJ,OAAO9D,KAAKW,eAAeyH,OAAOzC,GAAG5B,KAAK/D,KAAKW,eAAeyH,OAAOzC,EAAAA,CAAAA;UACpG;UAEQ,wBAAAgJ;AACF3O,iBAAKY,gBAAgB4D,WAAWwK,cAC7BhP,KAAKgO,yBAAyBtJ,UACjC1E,KAAKgO,yBAAyBtJ,QAAQ,IAAI9E,GAAAqP,wBAAwB,MAAMjP,KAAKkP,QAAAA,IAAQ,GAAOlP,KAAKc,mBAAAA,KAGnGd,KAAKgO,yBAAyBf,MAAAA,GAIhCjN,KAAKmL,iBAAiB6B,KAAK,EAAElJ,OAAO9D,KAAKW,eAAeyH,OAAOzC,GAAG5B,KAAK/D,KAAKW,eAAeyH,OAAOzC,EAAAA,CAAAA;UACpG;UAEO,mBAAAhC;AAAAA,gBAAAA;AAC8B,sBAAnClE,KAAAO,KAAKgO,yBAAyBtJ,UAAAA,WAAKjF,MAAAA,GAAEoP,sBAAAA;UACvC;UAEO,kBAAkBjL,IAAkBC,IAAAA;AAAAA,aACpC7D,KAAKgO,yBAAyBtJ,SAAS1E,KAAKgO,yBAAyBtJ,MAAMyK,WAC9EnP,KAAKkP,QAAAA,KAAQ,IAEblP,KAAKgO,yBAAyBtJ,MAAMmK,sBAAAA;UAExC;UAEQ,QAAQO,IAAAA;AAEd,gBAAA,CAAKpP,KAAK6N,aAAawB,uBAAuBrP,KAAK6N,aAAayB;AAE9D,qBAAA,KADAtP,KAAK4O,aAAAA;AAIP,kBAAMW,KAAUvP,KAAKW,eAAeyH,OAAOoH,QAAQxP,KAAKW,eAAeyH,OAAOzC,GACxE8J,KAA0BF,KAAUvP,KAAKW,eAAeyH,OAAOC;AAGrE,gBAAIoH,KAA0B,KAAKA,MAA2BzP,KAAKW,eAAe0D;AAEhF,qBAAA,KADArE,KAAK4O,aAAAA;AAKP,kBAAMc,KAAU5J,KAAK6J,IAAI3P,KAAKW,eAAeyH,OAAO1C,GAAG1F,KAAKW,eAAeiJ,OAAO,CAAA;AAElF,gBADA5J,KAAKW,eAAeyH,OAAOwH,MAAMC,IAAIN,EAAAA,EAAUO,SAASJ,IAAS1P,KAAKiO,KAAAA,GAAAA,WAClEjO,KAAKiO,MAAM8B,SAAf;AAIA,kBAAA,CAAK/P,KAAKc,oBAAoBqN,WAAW;AACvCnO,qBAAK4O,aAAAA,GACL5O,KAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOqN,OAAOzK;AACvD,sBAAM0K,KAAcjQ,KAAKY,gBAAgB4D,WAAWyL,aAC9CC,KAAsBlQ,KAAKY,gBAAgB4D,WAAW0L;AAU5D,uBATIA,MAA+C,WAAxBA,MACzBlQ,KAAKoO,iBAAiB8B,EAAAA,EAAqBR,IAASD,IAAyBzP,KAAKiO,KAAAA,GAEpFjO,KAAKoD,KAAK4D,QAAAA,GACVhH,KAAKoK,OAAO1E,IAAIgK,IAChB1P,KAAKoK,OAAOzE,IAAI8J,IAChBzP,KAAKoK,OAAO+D,YAAAA,OACZnO,KAAKoK,OAAO9H,QAAQ2N,IAAAA,MACpBjQ,KAAKoK,OAAOlF,QAAQlF,KAAKiO,MAAMkC,SAAAA;cAAAA;AAKjC,kBAAA,CAAInQ,KAAKgO,yBAAyBtJ,SAAU1E,KAAKgO,yBAAyBtJ,MAAM0L,iBAAhF;AAKA,oBAAIpQ,KAAKoK,QAAQ;AAEf,sBAAIpK,KAAKoK,OAAO1E,MAAMgK,MAClB1P,KAAKoK,OAAOzE,MAAM8J,MAClBzP,KAAKoK,OAAO+D,cAAcnO,KAAKc,oBAAoBqN,aACnDnO,KAAKoK,OAAO9H,UAAUtC,KAAKY,gBAAgB4D,WAAWyL,eACtDjQ,KAAKoK,OAAOlF,UAAUlF,KAAKiO,MAAMkC,SAAAA;AACnC;AAEFnQ,uBAAK4O,aAAAA;gBAAAA;AAGP5O,qBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoO,iBAAiBpO,KAAKY,gBAAgB4D,WAAWyL,eAAe,OAAA,EAASP,IAASD,IAAyBzP,KAAKiO,KAAAA,GACrHjO,KAAKoD,KAAK4D,QAAAA,GAEVhH,KAAKoK,OAAO1E,IAAIgK,IAChB1P,KAAKoK,OAAOzE,IAAI8J,IAChBzP,KAAKoK,OAAO+D,YAAAA,OACZnO,KAAKoK,OAAO9H,QAAQtC,KAAKY,gBAAgB4D,WAAWyL,aACpDjQ,KAAKoK,OAAOlF,QAAQlF,KAAKiO,MAAMkC,SAAAA;cAAAA;AAxB7BnQ,qBAAK4O,aAAAA;YAAAA;UAyBT;UAEQ,eAAAA;AACF5O,iBAAKoK,WAGH,EAAAiG,aAAarQ,KAAKc,oBAAoB2D,MAAM,IAC9CzE,KAAKwD,UAAAA,IAELxD,KAAKsQ,YAAYtQ,KAAKoK,OAAO1E,GAAG1F,KAAKoK,OAAOzE,GAAG3F,KAAKoK,OAAOlF,OAAO,CAAA,GAEpElF,KAAKoK,SAAS,EACZ1E,GAAG,GACHC,GAAG,GACHwI,WAAAA,OACA7L,OAAO,IACP4C,OAAO,EAAA;UAGb;UAEQ,iBAAiBQ,IAAWC,IAAWV,IAAAA;AAC7CjF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOqN,OAAOzK,KACvDvF,KAAKuQ,oBAAoB7K,IAAGC,IAAG3F,KAAKY,gBAAgB4D,WAAWgM,WAAAA,GAC/DxQ,KAAKoD,KAAK4D,QAAAA;UACZ;UAEQ,mBAAmBtB,IAAWC,IAAWV,IAAAA;AAC/CjF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOqN,OAAOzK,KACvDvF,KAAKyQ,WAAW/K,IAAGC,IAAGV,GAAKkL,SAAAA,GAAY,CAAA,GACvCnQ,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAO+N,aAAanL,KAC7DvF,KAAK2Q,mBAAmB1L,IAAMS,IAAGC,EAAAA,GACjC3F,KAAKoD,KAAK4D,QAAAA;UACZ;UAEQ,uBAAuBtB,IAAWC,IAAWV,IAAAA;AACnDjF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOqN,OAAOzK,KACvDvF,KAAK4Q,uBAAuBlL,IAAGC,EAAAA,GAC/B3F,KAAKoD,KAAK4D,QAAAA;UACZ;UAEQ,qBAAqBtB,IAAWC,IAAWV,IAAAA;AACjDjF,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAK+C,cAAcnG,KAAKU,cAAciC,OAAOqN,OAAOzK,KACzDvF,KAAK6Q,kBAAkBnL,IAAGC,IAAGV,GAAKkL,SAAAA,GAAY,CAAA,GAC9CnQ,KAAKoD,KAAK4D,QAAAA;UACZ;QAAA;AA9MF,QAAAtH,GAAA,oBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,YAAA,QCpBAA,GAAA,YAAA,MAAA;UAGE,cAAA;AACEM,iBAAK8Q,QAAQ,CAAA;UACf;UAEO,OAAO5L,IAAeC,IAAAA;AAC3B,qBAASO,KAAI,GAAGA,KAAIR,IAAOQ,MAAK;AAC1B1F,mBAAK8Q,MAAMjM,UAAUa,MACvB1F,KAAK8Q,MAAMC,KAAK,CAAA,CAAA;AAElB,uBAASpL,KAAI3F,KAAK8Q,MAAMpL,EAAAA,EAAGb,QAAQc,KAAIR,IAAQQ;AAC7C3F,qBAAK8Q,MAAMpL,EAAAA,EAAGqL,KAAAA,MAAKC;AAErBhR,mBAAK8Q,MAAMpL,EAAAA,EAAGb,SAASM;YAAAA;AAEzBnF,iBAAK8Q,MAAMjM,SAASK;UACtB;UAEO,QAAA+H;AACL,qBAASvH,KAAI,GAAGA,KAAI1F,KAAK8Q,MAAMjM,QAAQa;AACrC,uBAASC,KAAI,GAAGA,KAAI3F,KAAK8Q,MAAMpL,EAAAA,EAAGb,QAAQc;AACxC3F,qBAAK8Q,MAAMpL,EAAAA,EAAGC,EAAAA,IAAAA;UAGpB;QAAA;MAAA,GAAA,IAAA,CAAAlG,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAA;ACzBF,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAKA,IAAAA,GAAA,GAAA;QAEA,MAAagM,UAAwB,EAAA9L,gBAAAA;UAGnC,YACE4N,IACAC,IACAlN,IACAsK,IACA6C,IACAC,IACA1C,IACA4C,GACAC,GAAAA;AAEAhN,kBAAM0M,IAAUC,IAAW,QAAQlN,IAAAA,MAAcuN,GAAcJ,IAAeC,IAAgB1C,IAAmB4C,CAAAA,GAEjH9N,KAAK0B,SAASoJ,GAAWmG,oBAAoBxR,CAAAA,OAAKO,KAAKkR,yBAAyBzR,EAAAA,CAAAA,CAAAA,GAChFO,KAAK0B,SAASoJ,GAAWqG,oBAAoB1R,CAAAA,OAAKO,KAAKoR,yBAAyB3R,EAAAA,CAAAA,CAAAA;UAClF;UAEO,OAAOsF,IAAAA;AACZhE,kBAAM6L,OAAO7H,EAAAA,GAEb/E,KAAKoK,SAAAA;UACP;UAEO,QAAAvH;AACL7C,iBAAKqR,kBAAAA;UACP;UAEQ,oBAAAA;AACN,gBAAIrR,KAAKoK,QAAQ;AACfpK,mBAAKsQ,YAAYtQ,KAAKoK,OAAOkH,IAAItR,KAAKoK,OAAOmH,IAAIvR,KAAKoK,OAAOR,OAAO5J,KAAKoK,OAAOkH,IAAI,CAAA;AACpF,oBAAME,KAAiBxR,KAAKoK,OAAOqH,KAAKzR,KAAKoK,OAAOmH,KAAK;AACrDC,cAAAA,KAAiB,KACnBxR,KAAKsQ,YAAY,GAAGtQ,KAAKoK,OAAOmH,KAAK,GAAGvR,KAAKoK,OAAOR,MAAM4H,EAAAA,GAE5DxR,KAAKsQ,YAAY,GAAGtQ,KAAKoK,OAAOqH,IAAIzR,KAAKoK,OAAOsH,IAAI,CAAA,GACpD1R,KAAKoK,SAAAA;YAAS4G;UAElB;UAEQ,yBAAyBvR,IAAAA;AAU/B,gBATIA,GAAEiJ,OAAO,EAAAiJ,yBACX3R,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAO2E,WAAW/B,MAClD9F,GAAEiJ,OAAM,GAAA9I,GAAAgS,YAAWnS,GAAEiJ,EAAAA,IAE9B1I,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOkP,KAAKpS,GAAEiJ,EAAAA,EAAInD,MAE3DvF,KAAKoD,KAAKgD,YAAYpG,KAAKU,cAAciC,OAAOmP,WAAWvM,KAGzD9F,GAAE8R,OAAO9R,GAAEgS;AAEbzR,mBAAK4Q,uBAAuBnR,GAAE6R,IAAI7R,GAAE8R,IAAI9R,GAAEiS,KAAKjS,GAAE6R,EAAAA;iBAC5C;AAELtR,mBAAK4Q,uBAAuBnR,GAAE6R,IAAI7R,GAAE8R,IAAI9R,GAAEmK,OAAOnK,GAAE6R,EAAAA;AACnD,uBAAS3L,KAAIlG,GAAE8R,KAAK,GAAG5L,KAAIlG,GAAEgS,IAAI9L;AAC/B3F,qBAAK4Q,uBAAuB,GAAGjL,IAAGlG,GAAEmK,IAAAA;AAEtC5J,mBAAK4Q,uBAAuB,GAAGnR,GAAEgS,IAAIhS,GAAEiS,EAAAA;YAAAA;AAEzC1R,iBAAKoK,SAAS3K;UAChB;UAEQ,yBAAyBA,IAAAA;AAC/BO,iBAAKqR,kBAAAA;UACP;QAAA;AApEF,QAAA3R,GAAA,kBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,uBAAA;ACRA,cAAAE,KAAAD,GAAA,GAAA;QAYA,MAAa+L,UAA6B9L,GAAAC,gBAAAA;UAGxC,YACE4N,IACAC,IACAlN,IACAmN,IACAG,IACA5C,GACA0C,GACAG,GAAAA;AAEAhN,kBAAM0M,IAAUC,IAAW,aAAalN,IAAAA,MAAcuN,GAAcJ,IAAeC,GAAgB1C,GAAmB4C,EAAAA,GACtH9N,KAAK+R,YAAAA;UACP;UAEQ,cAAAA;AACN/R,iBAAKoK,SAAS,EACZtG,OAAAA,QACAC,KAAAA,QACAd,kBAAAA,QACAoF,OAAAA,OAAO2I;UAEX;UAEO,OAAOjM,IAAAA;AACZhE,kBAAM6L,OAAO7H,EAAAA,GAGT/E,KAAKsB,gBAAgByB,kBAAkB/C,KAAKsB,gBAAgB0B,iBAC9DhD,KAAK+R,YAAAA,GACL/R,KAAKgS,iBAAiBhS,KAAKsB,gBAAgByB,gBAAgB/C,KAAKsB,gBAAgB0B,cAAchD,KAAKsB,gBAAgB2B,gBAAAA;UAEvH;UAEO,QAAAJ;AACD7C,iBAAKoK,OAAOtG,SAAS9D,KAAKoK,OAAOrG,QACnC/D,KAAK+R,YAAAA,GACL/R,KAAKwD,UAAAA;UAET;UAEO,aAAAC;AACLzD,iBAAK6C,MAAAA,GACL7C,KAAKgS,iBAAiBhS,KAAKsB,gBAAgByB,gBAAgB/C,KAAKsB,gBAAgB0B,cAAchD,KAAKsB,gBAAgB2B,gBAAAA;UACrH;UAEO,cAAAS;AACL1D,iBAAK6C,MAAAA,GACL7C,KAAKgS,iBAAiBhS,KAAKsB,gBAAgByB,gBAAgB/C,KAAKsB,gBAAgB0B,cAAchD,KAAKsB,gBAAgB2B,gBAAAA;UACrH;UAEO,uBAAuBa,IAAqCC,IAAmCd,IAAAA;AACpGlC,kBAAM+B,uBAAuBgB,IAAOC,IAAKd,EAAAA,GACzCjD,KAAKgS,iBAAiBlO,IAAOC,IAAKd,EAAAA;UACpC;UAEQ,iBAAiBa,IAAqCC,IAAmCd,IAAAA;AAE/F,gBAAA,CAAKjD,KAAKiS,gBAAgBnO,IAAOC,IAAKd,IAAkBjD,KAAKW,eAAeyH,OAAOC,KAAAA;AACjF;AAOF,gBAHArI,KAAKwD,UAAAA,GAAAA,CAGAM,MAAAA,CAAUC;AAEb,qBAAA,KADA/D,KAAK+R,YAAAA;AAKP,kBAAMG,KAAmBpO,GAAM,CAAA,IAAK9D,KAAKW,eAAeyH,OAAOC,OACzD8J,KAAiBpO,GAAI,CAAA,IAAK/D,KAAKW,eAAeyH,OAAOC,OACrD+J,IAAyBtM,KAAKuM,IAAIH,IAAkB,CAAA,GACpDI,IAAuBxM,KAAK6J,IAAIwC,IAAgBnS,KAAKW,eAAe0D,OAAO,CAAA;AAGjF,gBAAI+N,KAA0BpS,KAAKW,eAAe0D,QAAQiO,IAAuB;AAC/EtS,mBAAKoK,OAAO/B,QAAQrI,KAAKW,eAAeyH,OAAOC;iBADjD;AASA,kBAJArI,KAAKoD,KAAKgD,aAAapG,KAAKc,oBAAoBqN,YAC5CnO,KAAKU,cAAciC,OAAO4P,iCAC1BvS,KAAKU,cAAciC,OAAO6P,wCAAwCjN,KAElEtC,IAAkB;AACpB,sBAAMwP,KAAW3O,GAAM,CAAA,GACjBoB,KAAQnB,GAAI,CAAA,IAAK0O,IACjBtN,KAASmN,IAAuBF,IAAyB;AAC/DpS,qBAAKyQ,WAAWgC,IAAUL,GAAwBlN,IAAOC,EAAAA;cAAAA,OACpD;AAEL,sBAAMsN,KAAWP,OAAqBE,IAAyBtO,GAAM,CAAA,IAAK,GACpE4O,IAAiBN,MAA2BD,KAAiBpO,GAAI,CAAA,IAAK/D,KAAKW,eAAeiJ;AAChG5J,qBAAKyQ,WAAWgC,IAAUL,GAAwBM,IAAiBD,IAAU,CAAA;AAG7E,sBAAME,IAAkB7M,KAAKuM,IAAIC,IAAuBF,IAAyB,GAAG,CAAA;AAIpF,oBAHApS,KAAKyQ,WAAW,GAAG2B,IAAyB,GAAGpS,KAAKW,eAAeiJ,MAAM+I,CAAAA,GAGrEP,MAA2BE,GAAsB;AAEnD,wBAAMM,KAAST,OAAmBG,IAAuBvO,GAAI,CAAA,IAAK/D,KAAKW,eAAeiJ;AACtF5J,uBAAKyQ,WAAW,GAAG6B,GAAsBM,IAAQ,CAAA;gBAAA;cAAA;AAKrD5S,mBAAKoK,OAAOtG,QAAQ,CAACA,GAAM,CAAA,GAAIA,GAAM,CAAA,CAAA,GACrC9D,KAAKoK,OAAOrG,MAAM,CAACA,GAAI,CAAA,GAAIA,GAAI,CAAA,CAAA,GAC/B/D,KAAKoK,OAAOnH,mBAAmBA,IAC/BjD,KAAKoK,OAAO/B,QAAQrI,KAAKW,eAAeyH,OAAOC;YAAAA;UACjD;UAEQ,gBAAgBvE,IAAqCC,IAAmCd,IAA2BoF,IAAAA;AACzH,mBAAA,CAAQrI,KAAK6S,qBAAqB/O,IAAO9D,KAAKoK,OAAOtG,KAAAA,KAAAA,CAClD9D,KAAK6S,qBAAqB9O,IAAK/D,KAAKoK,OAAOrG,GAAAA,KAC5Cd,OAAqBjD,KAAKoK,OAAOnH,oBACjCoF,OAAUrI,KAAKoK,OAAO/B;UAC1B;UAEQ,qBAAqByK,IAAsCC,IAAAA;AACjE,mBAAA,EAAA,CAAKD,MAAAA,CAAWC,OAITD,GAAO,CAAA,MAAOC,GAAO,CAAA,KAAMD,GAAO,CAAA,MAAOC,GAAO,CAAA;UACzD;QAAA;AApIF,QAAArT,GAAA,uBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAA;ACZA,cAAAE,KAAAD,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAGA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA;QASA,MAAa8L,UAAwB,EAAA5L,gBAAAA;UAOnC,YACE4N,IACAC,IACAlN,IACA+C,IACAoK,IACAC,IACiBoF,IACjB9H,IACA4C,GACAC,GAAAA;AAEAhN,kBAAM0M,IAAUC,IAAW,QAAQlN,IAAQ+C,IAAOwK,GAAcJ,IAAeC,IAAgB1C,IAAmB4C,CAAAA,GALjG,KAAAkF,0BAAAA,IAZX,KAAAC,kBAA0B,GAC1B,KAAAC,iBAAyB,IACzB,KAAAC,yBAAqD,CAAC,GACtD,KAAAC,YAAY,IAAI,EAAAlF,YAetBlO,KAAKoK,SAAS,IAAI,EAAAiJ,aAClBrT,KAAK0B,SAASkM,GAAe0F,uBAAuB,qBAAqB5O,CAAAA,OAAS1E,KAAKuT,iBAAiB7O,EAAAA,CAAAA,CAAAA;UAC1G;UAEO,OAAOK,IAAAA;AACZhE,kBAAM6L,OAAO7H,EAAAA;AAGb,kBAAMyO,KAAexT,KAAKwH,SAAAA,OAAS,KAAO;AACtCxH,iBAAKiT,oBAAoBlO,GAAIC,OAAOI,KAAKF,SAASlF,KAAKkT,mBAAmBM,OAC5ExT,KAAKiT,kBAAkBlO,GAAIC,OAAOI,KAAKF,OACvClF,KAAKkT,iBAAiBM,IACtBxT,KAAKmT,yBAAyB,CAAC,IAGjCnT,KAAKoK,OAAO6C,MAAAA,GACZjN,KAAKoK,OAAOwC,OAAO5M,KAAKW,eAAeiJ,MAAM5J,KAAKW,eAAe0D,IAAAA;UACnE;UAEO,QAAAxB;AACL7C,iBAAKoK,OAAO6C,MAAAA,GACZjN,KAAKwD,UAAAA;UACP;UAEQ,aACNiQ,IACAC,IACAC,IAAAA;AAMA,qBAAShO,KAAI8N,IAAU9N,MAAK+N,IAAS/N,MAAK;AACxC,oBAAMiO,KAAMjO,KAAI3F,KAAKW,eAAeyH,OAAOC,OACrCwL,KAAO7T,KAAKW,eAAeyH,OAAOwH,MAAMC,IAAI+D,EAAAA,GAC5CE,KAAe9T,KAAKgT,wBAAwBe,oBAAoBH,EAAAA;AACtE,uBAASlO,KAAI,GAAGA,KAAI1F,KAAKW,eAAeiJ,MAAMlE,MAAK;AACjDmO,gBAAAA,GAAM/D,SAASpK,IAAG1F,KAAKoT,SAAAA;AACvB,oBAAInO,KAAOjF,KAAKoT,WAGZY,KAAAA,OACAC,KAAYvO;AAIhB,oBAAwB,MAApBT,GAAKkL,SAAAA,GAAT;AAcA,sBAAI2D,GAAajP,SAAS,KAAKa,OAAMoO,GAAa,CAAA,EAAG,CAAA,GAAI;AACvDE,oBAAAA,KAAAA;AACA,0BAAME,KAAQJ,GAAaK,MAAAA;AAI3BlP,oBAAAA,KAAO,IAAIrF,GAAAwU,eACTpU,KAAKoT,WACLS,GAAMQ,kBAAAA,MAAwBH,GAAM,CAAA,GAAIA,GAAM,CAAA,CAAA,GAC9CA,GAAM,CAAA,IAAKA,GAAM,CAAA,CAAA,GAInBD,KAAYC,GAAM,CAAA,IAAK;kBAAA;AAAA,mBAOpBF,MAAYhU,KAAKsU,eAAerP,EAAAA,KAQ/BgP,KAAYJ,GAAMhP,SAAS,KAAKgP,GAAMU,aAAaN,KAAY,CAAA,MAAO,EAAAO,mBAExEvP,GAAK8K,WAAAA,WACL9K,GAAK8K,WAAW,KAAK,KASzB4D,GACE1O,IACAS,IACAC,EAAAA,GAGFD,KAAIuO;gBAAAA;cAAAA;YAAAA;UAGV;UAMQ,gBAAgBR,IAAkBC,IAAAA;AACxC,kBAAMe,KAAMzU,KAAKoD,MACXwG,KAAO5J,KAAKW,eAAeiJ;AACjC,gBAAI8K,KAAiB,GACjBC,KAAiB,GACjBC,KAA+B;AAEnCH,YAAAA,GAAIxO,KAAAA,GAEJjG,KAAK6U,aAAapB,IAAUC,IAAS,CAACzO,IAAMS,IAAGC,OAAAA;AAG7C,kBAAImP,KAAgB;AAEhB7P,cAAAA,GAAK8P,UAAAA,IAELD,KADE7P,GAAK+P,YAAAA,IACShV,KAAKU,cAAciC,OAAOmP,WAAWvM,MAC5CN,GAAKgQ,QAAAA,IACE,OAAO,EAAAC,cAAcC,WAAWlQ,GAAKmQ,WAAAA,CAAAA,EAAcC,KAAK,GAAA,CAAA,MAExDrV,KAAKU,cAAciC,OAAOkP,KAAK5M,GAAKmQ,WAAAA,CAAAA,EAAc7P,MAE3DN,GAAKqQ,QAAAA,IACdR,KAAgB,OAAO,EAAAI,cAAcC,WAAWlQ,GAAKsQ,WAAAA,CAAAA,EAAcF,KAAK,GAAA,CAAA,MAC/DpQ,GAAKuQ,YAAAA,MACdV,KAAgB9U,KAAKU,cAAciC,OAAOkP,KAAK5M,GAAKsQ,WAAAA,CAAAA,EAAchQ;AAKpE,kBAAIkQ,IAAAA;AACJzV,mBAAKa,mBAAmB6U,wBAAwBhQ,IAAG1F,KAAKW,eAAeyH,OAAOC,QAAQ1C,IAAAA,QAAcgQ,CAAAA,OAAAA;AAC1E,0BAApBA,GAAEC,QAAQ/J,SAAmB4J,MAG7BE,GAAEE,uBACJf,KAAgBa,GAAEE,mBAAmBtQ,MAEvCkQ,IAA4B,UAApBE,GAAEC,QAAQ/J;cAAe,CAAA,GAGb,SAAlB+I,OAGFF,KAAShP,IACTiP,KAAShP,KAGPA,OAAMgP,MAERF,GAAIrO,YAAYwO,MAAiB,IACjC5U,KAAKyQ,WAAWiE,IAAQC,IAAQ/K,KAAO8K,IAAQ,CAAA,GAC/CA,KAAShP,IACTiP,KAAShP,MACAiP,OAAkBE,OAE3BL,GAAIrO,YAAYwO,MAAiB,IACjC5U,KAAKyQ,WAAWiE,IAAQC,IAAQjP,KAAIgP,IAAQ,CAAA,GAC5CA,KAAShP,IACTiP,KAAShP,KAGXiP,KAAgBE;YAAa,CAAA,GAIT,SAAlBF,OACFH,GAAIrO,YAAYwO,IAChB5U,KAAKyQ,WAAWiE,IAAQC,IAAQ/K,KAAO8K,IAAQ,CAAA,IAGjDD,GAAIzN,QAAAA;UACN;UAEQ,gBAAgByM,IAAkBC,IAAAA;AACxC1T,iBAAK6U,aAAapB,IAAUC,IAAS,CAACzO,IAAMS,IAAGC,OAAM3F,KAAK8V,WAAW7Q,IAAMS,IAAGC,EAAAA,CAAAA;UAChF;UAEO,kBAAkB8N,IAAkBC,IAAAA;AAER,kBAA7B1T,KAAKoK,OAAO0G,MAAMjM,WAIlB7E,KAAKG,cACPH,KAAKG,WAAW4V,WAAAA,GAGlB/V,KAAKsQ,YAAY,GAAGmD,IAAUzT,KAAKW,eAAeiJ,MAAM8J,KAAUD,KAAW,CAAA,GAC7EzT,KAAKgW,gBAAgBvC,IAAUC,EAAAA,GAC/B1T,KAAKiW,gBAAgBxC,IAAUC,EAAAA;UACjC;UAKQ,eAAezO,IAAAA;AAGrB,gBAAwB,MAApBA,GAAKkL,SAAAA;AACP,qBAAA;AAIF,gBAAIlL,GAAK4D,QAAAA,IAAY;AACnB,qBAAA;AAGF,kBAAMX,KAAQjD,GAAK8C,SAAAA;AAGnB,gBAAI/H,KAAKmT,uBAAuB+C,eAAehO,EAAAA;AAC7C,qBAAOlI,KAAKmT,uBAAuBjL,EAAAA;AAIrClI,iBAAKoD,KAAK6C,KAAAA,GACVjG,KAAKoD,KAAKmE,OAAOvH,KAAKkT;AAKtB,kBAAMiD,KAAWrQ,KAAKsH,MAAMpN,KAAKoD,KAAKgT,YAAYlO,EAAAA,EAAOhD,KAAAA,IAASlF,KAAKiT;AAOvE,mBAJAjT,KAAKoD,KAAK4D,QAAAA,GAGVhH,KAAKmT,uBAAuBjL,EAAAA,IAASiO,IAC9BA;UACT;QAAA;AA1QF,QAAAzW,GAAA,kBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,oBAAA;ACfA,YAKI2W,IALAC,KAAM,GACNC,IAAM,GACNC,IAAAA,OACAC,IAAAA,OACAC,IAAAA;AAGJ,QAAAhX,GAAA,oBAAA,MAAA;UAWE,YACmBW,IACAsW,IACA9V,IACAC,IACAJ,IAAAA;AAJA,iBAAAL,YAAAA,IACA,KAAAsW,wBAAAA,IACA,KAAA9V,qBAAAA,IACA,KAAAC,sBAAAA,IACA,KAAAJ,gBAAAA,IAXH,KAAA8H,SAAkD,EAChEE,IAAI,GACJD,IAAI,GACJE,KAAK,EAAA;UAUP;UAMO,QAAQ1D,IAAiBS,IAAWC,GAAAA;AACzC3F,iBAAKwI,OAAOC,KAAKxD,GAAKwD,IACtBzI,KAAKwI,OAAOE,KAAKzD,GAAKyD,IACtB1I,KAAKwI,OAAOG,MAAgB,YAAV1D,GAAKwD,KAA4BxD,GAAK2R,SAASjO,MAAM,GAKvE4N,IAAM,GACND,KAAM,GACNG,IAAAA,OACAD,IAAAA,OACAE,IAAAA,OACAL,KAAUrW,KAAKU,cAAciC,QAG7B3C,KAAKa,mBAAmB6U,wBAAwBhQ,IAAGC,GAAG,UAAUgQ,CAAAA,OAAAA;AAC1DA,cAAAA,GAAEE,uBACJU,IAAMZ,GAAEE,mBAAmBgB,QAAQ,IAAI,UACvCJ,IAAAA,OAEEd,GAAEmB,uBACJR,KAAMX,GAAEmB,mBAAmBD,QAAQ,IAAI,UACvCL,IAAAA;YAAS,CAAA,GAKbE,IAAc1W,KAAK2W,sBAAsBI,eAAe/W,KAAKK,WAAWqF,IAAGC,CAAAA,GACvE+Q,MACFH,KAAOvW,KAAKc,oBAAoBqN,YAAYkI,GAAQW,4BAA4BX,GAAQY,mCAAmCJ,QAAQ,IAAI,UACvIJ,IAAAA,MACIJ,GAAQtJ,wBACVuJ,KAAMD,GAAQtJ,oBAAoB8J,QAAQ,IAAI,UAC9CL,IAAAA,QAKJxW,KAAKa,mBAAmB6U,wBAAwBhQ,IAAGC,GAAG,OAAOgQ,CAAAA,OAAAA;AACvDA,cAAAA,GAAEE,uBACJU,IAAMZ,GAAEE,mBAAmBgB,QAAQ,IAAI,UACvCJ,IAAAA,OAEEd,GAAEmB,uBACJR,KAAMX,GAAEmB,mBAAmBD,QAAQ,IAAI,UACvCL,IAAAA;YAAS,CAAA,GAMTC,MAGAF,IAFEG,IAAAA,YAEKzR,GAAKwD,KAAAA,aAA4C8N,IAAM,WAAA,YAGvDtR,GAAKwD,KAA6B8N,IAAM,WAG/CC,MAEFF,KAAAA,YAAOrR,GAAKyD,KAAAA,YAAgD4N,KAAM,WAK/C,WAAjBtW,KAAKwI,OAAOE,OACV+N,KAAAA,CAAWD,MAGXF,KAD4C,MAAxB,WAAjBtW,KAAKwI,OAAOC,MAAAA,aACRzI,KAAKwI,OAAOE,KAAuG,WAA/B2N,GAAQ/O,WAAWuP,QAAQ,IAAuC,WAAA,aAEtJ7W,KAAKwI,OAAOE,KAAuF,WAAjB1I,KAAKwI,OAAOC,IAEvG+N,IAAAA,OAAS,CAENC,KAAUD,MAGXD,IAD4C,MAAxB,WAAjBvW,KAAKwI,OAAOE,MAAAA,YACR1I,KAAKwI,OAAOC,KAAqF,WAA/B4N,GAAQvE,WAAW+E,QAAQ,IAAuC,WAAA,YAEpI7W,KAAKwI,OAAOC,KAAqE,WAAjBzI,KAAKwI,OAAOE,IAErF+N,IAAAA,QAKJJ,KAAAA,QAGArW,KAAKwI,OAAOC,KAAKgO,IAASF,IAAMvW,KAAKwI,OAAOC,IAC5CzI,KAAKwI,OAAOE,KAAK8N,IAASF,KAAMtW,KAAKwI,OAAOE;UAC9C;QAAA;MAAA,GAAA,KAAA,CAAAjJ,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,0BAAAA,GAAA,sBAAA;AClIF,cAAAE,KAAAD,GAAA,GAAA,GAIA,IAAAA,GAAA,GAAA,GAUMuX,IAA4C,CAAA;AAMlD,QAAAxX,GAAA,sBAAA,SACE+N,IACAmI,IACAjT,IACAwU,GACAC,GACAC,GACAC,GACAC,GAAAA;AAEA,gBAAMC,KAAY,GAAA,EAAAC,gBAAeN,GAAiBC,GAAkBC,GAAiBC,GAAkB1B,IAASjT,IAAQ4U,CAAAA;AAGxH,mBAAS5X,KAAI,GAAGA,KAAIuX,EAAerS,QAAQlF,MAAK;AAC9C,kBAAM+X,KAAQR,EAAevX,EAAAA,GACvBgY,KAAeD,GAAME,QAAQC,QAAQpK,EAAAA;AAC3C,gBAAIkK,MAAgB,GAAG;AACrB,mBAAI,GAAA,EAAAG,cAAaJ,GAAMK,QAAQP,CAAAA;AAC7B,uBAAOE,GAAMM;AAGc,oBAAzBN,GAAME,QAAQ/S,UAChB6S,GAAMM,MAAMzL,QAAAA,GACZ2K,EAAee,OAAOtY,IAAG,CAAA,KAEzB+X,GAAME,QAAQK,OAAON,IAAc,CAAA;AAErC;YAAA;UAAA;AAKJ,mBAAShY,KAAI,GAAGA,KAAIuX,EAAerS,QAAQlF,MAAK;AAC9C,kBAAM+X,KAAQR,EAAevX,EAAAA;AAC7B,iBAAI,GAAA,EAAAmY,cAAaJ,GAAMK,QAAQP,CAAAA;AAG7B,qBADAE,GAAME,QAAQ7G,KAAKtD,EAAAA,GACZiK,GAAMM;UAAAA;AAIjB,gBAAME,IAAmBzK,GAAiB0K,OACpCC,IAAoC,EACxCJ,OAAO,IAAIpY,GAAAyY,aAAanW,UAAUsV,GAAWU,EAAKI,cAAAA,GAClDP,QAAQP,GACRI,SAAS,CAACnK,EAAAA,EAAAA;AAGZ,iBADAyJ,EAAenG,KAAKqH,CAAAA,GACbA,EAASJ;QAClB,GAMAtY,GAAA,0BAAA,SAAwC+N,IAAAA;AACtC,mBAAS9N,KAAI,GAAGA,KAAIuX,EAAerS,QAAQlF,MAAK;AAC9C,kBAAM4Y,KAAQrB,EAAevX,EAAAA,EAAGiY,QAAQC,QAAQpK,EAAAA;AAChD,gBAAA,OAAI8K,IAAc;AACyB,oBAArCrB,EAAevX,EAAAA,EAAGiY,QAAQ/S,UAE5BqS,EAAevX,EAAAA,EAAGqY,MAAMzL,QAAAA,GACxB2K,EAAee,OAAOtY,IAAG,CAAA,KAGzBuX,EAAevX,EAAAA,EAAGiY,QAAQK,OAAOM,IAAO,CAAA;AAE1C;YAAA;UAAA;QAGN;MAAA,GAAA,KAAA,CAAA9Y,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,eAAAA,GAAA,iBAAA;ACtFA,cAAAE,KAAAD,GAAA,GAAA;AAEA,QAAAD,GAAA,iBAAA,SAA+ByX,IAAyBC,IAA0BC,IAAyBC,GAA0B1B,GAAqCjT,GAA0B4U,GAAAA;AAElM,gBAAMiB,IAA0B,EAC9B1G,YAAYnP,EAAOmP,YACnBxK,YAAY3E,EAAO2E,YACnB0I,QAAQpQ,GAAA6Y,YACR/H,cAAc9Q,GAAA6Y,YACd1L,qBAAqBnN,GAAA6Y,YACrBlG,gCAAgC3S,GAAA6Y,YAChCzB,2BAA2BpX,GAAA6Y,YAC3BjG,wCAAwC5S,GAAA6Y,YACxCxB,mCAAmCrX,GAAA6Y,YAGnC5G,MAAMlP,EAAOkP,KAAK6G,MAAAA,GAClBC,eAAehW,EAAOgW,eACtBC,mBAAmBjW,EAAOiW,kBAAAA;AAE5B,iBAAO,EACL/Q,cAAc+N,EAAQ/N,cACtB0P,kBAAAA,GACAhK,eAAeqI,EAAQrI,eACvBF,YAAYuI,EAAQvI,YACpB8J,iBAAiBA,IACjBC,kBAAkBA,IAClBC,iBAAiBA,IACjBC,kBAAkBA,GAClBpN,YAAY0L,EAAQ1L,YACpBlC,UAAU4N,EAAQ5N,UAClBiC,YAAY2L,EAAQ3L,YACpBD,gBAAgB4L,EAAQ5L,gBACxBuB,mBAAmBqK,EAAQrK,mBAC3BsN,4BAA4BjD,EAAQiD,4BACpCC,sBAAsBlD,EAAQkD,sBAC9BnW,QAAQ6V,EAAAA;QAEZ,GAEA9Y,GAAA,eAAA,SAA6BqZ,IAAqBC,IAAAA;AAChD,mBAASrZ,KAAI,GAAGA,KAAIoZ,GAAEpW,OAAOkP,KAAKhN,QAAQlF;AACxC,gBAAIoZ,GAAEpW,OAAOkP,KAAKlS,EAAAA,EAAGkX,SAASmC,GAAErW,OAAOkP,KAAKlS,EAAAA,EAAGkX;AAC7C,qBAAA;AAGJ,iBAAOkC,GAAExB,qBAAqByB,GAAEzB,oBAC5BwB,GAAElR,iBAAiBmR,GAAEnR,gBACrBkR,GAAE1L,eAAe2L,GAAE3L,cACnB0L,GAAExL,kBAAkByL,GAAEzL,iBACtBwL,GAAE7O,eAAe8O,GAAE9O,cACnB6O,GAAE/Q,aAAagR,GAAEhR,YACjB+Q,GAAE9O,eAAe+O,GAAE/O,cACnB8O,GAAE/O,mBAAmBgP,GAAEhP,kBACvB+O,GAAExN,sBAAsByN,GAAEzN,qBAC1BwN,GAAE1B,oBAAoB2B,GAAE3B,mBACxB0B,GAAEzB,qBAAqB0B,GAAE1B,oBACzByB,GAAEF,+BAA+BG,GAAEH,8BACnCE,GAAED,yBAAyBE,GAAEF,wBAC7BC,GAAEpW,OAAOmP,WAAW+E,SAASmC,GAAErW,OAAOmP,WAAW+E,QACjDkC,GAAEpW,OAAO2E,WAAWuP,SAASmC,GAAErW,OAAO2E,WAAWuP;QACvD,GAEAnX,GAAA,aAAA,SAA2BuZ,IAAAA;AACzB,iBAA4C,aAAxB,WAAZA,OAA8F,aAAxB,WAAZA;QACpE;MAAA,GAAA,KAAA,CAAAxZ,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAAA,GAAA,cAAAA,GAAA,yBAAA;ACrEA,cAAAE,KAAAD,GAAA,GAAA;AAEa,QAAAD,GAAAiS,yBAAyB,KAEzBjS,GAAAwZ,cAAc,KAIdxZ,GAAAgI,gBAAoC9H,GAAAyQ,aAAazQ,GAAAuZ,eAAe,WAAW;MAAA,GAAA,KAAA,CAAA1Z,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,0BAAA;ACDxF,QAAAA,GAAA,0BAAA,MAAA;UAcE,YACU0Z,IACAtY,IAAAA;AADA,iBAAAsY,kBAAAA,IACA,KAAAtY,sBAAAA,IAERd,KAAKoQ,kBAAAA,MACDpQ,KAAKc,oBAAoBqN,aAC3BnO,KAAKqZ,iBAAAA;UAET;UAEA,IAAA,WAAWlK;AAAsB,mBAAA,EAASnP,KAAKsZ,sBAAsBtZ,KAAKuZ;UAAiB;UAEpF,UAAAhN;AACDvM,iBAAKuZ,mBACPvZ,KAAKc,oBAAoByJ,OAAOiP,cAAcxZ,KAAKuZ,cAAAA,GACnDvZ,KAAKuZ,iBAAAA,SAEHvZ,KAAKsZ,uBACPtZ,KAAKc,oBAAoByJ,OAAOkP,aAAazZ,KAAKsZ,kBAAAA,GAClDtZ,KAAKsZ,qBAAAA,SAEHtZ,KAAK0Z,oBACP1Z,KAAKc,oBAAoByJ,OAAOoP,qBAAqB3Z,KAAK0Z,eAAAA,GAC1D1Z,KAAK0Z,kBAAAA;UAET;UAEO,wBAAA7K;AACD7O,iBAAKmP,aAITnP,KAAK4Z,0BAA0BC,KAAKC,IAAAA,GAEpC9Z,KAAKoQ,kBAAAA,MACApQ,KAAK0Z,oBACR1Z,KAAK0Z,kBAAkB1Z,KAAKc,oBAAoByJ,OAAOwP,sBAAsB,MAAA;AAC3E/Z,mBAAKoZ,gBAAAA,GACLpZ,KAAK0Z,kBAAAA;YAA2B,CAAA;UAGtC;UAEQ,iBAAiBM,KAAsBC,KAAAA;AAEzCja,iBAAKuZ,mBACPvZ,KAAKc,oBAAoByJ,OAAOiP,cAAcxZ,KAAKuZ,cAAAA,GACnDvZ,KAAKuZ,iBAAAA,SAOPvZ,KAAKsZ,qBAAqBtZ,KAAKc,oBAAoByJ,OAAOC,WAAW,MAAA;AAGnE,kBAAIxK,KAAK4Z,yBAAyB;AAChC,sBAAMM,KA1ES,OA0EgBL,KAAKC,IAAAA,IAAQ9Z,KAAK4Z;AAEjD,oBADA5Z,KAAK4Z,0BAAAA,QACDM,KAAO;AAET,yBAAA,KADAla,KAAKqZ,iBAAiBa,EAAAA;cAAAA;AAM1Bla,mBAAKoQ,kBAAAA,OACLpQ,KAAK0Z,kBAAkB1Z,KAAKc,oBAAoByJ,OAAOwP,sBAAsB,MAAA;AAC3E/Z,qBAAKoZ,gBAAAA,GACLpZ,KAAK0Z,kBAAAA;cAA2B,CAAA,GAIlC1Z,KAAKuZ,iBAAiBvZ,KAAKc,oBAAoByJ,OAAO4P,YAAY,MAAA;AAEhE,oBAAIna,KAAK4Z,yBAAyB;AAGhC,wBAAMM,KA/FO,OA+FkBL,KAAKC,IAAAA,IAAQ9Z,KAAK4Z;AAGjD,yBAFA5Z,KAAK4Z,0BAAAA,QAA0B5I,KAC/BhR,KAAKqZ,iBAAiBa,EAAAA;gBAAAA;AAKxBla,qBAAKoQ,kBAAAA,CAAmBpQ,KAAKoQ,iBAC7BpQ,KAAK0Z,kBAAkB1Z,KAAKc,oBAAoByJ,OAAOwP,sBAAsB,MAAA;AAC3E/Z,uBAAKoZ,gBAAAA,GACLpZ,KAAK0Z,kBAAAA;gBAA2B,CAAA;cAChC,GA1Ga,GAAA;YA2GC,GACjBM,EAAAA;UACL;UAEO,QAAAlL;AACL9O,iBAAKoQ,kBAAAA,MACDpQ,KAAKuZ,mBACPvZ,KAAKc,oBAAoByJ,OAAOiP,cAAcxZ,KAAKuZ,cAAAA,GACnDvZ,KAAKuZ,iBAAAA,SAEHvZ,KAAKsZ,uBACPtZ,KAAKc,oBAAoByJ,OAAOkP,aAAazZ,KAAKsZ,kBAAAA,GAClDtZ,KAAKsZ,qBAAAA,SAEHtZ,KAAK0Z,oBACP1Z,KAAKc,oBAAoByJ,OAAOoP,qBAAqB3Z,KAAK0Z,eAAAA,GAC1D1Z,KAAK0Z,kBAAAA;UAET;UAEO,SAAA3K;AAEL/O,iBAAK8O,MAAAA,GAEL9O,KAAK4Z,0BAAAA,QACL5Z,KAAKqZ,iBAAAA,GACLrZ,KAAK6O,sBAAAA;UACP;QAAA;MAAA,GAAA,KAAA,CAAApP,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,oBAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,0BAAA;AC3IF,cAAAE,KAAAD,GAAA,GAAA;AASa,QAAAD,GAAA0a,0BAA2E,EAEtF,KAAK,CAAC,EAAE1U,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAG7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAG7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAC7B,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACzD,KAAK,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGzD,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GACrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAEjE,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGzH,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAErC,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrC,MAAa,CACX,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrD,MAAa,CACX,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GACnD,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,GAGrD,MAAa,CAAC,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,GAAK,EAAE1G,GAAG,GAAGC,GAAG,GAAGwG,GAAG,GAAGC,GAAG,EAAA,CAAA,EAAA;AASnE,cAAMiO,IAAgF,EAEpF,KAAK,CACH,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,GACV,CAAC,GAAG,GAAG,GAAG,CAAA,CAAA,GAEZ,KAAK,CACH,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,CAAA,GAEN,KAAK,CACH,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,GACJ,CAAC,GAAG,CAAA,CAAA,EAAA;AAgDK,QAAA3a,GAAA4a,wBAAwH,EAEnI,KAAK,EAAE,GAAgB,cAAA,GACvB,KAAK,EAAE,GAAc,cAAA,GACrB,KAAK,EAAE,GAAgB,cAAA,GACvB,KAAK,EAAE,GAAc,cAAA,GACrB,KAAK,EAAE,GAAgB,sBAAA,GACvB,KAAK,EAAE,GAAc,sBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,qBAAA,GACvB,KAAK,EAAE,GAAc,qBAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,2BAAA,GACvB,KAAK,EAAE,GAAc,2BAAA,GACrB,KAAK,EAAE,GAAgB,0BAAA,GACvB,KAAK,EAAE,GAAc,0BAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GACrB,KAAK,EAAE,GAAgB,eAAA,GACvB,KAAK,EAAE,GAAc,eAAA,GAGrB,KAAK,EAAE,GAAgB,CAACC,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GACxF,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GACtF,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,cAAgB,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,aAAe,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,cAAgB,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,aAAe,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAChG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAC9F,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GACpI,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,mBAA+B,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAClH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,YAAAA,GACpG,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,kBAA8B,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,GAAAA,GAClH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,UAAU,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GAC3G,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,IAAI,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,SAAAA,GACvG,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAChH,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAC7G,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,QAAU,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,QAAU,MAAKA,EAAAA,KAAAA,GAChH,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAChK,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,kBAA8B,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,GAAAA,GAChH,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,gBAA4B,MAAKD,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,OAAS,MAAKA,EAAAA,KAAAA,GAC9G,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,MAAM,MAAKA,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,SAAW,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,IAAM,MAAKC,EAAAA,KAAO,MAAKD,EAAAA,KAAAA,GAG1O,KAAK,EAAE,GAAgB,YAAA,GACvB,KAAK,EAAE,GAAgB,YAAA,GACvB,KAAK,EAAE,GAAgB,sBAAA,GAGvB,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,4BAAsD,GAAc,eAAA,GAC3F,KAAK,EAAE,GAAgB,eAAsD,GAAc,cAAA,GAC3F,KAAK,EAAE,GAAgB,uBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,qBAAA,GAC3F,KAAK,EAAE,GAAgB,sBAAsD,GAAc,sBAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAC3F,KAAK,EAAE,GAAgB,gBAAsD,GAAc,2BAAA,GAG3F,KAAK,EAAE,GAAgB,8BAAA,GACvB,KAAK,EAAE,GAAc,8BAAA,GACrB,KAAK,EAAE,GAAgB,wDAAA,GACvB,KAAK,EAAE,GAAc,wDAAA,GACrB,KAAK,EAAE,GAAgB,8DAAA,GACvB,KAAK,EAAE,GAAc,8DAAA,GACrB,KAAK,EAAE,GAAgB,8BAAA,GACvB,KAAK,EAAE,GAAc,8BAAA,GACrB,KAAK,EAAE,GAAgB,wDAAA,GACvB,KAAK,EAAE,GAAc,wDAAA,GACrB,KAAK,EAAE,GAAgB,uDAAA,GACvB,KAAK,EAAE,GAAc,uDAAA,GAGrB,KAAK,EAAE,GAAgB,CAACA,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,GAC7F,KAAK,EAAE,GAAgB,CAACD,IAAIC,OAAO,aAAa,MAAMA,KAAK,OAAM,GAAA,QAAW,MAAMA,KAAK,OAAM,GAAA,cAAA,EAAA,GA0BlF9a,GAAA+a,uBAA0D,EAErE,KAAY,EAAE9E,GAAG,mBAAmB+E,MAAM,GAAiBC,cAAc,EAAA,GAEzE,KAAY,EAAEhF,GAAG,yBAAyB+E,MAAM,GAAmBE,aAAa,GAAGD,cAAc,EAAA,GAEjG,KAAY,EAAEhF,GAAG,mBAAmB+E,MAAM,GAAiBE,aAAa,EAAA,GAExE,KAAY,EAAEjF,GAAG,uBAAuB+E,MAAM,GAAmBE,aAAa,GAAGD,cAAc,EAAA,GAE/F,KAAY,EAAEhF,GAAG,wDAAwD+E,MAAM,GAAiBC,cAAc,EAAA,GAE9G,KAAY,EAAEhF,GAAG,sDAAsD+E,MAAM,GAAmBC,cAAc,EAAA,GAE9G,KAAY,EAAEhF,GAAG,wDAAwD+E,MAAM,GAAiBE,aAAa,EAAA,GAE7G,KAAY,EAAEjF,GAAG,2DAA2D+E,MAAM,GAAmBE,aAAa,EAAA,GAElH,KAAY,EAAEjF,GAAG,8BAA8B+E,MAAM,EAAA,GAErD,KAAY,EAAE/E,GAAG,qBAAqB+E,MAAM,GAAmBE,aAAa,GAAGD,cAAc,EAAA,GAE7F,KAAY,EAAEhF,GAAG,8BAA8B+E,MAAM,EAAA,GAErD,KAAY,EAAE/E,GAAG,8BAA8B+E,MAAM,EAAA,GAErD,KAAY,EAAE/E,GAAG,qBAAqB+E,MAAM,GAAmBE,aAAa,GAAGD,cAAc,EAAA,GAE7F,KAAY,EAAEhF,GAAG,8BAA8B+E,MAAM,EAAA,EAAA,GAGvDhb,GAAA+a,qBAAA,GAAA,IAAmC/a,GAAA+a,qBAAqB,GAAA,GAExD/a,GAAA+a,qBAAA,GAAA,IAAmC/a,GAAA+a,qBAAqB,GAAA,GAMxD/a,GAAA,oBAAA,SACE+U,IACAoG,IACAvU,IACAwU,GACA3D,GACAC,GACApP,GACAuP,GAAAA;AAEA,gBAAMwD,IAAyBrb,GAAA0a,wBAAwBS,EAAAA;AACvD,cAAIE;AAEF,mBAwBJ,SACEtG,IACAuG,IACA1U,IACAwU,IACA3D,IACAC,IAAAA;AAEA,uBAASzX,KAAI,GAAGA,KAAIqb,GAAenW,QAAQlF,MAAK;AAC9C,sBAAMsb,KAAMD,GAAerb,EAAAA,GACrBub,KAAU/D,KAAkB,GAC5BgE,KAAU/D,KAAmB;AACnC3C,gBAAAA,GAAI7O,SACFU,KAAU2U,GAAIvV,IAAIwV,IAClBJ,KAAUG,GAAItV,IAAIwV,IAClBF,GAAI9O,IAAI+O,IACRD,GAAI7O,IAAI+O,EAAAA;cAAAA;YAGd,EA5CyB1G,IAAKsG,GAAwBzU,IAASwU,GAAS3D,GAAiBC,CAAAA,GAAAA;AAIvF,gBAAMgE,IAAoBf,EAA4BQ,EAAAA;AACtD,cAAIO;AAEF,mBAyCJ,SACE3G,IACAuG,IACA1U,IACAwU,IACA3D,IACAC,IAAAA;AAEA,kBAAIiE,KAAaC,EAAezL,IAAImL,EAAAA;AAC/BK,cAAAA,OACHA,KAAa,oBAAIE,OACjBD,EAAeE,IAAIR,IAAgBK,EAAAA;AAErC,oBAAMjV,KAAYqO,GAAIrO;AACtB,kBAAyB,YAAA,OAAdA;AACT,sBAAM,IAAIqV,MAAM,8BAA8BrV,EAAAA,GAAAA;AAEhD,kBAAIsV,KAAUL,GAAWxL,IAAIzJ,EAAAA;AAC7B,kBAAA,CAAKsV,IAAS;AACZ,sBAAMxW,KAAQ8V,GAAe,CAAA,EAAGnW,QAC1BM,KAAS6V,GAAenW,QACxB8W,KAAYzZ,SAASC,cAAc,QAAA;AACzCwZ,gBAAAA,GAAUzW,QAAQA,IAClByW,GAAUxW,SAASA;AACnB,sBAAMyW,MAAS,GAAAhc,GAAAyD,cAAasY,GAAUrY,WAAW,IAAA,CAAA,GAC3CuY,KAAY,IAAIC,UAAU5W,IAAOC,EAAAA;AAGvC,oBAAI8D,IACA8S,IACA/C,IACAD;AACJ,oBAAI3S,GAAU4V,WAAW,GAAA;AACvB/S,kBAAAA,KAAIgT,SAAS7V,GAAUsS,MAAM,GAAG,CAAA,GAAI,EAAA,GACpCqD,KAAIE,SAAS7V,GAAUsS,MAAM,GAAG,CAAA,GAAI,EAAA,GACpCM,KAAIiD,SAAS7V,GAAUsS,MAAM,GAAG,CAAA,GAAI,EAAA,GACpCK,KAAI3S,GAAUvB,SAAS,KAAKoX,SAAS7V,GAAUsS,MAAM,GAAG,CAAA,GAAI,EAAA,KAAO;qBAC9D;AAAA,sBAAA,CAAItS,GAAU4V,WAAW,MAAA;AAG9B,0BAAM,IAAIP,MAAM,sCAAsCrV,EAAAA,8BAAAA;AAAAA,mBAFpD6C,IAAG8S,IAAG/C,IAAGD,EAAAA,IAAK3S,GAAU8V,UAAU,GAAG9V,GAAUvB,SAAS,CAAA,EAAGsX,MAAM,GAAA,EAAKC,IAAI3c,CAAAA,OAAK4c,WAAW5c,EAAAA,CAAAA;gBAAAA;AAK9F,yBAASkG,KAAI,GAAGA,KAAIR,IAAQQ;AAC1B,2BAASD,KAAI,GAAGA,KAAIR,IAAOQ;AACzBmW,oBAAAA,GAAUS,KAAuB,KAAjB3W,KAAIT,KAAQQ,GAAAA,IAAcuD,IAC1C4S,GAAUS,KAAuB,KAAjB3W,KAAIT,KAAQQ,MAAS,CAAA,IAAKqW,IAC1CF,GAAUS,KAAuB,KAAjB3W,KAAIT,KAAQQ,MAAS,CAAA,IAAKsT,IAC1C6C,GAAUS,KAAuB,KAAjB3W,KAAIT,KAAQQ,MAAS,CAAA,IAAKsV,GAAerV,EAAAA,EAAGD,EAAAA,KAAU,MAAJqT;AAGtE6C,gBAAAA,GAAOW,aAAaV,IAAW,GAAG,CAAA,GAClCH,MAAU,GAAA9b,GAAAyD,cAAaoR,GAAI+H,cAAcb,IAAW,IAAA,CAAA,GACpDN,GAAWG,IAAIpV,IAAWsV,EAAAA;cAAAA;AAE5BjH,cAAAA,GAAIrO,YAAYsV,IAChBjH,GAAI7O,SAASU,IAASwU,IAAS3D,IAAiBC,EAAAA;YAClD,EAnGoB3C,IAAK2G,GAAmB9U,IAASwU,GAAS3D,GAAiBC,CAAAA,GAAAA;AAI7E,gBAAMqF,IAAuB/c,GAAA4a,sBAAsBO,EAAAA;AACnD,cAAI4B;AAEF,mBAsIJ,SACEhI,IACAuG,IACA1U,IACAwU,IACA3D,IACAC,IACAG,IAAAA;AAEA9C,cAAAA,GAAItO,cAAcsO,GAAIrO;AACtB,yBAAK,CAAO6D,IAAYyS,EAAAA,KAAiBC,OAAOC,QAAQ5B,EAAAA,GAAiB;AAGvE,oBAAI6B;AAFJpI,gBAAAA,GAAIvO,UAAAA,GACJuO,GAAIpO,YAAYkR,KAAmBuF,OAAOb,SAAShS,EAAAA,GAKjD4S,KAH0B,cAAA,OAAjBH,KAGYA,GAFV,MACA,OAAMtF,KAAmBD,EAAAA,IAGfuF;AAEvB,2BAAWK,MAAeF,GAAmBV,MAAM,GAAA,GAAM;AACvD,wBAAMzB,KAAOqC,GAAY,CAAA,GACnBC,KAAIC,EAA0BvC,EAAAA;AACpC,sBAAA,CAAKsC,IAAG;AACNE,4BAAQC,MAAM,4CAA4CzC,EAAAA,GAAAA;AAC1D;kBAAA;AAEF,wBAAM0C,KAAiBL,GAAYb,UAAU,CAAA,EAAGC,MAAM,GAAA;AACjDiB,kBAAAA,GAAK,CAAA,KAAOA,GAAK,CAAA,KAGtBJ,GAAEvI,IAAK4I,EAAcD,IAAMjG,IAAiBC,IAAkB9Q,IAASwU,IAAAA,MAAevD,EAAAA,CAAAA;gBAAAA;AAExF9C,gBAAAA,GAAI1N,OAAAA,GACJ0N,GAAItN,UAAAA;cAAAA;YAER,EA5KuBsN,IAAKgI,GAAsBnW,IAASwU,GAAS3D,GAAiBC,GAAkBG,CAAAA,GAAAA;AAIrG,gBAAM+F,IAAsB5d,GAAA+a,qBAAqBI,EAAAA;AACjD,iBAAA,CAAA,CAAIyC,MAyKN,SACE7I,IACAuG,IACA1U,IACAwU,IACA3D,IACAC,IACApP,IACAuP,IAAAA;AAAAA,gBAAAA,IAAAA;AAGA,kBAAMgG,KAAa,IAAIC;AACvBD,YAAAA,GAAW5T,KAAKrD,IAASwU,IAAS3D,IAAiBC,EAAAA,GACnD3C,GAAI5K,KAAK0T,EAAAA,GAET9I,GAAIvO,UAAAA;AAEJ,kBAAMuX,KAAezV,KAAW;AAChCyM,YAAAA,GAAIpO,YAAYkR,KAAmBkG;AACnC,uBAAWV,MAAe/B,GAAerF,EAAEwG,MAAM,GAAA,GAAM;AACrD,oBAAMzB,KAAOqC,GAAY,CAAA,GACnBC,KAAIC,EAA0BvC,EAAAA;AACpC,kBAAA,CAAKsC,IAAG;AACNE,wBAAQC,MAAM,4CAA4CzC,EAAAA,GAAAA;AAC1D;cAAA;AAEF,oBAAM0C,KAAiBL,GAAYb,UAAU,CAAA,EAAGC,MAAM,GAAA;AACjDiB,cAAAA,GAAK,CAAA,KAAOA,GAAK,CAAA,KAGtBJ,GAAEvI,IAAK4I,EACLD,IACAjG,IACAC,IACA9Q,IACAwU,IAAAA,OAEAvD,KAC2B,UAA1BsD,KAAAG,GAAeJ,gBAAAA,WAAWC,KAAAA,KAAI,MAAM4C,KAAe,KACxB,UAA3B9H,KAAAqF,GAAeL,iBAAAA,WAAYhF,KAAAA,KAAI,MAAM8H,KAAe,EAAA,CAAA;YAAA;AAG7B,kBAAxBzC,GAAeN,QACjBjG,GAAItO,cAAcsO,GAAIrO,WACtBqO,GAAI1N,OAAAA,KAEJ0N,GAAIiJ,KAAAA,GAENjJ,GAAItN,UAAAA;UACN,EAzNsBsN,IAAK6I,GAAqBhX,IAASwU,GAAS3D,GAAiBC,GAAkBpP,GAAUuP,CAAAA,GAAAA;QAK/G;AAuBA,cAAM+D,IAAoF,oBAAIC;AA+L9F,iBAASoC,EAAMjZ,IAAe2N,IAAa1C,KAAc,GAAA;AACvD,iBAAO7J,KAAKuM,IAAIvM,KAAK6J,IAAIjL,IAAO2N,EAAAA,GAAM1C,EAAAA;QACxC;AAEA,cAAMsN,IAAsD,EAC1D,GAAK,CAACxI,IAA+B2I,OAAmB3I,GAAI3N,cAAcsW,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,GAC5H,GAAK,CAAC3I,IAA+B2I,OAAmB3I,GAAIvN,OAAOkW,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,GACjF,GAAK,CAAC3I,IAA+B2I,OAAmB3I,GAAI5N,OAAOuW,GAAK,CAAA,GAAIA,GAAK,CAAA,CAAA,EAAA;AAGnF,iBAASC,EAAcD,IAAgBQ,IAAmBC,IAAoBvX,IAAiBwU,IAAiBgD,IAAkBvG,IAA0BqD,KAAsB,GAAGD,IAAuB,GAAA;AAC1M,gBAAMnS,IAAS4U,GAAKhB,IAAI3c,CAAAA,OAAK4c,WAAW5c,EAAAA,KAAMwc,SAASxc,EAAAA,CAAAA;AAEvD,cAAI+I,EAAO3D,SAAS;AAClB,kBAAM,IAAI4W,MAAM,mCAAA;AAGlB,mBAAS/V,KAAI,GAAGA,KAAI8C,EAAO3D,QAAQa,MAAK;AAEtC8C,cAAO9C,EAAAA,KAAMkY,KAAahD,KAAcrD,KAAqBoD,IAAepD,IAGxEuG,MAAyB,MAAdtV,EAAO9C,EAAAA,MACpB8C,EAAO9C,EAAAA,IAAKiY,EAAM7X,KAAKwH,MAAM9E,EAAO9C,EAAAA,IAAK,GAAA,IAAO,KAAKkY,IAAW,CAAA,IAGlEpV,EAAO9C,EAAAA,KAAMY,KAAWsU,KAAcrD;AAGxC,mBAAS5R,KAAI,GAAGA,KAAI6C,EAAO3D,QAAQc,MAAK;AAEtC6C,cAAO7C,EAAAA,KAAMkY,IAGTC,MAAyB,MAAdtV,EAAO7C,EAAAA,MACpB6C,EAAO7C,EAAAA,IAAKgY,EAAM7X,KAAKwH,MAAM9E,EAAO7C,EAAAA,IAAK,GAAA,IAAO,KAAKkY,IAAY,CAAA,IAGnErV,EAAO7C,EAAAA,KAAMmV;AAGf,iBAAOtS;QACT;MAAA,GAAA,IAAA,CAAA/I,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,+BAAA;ACzqBA,cAAAE,KAAAD,GAAA,GAAA;AAGA,QAAAD,GAAA,+BAAA,SAA6Cqe,IAAsBC,IAA0CrK,IAAAA;AAK3G,cAAIsK,IAAuC,IAAID,GAAaE,eAAgBtB,CAAAA,OAAAA;AAC1E,kBAAMlF,KAAQkF,GAAQuB,KAAMzG,CAAAA,OAAUA,GAAM0G,WAAWL,EAAAA;AACvD,gBAAA,CAAKrG;AACH;AAIF,gBAAA,EAAM,+BAA+BA;AAGnC,qBAFAuG,QAAAA,KAAAA,EAAUI,WAAAA,GAAAA,MACVJ,IAAAA;AAKF,kBAAM/Y,IAAQwS,GAAM4G,0BAA0B,CAAA,EAAGC,YAC3CpZ,IAASuS,GAAM4G,0BAA0B,CAAA,EAAGE;AAC9CtZ,gBAAQ,KAAKC,IAAS,KACxBwO,GAASzO,GAAOC,CAAAA;UAAAA,CAAAA;AAGpB,cAAA;AACE8Y,cAASQ,QAAQV,IAAS,EAAE9C,KAAK,CAAC,0BAAA,EAAA,CAAA;UAAA,SAClCxb,IAAA;AACAwe,cAASI,WAAAA,GACTJ,IAAAA;UAAWjN;AAEb,kBAAO,GAAApR,GAAAsD,cAAa,MAAM+a,QAAAA,IAAAA,SAAAA,EAAUI,WAAAA,CAAAA;QACtC;MAAA,GAAA,KAAA,CAAA5e,IAAAC,OAAA;AC1BA,iBAAgBgf,GAAiBC,IAAAA;AAI/B,iBAAO,SAAUA,MAAaA,MAAa;QAC7C;AAAA,eAAA,eAAAjf,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,yBAAAA,GAAA,kCAAAA,GAAA,6BAAAA,GAAA,mBAAAA,GAAA,eAAA,QAZAA,GAAA,eAAA,SAAgCgF,IAAAA;AAC9B,cAAA,CAAKA;AACH,kBAAM,IAAI+W,MAAM,yBAAA;AAElB,iBAAO/W;QACT,GAEAhF,GAAA,mBAAAC,IAOAD,GAAA,6BAAA,SAA2Cif,IAAAA;AACzC,iBAAO,SAAUA,MAAaA,MAAa;QAC7C,GAMAjf,GAAA,kCAAA,SAAgDif,IAAAA;AAC9C,iBAAOD,GAAiBC,EAAAA,KAL1B,SAA2BA,IAAAA;AACzB,mBAAO,QAAUA,MAAaA,MAAa;UAC7C,EAG0DA,EAAAA;QAC1D,GAEAjf,GAAA,yBAAA,WAAA;AACE,iBAAO,EACL6F,KAAK,EACHxF,QAiBG,EACLmF,OAAO,GACPC,QAAQ,EAAA,GAlBNF,MAgBG,EACLC,OAAO,GACPC,QAAQ,EAAA,EAAA,GAhBRH,QAAQ,EACNjF,QAaG,EACLmF,OAAO,GACPC,QAAQ,EAAA,GAdNF,MAYG,EACLC,OAAO,GACPC,QAAQ,EAAA,GAbNC,MAAM,EACJF,OAAO,GACPC,QAAQ,GACRE,MAAM,GACNC,KAAK,EAAA,EAAA,EAAA;QAIb;MAAA,GAAA,KAAA,CAAA7F,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,6BAAA;QC1CA,MAAMkf,GAAAA;UAYJ,cAAAC;AACE7e,iBAAKiN,MAAAA;UACP;UAEO,QAAAA;AACLjN,iBAAK8e,eAAAA,OACL9e,KAAKiD,mBAAAA,OACLjD,KAAKkS,mBAAmB,GACxBlS,KAAKmS,iBAAiB,GACtBnS,KAAKoS,yBAAyB,GAC9BpS,KAAKsS,uBAAuB,GAC5BtS,KAAKyS,WAAW,GAChBzS,KAAK4S,SAAS,GACd5S,KAAK+C,iBAAAA,QACL/C,KAAKgD,eAAAA;UACP;UAEO,OAAOyK,IAAoB3J,IAAqCC,IAAmCd,KAAAA,OAA4B;AAIpI,gBAHAjD,KAAK+C,iBAAiBe,IACtB9D,KAAKgD,eAAee,IAAAA,CAEfD,MAAAA,CAAUC,MAAQD,GAAM,CAAA,MAAOC,GAAI,CAAA,KAAMD,GAAM,CAAA,MAAOC,GAAI,CAAA;AAE7D,qBAAA,KADA/D,KAAKiN,MAAAA;AAKP,kBAAMiF,IAAmBpO,GAAM,CAAA,IAAK2J,GAASrF,OAAO2W,OAAOC,WACrD7M,IAAiBpO,GAAI,CAAA,IAAK0J,GAASrF,OAAO2W,OAAOC,WACjD5M,IAAyBtM,KAAKuM,IAAIH,GAAkB,CAAA,GACpDI,IAAuBxM,KAAK6J,IAAIwC,GAAgB1E,GAASpJ,OAAO,CAAA;AAGlE+N,iBAA0B3E,GAASpJ,QAAQiO,IAAuB,IACpEtS,KAAKiN,MAAAA,KAIPjN,KAAK8e,eAAAA,MACL9e,KAAKiD,mBAAmBA,IACxBjD,KAAKkS,mBAAmBA,GACxBlS,KAAKmS,iBAAiBA,GACtBnS,KAAKoS,yBAAyBA,GAC9BpS,KAAKsS,uBAAuBA,GAC5BtS,KAAKyS,WAAW3O,GAAM,CAAA,GACtB9D,KAAK4S,SAAS7O,GAAI,CAAA;UACpB;UAEO,eAAe0J,IAAoB/H,IAAWC,IAAAA;AACnD,mBAAA,CAAA,CAAK3F,KAAK8e,iBAGVnZ,MAAK8H,GAASrF,OAAO2W,OAAOC,WACxBhf,KAAKiD,mBACHjD,KAAKyS,YAAYzS,KAAK4S,SACjBlN,MAAK1F,KAAKyS,YAAY9M,MAAK3F,KAAKoS,0BACrC1M,KAAI1F,KAAK4S,UAAUjN,MAAK3F,KAAKsS,uBAE1B5M,KAAI1F,KAAKyS,YAAY9M,MAAK3F,KAAKoS,0BACpC1M,MAAK1F,KAAK4S,UAAUjN,MAAK3F,KAAKsS,uBAE1B3M,KAAI3F,KAAKkS,oBAAoBvM,KAAI3F,KAAKmS,kBAC3CnS,KAAKkS,qBAAqBlS,KAAKmS,kBAAkBxM,OAAM3F,KAAKkS,oBAAoBxM,MAAK1F,KAAKyS,YAAY/M,KAAI1F,KAAK4S,UAC/G5S,KAAKkS,mBAAmBlS,KAAKmS,kBAAkBxM,OAAM3F,KAAKmS,kBAAkBzM,KAAI1F,KAAK4S,UACrF5S,KAAKkS,mBAAmBlS,KAAKmS,kBAAkBxM,OAAM3F,KAAKkS,oBAAoBxM,MAAK1F,KAAKyS;UAC7F;QAAA;AAGF,QAAA/S,GAAA,6BAAA,WAAA;AACE,iBAAO,IAAIkf;QACb;MAAA,GAAA,KAAA,SAAAnf,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAqJ,IAAAI,KAAA,UAAA,QAAAG,KAAAH,KAAA,IAAA3J,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAA4J,KAAA,QAAA,SAAA/J,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAmZ,KAAAtZ,GAAA,SAAA,GAAAsZ,MAAA,GAAAA;AAAA,eAAA9P,KAAAxJ,GAAAsZ,EAAA,OAAAvP,MAAAH,KAAA,IAAAJ,GAAAO,EAAA,IAAAH,KAAA,IAAAJ,GAAAvJ,IAAAC,IAAA6J,EAAA,IAAAP,GAAAvJ,IAAAC,EAAA,MAAA6J;AAAA,iBAAAH,KAAA,KAAAG,MAAA,OAAA,eAAA9J,IAAAC,IAAA6J,EAAA,GAAAA;QAAA;AAAA,eAAA,eAAA9J,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,eAAA;ACpFA,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAEA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GAMMsf,IAA0C,EAC9CjW,aAAa,GACbS,iBAAiB,EAAE/D,GAAG,GAAGC,GAAG,EAAA,GAC5BuZ,0BAA0B,EAAExZ,GAAG,GAAGC,GAAG,EAAA,GACrC+D,QAAQ,EAAEhE,GAAG,GAAGC,GAAG,EAAA,GACnBoD,MAAM,EAAErD,GAAG,GAAGC,GAAG,EAAA,GACjBwZ,eAAe,EAAEzZ,GAAG,GAAGC,GAAG,EAAA,EAAA;AA0B5B,YAAIyZ;QAEJ,MAAa/G,EAAAA;UAQX,IAAA,QAAWjY;AAA4D,mBAAOJ,KAAKqf;UAAQ;UAsB3F,YACmBC,IACAC,IACAC,IAAAA;AAFA,iBAAAF,YAAAA,IACA,KAAAC,UAAAA,IACA,KAAAC,kBAAAA,IAhCX,KAAAC,aAAAA,OAEA,KAAAC,YAA0E,IAAI,EAAAC,cAC9E,KAAAC,oBAAkF,IAAI,EAAAD,cAGtF,KAAAN,SAAsB,CAAA,GAItB,KAAAQ,eAA4B,CAAA,GAM5B,KAAAC,mBAAiC,EAAExa,KAAK,GAAGD,MAAM,GAAG0a,QAAQ,GAAGC,OAAO,EAAA,GACtE,KAAAC,qBAAoC,IAAI,EAAA/K,iBAExC,KAAAgL,eAAuB,KAKd,KAAAte,2BAA2B,IAAI,EAAAC,gBAChC,KAAAC,0BAA0B9B,KAAK4B,yBAAyBG,OACvD,KAAAoe,8BAA8B,IAAI,EAAAte,gBACnC,KAAAue,6BAA6BpgB,KAAKmgB,4BAA4Bpe,OA8CtE,KAAAse,qBAAAA,OAvCNrgB,KAAKsgB,eAAAA,GACLtgB,KAAKugB,aAAaC,EAChBlB,IAC+B,IAA/Btf,KAAKuf,QAAQpI,kBAAsBsJ,GACnCzgB,KAAKuf,QAAQnI,mBAAmBqJ,CAAAA,GAElCzgB,KAAK0gB,WAAU,GAAA,EAAArd,cAAarD,KAAKugB,WAAWjd,WAAW,MAAM,EAC3DC,OAAOvD,KAAKuf,QAAQhU,mBACpBoV,oBAAAA,KAAoB,CAAA,CAAA;UAExB;UAEO,UAAApU;AACL,uBAAWqU,MAAQ5gB,KAAKI;AACtBwgB,cAAAA,GAAK7gB,OAAOoD,OAAAA;AAEdnD,iBAAK4B,yBAAyB2K,QAAAA;UAChC;UAEO,SAAA3H;AACA5E,iBAAKyf,eACRzf,KAAK6gB,UAAAA,GACL7gB,KAAKyf,aAAAA;UAET;UAEQ,YAAAoB;AAEN,kBAAMC,KAAQ,IAAI,EAAAC;AAClB,qBAASphB,KAAI,IAAIA,KAAI,KAAKA;AACxBmhB,cAAAA,GAAME,QAAQ,MAAA;AACZ,oBAAA,CAAKhhB,KAAK0f,UAAU7P,IAAIlQ,IAAG,EAAAshB,eAAe,EAAAA,eAAe,EAAAC,WAAAA,GAAc;AACrE,wBAAMC,KAAkBnhB,KAAKohB,aAAazhB,IAAG,EAAAshB,eAAe,EAAAA,eAAe,EAAAC,WAAAA;AAC3ElhB,uBAAK0f,UAAUlE,IAAI7b,IAAG,EAAAshB,eAAe,EAAAA,eAAe,EAAAC,aAAaC,EAAAA;gBAAAA;cAAAA,CAAAA;UAIzE;UAGO,aAAApL;AACL,mBAAO/V,KAAKqgB;UACd;UAEO,eAAA5a;AACL,gBAAoC,MAAhCzF,KAAKqf,OAAO,CAAA,EAAGgC,WAAW3b,KAA2C,MAAhC1F,KAAKqf,OAAO,CAAA,EAAGgC,WAAW1b,GAAnE;AAGA,yBAAWib,MAAQ5gB,KAAKqf;AACtBuB,gBAAAA,GAAK3T,MAAAA;AAEPjN,mBAAK0f,UAAUzS,MAAAA,GACfjN,KAAK4f,kBAAkB3S,MAAAA,GACvBjN,KAAKyf,aAAAA;YAAa;UACpB;UAEQ,iBAAAa;AAKN,gBAAIjI,EAAaiJ,iBAAiBthB,KAAKqf,OAAOxa,UAAUiB,KAAKuM,IAAI,GAAGgG,EAAaiJ,aAAAA,GAAgB;AAG/F,oBAAMC,KAAcvhB,KAAKqf,OAAOmC,OAAO/hB,CAAAA,OACb,IAAjBA,GAAEM,OAAOmF,UAAcmT,EAAaoJ,kBAAkB,KAAA,EAC5DC,KAAK,CAAC3I,IAAGC,OACNA,GAAEjZ,OAAOmF,UAAU6T,GAAEhZ,OAAOmF,QACvB8T,GAAEjZ,OAAOmF,QAAQ6T,GAAEhZ,OAAOmF,QAE5B8T,GAAE2I,iBAAiB5I,GAAE4I,cAAAA;AAE9B,kBAAIC,KAAAA,IACA7Y,KAAO;AACX,uBAASpJ,KAAI,GAAGA,KAAI4hB,GAAY1c,QAAQlF;AACtC,oBAAI4hB,GAAY5hB,EAAAA,EAAGI,OAAOmF,UAAU6D;AAClC6Y,kBAAAA,KAAYjiB,IACZoJ,KAAOwY,GAAY5hB,EAAAA,EAAGI,OAAOmF;yBACpBvF,KAAIiiB,MAAc;AAC3B;AAKJ,oBAAMC,KAAeN,GAAY7I,MAAMkJ,IAAWA,KAAY,CAAA,GACxDE,KAA4BD,GAAazF,IAAI3c,CAAAA,OAAKA,GAAEsiB,OAAO,CAAA,EAAG/Y,WAAAA,EAAa0Y,KAAK,CAAC3I,IAAGC,OAAMD,KAAIC,KAAI,IAAA,EAAK,GACvGgJ,KAAkBhiB,KAAKI,MAAMyE,SAASgd,GAAahd,QAGnDod,KAAajiB,KAAKkiB,YAAYL,IAAcG,EAAAA;AAClDC,cAAAA,GAAW7Y;AAGX,uBAASzJ,KAAImiB,GAA0Bjd,SAAS,GAAGlF,MAAK,GAAGA;AACzDK,qBAAKmiB,YAAYL,GAA0BniB,EAAAA,CAAAA;AAI7CK,mBAAKI,MAAM2Q,KAAKkR,EAAAA,GAGhBjiB,KAAKqgB,qBAAAA,MACLrgB,KAAK4B,yBAAyBoL,KAAKiV,GAAWliB,MAAAA;YAAAA;AAIhD,kBAAMqiB,KAAU,IAAIC,EAAUriB,KAAKsf,WAAWtf,KAAKkgB,YAAAA;AAInD,mBAHAlgB,KAAKqf,OAAOtO,KAAKqR,EAAAA,GACjBpiB,KAAK6f,aAAa9O,KAAKqR,EAAAA,GACvBpiB,KAAK4B,yBAAyBoL,KAAKoV,GAAQriB,MAAAA,GACpCqiB;UACT;UAEQ,YAAYP,IAA2BG,IAAAA;AAC7C,kBAAMM,KAA4C,IAA/BT,GAAa,CAAA,EAAG9hB,OAAOmF,OACpC+c,KAAa,IAAII,EAAUriB,KAAKsf,WAAWgD,IAAYT,EAAAA;AAC7D,uBAAK,CAAOliB,IAAG4iB,EAAAA,KAAMV,GAAajF,QAAAA,GAAW;AAC3C,oBAAMtW,KAAU3G,KAAI4iB,GAAExiB,OAAOmF,QAAQod,IAC/BxH,KAAUhV,KAAKsH,MAAMzN,KAAI,CAAA,IAAK4iB,GAAExiB,OAAOoF;AAC7C8c,cAAAA,GAAWxN,IAAIlL,UAAUgZ,GAAExiB,QAAQuG,IAASwU,EAAAA;AAC5C,yBAAWiB,MAAKwG,GAAER;AAChBhG,gBAAAA,GAAE/S,cAAcgZ,IAChBjG,GAAEoD,cAAczZ,IAAIqW,GAAEhT,KAAKrD,IAAI4c,IAC/BvG,GAAEoD,cAAcxZ,IAAIoW,GAAEhT,KAAKpD,IAAI2c,IAC/BvG,GAAEtS,gBAAgB/D,KAAKY,IACvByV,GAAEtS,gBAAgB9D,KAAKmV,IACvBiB,GAAEmD,yBAAyBxZ,IAAIqW,GAAEtS,gBAAgB/D,IAAI4c,IACrDvG,GAAEmD,yBAAyBvZ,IAAIoW,GAAEtS,gBAAgB9D,IAAI2c;AAGvDtiB,mBAAKmgB,4BAA4BnT,KAAKuV,GAAExiB,MAAAA;AAGxC,oBAAMwY,KAAQvY,KAAK6f,aAAahI,QAAQ0K,EAAAA;AAAAA,qBACpChK,MACFvY,KAAK6f,aAAa5H,OAAOM,IAAO,CAAA;YAAA;AAGpC,mBAAO0J;UACT;UAEQ,YAAYO,IAAAA;AAClBxiB,iBAAKqf,OAAOpH,OAAOuK,IAAW,CAAA;AAC9B,qBAASC,KAAID,IAAWC,KAAIziB,KAAKqf,OAAOxa,QAAQ4d,MAAK;AACnD,oBAAMC,KAAgB1iB,KAAKqf,OAAOoD,EAAAA;AAClC,yBAAW1G,MAAK2G,GAAcX;AAC5BhG,gBAAAA,GAAE/S;AAEJ0Z,cAAAA,GAActZ;YAAAA;UAElB;UAEO,+BAA+BlB,IAAeO,IAAYC,IAAYC,IAAaga,IAAAA;AACxF,mBAAO3iB,KAAK4iB,iBAAiB5iB,KAAK4f,mBAAmB1X,IAAOO,IAAIC,IAAIC,IAAKga,EAAAA;UAC3E;UAEO,mBAAmBE,IAAcpa,IAAYC,IAAYC,IAAaga,IAAAA;AAC3E,mBAAO3iB,KAAK4iB,iBAAiB5iB,KAAK0f,WAAWmD,IAAMpa,IAAIC,IAAIC,IAAKga,EAAAA;UAClE;UAKQ,iBACNG,IACAC,IACAta,IACAC,IACAC,IACAga,KAAAA,OAAgC;AAOhC,mBALAvD,IAAS0D,GAASjT,IAAIkT,IAAKta,IAAIC,IAAIC,EAAAA,GAC9ByW,MACHA,IAASpf,KAAKohB,aAAa2B,IAAKta,IAAIC,IAAIC,IAAKga,EAAAA,GAC7CG,GAAStH,IAAIuH,IAAKta,IAAIC,IAAIC,IAAKyW,CAAAA,IAE1BA;UACT;UAEQ,uBAAuB4D,IAAAA;AAC7B,gBAAIA,MAAOhjB,KAAKuf,QAAQ5c,OAAOkP,KAAKhN;AAClC,oBAAM,IAAI4W,MAAM,4BAA4BuH,EAAAA;AAE9C,mBAAOhjB,KAAKuf,QAAQ5c,OAAOkP,KAAKmR,EAAAA;UAClC;UAEQ,oBAAoBC,IAAqBC,IAAiBC,IAAkBpe,IAAAA;AAClF,gBAAI/E,KAAKuf,QAAQhU;AAIf,qBAAO,EAAAkN;AAGT,gBAAIjQ;AACJ,oBAAQya,IAAAA;cACN,KAAK;cACL,KAAK;AACHza,gBAAAA,KAASxI,KAAKojB,uBAAuBF,EAAAA;AACrC;cACF,KAAK;AACH,sBAAMG,KAAM,EAAAnO,cAAcC,WAAW+N,EAAAA;AAErC1a,gBAAAA,KAAS,EAAAqO,KAAKyM,QAAQD,GAAI,CAAA,GAAIA,GAAI,CAAA,GAAIA,GAAI,CAAA,CAAA;AAC1C;cAEF;AAEI7a,gBAAAA,KADE2a,KACO,EAAAI,MAAMC,OAAOxjB,KAAKuf,QAAQ5c,OAAOmP,UAAAA,IAEjC9R,KAAKuf,QAAQ5c,OAAO2E;YAAAA;AAKnC,mBAAOkB;UACT;UAEQ,oBAAoBC,IAAYwa,IAAqBC,IAAiBxa,IAAY+a,IAAqBC,IAAiBP,IAAkBpe,IAAc4e,IAAeC,IAAAA;AAC7K,kBAAMC,KAAuB7jB,KAAK8jB,yBAAyBrb,IAAIwa,IAAaC,IAASxa,IAAI+a,IAAaC,IAAAA,OAAgBC,IAAM5e,IAAK6e,EAAAA;AACjI,gBAAIC;AACF,qBAAOA;AAGT,gBAAIrb;AACJ,oBAAQib,IAAAA;cACN,KAAK;cACL,KAAK;AACCzjB,qBAAKuf,QAAQ1G,8BAA8B8K,MAAQD,KAAU,MAC/DA,MAAW,IAEblb,KAASxI,KAAKojB,uBAAuBM,EAAAA;AACrC;cACF,KAAK;AACH,sBAAML,KAAM,EAAAnO,cAAcC,WAAWuO,EAAAA;AACrClb,gBAAAA,KAAS,EAAAqO,KAAKyM,QAAQD,GAAI,CAAA,GAAIA,GAAI,CAAA,GAAIA,GAAI,CAAA,CAAA;AAC1C;cAEF;AAEI7a,gBAAAA,KADE2a,KACOnjB,KAAKuf,QAAQ5c,OAAO2E,aAEpBtH,KAAKuf,QAAQ5c,OAAOmP;YAAAA;AAcnC,mBATI9R,KAAKuf,QAAQhU,sBACf/C,KAAS,EAAA+a,MAAMC,OAAOhb,EAAAA,IAIpBzD,OACFyD,KAAS,EAAA+a,MAAMQ,gBAAgBvb,IAAQ,EAAA0Q,WAAAA,IAGlC1Q;UACT;UAEQ,uBAAuBya,IAAqBC,IAAiBC,IAAAA;AACnE,oBAAQF,IAAAA;cACN,KAAK;cACL,KAAK;AACH,uBAAOjjB,KAAKojB,uBAAuBF,EAAAA,EAASrM;cAC9C,KAAK;AACH,uBAAOqM,MAAW;cAEpB;AACE,uBAAIC,KACKnjB,KAAKuf,QAAQ5c,OAAOmP,WAAW+E,OAEjC7W,KAAKuf,QAAQ5c,OAAO2E,WAAWuP;YAAAA;UAE5C;UAEQ,uBAAuB4M,IAAqBC,IAAiBP,IAAkBQ,IAAAA;AACrF,oBAAQF,IAAAA;cACN,KAAK;cACL,KAAK;AAIH,uBAHIzjB,KAAKuf,QAAQ1G,8BAA8B8K,MAAQD,KAAU,MAC/DA,MAAW,IAEN1jB,KAAKojB,uBAAuBM,EAAAA,EAAS7M;cAC9C,KAAK;AACH,uBAAO6M,MAAW;cAEpB;AACE,uBAAIP,KACKnjB,KAAKuf,QAAQ5c,OAAO2E,WAAWuP,OAEjC7W,KAAKuf,QAAQ5c,OAAOmP,WAAW+E;YAAAA;UAE5C;UAEQ,yBAAyBpO,IAAYwa,IAAqBC,IAAiBxa,IAAY+a,IAAqBC,IAAiBP,IAAkBQ,IAAe5e,IAAc6e,IAAAA;AAClL,gBAA0C,MAAtC5jB,KAAKuf,QAAQzG,wBAA8B8K;AAC7C;AAIF,kBAAM9S,KAAQ9Q,KAAKgkB,kBAAkBjf,EAAAA,GAC/Bkf,KAAgBnT,GAAMoT,SAASzb,IAAIC,EAAAA;AACzC,gBAAA,WAAIub;AACF,qBAAOA,MAAAA;AAGT,kBAAME,KAASnkB,KAAKokB,uBAAuBnB,IAAaC,IAASC,EAAAA,GAC3DkB,KAASrkB,KAAKskB,uBAAuBb,IAAaC,IAASP,IAASQ,EAAAA,GAGpEnb,KAAS,EAAAqO,KAAK0N,oBAAoBJ,IAAQE,IAAQrkB,KAAKuf,QAAQzG,wBAAwB/T,KAAM,IAAI,EAAA;AAEvG,gBAAA,CAAKyD;AAEH,qBAAA,KADAsI,GAAM0T,SAAS/b,IAAIC,IAAI,IAAA;AAIzB,kBAAM6a,KAAQ,EAAA1M,KAAKyM,QAChB9a,MAAU,KAAM,KAChBA,MAAU,KAAM,KAChBA,MAAU,IAAK,GAAA;AAIlB,mBAFAsI,GAAM0T,SAAS/b,IAAIC,IAAI6a,EAAAA,GAEhBA;UACT;UAEQ,kBAAkBxe,IAAAA;AACxB,mBAAIA,KACK/E,KAAKuf,QAAQ5c,OAAOiW,oBAEtB5Y,KAAKuf,QAAQ5c,OAAOgW;UAC7B;UAGQ,aAAa8L,IAA8Bhc,IAAYC,IAAYC,IAAaga,KAAAA,OAAgC;AACtH,kBAAMza,KAA+B,YAAA,OAAhBuc,KAA2BC,OAAOC,aAAaF,EAAAA,IAAeA,IAQ7EG,KAAe9e,KAAK6J,IAAI3P,KAAKuf,QAAQpI,kBAAkBrR,KAAKuM,IAAInK,GAAMrD,QAAQ,CAAA,IAAK4b,GAA8BzgB,KAAKkgB,YAAAA;AACxHlgB,iBAAKugB,WAAWrb,QAAQ0f,OAC1B5kB,KAAKugB,WAAWrb,QAAQ0f;AAG1B,kBAAMC,KAAgB/e,KAAK6J,IAAI3P,KAAKuf,QAAQnI,mBAAmBqJ,GAA8BzgB,KAAKkgB,YAAAA;AAWlG,gBAVIlgB,KAAKugB,WAAWpb,SAAS0f,OAC3B7kB,KAAKugB,WAAWpb,SAAS0f,KAE3B7kB,KAAK0gB,QAAQza,KAAAA,GAEbjG,KAAKigB,mBAAmBvX,KAAKA,IAC7B1I,KAAKigB,mBAAmBxX,KAAKA,IAC7BzI,KAAKigB,mBAAmBrJ,SAASjO,MAAMA,IAEnB3I,KAAKigB,mBAAmB6E,YAAAA;AAE1C,qBAAO7F;AAGT,kBAAM0E,KAAAA,CAAAA,CAAS3jB,KAAKigB,mBAAmBnW,OAAAA,GACjCqZ,KAAAA,CAAAA,CAAYnjB,KAAKigB,mBAAmBlL,UAAAA,GACpChQ,KAAAA,CAAAA,CAAQ/E,KAAKigB,mBAAmB8E,MAAAA,GAChCC,KAAAA,CAAAA,CAAWhlB,KAAKigB,mBAAmBlW,SAAAA,GACnCkb,KAAAA,CAAAA,CAAcjlB,KAAKigB,mBAAmBiF,YAAAA,GACtCC,IAAAA,CAAAA,CAAkBnlB,KAAKigB,mBAAmBmF,gBAAAA,GAC1CC,IAAAA,CAAAA,CAAarlB,KAAKigB,mBAAmBqF,WAAAA;AAC3C,gBAAI5B,IAAU1jB,KAAKigB,mBAAmB7K,WAAAA,GAClCqO,IAAczjB,KAAKigB,mBAAmBsF,eAAAA,GACtCrC,IAAUljB,KAAKigB,mBAAmB1K,WAAAA,GAClC0N,IAAcjjB,KAAKigB,mBAAmBuF,eAAAA;AAC1C,gBAAIrC,IAAS;AACX,oBAAMsC,KAAO/B;AACbA,kBAAUR,GACVA,IAAUuC;AACV,oBAAMC,KAAQjC;AACdA,kBAAcR,GACdA,IAAcyC;YAAAA;AAIhB,kBAAMC,IAAkB3lB,KAAK4lB,oBAAoB3C,GAAaC,GAASC,IAASpe,EAAAA;AAGhF/E,iBAAK0gB,QAAQmF,2BAA2B,QACxC7lB,KAAK0gB,QAAQta,YAAYuf,EAAgBpgB,KACzCvF,KAAK0gB,QAAQ9a,SAAS,GAAG,GAAG5F,KAAKugB,WAAWrb,OAAOlF,KAAKugB,WAAWpb,MAAAA,GACnEnF,KAAK0gB,QAAQmF,2BAA2B;AAGxC,kBAAM5b,IAAa0Z,KAAO3jB,KAAKuf,QAAQvV,iBAAiBhK,KAAKuf,QAAQtV,YAC/D6b,IAAYd,KAAS,WAAW;AACtChlB,iBAAK0gB,QAAQnZ,OACX,GAAGue,CAAAA,IAAa7b,CAAAA,IAAcjK,KAAKuf,QAAQvX,WAAWhI,KAAKuf,QAAQhI,gBAAAA,MAAsBvX,KAAKuf,QAAQrV,UAAAA,IACxGlK,KAAK0gB,QAAQjZ,eAAe,EAAAC;AAE5B,kBAAMqe,IAAkC,MAAjB7d,GAAMrD,WAAgB,GAAA,EAAA6Z,kBAAiBxW,GAAM8d,WAAW,CAAA,CAAA,GACzEC,IAA4C,MAAjB/d,GAAMrD,WAAgB,GAAA,EAAAqhB,4BAA2Bhe,GAAM8d,WAAW,CAAA,CAAA,GAC7FG,IAAkBnmB,KAAKomB,oBAAoB3d,IAAIwa,GAAaC,GAASxa,IAAI+a,GAAaC,GAASP,IAASpe,IAAK4e,KAAM,GAAA,EAAAC,iCAAgC1b,GAAM8d,WAAW,CAAA,CAAA,CAAA;AAC1KhmB,iBAAK0gB,QAAQta,YAAY+f,EAAgB5gB;AAGzC,kBAAM8gB,IAAUJ,IAA2B,IAAIxF;AAG/C,gBAAI6F,IAAAA;AAAc,sBACdtmB,KAAKuf,QAAQ1X,iBACfye,KAAc,GAAA,EAAAxe,mBAAkB9H,KAAK0gB,SAASxY,IAAOme,GAASA,GAASrmB,KAAKuf,QAAQpI,iBAAiBnX,KAAKuf,QAAQnI,kBAAkBpX,KAAKuf,QAAQvX,UAAUhI,KAAKuf,QAAQhI,gBAAAA;AAM1K,gBAEIgP,GAFAC,IAAAA,CAA6BT;AAUjC,gBANEQ,IADyB,YAAA,OAAhB9B,KACCzkB,KAAKwf,gBAAgBiH,QAAQhC,EAAAA,IAE7BzkB,KAAKwf,gBAAgBkH,mBAAmBjC,EAAAA,GAIhDQ,IAAW;AACbjlB,mBAAK0gB,QAAQza,KAAAA;AACb,oBAAMI,KAAYP,KAAKuM,IAAI,GAAGvM,KAAKsH,MAAMpN,KAAKuf,QAAQvX,WAAWhI,KAAKuf,QAAQhI,mBAAmB,EAAA,CAAA,GAE3FuD,KAAUzU,KAAY,KAAM,IAAI,MAAM;AAI5C,kBAHArG,KAAK0gB,QAAQra,YAAYA,IAGrBrG,KAAKigB,mBAAmB0G,wBAAAA;AAC1B3mB,qBAAK0gB,QAAQva,cAAcnG,KAAK0gB,QAAQta;uBAC/BpG,KAAKigB,mBAAmB2G,oBAAAA;AACjCJ,oBAAAA,OACAxmB,KAAK0gB,QAAQva,cAAc,OAAO,EAAA+O,cAAcC,WAAWnV,KAAKigB,mBAAmB4G,kBAAAA,CAAAA,EAAqBxR,KAAK,GAAA,CAAA;mBACxG;AACLmR,oBAAAA;AACA,oBAAI9d,KAAK1I,KAAKigB,mBAAmB4G,kBAAAA;AAC7B7mB,qBAAKuf,QAAQ1G,8BAA8B7Y,KAAKigB,mBAAmBnW,OAAAA,KAAYpB,KAAK,MACtFA,MAAM,IAER1I,KAAK0gB,QAAQva,cAAcnG,KAAKojB,uBAAuB1a,EAAAA,EAAInD;cAAAA;AAI7DvF,mBAAK0gB,QAAQxa,UAAAA;AACb,oBAAMK,KAAQ8f,GACRS,KAAOhhB,KAAKC,KAAKsgB,IAAUrmB,KAAKuf,QAAQjI,gBAAAA,IAAoBwD,MAAW6H,KAAmC,IAAZtc,KAAgB,IAC9GK,KAAOogB,KAAOzgB,IACd0gB,KAAOD,KAAmB,IAAZzgB;AAEpB,uBAAS1G,KAAI,GAAGA,KAAI4mB,GAAS5mB,MAAK;AAChCK,qBAAK0gB,QAAQza,KAAAA;AACb,sBAAM+gB,KAAUzgB,KAAQ5G,KAAIK,KAAKuf,QAAQpI,iBACnC8P,KAAW1gB,MAAS5G,KAAI,KAAKK,KAAKuf,QAAQpI,iBAC1C+P,KAASF,KAAUhnB,KAAKuf,QAAQpI,kBAAkB;AACxD,wBAAQnX,KAAKigB,mBAAmBrJ,SAASuQ,gBAAAA;kBACvC,KAAK;AACHnnB,yBAAK0gB,QAAQ7Z,OAAOmgB,IAASF,EAAAA,GAC7B9mB,KAAK0gB,QAAQxZ,OAAO+f,IAAUH,EAAAA,GAC9B9mB,KAAK0gB,QAAQ7Z,OAAOmgB,IAASD,EAAAA,GAC7B/mB,KAAK0gB,QAAQxZ,OAAO+f,IAAUF,EAAAA;AAC9B;kBACF,KAAK;AAGH,0BAAMK,KAAY/gB,MAAa,IAAI0gB,KAAOjhB,KAAKC,KAAKsgB,IAAUrmB,KAAKuf,QAAQjI,mBAAmBjR,KAAY,CAAA,IAAKyU,IACzGuM,KAAYhhB,MAAa,IAAIygB,KAAOhhB,KAAKC,KAAKsgB,IAAUrmB,KAAKuf,QAAQjI,mBAAmBjR,KAAY,CAAA,IAAKyU,IAIzGyC,KAAa,IAAIC;AACvBD,oBAAAA,GAAW5T,KAAKqd,IAASF,IAAM9mB,KAAKuf,QAAQpI,iBAAiB4P,KAAOD,EAAAA,GACpE9mB,KAAK0gB,QAAQ7W,KAAK0T,EAAAA,GAGlBvd,KAAK0gB,QAAQ7Z,OAAOmgB,KAAUhnB,KAAKuf,QAAQpI,kBAAkB,GAAGzQ,EAAAA,GAChE1G,KAAK0gB,QAAQ5Z,cACXkgB,KAAUhnB,KAAKuf,QAAQpI,kBAAkB,GAAGkQ,IAC5CL,IAASK,IACTL,IAAStgB,EAAAA,GAEX1G,KAAK0gB,QAAQ5Z,cACXkgB,IAASI,IACTF,IAAQE,IACRF,IAAQxgB,EAAAA,GAEV1G,KAAK0gB,QAAQ5Z,cACXogB,IAAQG,IACRJ,IAAUI,IACVJ,IAAUvgB,EAAAA,GAEZ1G,KAAK0gB,QAAQ5Z,cACXmgB,IAAUG,IACVH,KAAWjnB,KAAKuf,QAAQpI,kBAAkB,GAAGiQ,IAC7CH,KAAWjnB,KAAKuf,QAAQpI,kBAAkB,GAAGzQ,EAAAA;AAE/C;kBACF,KAAK;AACH1G,yBAAK0gB,QAAQzZ,YAAY,CAACnB,KAAKwH,MAAMjH,EAAAA,GAAYP,KAAKwH,MAAMjH,EAAAA,CAAAA,CAAAA,GAC5DrG,KAAK0gB,QAAQ7Z,OAAOmgB,IAASF,EAAAA,GAC7B9mB,KAAK0gB,QAAQxZ,OAAO+f,IAAUH,EAAAA;AAC9B;kBACF,KAAK;AACH9mB,yBAAK0gB,QAAQzZ,YAAY,CAAiC,IAAhCjH,KAAKuf,QAAQhI,kBAAsD,IAAhCvX,KAAKuf,QAAQhI,gBAAAA,CAAAA,GAC1EvX,KAAK0gB,QAAQ7Z,OAAOmgB,IAASF,EAAAA,GAC7B9mB,KAAK0gB,QAAQxZ,OAAO+f,IAAUH,EAAAA;AAC9B;kBAEF;AACE9mB,yBAAK0gB,QAAQ7Z,OAAOmgB,IAASF,EAAAA,GAC7B9mB,KAAK0gB,QAAQxZ,OAAO+f,IAAUH,EAAAA;gBAAAA;AAGlC9mB,qBAAK0gB,QAAQ3Z,OAAAA,GACb/G,KAAK0gB,QAAQ1Z,QAAAA;cAAAA;AAOf,kBALAhH,KAAK0gB,QAAQ1Z,QAAAA,GAAAA,CAKRsf,KAAetmB,KAAKuf,QAAQvX,YAAY,MAAA,CAGtChI,KAAKuf,QAAQhU,qBAA+B,QAAVrD,IAAe;AAGpDlI,qBAAK0gB,QAAQza,KAAAA,GACbjG,KAAK0gB,QAAQjZ,eAAe;AAC5B,sBAAM6f,KAAUtnB,KAAK0gB,QAAQtK,YAAYlO,EAAAA;AAEzC,oBADAlI,KAAK0gB,QAAQ1Z,QAAAA,GACT,8BAA8BsgB,MAAWA,GAAQC,2BAA2B,GAAG;AAEjFvnB,uBAAK0gB,QAAQza,KAAAA;AAIb,wBAAMsX,KAAa,IAAIC;AACvBD,kBAAAA,GAAW5T,KAAKpD,IAAOugB,KAAOhhB,KAAKC,KAAKM,KAAY,CAAA,GAAIrG,KAAKuf,QAAQpI,kBAAkBoP,GAASQ,KAAOD,KAAOhhB,KAAKC,KAAKM,KAAY,CAAA,CAAA,GACpIrG,KAAK0gB,QAAQ7W,KAAK0T,EAAAA,GAClBvd,KAAK0gB,QAAQra,YAA4C,IAAhCrG,KAAKuf,QAAQhI,kBACtCvX,KAAK0gB,QAAQva,cAAcwf,EAAgBpgB,KAC3CvF,KAAK0gB,QAAQ8G,WAAWtf,IAAOme,GAASA,IAAUrmB,KAAKuf,QAAQjI,gBAAAA,GAC/DtX,KAAK0gB,QAAQ1Z,QAAAA;gBAAAA;cAAAA;YAAAA;AAOrB,gBAAIqe,GAAU;AACZ,oBAAMhf,KAAYP,KAAKuM,IAAI,GAAGvM,KAAKsH,MAAMpN,KAAKuf,QAAQvX,WAAWhI,KAAKuf,QAAQhI,mBAAmB,EAAA,CAAA,GAC3FuD,KAAUzU,KAAY,KAAM,IAAI,MAAM;AAC5CrG,mBAAK0gB,QAAQra,YAAYA,IACzBrG,KAAK0gB,QAAQva,cAAcnG,KAAK0gB,QAAQta,WACxCpG,KAAK0gB,QAAQxa,UAAAA,GACblG,KAAK0gB,QAAQ7Z,OAAOwf,GAASA,IAAUvL,EAAAA,GACvC9a,KAAK0gB,QAAQxZ,OAAOmf,IAAUrmB,KAAKuf,QAAQlI,kBAAkBkP,GAASF,IAAUvL,EAAAA,GAChF9a,KAAK0gB,QAAQ3Z,OAAAA;YAAAA;AAUf,gBANKuf,KACHtmB,KAAK0gB,QAAQzY,SAASC,IAAOme,GAASA,IAAUrmB,KAAKuf,QAAQjI,gBAAAA,GAKjD,QAAVpP,MAAAA,CAAkBlI,KAAKuf,QAAQhU,mBAAmB;AACpD,kBAAIkc,KAAqBC,EAAW1nB,KAAK0gB,QAAQiH,aAAatB,GAASA,GAASrmB,KAAKuf,QAAQpI,iBAAiBnX,KAAKuf,QAAQnI,gBAAAA,GAAmBuO,GAAiBQ,GAAiBK,CAAAA;AAChL,kBAAIiB;AACF,yBAAS/d,KAAS,GAAGA,MAAU,MAC7B1J,KAAK0gB,QAAQza,KAAAA,GACbjG,KAAK0gB,QAAQta,YAAYuf,EAAgBpgB,KACzCvF,KAAK0gB,QAAQ9a,SAAS,GAAG,GAAG5F,KAAKugB,WAAWrb,OAAOlF,KAAKugB,WAAWpb,MAAAA,GACnEnF,KAAK0gB,QAAQ1Z,QAAAA,GACbhH,KAAK0gB,QAAQzY,SAASC,IAAOme,GAASA,IAAUrmB,KAAKuf,QAAQjI,mBAAmB5N,EAAAA,GAChF+d,KAAqBC,EAAW1nB,KAAK0gB,QAAQiH,aAAatB,GAASA,GAASrmB,KAAKuf,QAAQpI,iBAAiBnX,KAAKuf,QAAQnI,gBAAAA,GAAmBuO,GAAiBQ,GAAiBK,CAAAA,GACvKiB,KAP2B/d;AAAAA;YAAAA;AAetC,gBAAIyb,GAAe;AACjB,oBAAM9e,KAAYP,KAAKuM,IAAI,GAAGvM,KAAKsH,MAAMpN,KAAKuf,QAAQvX,WAAWhI,KAAKuf,QAAQhI,mBAAmB,EAAA,CAAA,GAC3FuD,KAAU9a,KAAK0gB,QAAQra,YAAY,KAAM,IAAI,MAAM;AACzDrG,mBAAK0gB,QAAQra,YAAYA,IACzBrG,KAAK0gB,QAAQva,cAAcnG,KAAK0gB,QAAQta,WACxCpG,KAAK0gB,QAAQxa,UAAAA,GACblG,KAAK0gB,QAAQ7Z,OAAOwf,GAASA,IAAUvgB,KAAKsH,MAAMpN,KAAKuf,QAAQjI,mBAAmB,CAAA,IAAKwD,EAAAA,GACvF9a,KAAK0gB,QAAQxZ,OAAOmf,IAAUrmB,KAAKuf,QAAQlI,kBAAkBkP,GAASF,IAAUvgB,KAAKsH,MAAMpN,KAAKuf,QAAQjI,mBAAmB,CAAA,IAAKwD,EAAAA,GAChI9a,KAAK0gB,QAAQ3Z,OAAAA;YAAAA;AAGf/G,iBAAK0gB,QAAQ1Z,QAAAA;AAIb,kBAAM6U,IAAY7b,KAAK0gB,QAAQiH,aAC7B,GAAG,GAAG3nB,KAAKugB,WAAWrb,OAAOlF,KAAKugB,WAAWpb,MAAAA;AAI/C,gBAAIyiB;AAQJ,gBAJEA,IAHG5nB,KAAKuf,QAAQhU,oBA0WtB,SAAoCsQ,IAAAA;AAClC,uBAASnS,KAAS,GAAGA,KAASmS,GAAUS,KAAKzX,QAAQ6E,MAAU;AAC7D,oBAAImS,GAAUS,KAAK5S,KAAS,CAAA,IAAK;AAC/B,yBAAA;AAGJ,qBAAA;YACF,EA9W2CmS,CAAAA,IAF3B6L,EAAW7L,GAAW8J,GAAiBQ,GAAiBK,CAAAA,GAMhEoB;AACF,qBAAO3I;AAGT,kBAAMkC,IAAkBnhB,KAAK6nB,sBAAsBhM,GAAW7b,KAAK8f,kBAAkB8E,IAAcqB,GAA0BK,GAAaD,CAAAA;AAG1I,gBAAIyB,GACAC;AACJ,uBAAa;AAEX,kBAAiC,MAA7B/nB,KAAK6f,aAAahb,QAAc;AAClC,sBAAMud,KAAUpiB,KAAKsgB,eAAAA;AACrBwH,oBAAa1F,IACb2F,IAAY3F,GAAQf,YACpB0G,EAAU5iB,SAASgc,EAAgBpY,KAAKpD;AACxC;cAAA;AAIFmiB,kBAAa9nB,KAAK6f,aAAa7f,KAAK6f,aAAahb,SAAS,CAAA,GAC1DkjB,IAAYD,EAAWzG;AACvB,yBAAWkB,MAAKviB,KAAK6f;AACfsB,kBAAgBpY,KAAKpD,KAAK4c,GAAElB,WAAWlc,WACzC2iB,IAAavF,IACbwF,IAAYxF,GAAElB;AAUlB,uBAAS1hB,KAAIK,KAAK6f,aAAahb,SAAS,GAAGlF,MAAK,GAAGA;AACjD,2BAAWiU,MAAO5T,KAAK6f,aAAalgB,EAAAA,EAAGqoB;AACjCpU,kBAAAA,GAAIzO,UAAU4iB,EAAU5iB,UAAUgc,EAAgBpY,KAAKpD,KAAKiO,GAAIzO,WAClE2iB,IAAa9nB,KAAK6f,aAAalgB,EAAAA,GAC/BooB,IAAYnU;AAQlB,kBAAImU,EAAUpiB,IAAIwb,EAAgBpY,KAAKpD,KAAKmiB,EAAW/nB,OAAOoF,UAAU4iB,EAAU5iB,SAASgc,EAAgBpY,KAAKpD,IAAI,GAA+B;AAGjJ,oBAAIsiB,KAAAA;AACJ,oBAAIH,EAAWzG,WAAW1b,IAAImiB,EAAWzG,WAAWlc,SAASgc,EAAgBpY,KAAKpD,KAAKmiB,EAAW/nB,OAAOoF,QAAQ;AAE/G,sBAAI+iB;AACJ,6BAAW3F,MAAKviB,KAAK6f;AACnB,wBAAI0C,GAAElB,WAAW1b,IAAI4c,GAAElB,WAAWlc,SAASgc,EAAgBpY,KAAKpD,IAAI4c,GAAExiB,OAAOoF,QAAQ;AACnF+iB,sBAAAA,KAAgB3F;AAChB;oBAAA;AAGJ,sBAAI2F;AACFJ,wBAAaI;2BAOX7P,EAAaiJ,iBACbthB,KAAKqf,OAAOxa,UAAUwT,EAAaiJ,iBACnCyG,EAAUpiB,IAAIwb,EAAgBpY,KAAKpD,KAAKmiB,EAAW/nB,OAAOoF,UAC1D4iB,EAAU5iB,UAAUgc,EAAgBpY,KAAKpD,KACzCoiB,EAAUriB,IAAIyb,EAAgBpY,KAAKrD,KAAKoiB,EAAW/nB,OAAOmF;AAG1D+iB,oBAAAA,KAAAA;uBACK;AAEL,0BAAM7F,KAAUpiB,KAAKsgB,eAAAA;AACrBwH,wBAAa1F,IACb2F,IAAY3F,GAAQf,YACpB0G,EAAU5iB,SAASgc,EAAgBpY,KAAKpD,GACxCsiB,KAAAA;kBAAqB;gBAAA;AAItBA,gBAAAA,OAECH,EAAWzG,WAAWlc,SAAS,KACjC2iB,EAAWE,UAAUjX,KAAK+W,EAAWzG,UAAAA,GAEvC0G,IAAY,EACVriB,GAAG,GACHC,GAAGmiB,EAAWzG,WAAW1b,IAAImiB,EAAWzG,WAAWlc,QACnDA,QAAQgc,EAAgBpY,KAAKpD,EAAAA,GAE/BmiB,EAAWE,UAAUjX,KAAKgX,CAAAA,GAG1BD,EAAWzG,aAAa,EACtB3b,GAAG,GACHC,GAAGoiB,EAAUpiB,IAAIoiB,EAAU5iB,QAC3BA,QAAQ,EAAA;cAAA;AAOd,kBAAI4iB,EAAUriB,IAAIyb,EAAgBpY,KAAKrD,KAAKoiB,EAAW/nB,OAAOmF;AAC5D;AAIE6iB,oBAAcD,EAAWzG,cAC3B0G,EAAUriB,IAAI,GACdqiB,EAAUpiB,KAAKoiB,EAAU5iB,QACzB4iB,EAAU5iB,SAAS,KAEnB2iB,EAAWE,UAAU/P,OAAO6P,EAAWE,UAAUnQ,QAAQkQ,CAAAA,GAAY,CAAA;YAAA;AAiCzE,mBA5BA5G,EAAgBnY,cAAchJ,KAAKqf,OAAOxH,QAAQiQ,CAAAA,GAClD3G,EAAgB1X,gBAAgB/D,IAAIqiB,EAAUriB,GAC9Cyb,EAAgB1X,gBAAgB9D,IAAIoiB,EAAUpiB,GAC9Cwb,EAAgBjC,yBAAyBxZ,IAAIqiB,EAAUriB,IAAIoiB,EAAW/nB,OAAOmF,OAC7Eic,EAAgBjC,yBAAyBvZ,IAAIoiB,EAAUpiB,IAAImiB,EAAW/nB,OAAOoF,QAG7Egc,EAAgBhC,cAAczZ,KAAKoiB,EAAW/nB,OAAOmF,OACrDic,EAAgBhC,cAAcxZ,KAAKmiB,EAAW/nB,OAAOoF,QAIrD4iB,EAAU5iB,SAASW,KAAKuM,IAAI0V,EAAU5iB,QAAQgc,EAAgBpY,KAAKpD,CAAAA,GACnEoiB,EAAUriB,KAAKyb,EAAgBpY,KAAKrD,GAGpCoiB,EAAWrT,IAAI8H,aACbV,GACAsF,EAAgB1X,gBAAgB/D,IAAI1F,KAAK8f,iBAAiBza,MAC1D8b,EAAgB1X,gBAAgB9D,IAAI3F,KAAK8f,iBAAiBxa,KAC1DtF,KAAK8f,iBAAiBza,MACtBrF,KAAK8f,iBAAiBxa,KACtB6b,EAAgBpY,KAAKrD,GACrByb,EAAgBpY,KAAKpD,CAAAA,GAEvBmiB,EAAWK,SAAShH,CAAAA,GACpB2G,EAAW1e,WAEJ+X;UACT;UASQ,sBAAsBtF,IAAsBuM,IAA2BxD,IAAsByD,IAA0B/B,IAAsBD,IAAAA;AACnJ+B,YAAAA,GAAY9iB,MAAM;AAClB,kBAAMH,KAASkjB,KAAkBroB,KAAKuf,QAAQnI,mBAAmBpX,KAAKugB,WAAWpb,QAC3ED,KAAQmjB,KAAkBroB,KAAKuf,QAAQpI,kBAAkByN;AAC/D,gBAAI0D,KAAAA;AACJ,qBAAS3iB,KAAI,GAAGA,KAAIR,IAAQQ,MAAK;AAC/B,uBAASD,KAAI,GAAGA,KAAIR,IAAOQ,MAAK;AAC9B,sBAAM6iB,KAAc5iB,KAAI3F,KAAKugB,WAAWrb,QAAQ,IAAQ,IAAJQ,KAAQ;AAC5D,oBAAoC,MAAhCmW,GAAUS,KAAKiM,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY9iB,MAAMK,IAClB2iB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAY/iB,OAAO,GACnBijB,KAAAA;AACA,qBAAS5iB,KAAI,GAAGA,KAAI2gB,KAAUnhB,IAAOQ,MAAK;AACxC,uBAASC,KAAI,GAAGA,KAAIR,IAAQQ,MAAK;AAC/B,sBAAM4iB,KAAc5iB,KAAI3F,KAAKugB,WAAWrb,QAAQ,IAAQ,IAAJQ,KAAQ;AAC5D,oBAAoC,MAAhCmW,GAAUS,KAAKiM,EAAAA,GAAoB;AACrCH,kBAAAA,GAAY/iB,OAAOK,IACnB4iB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAYpI,QAAQ9a,IACpBojB,KAAAA;AACA,qBAAS5iB,KAAI2gB,KAAUnhB,KAAQ,GAAGQ,MAAK2gB,IAAS3gB,MAAK;AACnD,uBAASC,KAAI,GAAGA,KAAIR,IAAQQ,MAAK;AAC/B,sBAAM4iB,KAAc5iB,KAAI3F,KAAKugB,WAAWrb,QAAQ,IAAQ,IAAJQ,KAAQ;AAC5D,oBAAoC,MAAhCmW,GAAUS,KAAKiM,EAAAA,GAAoB;AACrCH,kBAAAA,GAAYpI,QAAQta,IACpB4iB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJF,YAAAA,GAAYrI,SAAS5a,IACrBmjB,KAAAA;AACA,qBAAS3iB,KAAIR,KAAS,GAAGQ,MAAK,GAAGA,MAAK;AACpC,uBAASD,KAAI,GAAGA,KAAIR,IAAOQ,MAAK;AAC9B,sBAAM6iB,KAAc5iB,KAAI3F,KAAKugB,WAAWrb,QAAQ,IAAQ,IAAJQ,KAAQ;AAC5D,oBAAoC,MAAhCmW,GAAUS,KAAKiM,EAAAA,GAAoB;AACrCH,kBAAAA,GAAYrI,SAASpa,IACrB2iB,KAAAA;AACA;gBAAA;cAAA;AAGJ,kBAAIA;AACF;YAAA;AAGJ,mBAAO,EACLtf,aAAa,GACbS,iBAAiB,EAAE/D,GAAG,GAAGC,GAAG,EAAA,GAC5BuZ,0BAA0B,EAAExZ,GAAG,GAAGC,GAAG,EAAA,GACrCoD,MAAM,EACJrD,GAAG0iB,GAAYpI,QAAQoI,GAAY/iB,OAAO,GAC1CM,GAAGyiB,GAAYrI,SAASqI,GAAY9iB,MAAM,EAAA,GAE5C6Z,eAAe,EACbzZ,GAAI0iB,GAAYpI,QAAQoI,GAAY/iB,OAAO,GAC3CM,GAAIyiB,GAAYrI,SAASqI,GAAY9iB,MAAM,EAAA,GAE7CoE,QAAQ,EACNhE,GAAAA,CAAI0iB,GAAY/iB,OAAOghB,MAAYgC,MAAmB/B,KAAexgB,KAAKsH,OAAOpN,KAAKuf,QAAQpI,kBAAkBnX,KAAKuf,QAAQlI,mBAAmB,CAAA,IAAK,IACrJ1R,GAAAA,CAAIyiB,GAAY9iB,MAAM+gB,MAAYgC,MAAmB/B,KAA2C,MAA5BtmB,KAAKuf,QAAQlS,aAAmB,IAAIvH,KAAKwH,OAAOtN,KAAKuf,QAAQnI,mBAAmBpX,KAAKuf,QAAQjI,oBAAoB,CAAA,IAAK,GAAA,EAAA;UAGhM;QAAA;AAj4BF,QAAA5X,GAAA,eAAA,GAkXUE,GAAA,CADP,EAAA4oB,SAAAA,GAAAA,EAAAA,WAAAA,gBAAAA,IAAAA;QAmhBH,MAAMnG,EAAAA;UAKJ,IAAA,iBAAWV;AAA2B,mBAAO3hB,KAAKyoB,eAAezoB,KAAKD,OAAOmF,QAAQlF,KAAKD,OAAOoF;UAAS;UAG1G,IAAA,SAAW4c;AAA4C,mBAAO/hB,KAAK0oB;UAAS;UACrE,SAASpgB,IAAAA;AACdtI,iBAAK0oB,QAAQ3X,KAAKzI,EAAAA,GAClBtI,KAAKyoB,eAAengB,GAAMS,KAAKrD,IAAI4C,GAAMS,KAAKpD;UAChD;UAwBA,YACEzD,IACA6G,IACA4f,IAAAA;AAEA,gBArCM,KAAAF,cAAsB,GAGb,KAAAC,UAA8B,CAAA,GAUxC,KAAAtf,UAAU,GAYV,KAAAiY,aAAkC,EACvC3b,GAAG,GACHC,GAAG,GACHR,QAAQ,EAAA,GAEM,KAAA6iB,YAAmC,CAAA,GAO7CW;AACF,yBAAWpG,MAAKoG;AACd3oB,qBAAK0oB,QAAQ3X,KAAAA,GAAQwR,GAAER,MAAAA,GACvB/hB,KAAKyoB,eAAelG,GAAEkG;AAG1BzoB,iBAAKD,SAASygB,EAAate,IAAU6G,IAAMA,EAAAA,GAI3C/I,KAAKyU,OAAM,GAAA,EAAApR,cAAarD,KAAKD,OAAOuD,WAAW,MAAM,EAAEC,OAAAA,KAAO,CAAA,CAAA;UAChE;UAEO,QAAA0J;AACLjN,iBAAKyU,IAAIpN,UAAU,GAAG,GAAGrH,KAAKD,OAAOmF,OAAOlF,KAAKD,OAAOoF,MAAAA,GACxDnF,KAAKqhB,WAAW3b,IAAI,GACpB1F,KAAKqhB,WAAW1b,IAAI,GACpB3F,KAAKqhB,WAAWlc,SAAS,GACzBnF,KAAKgoB,UAAUnjB,SAAS,GACxB7E,KAAKoJ;UACP;QAAA;AAQF,iBAASse,EAAW7L,IAAsBpT,IAAYC,IAAYkgB,IAAAA;AAEhE,gBAAM3f,KAAIR,GAAGoO,SAAS,IAChBkF,KAAItT,GAAGoO,SAAS,KAAK,KACrBmC,KAAIvQ,GAAGoO,SAAS,IAAI,KACpBgS,KAAMngB,GAAGmO,SAAS,IAClBiS,KAAMpgB,GAAGmO,SAAS,KAAK,KACvBkS,KAAMrgB,GAAGmO,SAAS,IAAI,KAQtBmS,KAAYljB,KAAKsH,OAAOtH,KAAKmjB,IAAIhgB,KAAI4f,EAAAA,IAAO/iB,KAAKmjB,IAAIlN,KAAI+M,EAAAA,IAAOhjB,KAAKmjB,IAAIjQ,KAAI+P,EAAAA,KAAQ,EAAA;AAG3F,cAAInB,KAAAA;AACJ,mBAASle,KAAS,GAAGA,KAASmS,GAAUS,KAAKzX,QAAQ6E,MAAU;AAEzDmS,YAAAA,GAAUS,KAAK5S,EAAAA,MAAYT,MAC3B4S,GAAUS,KAAK5S,KAAS,CAAA,MAAOqS,MAC/BF,GAAUS,KAAK5S,KAAS,CAAA,MAAOsP,MAI7B4P,MACC9iB,KAAKmjB,IAAIpN,GAAUS,KAAK5S,EAAAA,IAAUT,EAAAA,IACnCnD,KAAKmjB,IAAIpN,GAAUS,KAAK5S,KAAS,CAAA,IAAKqS,EAAAA,IACtCjW,KAAKmjB,IAAIpN,GAAUS,KAAK5S,KAAS,CAAA,IAAKsP,EAAAA,IAAMgQ,KANhDnN,GAAUS,KAAK5S,KAAS,CAAA,IAAK,IAS3Bke,KAAAA;AAKN,iBAAOA;QACT;AAWA,iBAASpH,EAAate,IAAoBgD,IAAeC,IAAAA;AACvD,gBAAMpF,KAASmC,GAASC,cAAc,QAAA;AAGtC,iBAFApC,GAAOmF,QAAQA,IACfnF,GAAOoF,SAASA,IACTpF;QACT;MAAA,GAAA,KAAA,SAAAN,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAqJ,IAAAI,KAAA,UAAA,QAAAG,KAAAH,KAAA,IAAA3J,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAA4J,KAAA,QAAA,SAAA/J,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAmZ,KAAAtZ,GAAA,SAAA,GAAAsZ,MAAA,GAAAA;AAAA,eAAA9P,KAAAxJ,GAAAsZ,EAAA,OAAAvP,MAAAH,KAAA,IAAAJ,GAAAO,EAAA,IAAAH,KAAA,IAAAJ,GAAAvJ,IAAAC,IAAA6J,EAAA,IAAAP,GAAAvJ,IAAAC,EAAA,MAAA6J;AAAA,iBAAAH,KAAA,KAAAG,MAAA,OAAA,eAAA9J,IAAAC,IAAA6J,EAAA,GAAAA;QAAA,GAAA,IAAA,QAAA,KAAA,WAAA,SAAA/J,IAAAC,IAAA;AAAA,iBAAA,SAAAC,IAAAC,IAAA;AAAA,YAAAF,GAAAC,IAAAC,IAAAH,EAAA;UAAA;QAAA;AAAA,eAAA,eAAAC,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,yBAAAA,GAAA,iBAAA;ACljCA,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA;QAGA,MAAayU,UAAuB,EAAAc,cAAAA;UASlC,YAAYgU,IAAsBhhB,IAAehD,IAAAA;AAC/CnE,kBAAAA,GANK,KAAAgP,UAAkB,GAGlB,KAAAoZ,eAAuB,IAI5BnpB,KAAK0I,KAAKwgB,GAAUxgB,IACpB1I,KAAKyI,KAAKygB,GAAUzgB,IACpBzI,KAAKmpB,eAAejhB,IACpBlI,KAAKopB,SAASlkB;UAChB;UAEO,aAAAmkB;AAEL,mBAAO;UACT;UAEO,WAAAlZ;AACL,mBAAOnQ,KAAKopB;UACd;UAEO,WAAArhB;AACL,mBAAO/H,KAAKmpB;UACd;UAEO,UAAAtgB;AAGL,mBAAO;UACT;UAEO,gBAAgBnE,IAAAA;AACrB,kBAAM,IAAI+W,MAAM,iBAAA;UAClB;UAEO,gBAAA6N;AACL,mBAAO,CAACtpB,KAAK0I,IAAI1I,KAAK+H,SAAAA,GAAY/H,KAAKmQ,SAAAA,GAAYnQ,KAAK6I,QAAAA,CAAAA;UAC1D;QAAA;AA1CF,QAAAnJ,GAAA,iBAAA;AA6CO,YAAM6pB,IAAsB7pB,GAAA,yBAA5B,MAAM6pB,GAAAA;UAOX,YACkB9pB,IAAA;AAAQ,iBAAAkB,iBAAAA,IALlB,KAAA6oB,oBAAwC,CAAA,GACxC,KAAAC,yBAAiC,GACjC,KAAArW,YAAsB,IAAI,EAAAlF;UAI9B;UAEG,SAASwb,IAAAA;AACd,kBAAMC,KAA2B,EAC/BppB,IAAIP,KAAKypB,0BACTC,SAAAA,GAAAA;AAIF,mBADA1pB,KAAKwpB,kBAAkBzY,KAAK4Y,EAAAA,GACrBA,GAAOppB;UAChB;UAEO,WAAWqpB,IAAAA;AAChB,qBAASjqB,KAAI,GAAGA,KAAIK,KAAKwpB,kBAAkB3kB,QAAQlF;AACjD,kBAAIK,KAAKwpB,kBAAkB7pB,EAAAA,EAAGY,OAAOqpB;AAEnC,uBADA5pB,KAAKwpB,kBAAkBvR,OAAOtY,IAAG,CAAA,GAAA;AAKrC,mBAAA;UACF;UAEO,oBAAoBiU,IAAAA;AACzB,gBAAsC,MAAlC5T,KAAKwpB,kBAAkB3kB;AACzB,qBAAO,CAAA;AAGT,kBAAMgP,KAAO7T,KAAKW,eAAeyH,OAAOwH,MAAMC,IAAI+D,EAAAA;AAClD,gBAAA,CAAKC,MAAwB,MAAhBA,GAAKhP;AAChB,qBAAO,CAAA;AAGT,kBAAMglB,KAA6B,CAAA,GAC7BC,KAAUjW,GAAKQ,kBAAAA,IAAkB;AAMvC,gBAAI0V,KAAmB,GACnBC,KAAqB,GACrBC,KAAwB,GACxBC,KAAcrW,GAAKsW,MAAM,CAAA,GACzBC,KAAcvW,GAAKwW,MAAM,CAAA;AAE7B,qBAAS3kB,KAAI,GAAGA,KAAImO,GAAKyW,iBAAAA,GAAoB5kB;AAG3C,kBAFAmO,GAAK/D,SAASpK,IAAG1F,KAAKoT,SAAAA,GAEY,MAA9BpT,KAAKoT,UAAUjD,SAAAA,GAAnB;AAMA,oBAAInQ,KAAKoT,UAAU1K,OAAOwhB,MAAelqB,KAAKoT,UAAU3K,OAAO2hB,IAAa;AAG1E,sBAAI1kB,KAAIqkB,KAAmB,GAAG;AAC5B,0BAAMjW,KAAe9T,KAAKuqB,iBACxBT,IACAG,IACAD,IACAnW,IACAkW,EAAAA;AAEF,6BAASpqB,KAAI,GAAGA,KAAImU,GAAajP,QAAQlF;AACvCkqB,sBAAAA,GAAO9Y,KAAK+C,GAAanU,EAAAA,CAAAA;kBAAAA;AAK7BoqB,kBAAAA,KAAmBrkB,IACnBukB,KAAwBD,IACxBE,KAAclqB,KAAKoT,UAAU1K,IAC7B0hB,KAAcpqB,KAAKoT,UAAU3K;gBAAAA;AAG/BuhB,gBAAAA,MAAsBhqB,KAAKoT,UAAUrL,SAAAA,EAAWlD,UAAU,EAAA2lB,qBAAqB3lB;cAAAA;AAIjF,gBAAI7E,KAAKW,eAAeiJ,OAAOmgB,KAAmB,GAAG;AACnD,oBAAMjW,KAAe9T,KAAKuqB,iBACxBT,IACAG,IACAD,IACAnW,IACAkW,EAAAA;AAEF,uBAASpqB,KAAI,GAAGA,KAAImU,GAAajP,QAAQlF;AACvCkqB,gBAAAA,GAAO9Y,KAAK+C,GAAanU,EAAAA,CAAAA;YAAAA;AAI7B,mBAAOkqB;UACT;UAUQ,iBAAiBhW,IAAc4W,IAAoBC,IAAkBC,IAAuBlY,IAAAA;AAClG,kBAAMmY,KAAO/W,GAAKqI,UAAUuO,IAAYC,EAAAA;AAIxC,gBAAIG,KAAsC,CAAA;AAC1C,gBAAA;AACEA,cAAAA,KAAkB7qB,KAAKwpB,kBAAkB,CAAA,EAAGE,QAAQkB,EAAAA;YAAAA,SAC7CzN,IAAAA;AACPD,sBAAQC,MAAMA,EAAAA;YAAAA;AAEhB,qBAASxd,KAAI,GAAGA,KAAIK,KAAKwpB,kBAAkB3kB,QAAQlF;AAEjD,kBAAA;AACE,sBAAMmrB,KAAe9qB,KAAKwpB,kBAAkB7pB,EAAAA,EAAG+pB,QAAQkB,EAAAA;AACvD,yBAASnI,KAAI,GAAGA,KAAIqI,GAAajmB,QAAQ4d;AACvC8G,kBAAAA,GAAuBwB,aAAaF,IAAiBC,GAAarI,EAAAA,CAAAA;cAAAA,SAE7DtF,IAAAA;AACPD,wBAAQC,MAAMA,EAAAA;cAAAA;AAIlB,mBADAnd,KAAKgrB,0BAA0BH,IAAiBF,IAAUlY,EAAAA,GACnDoY;UACT;UAUQ,0BAA0BhB,IAA4BhW,IAAmBpB,IAAAA;AAC/E,gBAAIwY,KAAoB,GACpBC,KAAAA,OACAlB,KAAqB,GACrBmB,KAAetB,GAAOoB,EAAAA;AAG1B,gBAAKE,IAAL;AAIA,uBAASzlB,KAAI+M,IAAU/M,KAAI1F,KAAKW,eAAeiJ,MAAMlE,MAAK;AACxD,sBAAMR,KAAQ2O,GAAK1D,SAASzK,EAAAA,GACtBb,KAASgP,GAAKuX,UAAU1lB,EAAAA,EAAGb,UAAU,EAAA2lB,qBAAqB3lB;AAIhE,oBAAc,MAAVK,IAAJ;AAWA,sBAAA,CANKgmB,MAAuBC,GAAa,CAAA,KAAMnB,OAC7CmB,GAAa,CAAA,IAAKzlB,IAClBwlB,KAAAA,OAIEC,GAAa,CAAA,KAAMnB,IAAoB;AAOzC,wBANAmB,GAAa,CAAA,IAAKzlB,IAGlBylB,KAAetB,GAAAA,EAASoB,EAAAA,GAAAA,CAGnBE;AACH;AAOEA,oBAAAA,GAAa,CAAA,KAAMnB,MACrBmB,GAAa,CAAA,IAAKzlB,IAClBwlB,KAAAA,QAEAA,KAAAA;kBAAsB;AAM1BlB,kBAAAA,MAAsBnlB;gBAAAA;cAAAA;AAKpBsmB,cAAAA,OACFA,GAAa,CAAA,IAAKnrB,KAAKW,eAAeiJ;YAAAA;UAE1C;UAUQ,OAAA,aAAoBigB,IAA4BwB,IAAAA;AACtD,gBAAIC,KAAAA;AACJ,qBAAS3rB,KAAI,GAAGA,KAAIkqB,GAAOhlB,QAAQlF,MAAK;AACtC,oBAAMuU,KAAQ2V,GAAOlqB,EAAAA;AACrB,kBAAK2rB,IAAL;AAwBE,oBAAID,GAAS,CAAA,KAAMnX,GAAM,CAAA;AAIvB,yBADA2V,GAAOlqB,KAAI,CAAA,EAAG,CAAA,IAAK0rB,GAAS,CAAA,GACrBxB;AAGT,oBAAIwB,GAAS,CAAA,KAAMnX,GAAM,CAAA;AAKvB,yBAFA2V,GAAOlqB,KAAI,CAAA,EAAG,CAAA,IAAKmG,KAAKuM,IAAIgZ,GAAS,CAAA,GAAInX,GAAM,CAAA,CAAA,GAC/C2V,GAAO5R,OAAOtY,IAAG,CAAA,GACVkqB;AAKTA,gBAAAA,GAAO5R,OAAOtY,IAAG,CAAA,GACjBA;cAAAA,OA1CF;AACE,oBAAI0rB,GAAS,CAAA,KAAMnX,GAAM,CAAA;AAGvB,yBADA2V,GAAO5R,OAAOtY,IAAG,GAAG0rB,EAAAA,GACbxB;AAGT,oBAAIwB,GAAS,CAAA,KAAMnX,GAAM,CAAA;AAIvB,yBADAA,GAAM,CAAA,IAAKpO,KAAK6J,IAAI0b,GAAS,CAAA,GAAInX,GAAM,CAAA,CAAA,GAChC2V;AAGLwB,gBAAAA,GAAS,CAAA,IAAKnX,GAAM,CAAA,MAGtBA,GAAM,CAAA,IAAKpO,KAAK6J,IAAI0b,GAAS,CAAA,GAAInX,GAAM,CAAA,CAAA,GACvCoX,KAAAA;cAAU;YAAA;AAoChB,mBARIA,KAEFzB,GAAOA,GAAOhlB,SAAS,CAAA,EAAG,CAAA,IAAKwmB,GAAS,CAAA,IAGxCxB,GAAO9Y,KAAKsa,EAAAA,GAGPxB;UACT;QAAA;AAAA,QAAAnqB,GAAA,yBAvRW6pB,IAAsB3pB,GAAA,CAQ9B,EAAA,GAAA,EAAA2rB,cAAAA,CAAAA,GARQhC,CAAAA;MAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAA,OAAAA;AAAAA,eAAAA,eAAAA,IAAAA,cAAAA,EAAAA,OAAAA,KAAAA,CAAAA,GAAAA,GAAAA,gBAAAA,GAAAA,cAAAA,GAAAA,OAAAA,GAAAA,MAAAA,GAAAA,MAAAA,GAAAA,QAAAA,GAAAA,WAAAA,GAAAA,aAAAA;ACrDb,cAAA3pB,KAAAD,GAAA,GAAA;AAGA,YAAI6rB,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK;AAUT,YAAiBC,GAmBArI,GAuEAhe,GAkGAsmB,GAoCAhV;AA8FjB,iBAAgBiV,EAAYjR,IAAAA;AAC1B,gBAAMjb,KAAIib,GAAEtY,SAAS,EAAA;AACrB,iBAAO3C,GAAEiF,SAAS,IAAI,MAAMjF,KAAIA;QAClC;AAQA,iBAAgBmsB,EAAcC,IAAYC,IAAAA;AACxC,iBAAID,KAAKC,MACCA,KAAK,SAASD,KAAK,SAErBA,KAAK,SAASC,KAAK;QAC7B;AAtVa,QAAAvsB,GAAA+Y,aAAqB,EAChClT,KAAK,aACLsR,MAAM,EAAA,GAMR,SAAiB+U,IAAAA;AACC,UAAAnsB,GAAAysB,QAAhB,SAAsBjjB,IAAW8S,IAAW/C,IAAWD,IAAAA;AACrD,mBAAA,WAAIA,KACK,IAAI+S,EAAY7iB,EAAAA,CAAAA,GAAK6iB,EAAY/P,EAAAA,CAAAA,GAAK+P,EAAY9S,EAAAA,CAAAA,GAAK8S,EAAY/S,EAAAA,CAAAA,KAErE,IAAI+S,EAAY7iB,EAAAA,CAAAA,GAAK6iB,EAAY/P,EAAAA,CAAAA,GAAK+P,EAAY9S,EAAAA,CAAAA;UAC3D,GAEgBvZ,GAAA0sB,SAAhB,SAAuBljB,IAAW8S,IAAW/C,IAAWD,KAAY,KAAA;AAIlE,oBAAQ9P,MAAK,KAAK8S,MAAK,KAAK/C,MAAK,IAAID,QAAO;UAC9C;QACD,EAdgB6S,MAAQlsB,GAAA,WAARksB,IAAQ,CAAA,EAAA,GAmBzB,SAAiBnsB,IAAA;AAgDf,mBAAgB2sB,GAAQ7I,IAAe6I,IAAAA;AAGrC,mBAFAT,IAAK7lB,KAAKwH,MAAgB,MAAV8e,EAAAA,GAAAA,CACfZ,GAAIC,GAAIC,CAAAA,IAAM7U,EAAKwV,WAAW9I,GAAM1M,IAAAA,GAC9B,EACLtR,KAAKqmB,EAASM,MAAMV,GAAIC,GAAIC,GAAIC,CAAAA,GAChC9U,MAAM+U,EAASO,OAAOX,GAAIC,GAAIC,GAAIC,CAAAA,EAAAA;UAEtC;AAtDgB,UAAAlsB,GAAA6sB,QAAhB,SAAsB7jB,IAAYC,IAAAA;AAEhC,gBADAijB,KAAgB,MAAVjjB,GAAGmO,QAAe,KACb,MAAP8U;AACF,qBAAO,EACLpmB,KAAKmD,GAAGnD,KACRsR,MAAMnO,GAAGmO,KAAAA;AAGb,kBAAMgS,KAAOngB,GAAGmO,QAAQ,KAAM,KACxBiS,KAAOpgB,GAAGmO,QAAQ,KAAM,KACxBkS,KAAOrgB,GAAGmO,QAAQ,IAAK,KACvB0V,KAAO9jB,GAAGoO,QAAQ,KAAM,KACxB2V,KAAO/jB,GAAGoO,QAAQ,KAAM,KACxB4V,KAAOhkB,GAAGoO,QAAQ,IAAK;AAM7B,mBALA2U,IAAKe,KAAMzmB,KAAKwH,OAAOub,KAAM0D,MAAOZ,CAAAA,GACpCF,IAAKe,KAAM1mB,KAAKwH,OAAOwb,KAAM0D,MAAOb,CAAAA,GACpCD,IAAKe,KAAM3mB,KAAKwH,OAAOyb,KAAM0D,MAAOd,CAAAA,GAG7B,EAAEpmB,KAFGqmB,EAASM,MAAMV,GAAIC,GAAIC,CAAAA,GAErB7U,MADD+U,EAASO,OAAOX,GAAIC,GAAIC,CAAAA,EAAAA;UAEvC,GAEgBjsB,GAAAitB,WAAhB,SAAyBnJ,IAAAA;AACvB,mBAA+B,QAAV,MAAbA,GAAM1M;UAChB,GAEgBpX,GAAA8kB,sBAAhB,SAAoC9b,IAAYC,IAAYikB,IAAAA;AAC1D,kBAAMnkB,KAASqO,EAAK0N,oBAAoB9b,GAAGoO,MAAMnO,GAAGmO,MAAM8V,EAAAA;AAC1D,gBAAKnkB;AAGL,qBAAOqO,EAAKyM,QACT9a,MAAU,KAAK,KACfA,MAAU,KAAK,KACfA,MAAU,IAAK,GAAA;UAEpB,GAEgB/I,GAAA+jB,SAAhB,SAAuBD,IAAAA;AACrB,kBAAMqJ,MAA0B,MAAbrJ,GAAM1M,UAAiB;AAE1C,mBAAA,CADC2U,GAAIC,GAAIC,CAAAA,IAAM7U,EAAKwV,WAAWO,EAAAA,GACxB,EACLrnB,KAAKqmB,EAASM,MAAMV,GAAIC,GAAIC,CAAAA,GAC5B7U,MAAM+V,GAAAA;UAEV,GAEgBntB,GAAA2sB,UAAO1sB,IASPD,GAAAskB,kBAAhB,SAAgCR,IAAesJ,IAAAA;AAE7C,mBADAlB,IAAkB,MAAbpI,GAAM1M,MACJuV,GAAQ7I,IAAQoI,IAAKkB,KAAU,GAAA;UACxC,GAEgBptB,GAAA0V,aAAhB,SAA2BoO,IAAAA;AACzB,mBAAO,CAAEA,GAAM1M,QAAQ,KAAM,KAAO0M,GAAM1M,QAAQ,KAAM,KAAO0M,GAAM1M,QAAQ,IAAK,GAAA;UACpF;QACD,EAjEgB0M,MAAK7jB,GAAA,QAAL6jB,IAAK,CAAA,EAAA,GAuEtB,SAAiB9jB,IAAA;AACf,cAAIqtB,IACAC;AACJ,cAAA,CAAKntB,GAAAotB,QAAQ;AACX,kBAAMjtB,KAASmC,SAASC,cAAc,QAAA;AACtCpC,YAAAA,GAAOmF,QAAQ,GACfnF,GAAOoF,SAAS;AAChB,kBAAMsP,KAAM1U,GAAOuD,WAAW,MAAM,EAClCqd,oBAAAA,KAAoB,CAAA;AAElBlM,YAAAA,OACFqY,KAAOrY,IACPqY,GAAKjH,2BAA2B,QAChCkH,KAAeD,GAAKG,qBAAqB,GAAG,GAAG,GAAG,CAAA;UAAA;AAWtC,UAAAxtB,GAAA6jB,UAAhB,SAAwB/d,IAAAA;AAEtB,gBAAIA,GAAI2nB,MAAM,gBAAA;AACZ,sBAAQ3nB,GAAIV,QAAAA;gBACV,KAAK;AAIH,yBAHA2mB,IAAKvP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GACzC1B,IAAKxP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GACzCzB,IAAKzP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GAClCtW,EAAKyM,QAAQkI,GAAIC,GAAIC,CAAAA;gBAE9B,KAAK;AAKH,yBAJAF,IAAKvP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GACzC1B,IAAKxP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GACzCzB,IAAKzP,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GACzCxB,IAAK1P,SAAS1W,GAAImT,MAAM,GAAG,CAAA,EAAGyU,OAAO,CAAA,GAAI,EAAA,GAClCtW,EAAKyM,QAAQkI,GAAIC,GAAIC,GAAIC,CAAAA;gBAElC,KAAK;AACH,yBAAO,EACLpmB,KAAAA,IACAsR,OAAOoF,SAAS1W,GAAImT,MAAM,CAAA,GAAI,EAAA,KAAO,IAAI,SAAU,EAAA;gBAEvD,KAAK;AACH,yBAAO,EACLnT,KAAAA,IACAsR,MAAMoF,SAAS1W,GAAImT,MAAM,CAAA,GAAI,EAAA,MAAQ,EAAA;cAAA;AAM7C,kBAAM0U,KAAY7nB,GAAI2nB,MAAM,oFAAA;AAC5B,gBAAIE;AAKF,qBAJA5B,IAAKvP,SAASmR,GAAU,CAAA,CAAA,GACxB3B,IAAKxP,SAASmR,GAAU,CAAA,CAAA,GACxB1B,IAAKzP,SAASmR,GAAU,CAAA,CAAA,GACxBzB,IAAK7lB,KAAKwH,MAAoE,OAAA,WAA7D8f,GAAU,CAAA,IAAmB,IAAI/Q,WAAW+Q,GAAU,CAAA,CAAA,EAAA,GAChEvW,EAAKyM,QAAQkI,GAAIC,GAAIC,GAAIC,CAAAA;AAIlC,gBAAA,CAAKmB,MAAAA,CAASC;AACZ,oBAAM,IAAItR,MAAM,qCAAA;AAOlB,gBAFAqR,GAAK1mB,YAAY2mB,IACjBD,GAAK1mB,YAAYb,IACa,YAAA,OAAnBunB,GAAK1mB;AACd,oBAAM,IAAIqV,MAAM,qCAAA;AAOlB,gBAJAqR,GAAKlnB,SAAS,GAAG,GAAG,GAAG,CAAA,GAAA,CACtB4lB,GAAIC,GAAIC,GAAIC,CAAAA,IAAMmB,GAAKnF,aAAa,GAAG,GAAG,GAAG,CAAA,EAAGrL,MAGtC,QAAPqP;AACF,oBAAM,IAAIlQ,MAAM,qCAAA;AAMlB,mBAAO,EACL5E,MAAM+U,EAASO,OAAOX,GAAIC,GAAIC,GAAIC,CAAAA,GAClCpmB,KAAAA,GAAAA;UAEJ;QACD,EA7FgBA,MAAG7F,GAAA,MAAH6F,IAAG,CAAA,EAAA,GAkGpB,SAAiB9F,IAAA;AAsBf,mBAAgB4tB,GAAmBpkB,IAAW8S,IAAW/C,IAAAA;AACvD,kBAAMsU,KAAKrkB,KAAI,KACTskB,KAAKxR,KAAI,KACTyR,KAAKxU,KAAI;AAIf,mBAAY,UAHDsU,MAAM,UAAUA,KAAK,QAAQxnB,KAAK2nB,KAAKH,KAAK,SAAS,OAAO,GAAA,KAG7C,UAFfC,MAAM,UAAUA,KAAK,QAAQznB,KAAK2nB,KAAKF,KAAK,SAAS,OAAO,GAAA,KAE/B,UAD7BC,MAAM,UAAUA,KAAK,QAAQ1nB,KAAK2nB,KAAKD,KAAK,SAAS,OAAO,GAAA;UAEzE;AAvBgB,UAAA/tB,GAAAiuB,oBAAhB,SAAkC7B,IAAAA;AAChC,mBAAOwB,GACJxB,MAAO,KAAM,KACbA,MAAO,IAAM,KACA,MAAdpsB,EAAA;UACJ,GAUgBA,GAAA4tB,qBAAkB3tB;QASnC,EA/BgBmsB,MAAGnsB,GAAA,MAAHmsB,IAAG,CAAA,EAAA,GAoCpB,SAAiBhV,IAAAA;AAyCf,mBAAgB8W,GAAgBxJ,IAAgBE,IAAgBsI,IAAAA;AAG9D,kBAAMJ,KAAOpI,MAAU,KAAM,KACvBqI,KAAOrI,MAAU,KAAM,KACvBsI,KAAOtI,MAAW,IAAK;AAC7B,gBAAI0E,KAAOxE,MAAU,KAAM,KACvByE,KAAOzE,MAAU,KAAM,KACvB0E,KAAO1E,MAAW,IAAK,KACvBuJ,KAAK7B,EAAcF,EAAIwB,mBAAmBxE,IAAKC,IAAKC,EAAAA,GAAM8C,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAC/F,mBAAOmB,KAAKjB,OAAU9D,KAAM,KAAKC,KAAM,KAAKC,KAAM;AAEhDF,cAAAA,MAAO/iB,KAAKuM,IAAI,GAAGvM,KAAKC,KAAW,MAAN8iB,EAAAA,CAAAA,GAC7BC,MAAOhjB,KAAKuM,IAAI,GAAGvM,KAAKC,KAAW,MAAN+iB,EAAAA,CAAAA,GAC7BC,MAAOjjB,KAAKuM,IAAI,GAAGvM,KAAKC,KAAW,MAANgjB,EAAAA,CAAAA,GAC7B6E,KAAK7B,EAAcF,EAAIwB,mBAAmBxE,IAAKC,IAAKC,EAAAA,GAAM8C,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAE7F,oBAAQ5D,MAAO,KAAKC,MAAO,KAAKC,MAAO,IAAI,SAAU;UACvD;AAEA,mBAAgB8E,GAAkB1J,IAAgBE,IAAgBsI,IAAAA;AAGhE,kBAAMJ,KAAOpI,MAAU,KAAM,KACvBqI,KAAOrI,MAAU,KAAM,KACvBsI,KAAOtI,MAAW,IAAK;AAC7B,gBAAI0E,KAAOxE,MAAU,KAAM,KACvByE,KAAOzE,MAAU,KAAM,KACvB0E,KAAO1E,MAAW,IAAK,KACvBuJ,KAAK7B,EAAcF,EAAIwB,mBAAmBxE,IAAKC,IAAKC,EAAAA,GAAM8C,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAC/F,mBAAOmB,KAAKjB,OAAU9D,KAAM,OAAQC,KAAM,OAAQC,KAAM;AAEtDF,cAAAA,KAAM/iB,KAAK6J,IAAI,KAAMkZ,KAAM/iB,KAAKC,KAAmB,OAAb,MAAM8iB,GAAAA,CAAAA,GAC5CC,KAAMhjB,KAAK6J,IAAI,KAAMmZ,KAAMhjB,KAAKC,KAAmB,OAAb,MAAM+iB,GAAAA,CAAAA,GAC5CC,KAAMjjB,KAAK6J,IAAI,KAAMoZ,KAAMjjB,KAAKC,KAAmB,OAAb,MAAMgjB,GAAAA,CAAAA,GAC5C6E,KAAK7B,EAAcF,EAAIwB,mBAAmBxE,IAAKC,IAAKC,EAAAA,GAAM8C,EAAIwB,mBAAmBd,IAAKC,IAAKC,EAAAA,CAAAA;AAE7F,oBAAQ5D,MAAO,KAAKC,MAAO,KAAKC,MAAO,IAAI,SAAU;UACvD;AAjEgB,UAAAtpB,GAAA8kB,sBAAhB,SAAoCJ,IAAgBE,IAAgBsI,IAAAA;AAClE,kBAAMmB,KAAMjC,EAAI6B,kBAAkBvJ,MAAU,CAAA,GACtC4J,KAAMlC,EAAI6B,kBAAkBrJ,MAAU,CAAA;AAE5C,gBADW0H,EAAc+B,IAAKC,EAAAA,IACrBpB,IAAO;AACd,kBAAIoB,KAAMD,IAAK;AACb,sBAAME,KAAUL,GAAgBxJ,IAAQE,IAAQsI,EAAAA,GAC1CsB,KAAelC,EAAc+B,IAAKjC,EAAI6B,kBAAkBM,MAAW,CAAA,CAAA;AACzE,oBAAIC,KAAetB,IAAO;AACxB,wBAAMuB,KAAUL,GAAkB1J,IAAQE,IAAQsI,EAAAA;AAElD,yBAAOsB,KADclC,EAAc+B,IAAKjC,EAAI6B,kBAAkBQ,MAAW,CAAA,CAAA,IACpCF,KAAUE;gBAAAA;AAEjD,uBAAOF;cAAAA;AAET,oBAAMA,KAAUH,GAAkB1J,IAAQE,IAAQsI,EAAAA,GAC5CsB,KAAelC,EAAc+B,IAAKjC,EAAI6B,kBAAkBM,MAAW,CAAA,CAAA;AACzE,kBAAIC,KAAetB,IAAO;AACxB,sBAAMuB,KAAUP,GAAgBxJ,IAAQE,IAAQsI,EAAAA;AAEhD,uBAAOsB,KADclC,EAAc+B,IAAKjC,EAAI6B,kBAAkBQ,MAAW,CAAA,CAAA,IACpCF,KAAUE;cAAAA;AAEjD,qBAAOF;YAAAA;UAGX,GAEgBvuB,GAAAkuB,kBAAejuB,IAoBfD,GAAAouB,oBAAiBluB,IAqBjBF,GAAA4sB,aAAhB,SAA2B3nB,IAAAA;AACzB,mBAAO,CAAEA,MAAS,KAAM,KAAOA,MAAS,KAAM,KAAOA,MAAS,IAAK,KAAc,MAARA,EAAAA;UAC3E,GAEgBjF,GAAA6jB,UAAhB,SAAwBra,IAAW8S,IAAW/C,IAAWD,IAAAA;AACvD,mBAAO,EACLxT,KAAKqmB,EAASM,MAAMjjB,IAAG8S,IAAG/C,IAAGD,EAAAA,GAC7BlC,MAAM+U,EAASO,OAAOljB,IAAG8S,IAAG/C,IAAGD,EAAAA,EAAAA;UAEnC;QACD,EA5FgBlC,MAAInX,GAAA,OAAJmX,IAAI,CAAA,EAAA,GA8FrBnX,GAAA,cAAA,GAWAA,GAAA,gBAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,eAAAA,GAAA,eAAA,QCzUAA,GAAA,eAAA,MAAA;UAAA,cAAA;AACU,iBAAAyuB,aAAgC,CAAA,GAEhC,KAAAC,YAAAA;UA4CV;UA1CE,IAAA,QAAWrsB;AAmBT,mBAlBK/B,KAAKquB,WACRruB,KAAKquB,SAAUC,CAAAA,QACbtuB,KAAKmuB,WAAWpd,KAAKud,EAAAA,GACF,EACjB/hB,SAAS,MAAA;AACP,kBAAA,CAAKvM,KAAKouB;AACR,yBAASzuB,KAAI,GAAGA,KAAIK,KAAKmuB,WAAWtpB,QAAQlF;AAC1C,sBAAIK,KAAKmuB,WAAWxuB,EAAAA,MAAO2uB;AAEzB,2BAAA,KADAtuB,KAAKmuB,WAAWlW,OAAOtY,IAAG,CAAA;;YAAA,EAAA,KAUjCK,KAAKquB;UACd;UAEO,KAAKE,IAASC,IAAAA;AACnB,kBAAM1N,KAA2B,CAAA;AACjC,qBAASnhB,KAAI,GAAGA,KAAIK,KAAKmuB,WAAWtpB,QAAQlF;AAC1CmhB,cAAAA,GAAM/P,KAAK/Q,KAAKmuB,WAAWxuB,EAAAA,CAAAA;AAE7B,qBAASA,KAAI,GAAGA,KAAImhB,GAAMjc,QAAQlF;AAChCmhB,cAAAA,GAAMnhB,EAAAA,EAAG8uB,KAAAA,QAAgBF,IAAMC,EAAAA;UAEnC;UAEO,UAAAjiB;AACLvM,iBAAK0uB,eAAAA,GACL1uB,KAAKouB,YAAAA;UACP;UAEO,iBAAAM;AACD1uB,iBAAKmuB,eACPnuB,KAAKmuB,WAAWtpB,SAAS;UAE7B;QAAA,GAGFnF,GAAA,eAAA,SAAgCivB,IAAiBC,IAAAA;AAC/C,iBAAOD,GAAKlvB,CAAAA,OAAKmvB,GAAG5hB,KAAKvN,EAAAA,CAAAA;QAC3B;MAAA,GAAA,KAAA,CAAAA,IAAAC,OAAA;ACuBA,iBAAgBmvB,GAAaC,IAAAA;AAC3B,qBAAWnZ,MAAKmZ;AACdnZ,YAAAA,GAAEpJ,QAAAA;AAEJuiB,UAAAA,GAAYjqB,SAAS;QACvB;AAAA,eAAA,eAAAnF,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,4BAAAA,GAAA,eAAAA,GAAA,eAAAA,GAAA,oBAAAA,GAAA,aAAA,QAzFAA,GAAA,aAAA,MAAA;UAAA,cAAA;AACY,iBAAAqvB,eAA8B,CAAA,GAC9B,KAAAC,cAAAA;UAkCZ;UA7BS,UAAAziB;AACLvM,iBAAKgvB,cAAAA;AACL,uBAAWrZ,MAAK3V,KAAK+uB;AACnBpZ,cAAAA,GAAEpJ,QAAAA;AAEJvM,iBAAK+uB,aAAalqB,SAAS;UAC7B;UAOO,SAAgC8Q,IAAAA;AAErC,mBADA3V,KAAK+uB,aAAahe,KAAK4E,EAAAA,GAChBA;UACT;UAOO,WAAkCA,IAAAA;AACvC,kBAAM4C,KAAQvY,KAAK+uB,aAAalX,QAAQlC,EAAAA;AAAAA,mBACpC4C,MACFvY,KAAK+uB,aAAa9W,OAAOM,IAAO,CAAA;UAEpC;QAAA,GAGF7Y,GAAA,oBAAA,MAAA;UAAA,cAAA;AAEU,iBAAAsvB,cAAAA;UAgCV;UA3BE,IAAA,QAAWtqB;AACT,mBAAO1E,KAAKgvB,cAAAA,SAA0BhvB,KAAKivB;UAC7C;UAKA,IAAA,MAAiBvqB,IAAAA;AAAAA,gBAAAA;AACX1E,iBAAKgvB,eAAetqB,OAAU1E,KAAKivB,WAG5B,UAAXvvB,KAAAM,KAAKivB,WAAAA,WAAMvvB,MAAAA,GAAE6M,QAAAA,GACbvM,KAAKivB,SAASvqB;UAChB;UAKO,QAAAuI;AACLjN,iBAAK0E,QAAAA;UACP;UAEO,UAAA6H;AAAAA,gBAAAA;AACLvM,iBAAKgvB,cAAAA,MACM,UAAXvvB,KAAAO,KAAKivB,WAAAA,WAAMxvB,MAAAA,GAAE8M,QAAAA,GACbvM,KAAKivB,SAAAA;UACP;QAAA,GAMFvvB,GAAA,eAAA,SAA6Bsd,IAAAA;AAC3B,iBAAO,EAAEzQ,SAASyQ,GAAAA;QACpB,GAKAtd,GAAA,eAAAC,IAUAD,GAAA,4BAAA,SAA0CwvB,IAAAA;AACxC,iBAAO,EAAE3iB,SAAS,MAAMsiB,GAAaK,EAAAA,EAAAA;QACvC;MAAA,GAAA,KAAA,CAAAzvB,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,YAAA;QCtGA,MAAayvB,GAAAA;UAAb,cAAA;AACU,iBAAAC,QAA8F,CAAC;UAgBzG;UAdS,IAAIC,IAAeC,IAAiB5qB,IAAAA;AACpC1E,iBAAKovB,MAAMC,EAAAA,MACdrvB,KAAKovB,MAAMC,EAAAA,IAAS,CAAC,IAEvBrvB,KAAKovB,MAAMC,EAAAA,EAA2BC,EAAAA,IAAU5qB;UAClD;UAEO,IAAI2qB,IAAeC,IAAAA;AACxB,mBAAOtvB,KAAKovB,MAAMC,EAAAA,IAA4BrvB,KAAKovB,MAAMC,EAAAA,EAA2BC,EAAAA,IAAAA;UACtF;UAEO,QAAAriB;AACLjN,iBAAKovB,QAAQ,CAAC;UAChB;QAAA;AAhBF,QAAA1vB,GAAA,YAAAC,IAmBAD,GAAA,aAAA,MAAA;UAAA,cAAA;AACU,iBAAA0vB,QAAwE,IAAID;UAgBtF;UAdS,IAAIE,IAAeC,IAAiBC,IAAeC,GAAiB9qB,GAAAA;AACpE1E,iBAAKovB,MAAMvf,IAAIwf,IAAOC,EAAAA,KACzBtvB,KAAKovB,MAAM5T,IAAI6T,IAAOC,IAAQ,IAAIH,IAAAA,GAEpCnvB,KAAKovB,MAAMvf,IAAIwf,IAAOC,EAAAA,EAAS9T,IAAI+T,IAAOC,GAAQ9qB,CAAAA;UACpD;UAEO,IAAI2qB,IAAeC,IAAiBC,IAAeC,IAAAA;AAAAA,gBAAAA;AACxD,mBAAoC,UAA7B,IAAAxvB,KAAKovB,MAAMvf,IAAIwf,IAAOC,EAAAA,MAAAA,WAAO,IAAA,SAAA,EAAEzf,IAAI0f,IAAOC,EAAAA;UACnD;UAEO,QAAAviB;AACLjN,iBAAKovB,MAAMniB,MAAAA;UACb;QAAA;MAAA,GAAA,KAAA,CAAAxN,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,aAAAA,GAAA,UAAAA,GAAA,YAAAA,GAAA,WAAAA,GAAA,SAAAA,GAAA,QAAAA,GAAA,mBAAAA,GAAA,WAAAA,GAAA,eAAAA,GAAA,YAAAA,GAAA,SAAA,QCzBWA,GAAAstB,SAA+B,eAAA,OAAdyC;AAC9B,cAAMC,KAAahwB,GAAM,SAAI,SAAS+vB,UAAUC,WAC1CC,KAAYjwB,GAAM,SAAI,SAAS+vB,UAAUE;AAElC,QAAAjwB,GAAA2Q,YAAYqf,GAAUE,SAAS,SAAA,GAC/BlwB,GAAAyZ,eAAeuW,GAAUE,SAAS,MAAA,GAClClwB,GAAA4K,WAAW,iCAAiCulB,KAAKH,EAAAA,GAC9DhwB,GAAA,mBAAA,WAAA;AACE,cAAA,CAAKA,GAAA4K;AACH,mBAAO;AAET,gBAAMwlB,KAAeJ,GAAUxC,MAAM,gBAAA;AACrC,iBAAqB,SAAjB4C,MAAyBA,GAAajrB,SAAS,IAC1C,IAEFoX,SAAS6T,GAAa,CAAA,CAAA;QAC/B,GAKapwB,GAAAqwB,QAAQ,CAAC,aAAa,YAAY,UAAU,QAAA,EAAUH,SAASD,EAAAA,GAC/DjwB,GAAAswB,SAAsB,WAAbL,IACTjwB,GAAAuwB,WAAwB,aAAbN,IACXjwB,GAAAwwB,YAAY,CAAC,WAAW,SAAS,SAAS,OAAA,EAASN,SAASD,EAAAA,GAC5DjwB,GAAAywB,UAAUR,GAAS9X,QAAQ,OAAA,KAAY,GAEvCnY,GAAA0wB,aAAa,WAAWP,KAAKH,EAAAA;MAAAA,GAAAA,KAAAA,CAAAA,IAAAA,IAAAA,OAAAA;AAAAA,eAAAA,eAAAA,IAAAA,cAAAA,EAAAA,OAAAA,KAAAA,CAAAA,GAAAA,GAAAA,oBAAAA,GAAAA,gBAAAA,GAAAA,oBAAAA;ACrC1C,cAAA9vB,KAAAD,GAAA,GAAA;QA2BA,MAAe0wB,EAAAA;UAAf,cAAA;AACU,iBAAAC,SAAmC,CAAA,GAEnC,KAAAC,KAAK;UAkEf;UA7DS,QAAQC,IAAAA;AACbxwB,iBAAKswB,OAAOvf,KAAKyf,EAAAA,GACjBxwB,KAAKywB,OAAAA;UACP;UAEO,QAAAC;AACL,mBAAO1wB,KAAKuwB,KAAKvwB,KAAKswB,OAAOzrB;AACtB7E,mBAAKswB,OAAOtwB,KAAKuwB,EAAAA,EAAAA,KACpBvwB,KAAKuwB;AAGTvwB,iBAAKiN,MAAAA;UACP;UAEO,QAAAA;AACDjN,iBAAK2wB,kBACP3wB,KAAK4wB,gBAAgB5wB,KAAK2wB,aAAAA,GAC1B3wB,KAAK2wB,gBAAAA,SAEP3wB,KAAKuwB,KAAK,GACVvwB,KAAKswB,OAAOzrB,SAAS;UACvB;UAEQ,SAAA4rB;AACDzwB,iBAAK2wB,kBACR3wB,KAAK2wB,gBAAgB3wB,KAAK6wB,iBAAiB7wB,KAAK8wB,SAASxiB,KAAKtO,IAAAA,CAAAA;UAElE;UAEQ,SAAS+wB,IAAAA;AACf/wB,iBAAK2wB,gBAAAA;AACL,gBAAIK,KAAe,GACfC,KAAc,GACdC,KAAwBH,GAASI,cAAAA,GACjCC,KAAoB;AACxB,mBAAOpxB,KAAKuwB,KAAKvwB,KAAKswB,OAAOzrB,UAAQ;AAanC,kBAZAmsB,KAAenX,KAAKC,IAAAA,GACf9Z,KAAKswB,OAAOtwB,KAAKuwB,EAAAA,EAAAA,KACpBvwB,KAAKuwB,MAKPS,KAAelrB,KAAKuM,IAAI,GAAGwH,KAAKC,IAAAA,IAAQkX,EAAAA,GACxCC,KAAcnrB,KAAKuM,IAAI2e,IAAcC,EAAAA,GAGrCG,KAAoBL,GAASI,cAAAA,GACX,MAAdF,KAAoBG;AAOtB,uBAJIF,KAAwBF,KAAAA,OAC1B9T,QAAQmU,KAAK,4CAA4CvrB,KAAKmjB,IAAInjB,KAAKwH,MAAM4jB,KAAwBF,EAAAA,CAAAA,CAAAA,IAAAA,GAAAA,KAEvGhxB,KAAKywB,OAAAA;AAGPS,cAAAA,KAAwBE;YAAAA;AAE1BpxB,iBAAKiN,MAAAA;UACP;QAAA;QAQF,MAAaqkB,UAA0BjB,EAAAA;UAC3B,iBAAiB1c,IAAAA;AACzB,mBAAOnJ,WAAW,MAAMmJ,GAAS3T,KAAKuxB,gBAAgB,EAAA,CAAA,CAAA;UACxD;UAEU,gBAAgBC,IAAAA;AACxB/X,yBAAa+X,EAAAA;UACf;UAEQ,gBAAgBC,IAAAA;AACtB,kBAAM1tB,KAAM8V,KAAKC,IAAAA,IAAQ2X;AACzB,mBAAO,EACLN,eAAe,MAAMrrB,KAAKuM,IAAI,GAAGtO,KAAM8V,KAAKC,IAAAA,CAAAA,EAAAA;UAEhD;QAAA;AAdF,QAAApa,GAAA,oBAAA,GAoCaA,GAAAqhB,gBAAAA,CAAkBnhB,GAAAotB,UAAU,yBAAyBziB,SAnBlE,cAAoC8lB,EAAAA;UACxB,iBAAiB1c,IAAAA;AACzB,mBAAO+d,oBAAoB/d,EAAAA;UAC7B;UAEU,gBAAgB6d,IAAAA;AACxBG,+BAAmBH,EAAAA;UACrB;QAAA,IAYkGF,GAMpG5xB,GAAA,oBAAA,MAAA;UAGE,cAAAmf;AACE7e,iBAAK4xB,SAAS,IAAIlyB,GAAAqhB;UACpB;UAEO,IAAIyP,IAAAA;AACTxwB,iBAAK4xB,OAAO3kB,MAAAA,GACZjN,KAAK4xB,OAAO5Q,QAAQwP,EAAAA;UACtB;UAEO,QAAAE;AACL1wB,iBAAK4xB,OAAOlB,MAAAA;UACd;QAAA;MAAA,GAAA,KAAA,CAAAjxB,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,gBAAAA,GAAA,gBAAA;QC5JF,MAAawV,GAAAA;UAAb,cAAA;AAsBS,iBAAAxM,KAAK,GACL,KAAAD,KAAK,GACL,KAAAmO,WAA2B,IAAIib;UAgGxC;UAvHS,OAAA,WAAkBntB,IAAAA;AACvB,mBAAO,CACLA,OAAU,KAAuB,KACjCA,OAAU,IAAyB,KAC3B,MAARA,EAAAA;UAEJ;UAEO,OAAA,aAAoBA,IAAAA;AACzB,oBAAmB,MAAXA,GAAM,CAAA,MAAa,MAAmC,MAAXA,GAAM,CAAA,MAAa,IAAoC,MAAXA,GAAM,CAAA;UACvG;UAEO,QAAAotB;AACL,kBAAMC,KAAS,IAAI7c;AAInB,mBAHA6c,GAAOrpB,KAAK1I,KAAK0I,IACjBqpB,GAAOtpB,KAAKzI,KAAKyI,IACjBspB,GAAOnb,WAAW5W,KAAK4W,SAASkb,MAAAA,GACzBC;UACT;UAQO,YAAAhd;AAA4B,mBAAiB,WAAV/U,KAAK0I;UAAsB;UAC9D,SAAAoB;AAA4B,mBAAiB,YAAV9J,KAAK0I;UAAmB;UAC3D,cAAAwc;AACL,mBAAIllB,KAAKgyB,iBAAAA,KAAuD,MAAjChyB,KAAK4W,SAASuQ,iBACpC,IAEQ,YAAVnnB,KAAK0I;UACd;UACO,UAAAupB;AAA4B,mBAAiB,YAAVjyB,KAAK0I;UAAoB;UAC5D,cAAAoc;AAA4B,mBAAiB,aAAV9kB,KAAK0I;UAAwB;UAChE,WAAAqB;AAA4B,mBAAiB,WAAV/J,KAAKyI;UAAqB;UAC7D,QAAAsc;AAA4B,mBAAiB,YAAV/kB,KAAKyI;UAAkB;UAC1D,kBAAA2c;AAA4B,mBAAiB,aAAVplB,KAAK0I;UAA4B;UACpE,cAAAwpB;AAA4B,mBAAiB,YAAVlyB,KAAKyI;UAAwB;UAChE,aAAA6c;AAA4B,mBAAiB,aAAVtlB,KAAKyI;UAAuB;UAG/D,iBAAA8c;AAA2B,mBAAiB,WAAVvlB,KAAK0I;UAAyB;UAChE,iBAAA8c;AAA2B,mBAAiB,WAAVxlB,KAAKyI;UAAyB;UAChE,UAAAwM;AAA2B,mBAA0C,aAAxB,WAAVjV,KAAK0I;UAAgD;UACxF,UAAA4M;AAA2B,mBAA0C,aAAxB,WAAVtV,KAAKyI;UAAgD;UACxF,cAAA0pB;AAA2B,mBAA0C,aAAxB,WAAVnyB,KAAK0I,OAAqF,aAAxB,WAAV1I,KAAK0I;UAAiD;UACjJ,cAAA8M;AAA2B,mBAA0C,aAAxB,WAAVxV,KAAKyI,OAAqF,aAAxB,WAAVzI,KAAKyI;UAAiD;UACjJ,cAAAuM;AAA2B,mBAA0C,MAAxB,WAAVhV,KAAK0I;UAAgC;UACxE,cAAA0pB;AAA2B,mBAA0C,MAAxB,WAAVpyB,KAAKyI;UAAgC;UACxE,qBAAA4pB;AAAgC,mBAAmB,MAAZryB,KAAK0I,MAAwB,MAAZ1I,KAAKyI;UAAU;UAGvE,aAAA2M;AACL,oBAAkB,WAAVpV,KAAK0I,IAAAA;cACX,KAAK;cACL,KAAK;AAAqB,uBAAiB,MAAV1I,KAAK0I;cACtC,KAAK;AAAqB,uBAAiB,WAAV1I,KAAK0I;cACtC;AAA0B,uBAAA;YAAQ;UAEtC;UACO,aAAA6M;AACL,oBAAkB,WAAVvV,KAAKyI,IAAAA;cACX,KAAK;cACL,KAAK;AAAqB,uBAAiB,MAAVzI,KAAKyI;cACtC,KAAK;AAAqB,uBAAiB,WAAVzI,KAAKyI;cACtC;AAA0B,uBAAA;YAAQ;UAEtC;UAGO,mBAAAupB;AACL,mBAAiB,YAAVhyB,KAAKyI;UACd;UACO,iBAAA6pB;AACDtyB,iBAAK4W,SAASgR,QAAAA,IAChB5nB,KAAKyI,MAAAA,aAELzI,KAAKyI,MAAM;UAEf;UACO,oBAAAoe;AACL,gBAAe,YAAV7mB,KAAKyI,MAAAA,CAA+BzI,KAAK4W,SAAS2b;AACrD,sBAAuC,WAA/BvyB,KAAK4W,SAAS2b,gBAAAA;gBACpB,KAAK;gBACL,KAAK;AAAqB,yBAAsC,MAA/BvyB,KAAK4W,SAAS2b;gBAC/C,KAAK;AAAqB,yBAAsC,WAA/BvyB,KAAK4W,SAAS2b;gBAC/C;AAA0B,yBAAOvyB,KAAKoV,WAAAA;cAAAA;AAG1C,mBAAOpV,KAAKoV,WAAAA;UACd;UACO,wBAAAod;AACL,mBAAkB,YAAVxyB,KAAKyI,MAAAA,CAA+BzI,KAAK4W,SAAS2b,iBACvB,WAA/BvyB,KAAK4W,SAAS2b,iBACdvyB,KAAKulB,eAAAA;UACX;UACO,sBAAAqB;AACL,mBAAkB,YAAV5mB,KAAKyI,MAAAA,CAA+BzI,KAAK4W,SAAS2b,iBACE,aAAxB,WAA/BvyB,KAAK4W,SAAS2b,kBACfvyB,KAAKiV,QAAAA;UACX;UACO,0BAAAwd;AACL,mBAAkB,YAAVzyB,KAAKyI,MAAAA,CAA+BzI,KAAK4W,SAAS2b,iBACE,aAAxB,WAA/BvyB,KAAK4W,SAAS2b,mBAC8C,aAAxB,WAA/BvyB,KAAK4W,SAAS2b,kBACpBvyB,KAAKmyB,YAAAA;UACX;UACO,0BAAAxL;AACL,mBAAkB,YAAV3mB,KAAKyI,MAAAA,CAA+BzI,KAAK4W,SAAS2b,iBACE,MAAxB,WAA/BvyB,KAAK4W,SAAS2b,kBACfvyB,KAAKgV,YAAAA;UACX;UACO,oBAAA0d;AACL,mBAAiB,YAAV1yB,KAAK0I,KACG,YAAV1I,KAAKyI,KAA4BzI,KAAK4W,SAASuQ,iBAAiB,IACjE;UACN;QAAA;AAvHF,QAAAznB,GAAA,gBAAAC;QA+HA,MAAakyB,GAAAA;UAEX,IAAA,MAAWlpB;AACT,mBAAI3I,KAAK2yB,SAAAA,aAEJ3yB,KAAK4yB,OACL5yB,KAAKmnB,kBAAkB,KAGrBnnB,KAAK4yB;UACd;UACA,IAAA,IAAeluB,IAAAA;AAAiB1E,iBAAK4yB,OAAOluB;UAAO;UAEnD,IAAA,iBAAWyiB;AAET,mBAAInnB,KAAK2yB,SACA,KAEW,YAAZ3yB,KAAK4yB,SAAoC;UACnD;UACA,IAAA,eAA0BluB,IAAAA;AACxB1E,iBAAK4yB,QAAAA,YACL5yB,KAAK4yB,QAASluB,MAAS,KAAM;UAC/B;UAEA,IAAA,iBAAW6tB;AACT,mBAAmB,WAAZvyB,KAAK4yB;UACd;UACA,IAAA,eAA0BluB,IAAAA;AACxB1E,iBAAK4yB,QAAAA,WACL5yB,KAAK4yB,QAAgB,WAARluB;UACf;UAGA,IAAA,QAAWmuB;AACT,mBAAO7yB,KAAK2yB;UACd;UACA,IAAA,MAAiBjuB,IAAAA;AACf1E,iBAAK2yB,SAASjuB;UAChB;UAEA,YACEiE,KAAc,GACdkqB,KAAgB,GAAA;AA1CV,iBAAAD,OAAe,GAgCf,KAAAD,SAAiB,GAYvB3yB,KAAK4yB,OAAOjqB,IACZ3I,KAAK2yB,SAASE;UAChB;UAEO,QAAAf;AACL,mBAAO,IAAID,GAAc7xB,KAAK4yB,MAAM5yB,KAAK2yB,MAAAA;UAC3C;UAMO,UAAA/K;AACL,mBAA+B,MAAxB5nB,KAAKmnB,kBAA0D,MAAhBnnB,KAAK2yB;UAC7D;QAAA;AA3DF,QAAAjzB,GAAA,gBAAAE;MAAA,GAAA,KAAA,CAAAH,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,WAAA;ACjIA,cAAAE,KAAAD,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA,GACA,IAAAA,GAAA,GAAA;QAKA,MAAauO,UAAiB,EAAAgH,cAAAA;UAA9B,cAAA;AAAA,kBAAA,GAAA,SAAA,GAQS,KAAAnF,UAAU,GACV,KAAArH,KAAK,GACL,KAAAD,KAAK,GACL,KAAAmO,WAA2B,IAAI,EAAAib,iBAC/B,KAAA1I,eAAe;UAoExB;UA9ES,OAAA,aAAoBzkB,IAAAA;AACzB,kBAAMouB,KAAM,IAAI5kB;AAEhB,mBADA4kB,GAAIC,gBAAgBruB,EAAAA,GACbouB;UACT;UAQO,aAAAzJ;AACL,mBAAsB,UAAfrpB,KAAK+P;UACd;UAEO,WAAAI;AACL,mBAAOnQ,KAAK+P,WAAW;UACzB;UAEO,WAAAhI;AACL,mBAAmB,UAAf/H,KAAK+P,UACA/P,KAAKmpB,eAEK,UAAfnpB,KAAK+P,WACA,GAAAnQ,GAAAozB,qBAAmC,UAAfhzB,KAAK+P,OAAAA,IAE3B;UACT;UAOO,UAAAlH;AACL,mBAAQ7I,KAAKqpB,WAAAA,IACTrpB,KAAKmpB,aAAanD,WAAWhmB,KAAKmpB,aAAatkB,SAAS,CAAA,IACzC,UAAf7E,KAAK+P;UACX;UAEO,gBAAgBrL,IAAAA;AACrB1E,iBAAK0I,KAAKhE,GAAM,EAAAuuB,oBAAAA,GAChBjzB,KAAKyI,KAAK;AACV,gBAAIyqB,KAAAA;AAEJ,gBAAIxuB,GAAM,EAAAyuB,oBAAAA,EAAsBtuB,SAAS;AACvCquB,cAAAA,KAAAA;qBAE8C,MAAvCxuB,GAAM,EAAAyuB,oBAAAA,EAAsBtuB,QAAc;AACjD,oBAAMge,KAAOne,GAAM,EAAAyuB,oBAAAA,EAAsBnN,WAAW,CAAA;AAGpD,kBAAI,SAAUnD,MAAQA,MAAQ,OAAQ;AACpC,sBAAMyM,KAAS5qB,GAAM,EAAAyuB,oBAAAA,EAAsBnN,WAAW,CAAA;AAClD,yBAAUsJ,MAAUA,MAAU,QAChCtvB,KAAK+P,UAA6B,QAAjB8S,KAAO,SAAkByM,KAAS,QAAS,QAAY5qB,GAAM,EAAA0uB,qBAAAA,KAA0B,KAGxGF,KAAAA;cAAW;AAIbA,gBAAAA,KAAAA;YAAW;AAIblzB,mBAAK+P,UAAUrL,GAAM,EAAAyuB,oBAAAA,EAAsBnN,WAAW,CAAA,IAAMthB,GAAM,EAAA0uB,qBAAAA,KAA0B;AAE1FF,YAAAA,OACFlzB,KAAKmpB,eAAezkB,GAAM,EAAAyuB,oBAAAA,GAC1BnzB,KAAK+P,UAAU,UAA4BrL,GAAM,EAAA0uB,qBAAAA,KAA0B;UAE/E;UAEO,gBAAA9J;AACL,mBAAO,CAACtpB,KAAK0I,IAAI1I,KAAK+H,SAAAA,GAAY/H,KAAKmQ,SAAAA,GAAYnQ,KAAK6I,QAAAA,CAAAA;UAC1D;QAAA;AA/EF,QAAAnJ,GAAA,WAAA;MAAA,GAAA,KAAA,CAAAD,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,uBAAAA,GAAA,iBAAAA,GAAA,kBAAAA,GAAA,iBAAAA,GAAA,uBAAAA,GAAA,wBAAAA,GAAA,uBAAAA,GAAA,uBAAAA,GAAA,cAAAA,GAAA,eAAAA,GAAA,gBAAA,QCRaA,GAAAuhB,gBAAgB,GAChBvhB,GAAA2zB,eAAe,MAAa3zB,GAAAuhB,iBAAiB,GAC7CvhB,GAAAwhB,cAAc,GAEdxhB,GAAAuzB,uBAAuB,GACvBvzB,GAAAyzB,uBAAuB,GACvBzzB,GAAA0zB,wBAAwB,GACxB1zB,GAAA4zB,uBAAuB,GAOvB5zB,GAAA6zB,iBAAiB,IACjB7zB,GAAA8zB,kBAAkB,GAClB9zB,GAAA8U,iBAAiB,GAOjB9U,GAAA8qB,uBAAuB,KACvB9qB,GAAA+zB,wBAAwB,GACxB/zB,GAAAoJ,uBAAuB;MAAA,GAAA,KAAA,CAAArJ,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,cAAAA,GAAA,gBAAAA,GAAA,gBAAAA,GAAA,sBAAA,QClBpCA,GAAA,sBAAA,SAAoCg0B,IAAAA;AAClC,iBAAIA,KAAY,SACdA,MAAa,OACNhP,OAAOC,aAAiC,SAAnB+O,MAAa,GAAA,IAAgBhP,OAAOC,aAAc+O,KAAY,OAAS,KAAA,KAE9FhP,OAAOC,aAAa+O,EAAAA;QAC7B,GAOAh0B,GAAA,gBAAA,SAA8B4c,IAAmBxY,KAAgB,GAAGC,KAAcuY,GAAKzX,QAAAA;AACrF,cAAI2D,KAAS;AACb,mBAAS7I,IAAImE,IAAOnE,IAAIoE,IAAAA,EAAOpE,GAAG;AAChC,gBAAIgf,KAAYrC,GAAK3c,CAAAA;AACjBgf,YAAAA,KAAY,SAMdA,MAAa,OACbnW,MAAUkc,OAAOC,aAAiC,SAAnBhG,MAAa,GAAA,IAAgB+F,OAAOC,aAAchG,KAAY,OAAS,KAAA,KAEtGnW,MAAUkc,OAAOC,aAAahG,EAAAA;UAAAA;AAGlC,iBAAOnW;QACT,GAMA9I,GAAA,gBAAA,MAAA;UAAA,cAAA;AACU,iBAAAi0B,WAAmB;UAkE7B;UA7DS,QAAA1mB;AACLjN,iBAAK2zB,WAAW;UAClB;UAUO,OAAOC,IAAexV,IAAAA;AAC3B,kBAAMvZ,KAAS+uB,GAAM/uB;AAErB,gBAAA,CAAKA;AACH,qBAAO;AAGT,gBAAIkE,KAAO,GACP8qB,IAAW;AAGf,gBAAI7zB,KAAK2zB,UAAU;AACjB,oBAAMrE,KAASsE,GAAM5N,WAAW6N,GAAAA;AAC5B,uBAAUvE,MAAUA,MAAU,QAChClR,GAAOrV,IAAAA,IAAqC,QAA1B/I,KAAK2zB,WAAW,SAAkBrE,KAAS,QAAS,SAGtElR,GAAOrV,IAAAA,IAAU/I,KAAK2zB,UACtBvV,GAAOrV,IAAAA,IAAUumB,KAEnBtvB,KAAK2zB,WAAW;YAAA;AAGlB,qBAASh0B,IAAIk0B,GAAUl0B,IAAIkF,IAAAA,EAAUlF,GAAG;AACtC,oBAAMkjB,KAAO+Q,GAAM5N,WAAWrmB,CAAAA;AAE9B,kBAAI,SAAUkjB,MAAQA,MAAQ,OAA9B;AACE,oBAAA,EAAMljB,KAAKkF;AAET,yBADA7E,KAAK2zB,WAAW9Q,IACT9Z;AAET,sBAAMumB,IAASsE,GAAM5N,WAAWrmB,CAAAA;AAC5B,yBAAU2vB,KAAUA,KAAU,QAChClR,GAAOrV,IAAAA,IAA4B,QAAjB8Z,KAAO,SAAkByM,IAAS,QAAS,SAG7DlR,GAAOrV,IAAAA,IAAU8Z,IACjBzE,GAAOrV,IAAAA,IAAUumB;cAAAA;AAIR,0BAATzM,OAIJzE,GAAOrV,IAAAA,IAAU8Z;YAAAA;AAEnB,mBAAO9Z;UACT;QAAA,GAMFrJ,GAAA,cAAA,MAAA;UAAA,cAAA;AACS,iBAAAo0B,UAAsB,IAAIC,WAAW,CAAA;UAgO9C;UA3NS,QAAA9mB;AACLjN,iBAAK8zB,QAAQpW,KAAK,CAAA;UACpB;UAUO,OAAOkW,IAAmBxV,IAAAA;AAC/B,kBAAMvZ,KAAS+uB,GAAM/uB;AAErB,gBAAA,CAAKA;AACH,qBAAO;AAGT,gBACImvB,IACAC,GACAC,GACAC,GAJAprB,IAAO,GAKP4V,IAAY,GACZkV,IAAW;AAGf,gBAAI7zB,KAAK8zB,QAAQ,CAAA,GAAI;AACnB,kBAAIM,KAAAA,OACAC,KAAKr0B,KAAK8zB,QAAQ,CAAA;AACtBO,cAAAA,MAAyB,QAAV,MAALA,MAAwB,KAAyB,QAAV,MAALA,MAAwB,KAAO;AAC3E,kBACIC,IADAC,KAAM;AAEV,sBAAQD,KAA4B,KAAtBt0B,KAAK8zB,QAAAA,EAAUS,EAAAA,MAAgBA,KAAM;AACjDF,gBAAAA,OAAO,GACPA,MAAMC;AAGR,oBAAM5Z,KAAsC,QAAV,MAAlB1a,KAAK8zB,QAAQ,CAAA,KAAwB,IAAmC,QAAV,MAAlB9zB,KAAK8zB,QAAQ,CAAA,KAAwB,IAAI,GAC/FU,KAAU9Z,KAAO6Z;AACvB,qBAAOV,IAAWW,MAAS;AACzB,oBAAIX,KAAYhvB;AACd,yBAAO;AAGT,oBADAyvB,KAAMV,GAAMC,GAAAA,GACS,QAAV,MAANS,KAAsB;AAEzBT,uBACAO,KAAAA;AACA;gBAAA;AAGAp0B,qBAAK8zB,QAAQS,IAAAA,IAASD,IACtBD,OAAO,GACPA,MAAY,KAANC;cAAAA;AAGLF,cAAAA,OAEU,MAAT1Z,KACE2Z,KAAK,MAEPR,MAEAzV,GAAOrV,GAAAA,IAAUsrB,KAED,MAAT3Z,KACL2Z,KAAK,QAAWA,MAAM,SAAUA,MAAM,SAAkB,UAAPA,OAGnDjW,GAAOrV,GAAAA,IAAUsrB,MAGfA,KAAK,SAAYA,KAAK,YAGxBjW,GAAOrV,GAAAA,IAAUsrB,MAIvBr0B,KAAK8zB,QAAQpW,KAAK,CAAA;YAAA;AAIpB,kBAAM+W,IAAW5vB,KAAS;AAC1B,gBAAIlF,IAAIk0B;AACR,mBAAOl0B,IAAIkF,MAAQ;AAejB,qBAAA,EAAA,EAAOlF,IAAI80B,MACiB,OAApBT,KAAQJ,GAAMj0B,CAAAA,MACU,OAAxBs0B,IAAQL,GAAMj0B,IAAI,CAAA,MACM,OAAxBu0B,IAAQN,GAAMj0B,IAAI,CAAA,MACM,OAAxBw0B,IAAQP,GAAMj0B,IAAI,CAAA;AAExBye,gBAAAA,GAAOrV,GAAAA,IAAUirB,IACjB5V,GAAOrV,GAAAA,IAAUkrB,GACjB7V,GAAOrV,GAAAA,IAAUmrB,GACjB9V,GAAOrV,GAAAA,IAAUorB,GACjBx0B,KAAK;AAOP,kBAHAq0B,KAAQJ,GAAMj0B,GAAAA,GAGVq0B,KAAQ;AACV5V,gBAAAA,GAAOrV,GAAAA,IAAUirB;uBAGW,QAAV,MAARA,KAAwB;AAClC,oBAAIr0B,KAAKkF;AAEP,yBADA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IACXjrB;AAGT,oBADAkrB,IAAQL,GAAMj0B,GAAAA,GACS,QAAV,MAARs0B,IAAwB;AAE3Bt0B;AACA;gBAAA;AAGF,oBADAgf,KAAqB,KAARqV,OAAiB,IAAa,KAARC,GAC/BtV,IAAY,KAAM;AAEpBhf;AACA;gBAAA;AAEFye,gBAAAA,GAAOrV,GAAAA,IAAU4V;cAAAA,WAGW,QAAV,MAARqV,KAAwB;AAClC,oBAAIr0B,KAAKkF;AAEP,yBADA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IACXjrB;AAGT,oBADAkrB,IAAQL,GAAMj0B,GAAAA,GACS,QAAV,MAARs0B,IAAwB;AAE3Bt0B;AACA;gBAAA;AAEF,oBAAIA,KAAKkF;AAGP,yBAFA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IAClBh0B,KAAK8zB,QAAQ,CAAA,IAAKG,GACXlrB;AAGT,oBADAmrB,IAAQN,GAAMj0B,GAAAA,GACS,QAAV,MAARu0B,IAAwB;AAE3Bv0B;AACA;gBAAA;AAGF,oBADAgf,KAAqB,KAARqV,OAAiB,MAAc,KAARC,MAAiB,IAAa,KAARC,GACtDvV,IAAY,QAAWA,KAAa,SAAUA,KAAa,SAAyB,UAAdA;AAExE;AAEFP,gBAAAA,GAAOrV,GAAAA,IAAU4V;cAAAA,WAGW,QAAV,MAARqV,KAAwB;AAClC,oBAAIr0B,KAAKkF;AAEP,yBADA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IACXjrB;AAGT,oBADAkrB,IAAQL,GAAMj0B,GAAAA,GACS,QAAV,MAARs0B,IAAwB;AAE3Bt0B;AACA;gBAAA;AAEF,oBAAIA,KAAKkF;AAGP,yBAFA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IAClBh0B,KAAK8zB,QAAQ,CAAA,IAAKG,GACXlrB;AAGT,oBADAmrB,IAAQN,GAAMj0B,GAAAA,GACS,QAAV,MAARu0B,IAAwB;AAE3Bv0B;AACA;gBAAA;AAEF,oBAAIA,KAAKkF;AAIP,yBAHA7E,KAAK8zB,QAAQ,CAAA,IAAKE,IAClBh0B,KAAK8zB,QAAQ,CAAA,IAAKG,GAClBj0B,KAAK8zB,QAAQ,CAAA,IAAKI,GACXnrB;AAGT,oBADAorB,IAAQP,GAAMj0B,GAAAA,GACS,QAAV,MAARw0B,IAAwB;AAE3Bx0B;AACA;gBAAA;AAGF,oBADAgf,KAAqB,IAARqV,OAAiB,MAAc,KAARC,MAAiB,MAAc,KAARC,MAAiB,IAAa,KAARC,GAC7ExV,IAAY,SAAYA,IAAY;AAEtC;AAEFP,gBAAAA,GAAOrV,GAAAA,IAAU4V;cAAAA;YAAAA;AAKrB,mBAAO5V;UACT;QAAA;MAAA,GAAA,KAAA,SAAAtJ,IAAAC,IAAAC,IAAA;AAAA,YAAAC,KAAA,QAAA,KAAA,cAAA,SAAAH,IAAAC,IAAAC,IAAAC,IAAA;AAAA,cAAAqJ,IAAAI,KAAA,UAAA,QAAAG,KAAAH,KAAA,IAAA3J,KAAA,SAAAE,KAAAA,KAAA,OAAA,yBAAAF,IAAAC,EAAA,IAAAC;AAAA,cAAA,YAAA,OAAA,WAAA,cAAA,OAAA,QAAA;AAAA,YAAA4J,KAAA,QAAA,SAAA/J,IAAAC,IAAAC,IAAAC,EAAA;;AAAA,qBAAAmZ,KAAAtZ,GAAA,SAAA,GAAAsZ,MAAA,GAAAA;AAAA,eAAA9P,KAAAxJ,GAAAsZ,EAAA,OAAAvP,MAAAH,KAAA,IAAAJ,GAAAO,EAAA,IAAAH,KAAA,IAAAJ,GAAAvJ,IAAAC,IAAA6J,EAAA,IAAAP,GAAAvJ,IAAAC,EAAA,MAAA6J;AAAA,iBAAAH,KAAA,KAAAG,MAAA,OAAA,eAAA9J,IAAAC,IAAA6J,EAAA,GAAAA;QAAA,GAAA,IAAA,QAAA,KAAA,WAAA,SAAA/J,IAAAC,IAAA;AAAA,iBAAA,SAAAC,IAAAC,IAAA;AAAA,YAAAF,GAAAC,IAAAC,IAAAH,EAAA;UAAA;QAAA;AAAA,eAAA,eAAAC,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,YAAAA,GAAA,iBAAAA,GAAA,aAAA;ACnVF,cAAA,IAAAC,GAAA,GAAA,GACA,IAAAA,GAAA,EAAA,GAgBM+0B,IAAwD,EAC5DC,OAAO,EAAAC,aAAaC,OACpBC,OAAO,EAAAF,aAAaG,OACpBC,MAAM,EAAAJ,aAAaK,MACnB5D,MAAM,EAAAuD,aAAaM,MACnB/X,OAAO,EAAAyX,aAAaO,OACpBC,KAAK,EAAAR,aAAaS,IAAAA;AAKb,YAiEHC,GAjESC,IAAU71B,GAAA,aAAhB,cAAyB,EAAAI,WAAAA;UAI9B,IAAA,WAAW01B;AAA2B,mBAAOx1B,KAAKy1B;UAAW;UAE7D,YACmBh2B,IAAA;AAEjBsB,kBAAAA,GAFkC,KAAAH,kBAAAA,IAJ5B,KAAA60B,YAA0B,EAAAb,aAAaS,KAO7Cr1B,KAAK01B,gBAAAA,GACL11B,KAAK0B,SAAS1B,KAAKY,gBAAgB0S,uBAAuB,YAAY,MAAMtT,KAAK01B,gBAAAA,CAAAA,CAAAA,GAGjFJ,IAAct1B;UAChB;UAEQ,kBAAA01B;AACN11B,iBAAKy1B,YAAYf,EAAqB10B,KAAKY,gBAAgB4D,WAAWgxB,QAAAA;UACxE;UAEQ,wBAAwBG,IAAAA;AAC9B,qBAASh2B,KAAI,GAAGA,KAAIg2B,GAAe9wB,QAAQlF;AACR,4BAAA,OAAtBg2B,GAAeh2B,EAAAA,MACxBg2B,GAAeh2B,EAAAA,IAAKg2B,GAAeh2B,EAAAA,EAAAA;UAGzC;UAEQ,KAAK+a,IAAekb,IAAiBD,IAAAA;AAC3C31B,iBAAK61B,wBAAwBF,EAAAA,GAC7Bjb,GAAK+T,KAAKvR,UAAUld,KAAKY,gBAAgBgV,QAAQkgB,SAAS,KAjC3C,gBAiC8DF,IAAAA,GAAYD,EAAAA;UAC3F;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B31B,iBAAKy1B,aAAa,EAAAb,aAAaC,SACjC70B,KAAK+1B,KAAyF,UAApFn2B,KAAmC,UAAnCD,KAAAK,KAAKY,gBAAgBgV,QAAQkgB,WAAAA,WAAMn2B,KAAA,SAAAA,GAAEg1B,MAAMrmB,KAAKtO,KAAKY,gBAAgBgV,QAAQkgB,MAAAA,MAAAA,WAAOl2B,KAAAA,KAAIsd,QAAQ8Y,KAAKJ,IAASD,EAAAA;UAE5H;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B31B,iBAAKy1B,aAAa,EAAAb,aAAaG,SACjC/0B,KAAK+1B,KAAyF,UAApFn2B,KAAmC,UAAnCD,KAAAK,KAAKY,gBAAgBgV,QAAQkgB,WAAAA,WAAMn2B,KAAA,SAAAA,GAAEm1B,MAAMxmB,KAAKtO,KAAKY,gBAAgBgV,QAAQkgB,MAAAA,MAAAA,WAAOl2B,KAAAA,KAAIsd,QAAQ8Y,KAAKJ,IAASD,EAAAA;UAE5H;UAEO,KAAKC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC1B31B,iBAAKy1B,aAAa,EAAAb,aAAaK,QACjCj1B,KAAK+1B,KAAwF,UAAnFn2B,KAAmC,UAAnCD,KAAAK,KAAKY,gBAAgBgV,QAAQkgB,WAAAA,WAAMn2B,KAAA,SAAAA,GAAEq1B,KAAK1mB,KAAKtO,KAAKY,gBAAgBgV,QAAQkgB,MAAAA,MAAAA,WAAOl2B,KAAAA,KAAIsd,QAAQ8X,MAAMY,IAASD,EAAAA;UAE5H;UAEO,KAAKC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC1B31B,iBAAKy1B,aAAa,EAAAb,aAAaM,QACjCl1B,KAAK+1B,KAAwF,UAAnFn2B,KAAmC,UAAnCD,KAAAK,KAAKY,gBAAgBgV,QAAQkgB,WAAAA,WAAMn2B,KAAA,SAAAA,GAAE0xB,KAAK/iB,KAAKtO,KAAKY,gBAAgBgV,QAAQkgB,MAAAA,MAAAA,WAAOl2B,KAAAA,KAAIsd,QAAQmU,MAAMuE,IAASD,EAAAA;UAE5H;UAEO,MAAMC,OAAoBD,IAAAA;AAAAA,gBAAAA,IAAAA;AAC3B31B,iBAAKy1B,aAAa,EAAAb,aAAaO,SACjCn1B,KAAK+1B,KAAyF,UAApFn2B,KAAmC,UAAnCD,KAAAK,KAAKY,gBAAgBgV,QAAQkgB,WAAAA,WAAMn2B,KAAA,SAAAA,GAAEwd,MAAM7O,KAAKtO,KAAKY,gBAAgBgV,QAAQkgB,MAAAA,MAAAA,WAAOl2B,KAAAA,KAAIsd,QAAQC,OAAOyY,IAASD,EAAAA;UAE9H;QAAA;AAAA,QAAAj2B,GAAA,aA9DW61B,IAAU31B,GAAA,CAOlB,EAAA,GAAA,EAAAq2B,eAAAA,CAAAA,GAPQV,CAAAA,GAkEb71B,GAAA,iBAAA,SAA+Bo2B,IAAAA;AAC7BR,cAAcQ;QAChB,GAKAp2B,GAAA,YAAA,SAA0Bw2B,IAAcnT,IAAaoT,IAAAA;AACnD,cAAgC,cAAA,OAArBA,GAAWzxB;AACpB,kBAAM,IAAI+W,MAAM,eAAA;AAElB,gBACM2a,KAAKD,GAAWzxB;AACtByxB,UAAAA,GAAgB,QAAI,YAAa/Y,IAAAA;AAE/B,gBAAIkY,EAAYE,aAAa,EAAAZ,aAAaC;AACxC,qBAAOuB,GAAGC,MAAMr2B,MAAMod,EAAAA;AAGxBkY,cAAYX,MAAM,iBAAiByB,GAAGE,IAAAA,IAAQlZ,GAAKhB,IAAI3c,CAAAA,OAAK82B,KAAKC,UAAU/2B,EAAAA,CAAAA,EAAI4V,KAAK,IAAA,CAAA,GAAA;AACpF,kBAAM7M,KAAS4tB,GAAGC,MAAMr2B,MAAMod,EAAAA;AAE9B,mBADAkY,EAAYX,MAAM,iBAAiByB,GAAGE,IAAAA,WAAe9tB,EAAAA,GAC9CA;UACT;QACF;MAAA,GAAA,KAAA,CAAA/I,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAAA,GAAA,yBAAAA,GAAA,kBAAA;AC9GA,cAAM+2B,KAAY,aACZC,KAAkB;AAEX,QAAAh3B,GAAAi3B,kBAAwD,oBAAIpb,OAEzE7b,GAAA,yBAAA,SAAuCk3B,IAAAA;AACrC,iBAAOA,GAAKF,EAAAA,KAAoB,CAAA;QAClC,GAEAh3B,GAAA,kBAAA,SAAmCa,IAAAA;AACjC,cAAIb,GAAAi3B,gBAAgBE,IAAIt2B,EAAAA;AACtB,mBAAOb,GAAAi3B,gBAAgB9mB,IAAItP,EAAAA;AAG7B,gBAAMu2B,IAAiB,SAAU1Y,IAAkB2E,IAAaxK,GAAAA;AAC9D,gBAAyB,MAArBwe,UAAUlyB;AACZ,oBAAM,IAAI4W,MAAM,kEAAA;AAAA,aAYtB,SAAgClb,IAAc6d,IAAkB7F,IAAAA;AACzD6F,cAAAA,GAAeqY,EAAAA,MAAerY,KAChCA,GAAesY,EAAAA,EAAiB3lB,KAAK,EAAExQ,IAAAA,IAAIgY,OAAAA,GAAAA,CAAAA,KAE3C6F,GAAesY,EAAAA,IAAmB,CAAC,EAAEn2B,IAAAA,IAAIgY,OAAAA,GAAAA,CAAAA,GACzC6F,GAAeqY,EAAAA,IAAarY;YAEjC,EAhB2B0Y,GAAW1Y,IAAQ7F,CAAAA;UAC5C;AAKA,iBAHAue,EAAUv0B,WAAW,MAAMhC,IAE3Bb,GAAAi3B,gBAAgBnb,IAAIjb,IAAIu2B,CAAAA,GACjBA;QACT;MAAA,GAAA,IAAA,CAAAr3B,IAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAD,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,qBAAAA,GAAA,kBAAAA,GAAA,kBAAAA,GAAA,kBAAAA,GAAA,cAAAA,GAAA,eAAAA,GAAA,wBAAAA,GAAA,kBAAAA,GAAA,eAAAA,GAAA,oBAAAA,GAAA,iBAAA;AC/BA,cAAAE,KAAAD,GAAA,GAAA;AAuIA,YAAYi1B;AApIC,QAAAl1B,GAAA6rB,kBAAiB,GAAA3rB,GAAAo3B,iBAAgC,eAAA,GAiBjDt3B,GAAAu3B,qBAAoB,GAAAr3B,GAAAo3B,iBAAmC,kBAAA,GAgCvDt3B,GAAAw3B,gBAAe,GAAAt3B,GAAAo3B,iBAA8B,aAAA,GAsC7Ct3B,GAAAy3B,mBAAkB,GAAAv3B,GAAAo3B,iBAAiC,gBAAA,GAoCnDt3B,GAAA03B,yBAAwB,GAAAx3B,GAAAo3B,iBAAuC,sBAAA,GAS5E,SAAYpC,IAAAA;AACV,UAAAn1B,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,OAAA,CAAA,IAAA,QACAA,GAAAA,GAAA,OAAA,CAAA,IAAA,QACAA,GAAAA,GAAA,QAAA,CAAA,IAAA,SACAA,GAAAA,GAAA,MAAA,CAAA,IAAA;QACD,EAPWm1B,MAAYl1B,GAAA,eAAZk1B,IAAY,CAAA,EAAA,GASXl1B,GAAA23B,eAAc,GAAAz3B,GAAAo3B,iBAA6B,YAAA,GAa3Ct3B,GAAAu2B,mBAAkB,GAAAr2B,GAAAo3B,iBAAiC,gBAAA,GAqHnDt3B,GAAA43B,mBAAkB,GAAA13B,GAAAo3B,iBAAiC,gBAAA,GAgBnDt3B,GAAA63B,mBAAkB,GAAA33B,GAAAo3B,iBAAiC,gBAAA,GAwBnDt3B,GAAA83B,sBAAqB,GAAA53B,GAAAo3B,iBAAoC,mBAAA;MAAA,EAAA,GCjUlES,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC;AACH,iBAAOA,EAAax4B;AAGrB,YAAIC,IAASo4B,EAAyBE,EAAAA,IAAY,EAGjDv4B,SAAS,CAAC,EAAA;AAOX,eAHAy4B,EAAoBF,EAAAA,EAAUlJ,KAAKpvB,EAAOD,SAASC,GAAQA,EAAOD,SAASs4B,CAAAA,GAGpEr4B,EAAOD;MACf;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAAK,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,cAAA;ACfA,cAAAC,KAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA,GAGA,IAAA,EAAA,GAAA;QAEA,MAAao4B,UAAoB,EAAAh4B,WAAAA;UAAjC,cAAA;AAAA,kBAAA,GAAA,SAAA,GAImB,KAAAuL,wBAAwBrL,KAAK0B,SAAS,IAAIhC,GAAAmC,cAAAA,GAC3C,KAAAyJ,uBAAuBtL,KAAKqL,sBAAsBtJ,OACjD,KAAAH,2BAA2B5B,KAAK0B,SAAS,IAAIhC,GAAAmC,cAAAA,GAC9C,KAAAC,0BAA0B9B,KAAK4B,yBAAyBG;UA8C1E;UA5CE,IAAA,eAAW0K;AAAAA,gBAAAA;AACT,mBAAqB,UAAdhN,KAAAO,KAAK+3B,cAAAA,WAASt4B,KAAA,SAAAA,GAAEgN;UACzB;UAEO,SAASgB,IAAAA;AACd,kBAAMyK,KAAQzK,GAAiB0K;AAC/B,gBAAA,CAAK1K,GAASsQ;AAEZ,qBAAA,KADA/d,KAAK0B,SAASwW,GAAK8f,WAAW,MAAMh4B,KAAKi4B,SAASxqB,EAAAA,CAAAA,CAAAA;AAIpDzN,iBAAKK,YAAYoN;AACjB,kBAAMxC,KAAciN,GAAKjN,aACnB2C,KAAiBsK,GAAKtK,gBACtBsqB,IAAgBhgB,GAAKggB,eACrBC,IAAYjgB,GAAKpN,YAEjBstB,IAAalgB,IACbvK,IAAgCyqB,EAAWz3B,gBAC3C03B,IAAgCD,EAAWE,gBAC3CttB,IAAkDotB,EAAWplB,yBAC7DulB,IAAoCH,EAAWrtB,kBAC/C+C,IAA0CsqB,EAAWt3B,qBACrDoK,IAAwCktB,EAAWv3B,oBACnD23B,IAA0BJ,EAAWK,aACrC1qB,IAA8BqqB,EAAW13B;AAAAA,aAI/C,GAAA,EAAAg4B,gBAAeF,CAAAA,GAEfx4B,KAAK+3B,YAAY,IAAI,EAAAntB,eAAe6C,IAAUyqB,GAAeC,GAAWxqB,GAAe4qB,GAAiB3qB,IAAgB5C,GAAwBC,IAAa6C,GAAoB5C,GAAmB6C,CAAAA,GACpM/N,KAAK0B,UAAS,GAAAhC,GAAAiF,cAAa3E,KAAK+3B,UAAUzsB,sBAAsBtL,KAAKqL,qBAAAA,CAAAA,GACrErL,KAAK0B,UAAS,GAAAhC,GAAAiF,cAAa3E,KAAK+3B,UAAUj2B,yBAAyB9B,KAAK4B,wBAAAA,CAAAA,GACxEy2B,EAAcM,YAAY34B,KAAK+3B,SAAAA,GAC/BM,EAAc1rB,aAAagB,EAAc/D,MAAM+D,EAActJ,IAAAA,GAE7DrE,KAAK0B,UAAS,GAAA,EAAAwB,cAAa,MAAA;AAAA,kBAAAxD;AACzB24B,gBAAcM,YAAa34B,KAAKK,UAAkB8X,MAAMygB,gBAAAA,CAAAA,GACxDP,EAAc1rB,aAAac,GAAS7D,MAAM6D,GAASpJ,IAAAA,GACrC,UAAd3E,KAAAM,KAAK+3B,cAAAA,WAASr4B,MAAAA,GAAE6M,QAAAA,GAChBvM,KAAK+3B,YAAAA;YAAqB,CAAA,CAAA;UAE9B;QAAA;AApDF,QAAAt4B,GAAA,cAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "e", "t", "i", "s", "BaseRender<PERSON><PERSON>er", "Disposable", "canvas", "this", "_canvas", "cacheCanvas", "_char<PERSON><PERSON>as", "pages", "_terminal", "_container", "id", "zIndex", "_alpha", "_themeService", "_bufferService", "_optionsService", "_decorationService", "_coreBrowserService", "super", "_deviceCharWidth", "_deviceCharHeight", "_device<PERSON>ell<PERSON>idth", "_deviceCellHeight", "_deviceCharLeft", "_deviceCharTop", "_selectionModel", "createSelectionRenderModel", "_bitmapGenerator", "_charAtlasDisposable", "register", "MutableDisposable", "_onAddTextureAtlasCanvas", "EventEmitter", "onAddTextureAtlasCanvas", "event", "_cellColorResolver", "CellColorResolver", "document", "createElement", "classList", "add", "style", "toString", "_initCanvas", "append<PERSON><PERSON><PERSON>", "_refreshCharAtlas", "colors", "onChangeColors", "reset", "handleSelectionChanged", "selectionStart", "selectionEnd", "columnSelectMode", "toDisposable", "remove", "_ctx", "throwIfFalsy", "getContext", "alpha", "_clearAll", "handleBlur", "handleFocus", "handleCursorMove", "startRow", "endRow", "start", "end", "update", "oldCanvas", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "handleGridChanged", "rows", "colorSet", "acquireTextureAtlas", "rawOptions", "dpr", "value", "forwardEvent", "warmUp", "length", "BitmapGenerator", "dim", "device", "cell", "width", "height", "char", "left", "top", "css", "clearTextureAtlas", "clearTexture", "x", "y", "fillRect", "cellOffset", "Math", "ceil", "pixelOffset", "save", "beginPath", "strokeStyle", "fillStyle", "lineWidth", "xOffset", "xLeft", "xMid", "xRight", "yMid", "yMidBot", "yMidTop", "moveTo", "bezierCurveTo", "stroke", "restore", "setLineDash", "lineTo", "closePath", "strokeRect", "clearRect", "background", "font", "_getFont", "textBaseline", "TEXT_BASELINE", "_clipRow", "drawSuccess", "customGlyphs", "tryDrawCustomChar", "getChars", "fontSize", "fillText", "chars", "resolve", "buffer", "ydisp", "glyph", "getRasterizedGlyphCombinedChar", "result", "bg", "fg", "ext", "getRasterizedGlyph", "getCode", "WHITESPACE_CELL_CODE", "size", "texturePage", "r", "bitmap", "close", "version", "o", "refresh", "drawImage", "n", "texturePosition", "offset", "rect", "cols", "clip", "isBold", "isItalic", "fontWeightBold", "fontWeight", "fontFamily", "_bitmap", "_state", "_commitTimeout", "<PERSON><PERSON><PERSON><PERSON>", "window", "setTimeout", "_generate", "createImageBitmap", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_screenElement", "linkifier2", "_charSizeService", "characterJoinerService", "coreService", "decorationService", "_onRequestRedraw", "onRequestRedraw", "_onChangeTextureAtlas", "onChangeTextureAtlas", "allowTransparency", "_renderLayers", "TextRender<PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layer", "dimensions", "createRenderDimensions", "_devicePixelRatio", "_updateDimensions", "observeDevicePixelDimensions", "w", "h", "_setCanvasDevicePixelDimensions", "l", "dispose", "removeTerminalFromCache", "textureAtlas", "handleDevicePixelRatioChange", "handleResize", "resize", "handleCharSizeChanged", "_runOperation", "selectionForeground", "fire", "clear", "operation", "hasValidSize", "floor", "lineHeight", "round", "letterSpacing", "_requestRedrawViewport", "terminal", "container", "bufferService", "optionsService", "_coreService", "coreBrowserService", "themeService", "_cursorBlinkStateManager", "_cell", "CellData", "isFocused", "_cursor<PERSON><PERSON>ers", "_renderBarCursor", "bind", "_renderBlockCursor", "_renderUnderlineCursor", "_renderOutlineCursor", "onOptionChange", "_handleOptionsChanged", "_clearCursor", "restartBlinkAnimation", "pause", "resume", "cursorBlink", "CursorBlinkStateManager", "_render", "isPaused", "triggeredByAnimationFrame", "isCursorInitialized", "isCursorHidden", "cursorY", "ybase", "viewportRelativeCursorY", "cursorX", "min", "lines", "get", "loadCell", "content", "cursor", "cursorStyle", "cursorInactiveStyle", "getWidth", "isCursorVisible", "isFirefox", "_clearCells", "_fillLeftLineAtCell", "cursor<PERSON><PERSON><PERSON>", "_fill<PERSON>ells", "cursorAccent", "_fillCharTrueColor", "_fillBottomLineAtCells", "_strokeRectAtCell", "cache", "push", "undefined", "onShowLinkUnderline", "_handleShowLinkUnderline", "onHideLinkUnderline", "_handleHideLinkUnderline", "_clearCurrentLink", "x1", "y1", "middleRowCount", "y2", "x2", "INVERTED_DEFAULT_COLOR", "is256Color", "ansi", "foreground", "_clearState", "_redrawSelection", "_didStateChange", "viewportStartRow", "viewportEndRow", "viewportCappedStartRow", "max", "viewportCappedEndRow", "selectionBackgroundTransparent", "selectionInactiveBackgroundTransparent", "startCol", "startRowEndCol", "middleRowsCount", "endCol", "_areCoordinatesEqual", "coord1", "coord2", "_characterJoinerService", "_characterWidth", "_characterFont", "_characterOverlapCache", "_workCell", "<PERSON><PERSON><PERSON><PERSON>", "onSpecificOptionChange", "_setTransparency", "terminalFont", "firstRow", "lastRow", "callback", "row", "line", "joinedRanges", "getJoinedCharacters", "isJoined", "lastCharX", "range", "shift", "JoinedCellData", "translateToString", "_isOverlapping", "getCodePoint", "NULL_CELL_CODE", "ctx", "startX", "startY", "prevFillStyle", "_forEachCell", "nextFillStyle", "isInverse", "isFgDefault", "isFgRGB", "AttributeData", "toColorRGB", "getFgColor", "join", "isBgRGB", "getBgColor", "isBgPalette", "isTop", "forEachDecorationAtCell", "d", "options", "backgroundColorRGB", "_drawChars", "beginFrame", "_drawBackground", "_drawForeground", "hasOwnProperty", "overlaps", "measureText", "$colors", "$fg", "$bg", "$hasFg", "$hasBg", "$isSelected", "_selectionRenderModel", "extended", "rgba", "foregroundColorRGB", "isCellSelected", "selectionBackgroundOpaque", "selectionInactiveBackgroundOpaque", "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceCellWidth", "deviceCellHeight", "deviceCharWidth", "deviceCharHeight", "devicePixelRatio", "newConfig", "generateConfig", "entry", "ownedByIndex", "ownedBy", "indexOf", "configEquals", "config", "atlas", "splice", "core", "_core", "newEntry", "TextureAtlas", "unicodeService", "index", "clonedColors", "NULL_COLOR", "slice", "contrastCache", "halfContrastCache", "drawBoldTextInBrightColors", "minimumContrastRatio", "a", "b", "colorCode", "DIM_OPACITY", "isLegacyEdge", "_renderCallback", "_restartInterval", "_blinkStartTimeout", "_blinkInterval", "clearInterval", "clearTimeout", "_animationFrame", "cancelAnimationFrame", "_animationTimeRestarted", "Date", "now", "requestAnimationFrame", "timeToStart", "BLINK_INTERVAL", "time", "setInterval", "blockElementDefinitions", "patternCharacterDefinitions", "boxDrawingDefinitions", "xp", "yp", "powerlineDefinitions", "type", "rightPadding", "leftPadding", "c", "yOffset", "blockElementDefinition", "charDefinition", "box", "xEighth", "yEighth", "patternDefinition", "patternSet", "cachedPatterns", "Map", "set", "Error", "pattern", "tmpCanvas", "tmpCtx", "imageData", "ImageData", "g", "startsWith", "parseInt", "substring", "split", "map", "parseFloat", "data", "putImageData", "createPattern", "boxDrawingDefinition", "instructions", "Object", "entries", "actualInstructions", "Number", "instruction", "f", "svgToCanvasInstructionMap", "console", "error", "args", "<PERSON><PERSON><PERSON><PERSON>", "powerlineDefinition", "clipRegion", "Path2D", "cssLineWidth", "fill", "clamp", "cellWidth", "cellHeight", "doClamp", "element", "parentWindow", "observer", "ResizeObserver", "find", "target", "disconnect", "devicePixelContentBoxSize", "inlineSize", "blockSize", "observe", "isPowerlineGlyph", "codepoint", "SelectionRenderModel", "constructor", "hasSelection", "active", "viewportY", "NULL_RASTERIZED_GLYPH", "texturePositionClipSpace", "sizeClipSpace", "$glyph", "_pages", "_document", "_config", "_unicodeService", "_didWarmUp", "_cacheMap", "FourKeyMap", "_cacheMapCombined", "_activePages", "_workBoundingBox", "bottom", "right", "_workAttributeData", "_textureSize", "_onRemoveTextureAtlasCanvas", "onRemoveTextureAtlasCanvas", "_requestClearModel", "_createNewPage", "_tmpCanvas", "createCanvas", "TMP_CANVAS_GLYPH_PADDING", "_tmpCtx", "willReadFrequently", "page", "_doWarmUp", "queue", "IdleTaskQueue", "enqueue", "DEFAULT_COLOR", "DEFAULT_EXT", "rasterizedGlyph", "_drawTo<PERSON>ache", "currentRow", "maxAtlasPages", "pagesBySize", "filter", "maxTextureSize", "sort", "percentageUsed", "sameSizeI", "mergingPages", "sortedMergingPagesIndexes", "glyphs", "mergedPageIndex", "mergedPage", "_mergePages", "_deletePage", "newPage", "AtlasPage", "mergedSize", "p", "pageIndex", "j", "adjustingPage", "restrictToCellHeight", "_getFromCacheMap", "code", "cacheMap", "key", "idx", "bgColorMode", "bgColor", "inverse", "_getColorFromAnsiIndex", "arr", "toColor", "color", "opaque", "fgColorMode", "fgColor", "bold", "excludeFromContrastRatioDemands", "minimumContrastColor", "_getMinimumContrastColor", "multiplyOpacity", "_getContrastCache", "adjustedColor", "getColor", "bgRgba", "_resolveBackgroundRgba", "fgRgba", "_resolveForegroundRgba", "ensureContrastRatio", "setColor", "codeOrChars", "String", "fromCharCode", "<PERSON><PERSON><PERSON><PERSON>", "allowedHeight", "isInvisible", "isDim", "italic", "underline", "isUnderline", "strikethrough", "isStrikethrough", "overline", "isOverline", "getFgColorMode", "getBgColorMode", "temp", "temp2", "backgroundColor", "_getBackgroundColor", "globalCompositeOperation", "fontStyle", "powerlineGlyph", "charCodeAt", "restrictedPowerlineGlyph", "isRestrictedPowerlineGlyph", "foregroundColor", "_getForegroundColor", "padding", "customGlyph", "ch<PERSON><PERSON><PERSON>", "enableClearThresholdCheck", "wcwidth", "getStringCell<PERSON>th", "isUnderlineColorDefault", "isUnderlineColorRGB", "getUnderlineColor", "yTop", "yBot", "xChLeft", "xChRight", "xChMid", "underlineStyle", "y<PERSON>urlyBot", "yCurlyTop", "metrics", "actualBoundingBoxDescent", "strokeText", "isBeyondCellBounds", "clearColor", "getImageData", "isEmpty", "_findGlyphBoundingBox", "activePage", "activeRow", "fixedRows", "wasPageAndRowFound", "candidate<PERSON>age", "addGlyph", "boundingBox", "restrictedGlyph", "found", "alphaOffset", "traceCall", "_usedPixels", "_glyphs", "sourcePages", "enableThresholdCheck", "fgR", "fgG", "fgB", "threshold", "abs", "firstCell", "combinedData", "_width", "isCombined", "getAsCharData", "CharacterJoinerService", "_characterJoiners", "_nextCharacterJoinerId", "handler", "joiner", "joinerId", "ranges", "lineStr", "rangeStartColumn", "currentStringIndex", "rangeStartStringIndex", "rangeAttrFG", "getFg", "rangeAttrBG", "getBg", "getTrimmedLength", "_getJoinedRang<PERSON>", "WHITESPACE_CELL_CHAR", "startIndex", "endIndex", "lineData", "text", "allJoinedRanges", "joiner<PERSON><PERSON><PERSON>", "_mergeRanges", "_stringRangesToCellRanges", "currentRangeIndex", "currentRangeStarted", "currentRange", "getString", "newRange", "inRange", "IBufferService", "$r", "$g", "$b", "$a", "channels", "rgb", "toPaddedHex", "contrastRatio", "l1", "l2", "to<PERSON>s", "toRgba", "opacity", "toChannels", "blend", "bgR", "bgG", "bgB", "isOpaque", "ratio", "rgbaColor", "factor", "$ctx", "$litmusColor", "isNode", "createLinearGradient", "match", "repeat", "rgbaMatch", "relativeLuminance2", "rs", "gs", "bs", "pow", "relativeLuminance", "reduceLuminance", "cr", "increaseLuminance", "bgL", "fgL", "resultA", "resultARatio", "resultB", "_listeners", "_disposed", "_event", "listener", "arg1", "arg2", "call", "clearListeners", "from", "to", "dispose<PERSON><PERSON><PERSON>", "disposables", "_disposables", "_isDisposed", "_value", "array", "TwoKeyMap", "_data", "first", "second", "third", "fourth", "navigator", "userAgent", "platform", "includes", "test", "majorVersion", "isMac", "isIpad", "isIphone", "isWindows", "isLinux", "isChromeOS", "TaskQueue", "_tasks", "_i", "task", "_start", "flush", "_idleCallback", "_cancelCallback", "_requestCallback", "_process", "deadline", "taskDuration", "longestTask", "lastDeadlineRemaining", "timeRemaining", "deadlineRemaining", "warn", "PriorityTaskQueue", "_createDeadline", "identifier", "duration", "requestIdleCallback", "cancelIdleCallback", "_queue", "ExtendedAttrs", "clone", "newObj", "hasExtendedAttrs", "isBlink", "isProtected", "isFgPalette", "isBgDefault", "isAttributeDefault", "updateExtended", "underlineColor", "getUnderlineColorMode", "isUnderlineColorPalette", "getUnderlineStyle", "_urlId", "_ext", "urlId", "obj", "setFromCharData", "stringFromCodePoint", "CHAR_DATA_ATTR_INDEX", "combined", "CHAR_DATA_CHAR_INDEX", "CHAR_DATA_WIDTH_INDEX", "DEFAULT_ATTR", "CHAR_DATA_CODE_INDEX", "NULL_CELL_CHAR", "NULL_CELL_WIDTH", "WHITESPACE_CELL_WIDTH", "codePoint", "_interim", "input", "startPos", "interim", "Uint8Array", "byte1", "byte2", "byte3", "byte4", "discardInterim", "cp", "tmp", "pos", "missing", "fourStop", "optionsKeyToLogLevel", "trace", "LogLevelEnum", "TRACE", "debug", "DEBUG", "info", "INFO", "WARN", "ERROR", "off", "OFF", "<PERSON><PERSON><PERSON><PERSON>", "LogService", "logLevel", "_logLevel", "_updateLogLevel", "optionalParams", "message", "_evalLazyOptionalParams", "logger", "_log", "log", "IOptionsService", "_target", "descriptor", "fn", "apply", "name", "JSON", "stringify", "DI_TARGET", "DI_DEPENDENCIES", "serviceRegistry", "ctor", "has", "decorator", "arguments", "createDecorator", "ICoreMouseService", "ICoreService", "ICharsetService", "IInstantiationService", "ILogService", "IOscLinkService", "IUnicodeService", "IDecorationService", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "CanvasAddon", "_renderer", "onWillOpen", "activate", "screenElement", "linkifier", "unsafeCore", "renderService", "_renderService", "charSizeService", "logService", "_logService", "<PERSON><PERSON><PERSON><PERSON>og<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_create<PERSON><PERSON><PERSON>"]}