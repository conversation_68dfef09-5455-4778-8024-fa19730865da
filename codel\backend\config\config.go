package config

import (
	"log"

	"github.com/caarlos0/env/v10"
	"github.com/joho/godotenv"
)

type config struct {
	// General
	DatabaseURL string `env:"DATABASE_URL" envDefault:"database.db"`
	Port        int    `env:"PORT" envDefault:"8080"`

	// OpenAI
	OpenAIKey       string `env:"OPEN_AI_KEY"`
	OpenAIModel     string `env:"OPEN_AI_MODEL" envDefault:"gpt-4-0125-preview"`
	OpenAIServerURL string `env:"OPEN_AI_SERVER_URL" envDefault:"https://api.openai.com/v1"`

	// Ollama
	OllamaModel     string `env:"OLLAMA_MODEL"`
	OllamaServerURL string `env:"OLLAMA_SERVER_URL" envDefault:"http://host.docker.internal:11434"`

	// Gemini
	GeminiAPIKey string `env:"GEMINI_API_KEY"`
	GeminiModel  string `env:"GEMINI_MODEL" envDefault:"gemini-2.0-flash-exp"`

	// Mistral
	MistralAPIKey string `env:"MISTRAL_API_KEY"`
	MistralModel  string `env:"MISTRAL_MODEL" envDefault:"mistral-large-latest"`

	// DeepSeek
	DeepSeekAPIKey string `env:"DEEPSEEK_API_KEY"`
	DeepSeekModel  string `env:"DEEPSEEK_MODEL" envDefault:"deepseek-chat"`
}

var Config config

func Init() {
	godotenv.Load()

	if err := env.ParseWithOptions(&Config, env.Options{
		RequiredIfNoDef: false,
	}); err != nil {
		log.Fatalf("Unable to parse config: %v\n", err)
	}
}
