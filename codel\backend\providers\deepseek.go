package providers

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/semanser/ai-coder/config"
	"github.com/semanser/ai-coder/database"
	"github.com/semanser/ai-coder/templates"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
)

type DeepSeekProvider struct {
	llm llms.Model
}

func (p DeepSeekProvider) New() Provider {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.DeepSeekAPIKey
	}
	
	llm, err := openai.New(
		openai.WithToken(apiKey),
		openai.WithBaseURL("https://api.deepseek.com/v1"),
		openai.WithModel(config.Config.DeepSeekModel),
	)
	if err != nil {
		log.Fatalf("Failed to create DeepSeek client: %v", err)
	}
	return DeepSeekProvider{llm: llm}
}

func (p DeepSeekProvider) Name() ProviderType {
	return ProviderDeepSeek
}

func (p DeepSeekProvider) Summary(query string, n int) (string, error) {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.DeepSeekAPIKey
	}
	
	llm, err := openai.New(
		openai.WithToken(apiKey),
		openai.WithBaseURL("https://api.deepseek.com/v1"),
		openai.WithModel(config.Config.DeepSeekModel),
	)
	if err != nil {
		return "", fmt.Errorf("failed to create DeepSeek client for summary: %v", err)
	}

	prompt := templates.SummaryPrompt(query, n)
	completion, err := llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get summary from DeepSeek: %v", err)
	}
	return completion, nil
}

func (p DeepSeekProvider) DockerImageName(task string) (string, error) {
	prompt := templates.DockerPrompt(task)
	completion, err := p.llm.Call(context.Background(), prompt)
	if err != nil {
		return "", fmt.Errorf("failed to get docker image name from DeepSeek: %v", err)
	}
	return completion, nil
}

func (p DeepSeekProvider) NextTask(args NextTaskOptions) *database.Task {
	var messages []llms.MessageContent

	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		apiKey = config.Config.DeepSeekAPIKey
	}
	
	llm, err := openai.New(
		openai.WithToken(apiKey),
		openai.WithBaseURL("https://api.deepseek.com/v1"),
		openai.WithModel(config.Config.DeepSeekModel),
	)
	if err != nil {
		log.Printf("Failed to create DeepSeek client for next task: %v", err)
		return defaultAskTask("Failed to initialize DeepSeek model for next task generation.")
	}

	prompt := templates.AgentPrompt(args.DockerImage)
	messages = tasksToMessages(args.Tasks, prompt)

	options := []llms.CallOption{
		llms.WithTools(Tools),
		llms.WithStreamingFunc(func(ctx context.Context, chunk []byte) error {
			if StreamFunc != nil {
				StreamFunc(chunk)
			}
			return nil
		}),
	}

	content, err := llm.GenerateContent(context.Background(), messages, options...)
	if err != nil {
		log.Printf("Failed to generate content from DeepSeek, asking user: %v", err)
		return defaultAskTask("There was an error generating the next task.")
	}

	if len(content.Choices) == 0 {
		return defaultAskTask("No choices found for the next task.")
	}

	task, err := toolToTask(content.Choices)
	if err != nil {
		log.Printf("Failed to convert tool call to task, asking user: %v", err)
		return defaultAskTask(fmt.Sprintf("There was an error processing the tool call: %v", err))
	}

	return task
}
