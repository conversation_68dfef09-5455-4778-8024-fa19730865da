export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  JSON: { input: any; output: any; }
  Time: { input: any; output: any; }
  Uint: { input: any; output: any; }
};

export type Browser = {
  __typename?: 'Browser';
  screenshotUrl: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type Flow = {
  __typename?: 'Flow';
  browser: Browser;
  id: Scalars['Uint']['output'];
  model: Model;
  name: Scalars['String']['output'];
  status: FlowStatus;
  tasks: Array<Task>;
  terminal: Terminal;
};

export enum FlowStatus {
  Finished = 'finished',
  InProgress = 'inProgress'
}

export type Log = {
  __typename?: 'Log';
  id: Scalars['Uint']['output'];
  text: Scalars['String']['output'];
};

export type Model = {
  __typename?: 'Model';
  id: Scalars['String']['output'];
  provider: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  _exec: Scalars['String']['output'];
  createFlow: Flow;
  createTask: Task;
  finishFlow: Flow;
};


export type Mutation_ExecArgs = {
  command: Scalars['String']['input'];
  containerId: Scalars['String']['input'];
};


export type MutationCreateFlowArgs = {
  modelId: Scalars['String']['input'];
  modelProvider: Scalars['String']['input'];
};


export type MutationCreateTaskArgs = {
  flowId: Scalars['Uint']['input'];
  query: Scalars['String']['input'];
};


export type MutationFinishFlowArgs = {
  flowId: Scalars['Uint']['input'];
};

export type Query = {
  __typename?: 'Query';
  availableModels: Array<Model>;
  flow: Flow;
  flows: Array<Flow>;
};


export type QueryFlowArgs = {
  id: Scalars['Uint']['input'];
};

export type Subscription = {
  __typename?: 'Subscription';
  browserUpdated: Browser;
  flowUpdated: Flow;
  taskAdded: Task;
  taskUpdated: Task;
  terminalLogsAdded: Log;
};


export type SubscriptionBrowserUpdatedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionFlowUpdatedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionTaskAddedArgs = {
  flowId: Scalars['Uint']['input'];
};


export type SubscriptionTerminalLogsAddedArgs = {
  flowId: Scalars['Uint']['input'];
};

export type Task = {
  __typename?: 'Task';
  args: Scalars['JSON']['output'];
  createdAt: Scalars['Time']['output'];
  id: Scalars['Uint']['output'];
  message: Scalars['String']['output'];
  results: Scalars['JSON']['output'];
  status: TaskStatus;
  type: TaskType;
};

export enum TaskStatus {
  Failed = 'failed',
  Finished = 'finished',
  InProgress = 'inProgress',
  Stopped = 'stopped'
}

export enum TaskType {
  Ask = 'ask',
  Browser = 'browser',
  Code = 'code',
  Done = 'done',
  Input = 'input',
  Terminal = 'terminal'
}

export type Terminal = {
  __typename?: 'Terminal';
  connected: Scalars['Boolean']['output'];
  containerName: Scalars['String']['output'];
  logs: Array<Log>;
};

export type FlowQueryVariables = Exact<{
  id: Scalars['Uint']['input'];
}>;


export type FlowQuery = { __typename?: 'Query', flow: { __typename?: 'Flow', id: any, name: string, status: FlowStatus, model: { __typename?: 'Model', id: string, provider: string }, tasks: Array<{ __typename?: 'Task', id: any, type: TaskType, status: TaskStatus, message: string, args: any, results: any, createdAt: any }>, terminal: { __typename?: 'Terminal', containerName: string, connected: boolean, logs: Array<{ __typename?: 'Log', id: any, text: string }> }, browser: { __typename?: 'Browser', url: string, screenshotUrl: string } } };

export type FlowsQueryVariables = Exact<{ [key: string]: never; }>;


export type FlowsQuery = { __typename?: 'Query', flows: Array<{ __typename?: 'Flow', id: any, name: string, status: FlowStatus, model: { __typename?: 'Model', id: string, provider: string } }> };

export type AvailableModelsQueryVariables = Exact<{ [key: string]: never; }>;


export type AvailableModelsQuery = { __typename?: 'Query', availableModels: Array<{ __typename?: 'Model', id: string, provider: string }> };

export type CreateFlowMutationVariables = Exact<{
  modelProvider: Scalars['String']['input'];
  modelId: Scalars['String']['input'];
}>;


export type CreateFlowMutation = { __typename?: 'Mutation', createFlow: { __typename?: 'Flow', id: any, name: string, status: FlowStatus, model: { __typename?: 'Model', id: string, provider: string } } };

export type CreateTaskMutationVariables = Exact<{
  flowId: Scalars['Uint']['input'];
  query: Scalars['String']['input'];
}>;


export type CreateTaskMutation = { __typename?: 'Mutation', createTask: { __typename?: 'Task', id: any, type: TaskType, status: TaskStatus, message: string, args: any, results: any, createdAt: any } };

export type FinishFlowMutationVariables = Exact<{
  flowId: Scalars['Uint']['input'];
}>;


export type FinishFlowMutation = { __typename?: 'Mutation', finishFlow: { __typename?: 'Flow', id: any, status: FlowStatus } };

export type TaskAddedSubscriptionVariables = Exact<{
  flowId: Scalars['Uint']['input'];
}>;


export type TaskAddedSubscription = { __typename?: 'Subscription', taskAdded: { __typename?: 'Task', id: any, type: TaskType, status: TaskStatus, message: string, args: any, results: any, createdAt: any } };

export type TaskUpdatedSubscriptionVariables = Exact<{ [key: string]: never; }>;


export type TaskUpdatedSubscription = { __typename?: 'Subscription', taskUpdated: { __typename?: 'Task', id: any, type: TaskType, status: TaskStatus, message: string, args: any, results: any, createdAt: any } };

export type FlowUpdatedSubscriptionVariables = Exact<{
  flowId: Scalars['Uint']['input'];
}>;


export type FlowUpdatedSubscription = { __typename?: 'Subscription', flowUpdated: { __typename?: 'Flow', id: any, name: string, status: FlowStatus, model: { __typename?: 'Model', id: string, provider: string }, tasks: Array<{ __typename?: 'Task', id: any, type: TaskType, status: TaskStatus, message: string, args: any, results: any, createdAt: any }> } };

export type BrowserUpdatedSubscriptionVariables = Exact<{
  flowId: Scalars['Uint']['input'];
}>;


export type BrowserUpdatedSubscription = { __typename?: 'Subscription', browserUpdated: { __typename?: 'Browser', url: string, screenshotUrl: string } };

export type TerminalLogsAddedSubscriptionVariables = Exact<{
  flowId: Scalars['Uint']['input'];
}>;


export type TerminalLogsAddedSubscription = { __typename?: 'Subscription', terminalLogsAdded: { __typename?: 'Log', id: any, text: string } };
