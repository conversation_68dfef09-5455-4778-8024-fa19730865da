package templates

import (
	"bytes"
	"fmt" // Added import for fmt
	"io/fs"
	"path"
	"text/template"

	"github.com/semanser/ai-coder/assets" // Added import for assets
)

var RootFolder = "templates"

func Render(fs fs.ReadFileFS, name string, params any) (string, error) {
	p := path.Join(RootFolder, string(name))
	promptBytes, err := fs.ReadFile(p)

	if err != nil {
		return "", err
	}

	prompt := string(promptBytes)

	t := template.Must(template.New(string(name)).Parse(prompt))
	if err != nil {
		return "", err
	}

	buf := &bytes.Buffer{}
	err = t.Execute(buf, params)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

func SummaryPrompt(query string, n int) string {
	prompt, err := Render(assets.PromptTemplates, "prompts/summary.tmpl", map[string]any{
		"Text": query,
		"N":    n,
	})
	if err != nil {
		return fmt.Sprintf("Error rendering summary prompt: %v", err)
	}
	return prompt
}

func DockerPrompt(task string) string {
	prompt, err := Render(assets.PromptTemplates, "prompts/docker.tmpl", map[string]any{
		"Task": task,
	})
	if err != nil {
		return fmt.Sprintf("Error rendering docker prompt: %v", err)
	}
	return prompt
}

func AgentPrompt(dockerImage string) string {
	prompt, err := Render(assets.PromptTemplates, "prompts/agent.tmpl", map[string]any{
		"DockerImage": dockerImage,
	})
	if err != nil {
		return fmt.Sprintf("Error rendering agent prompt: %v", err)
	}
	return prompt
}
